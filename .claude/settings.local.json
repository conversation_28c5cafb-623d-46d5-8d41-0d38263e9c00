{"permissions": {"allow": ["Bash(brew list:*)", "Bash(brew uninstall:*)", "Bash(pip3 uninstall:*)", "Read(/Users/<USER>/**)", "<PERSON><PERSON>(unalias:*)", "Bash(hash -r)", "Read(/Users/<USER>/**)", "Read(/Users/<USER>/**)", "Read(/private/etc/**)", "<PERSON><PERSON>(alias -<PERSON>)", "Bash(unset:*)", "Bash(hash:*)", "<PERSON><PERSON>(source:*)", "Bash(zsh:*)", "<PERSON><PERSON>(curl:*)", "Read(/tmp/**)", "<PERSON><PERSON>(docker exec:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "Read(//private/tmp/**)"], "deny": [], "ask": []}}