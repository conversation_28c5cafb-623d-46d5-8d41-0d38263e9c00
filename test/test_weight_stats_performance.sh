#!/bin/bash

echo "测试权重统计接口性能..."
echo "================================"

# API地址
API_URL="http://localhost:9999/api/v1/weight/stats"

# 获取认证cookie（根据你的实际登录情况调整）
echo "1. 执行5次请求，测试响应时间："
echo "--------------------------------"

for i in {1..5}; do
    echo -n "第 $i 次请求: "
    # 使用 time 命令测量请求时间
    response_time=$(curl -o /dev/null -s -w '%{time_total}' \
        -H "Cookie: session=your_session_cookie" \
        "$API_URL")
    
    # 获取缓存状态
    cache_status=$(curl -s -I \
        -H "Cookie: session=your_session_cookie" \
        "$API_URL" | grep "X-Cache" | awk '{print $2}')
    
    echo "响应时间: ${response_time}s (缓存: ${cache_status})"
    
    # 第一次请求后短暂等待，让缓存生效
    if [ $i -eq 1 ]; then
        sleep 0.5
    fi
done

echo ""
echo "2. 获取接口返回数据："
echo "--------------------------------"
curl -s \
    -H "Cookie: session=your_session_cookie" \
    "$API_URL" | python3 -m json.tool

echo ""
echo "================================"
echo "优化说明："
echo "1. 使用单个优化SQL查询替代N+1查询"
echo "2. 添加了复合索引 idx_weight_history_domain_id"
echo "3. 添加了部分索引 idx_weight_history_weights"
echo "4. Redis缓存时间增加到2分钟"
echo "5. 第一次请求会查询数据库(MISS)，后续请求从缓存读取(HIT)"
echo "================================"