package main

import (
	"fmt"
	"strings"
)

// 模拟处理流程
func simulateOldOrder(html string) string {
	fmt.Println("\n=== 旧的处理顺序（问题版本）===")
	
	// 第1步：SEO关键词注入
	fmt.Println("第1步：注入SEO关键词")
	html = strings.ReplaceAll(html, "内容", "内容 测试关键词ABC")
	html = strings.ReplaceAll(html, "示例", "示例 搜索引擎SEO")
	fmt.Printf("  结果: %s\n", html)
	
	// 第2步：伪原创
	fmt.Println("第2步：伪原创处理")
	// 不影响关键词
	
	// 第3步：简繁转换
	fmt.Println("第3步：简繁转换")
	html = strings.ReplaceAll(html, "搜索引擎", "搜尋引擎")
	html = strings.ReplaceAll(html, "测试", "測試")
	fmt.Printf("  结果: %s\n", html)
	
	// 第8步：拼音标注
	fmt.Println("第8步：拼音标注")
	// 假设给ABC加上特殊字符
	html = strings.ReplaceAll(html, "ABC", "【ABC】")
	fmt.Printf("  结果: %s\n", html)
	
	return html
}

func simulateNewOrder(html string) string {
	fmt.Println("\n=== 新的处理顺序（修复版本）===")
	
	// 第1步：伪原创
	fmt.Println("第1步：伪原创处理")
	// 不影响内容
	
	// 第3步：简繁转换
	fmt.Println("第3步：简繁转换")
	html = strings.ReplaceAll(html, "搜索引擎", "搜尋引擎")
	html = strings.ReplaceAll(html, "测试", "測試")
	fmt.Printf("  结果: %s\n", html)
	
	// 第8步：拼音标注
	fmt.Println("第8步：拼音标注")
	// 给原有内容加特殊字符，但此时还没有关键词
	fmt.Printf("  结果: %s\n", html)
	
	// 第10步：SEO关键词注入（在所有转换之后）
	fmt.Println("第10步：注入SEO关键词")
	html = strings.ReplaceAll(html, "内容", "内容 测试关键词ABC")
	html = strings.ReplaceAll(html, "示例", "示例 搜索引擎SEO")
	fmt.Printf("  结果: %s\n", html)
	
	return html
}

func main() {
	// 测试HTML
	originalHTML := "<html><body>这是内容，示例文本。</body></html>"
	
	fmt.Println("原始HTML:", originalHTML)
	fmt.Println("="*50)
	
	// 测试旧顺序
	oldResult := simulateOldOrder(originalHTML)
	fmt.Println("\n旧顺序最终结果:")
	fmt.Println(oldResult)
	fmt.Println("\n问题：")
	fmt.Println("- '测试关键词ABC' 被转换成 '測試关键词【ABC】'")
	fmt.Println("- '搜索引擎SEO' 被转换成 '搜尋引擎SEO'")
	
	fmt.Println("\n" + "="*50)
	
	// 测试新顺序
	newResult := simulateNewOrder(originalHTML)
	fmt.Println("\n新顺序最终结果:")
	fmt.Println(newResult)
	fmt.Println("\n优势：")
	fmt.Println("- '测试关键词ABC' 保持原样")
	fmt.Println("- '搜索引擎SEO' 保持原样")
	fmt.Println("- SEO关键词不受其他处理步骤影响")
}