package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
	
	"github.com/go-redis/redis/v8"
	"context"
)

func main() {
	// 检查Redis缓存
	ctx := context.Background()
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	
	fmt.Println("=== 检查缓存状态 ===")
	fmt.Println("时间:", time.Now().Format("15:04:05"))
	
	// 1. 检查Redis中的站点配置
	domains := []string{"www.example.com", "example.com"}
	for _, domain := range domains {
		key := fmt.Sprintf("site:%s", domain)
		val, err := rdb.Get(ctx, key).Result()
		if err == redis.Nil {
			fmt.Printf("\nRedis [%s]: 无缓存\n", key)
		} else if err != nil {
			fmt.Printf("\nRedis [%s]: 错误 - %v\n", key, err)
		} else {
			var site map[string]interface{}
			if err := json.Unmarshal([]byte(val), &site); err == nil {
				fmt.Printf("\nRedis [%s]: 有缓存\n", key)
				fmt.Printf("  - ID: %.0f\n", site["ID"])
				fmt.Printf("  - Domain: %s\n", site["Domain"])
				fmt.Printf("  - TargetURL: %s\n", site["TargetURL"])
				fmt.Printf("  - CacheStatus: %s\n", site["CacheStatus"])
			}
		}
	}
	
	// 2. 检查Redis中的HTML缓存
	fmt.Println("\n--- HTML缓存 ---")
	pattern := "html:*example.com*"
	iter := rdb.Scan(ctx, 0, pattern, 100).Iterator()
	count := 0
	for iter.Next(ctx) {
		count++
		fmt.Printf("  找到: %s\n", iter.Val())
	}
	if count == 0 {
		fmt.Printf("  无HTML缓存 (pattern: %s)\n", pattern)
	}
	
	// 3. 通过API获取当前配置
	fmt.Println("\n--- API获取站点配置 ---")
	req, _ := http.NewRequest("GET", "http://localhost:9999/api/v1/sites/15601", nil)
	req.Header.Set("Cookie", "admin_token=78fbaf56b090e982b9c513a2fb7c4678999f8d5e33ffdb11349cb3ef7a6d0f9d")
	
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()
	
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	
	if data, ok := result["data"].(map[string]interface{}); ok {
		fmt.Printf("  - ID: %.0f\n", data["id"])
		fmt.Printf("  - Domain: %s\n", data["domain"])
		fmt.Printf("  - TargetURL: %s\n", data["target_url"])
		fmt.Printf("  - CacheStatus: %s\n", data["cache_status"])
		fmt.Printf("  - UpdatedAt: %s\n", data["updated_at"])
	}
	
	// 4. 测试前台获取
	fmt.Println("\n--- 前台响应测试 ---")
	resp2, err := http.Get("http://www.example.com:9090/")
	if err != nil {
		log.Fatal(err)
	}
	defer resp2.Body.Close()
	
	fmt.Printf("  - X-Cache: %s\n", resp2.Header.Get("X-Cache"))
	fmt.Printf("  - X-Cache-Time: %s\n", resp2.Header.Get("X-Cache-Time"))
	
	// 5. 检查Redis TTL
	fmt.Println("\n--- 缓存TTL ---")
	for _, domain := range domains {
		key := fmt.Sprintf("site:%s", domain)
		ttl, err := rdb.TTL(ctx, key).Result()
		if err == nil && ttl > 0 {
			fmt.Printf("  %s TTL: %v\n", key, ttl)
		}
	}
}