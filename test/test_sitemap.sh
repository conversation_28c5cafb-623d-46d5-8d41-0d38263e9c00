#!/bin/bash

# 测试 sitemap 生成功能
echo "========================================="
echo "测试 Sitemap 生成功能"
echo "========================================="

# 设置测试域名
TEST_DOMAIN="wap.zsrenjun.com"
API_BASE="http://localhost:9999/api/v1"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 登录获取 session
echo -e "${YELLOW}1. 登录管理后台...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  -c cookie.txt)

if [[ $LOGIN_RESPONSE == *"\"success\":true"* ]]; then
  echo -e "${GREEN}✓ 登录成功${NC}"
else
  echo -e "${RED}✗ 登录失败${NC}"
  exit 1
fi

# 获取站点信息
echo -e "${YELLOW}2. 查找测试站点...${NC}"
SITES_RESPONSE=$(curl -s "$API_BASE/sites?domain=$TEST_DOMAIN" -b cookie.txt)
SITE_ID=$(echo $SITES_RESPONSE | grep -o '"id":[0-9]*' | head -1 | grep -o '[0-9]*')

if [ -z "$SITE_ID" ]; then
  echo -e "${RED}✗ 未找到域名为 $TEST_DOMAIN 的站点${NC}"
  echo "请先创建测试站点"
  exit 1
fi

echo -e "${GREEN}✓ 找到站点 ID: $SITE_ID${NC}"

# 生成 sitemap
echo -e "${YELLOW}3. 触发 Sitemap 生成...${NC}"
GENERATE_RESPONSE=$(curl -s -X POST "$API_BASE/sites/$SITE_ID/generate-sitemap" -b cookie.txt)

if [[ $GENERATE_RESPONSE == *"\"success\":true"* ]]; then
  echo -e "${GREEN}✓ Sitemap 生成成功${NC}"
else
  echo -e "${RED}✗ Sitemap 生成失败${NC}"
  echo "响应: $GENERATE_RESPONSE"
fi

# 获取 sitemap 内容
echo -e "${YELLOW}4. 获取 Sitemap 内容...${NC}"
SITEMAP_URL="http://localhost:9090/sitemap.xml"
SITEMAP_CONTENT=$(curl -s -H "Host: $TEST_DOMAIN" $SITEMAP_URL)

if [[ $SITEMAP_CONTENT == *"<?xml"* ]]; then
  echo -e "${GREEN}✓ 成功获取 Sitemap${NC}"
  
  # 检查首页是否在第一位
  echo -e "${YELLOW}5. 验证 Sitemap 格式...${NC}"
  
  # 提取第一个 URL
  FIRST_URL=$(echo "$SITEMAP_CONTENT" | grep -o '<loc>[^<]*</loc>' | head -1 | sed 's/<[^>]*>//g')
  echo "   第一个 URL: $FIRST_URL"
  
  # 检查是否是主域名
  if [[ $FIRST_URL == "https://$TEST_DOMAIN/" ]] || [[ $FIRST_URL == "http://$TEST_DOMAIN/" ]]; then
    echo -e "${GREEN}   ✓ 主域名在第一位${NC}"
  else
    echo -e "${RED}   ✗ 主域名不在第一位${NC}"
  fi
  
  # 检查第一个 URL 的优先级
  FIRST_PRIORITY=$(echo "$SITEMAP_CONTENT" | grep -A3 '<loc>[^<]*</loc>' | head -4 | grep -o '<priority>[^<]*</priority>' | head -1 | sed 's/<[^>]*>//g')
  echo "   第一个 URL 优先级: $FIRST_PRIORITY"
  
  if [[ $FIRST_PRIORITY == "1" ]] || [[ $FIRST_PRIORITY == "1.0" ]]; then
    echo -e "${GREEN}   ✓ 主域名优先级最高 (1.0)${NC}"
  else
    echo -e "${RED}   ✗ 主域名优先级不是 1.0${NC}"
  fi
  
  # 统计 URL 数量
  URL_COUNT=$(echo "$SITEMAP_CONTENT" | grep -c '<loc>')
  echo -e "${YELLOW}   URL 总数: $URL_COUNT${NC}"
  
  # 检查是否有重复的主域名
  HOME_COUNT=$(echo "$SITEMAP_CONTENT" | grep -c "https://$TEST_DOMAIN/")
  if [ $HOME_COUNT -gt 1 ]; then
    echo -e "${RED}   ✗ 发现重复的主域名条目 (数量: $HOME_COUNT)${NC}"
  else
    echo -e "${GREEN}   ✓ 没有重复的主域名${NC}"
  fi
  
  # 显示前5个URL
  echo -e "${YELLOW}6. Sitemap 前5个URL:${NC}"
  echo "$SITEMAP_CONTENT" | grep '<loc>' | head -5 | sed 's/.*<loc>/   - /;s/<\/loc>//'
  
else
  echo -e "${RED}✗ 无法获取 Sitemap 内容${NC}"
fi

# 清理
rm -f cookie.txt

echo -e "${YELLOW}=========================================${NC}"
echo -e "${GREEN}测试完成！${NC}"
echo -e "${YELLOW}=========================================${NC}"