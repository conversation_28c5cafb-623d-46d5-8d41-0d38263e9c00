#!/bin/bash

# 测试SEO关键词注入顺序修复
# 确保SEO关键词不会被拼音和简繁转换影响

API_BASE="http://localhost:9999/api/v1"
COOKIE="session=MTczNjE2NjU0MHxEWDhFQVFMX2dBQUJFQUVRQUFBbl80QUFBUVp6ZEhKcGJtY01CZ0FFWVdSdGFXNGNjM1J5YVc1bkRBY0FCV04xY25KbGJuUUhjM1J5YVc1bkRBY0FCV04xY25KbGJuUThfNEFBfC1sM_NRCGJhD6Dx29FO4j-3EWQGXgNYFBN9YOhQJP2s"

echo "测试SEO关键词注入顺序修复"
echo "=============================="
echo ""

# 1. 创建测试站点
echo "1. 创建测试站点..."
SITE_ID=$(curl -s -X POST "$API_BASE/sites" \
  -H "Cookie: $COOKIE" \
  -H "Content-Type: application/json" \
  -d '{
    "domain": "test-seo-order.com",
    "target_url": "https://example.com",
    "enable_traditional_convert": true,
    "inject_config": {
      "enable_keyword": true,
      "keywords": ["测试关键词ABC", "搜索引擎SEO"],
      "enable_pinyin": true,
      "enable_pinyin_special_chars": true,
      "pinyin_special_chars": "【】",
      "pinyin_special_chars_ratio": 0.3
    }
  }' | grep -o '"id":[0-9]*' | grep -o '[0-9]*')

if [ -z "$SITE_ID" ]; then
  echo "✗ 创建站点失败"
  exit 1
fi
echo "✓ 站点创建成功，ID: $SITE_ID"

# 2. 测试镜像页面
echo ""
echo "2. 测试镜像页面..."
echo "访问: http://localhost:9090 (Host: test-seo-order.com)"
echo ""

# 使用curl测试镜像页面
RESPONSE=$(curl -s "http://localhost:9090/" \
  -H "Host: test-seo-order.com" \
  -H "User-Agent: Mozilla/5.0")

# 检查关键词是否存在且未被转换
echo "3. 检查SEO关键词："
echo "-------------------"

# 检查关键词ABC是否保持原样（不被拼音转换）
if echo "$RESPONSE" | grep -q "测试关键词ABC"; then
  echo "✓ '测试关键词ABC' 保持原样，未被拼音转换"
else
  echo "✗ '测试关键词ABC' 可能被转换了"
  echo "  检查是否被拼音化..."
  if echo "$RESPONSE" | grep -q "测试关键词【ABC】"; then
    echo "  ✗ 发现被拼音特殊字符包裹: 测试关键词【ABC】"
  fi
fi

# 检查关键词SEO是否保持原样
if echo "$RESPONSE" | grep -q "搜索引擎SEO"; then
  echo "✓ '搜索引擎SEO' 保持原样，未被转换"
else
  echo "✗ '搜索引擎SEO' 可能被转换了"
  echo "  检查是否被简繁转换..."
  if echo "$RESPONSE" | grep -q "搜尋引擎SEO"; then
    echo "  ✗ 发现被简繁转换: 搜尋引擎SEO"
  fi
fi

# 4. 清理测试数据
echo ""
echo "4. 清理测试数据..."
curl -s -X DELETE "$API_BASE/sites/$SITE_ID" \
  -H "Cookie: $COOKIE" > /dev/null

echo "✓ 测试完成"
echo ""
echo "总结："
echo "------"
echo "如果SEO关键词保持原样，说明修复成功。"
echo "如果SEO关键词被转换，说明还需要进一步调整。"