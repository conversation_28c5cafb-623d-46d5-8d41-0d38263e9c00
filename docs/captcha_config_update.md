# 验证码配置功能更新文档

## 更新时间
2025年1月15日

## 功能说明
为系统添加了登录验证码的开关功能，管理员可以在系统设置中启用或禁用登录验证码。

## 数据库更改

### system_settings 表新增字段：
- `enable_captcha` (BOOLEAN, DEFAULT FALSE) - 是否启用登录验证码
- `captcha_length` (INTEGER, DEFAULT 4) - 验证码长度（3-8位）
- `captcha_expiry` (INTEGER, DEFAULT 300) - 验证码过期时间（秒）

### 执行迁移脚本
```bash
psql -U sitecluster -d sitecluster -f scripts/add_captcha_config.sql
```

## 代码更改

### 1. 后端更改

#### model/system_settings.go
- 添加验证码配置字段

#### service/system_settings.go
- 添加 `IsCaptchaEnabled()` 方法检查验证码是否启用
- 添加 `GetCaptchaConfig()` 方法获取验证码配置
- 更新 `UpdateSystemSettings()` 支持保存验证码配置

#### handler/auth.go
- 添加 `GetCaptchaConfig()` 处理函数
- 更新 `Login()` 方法，根据配置决定是否验证验证码
- 更新 `AuthHandler` 结构体，添加 `systemSettingsService`

#### router.go
- 添加 `/api/v1/auth/captcha-config` 路由
- 更新 `NewAuthHandler` 调用，传入 `systemSettingsService`

### 2. 前端更改

#### templates/settings.html
- 在基础设置面板中添加验证码配置区域
- 添加验证码开关和相关配置项
- 更新保存和加载逻辑，支持验证码配置

#### templates/login.html
- 验证码输入框默认隐藏
- 页面加载时调用 API 检查验证码配置
- 根据配置动态显示/隐藏验证码
- 登录请求根据配置决定是否发送验证码字段

## API 变更

### 新增 API
- `GET /api/v1/auth/captcha-config` - 获取验证码配置（无需认证）
  - 响应格式：
    ```json
    {
      "success": true,
      "data": {
        "enabled": false,
        "length": 4,
        "expiry": 300
      }
    }
    ```

### 修改 API
- `POST /api/v1/auth/login` - 验证码字段变为可选
  - 当验证码未启用时，可以不传 `captcha_id` 和 `captcha_code`
  - 当验证码启用时，必须传入有效的验证码

## 使用说明

### 启用验证码
1. 登录管理后台
2. 进入"系统设置"页面
3. 在"基础设置"标签页中找到"登录验证码设置"
4. 打开"启用登录验证码"开关
5. 设置验证码长度和过期时间
6. 点击"保存设置"

### 禁用验证码
1. 在系统设置中关闭"启用登录验证码"开关
2. 保存设置后立即生效

## 注意事项
1. 验证码配置更改后立即生效，无需重启服务
2. 默认情况下验证码是禁用的
3. 验证码长度范围：3-8位
4. 验证码过期时间范围：60-1800秒（1-30分钟）
5. 启用验证码可以有效防止暴力破解攻击

## 安全建议
- 生产环境建议启用验证码
- 配合IP限制功能使用效果更佳
- 定期检查登录日志，发现异常及时处理