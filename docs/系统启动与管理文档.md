# 站群管理系统 - 完整启动与管理文档

## 目录
1. [系统要求](#系统要求)
2. [快速启动](#快速启动)
3. [启动方式详解](#启动方式详解)
4. [自动恢复机制](#自动恢复机制)
5. [性能优化配置](#性能优化配置)
6. [故障排查](#故障排查)
7. [监控与健康检查](#监控与健康检查)
8. [API接口文档](#api接口文档)

---

## 系统要求

### 软件环境
- **Go**: 1.23.0 或更高版本
- **PostgreSQL**: 12.0 或更高版本
- **Redis**: 6.0 或更高版本
- **操作系统**: Linux/macOS/Windows

### 硬件建议
- **CPU**: 4核心或以上
- **内存**: 4GB或以上
- **硬盘**: 50GB可用空间（用于缓存）
- **网络**: 稳定的互联网连接

---

## 快速启动

### 方法1: 使用启动脚本（推荐）
```bash
# 1. 设置执行权限
chmod +x start.sh

# 2. 启动服务
./start.sh

# 服务将在以下端口启动：
# - 9090: 前台镜像站点访问
# - 9999: 后台管理系统
```

### 方法2: 使用自动恢复脚本
```bash
# 1. 设置执行权限
chmod +x auto_restart.sh

# 2. 启动带自动恢复的服务
./auto_restart.sh

# 特性：
# - 每30秒健康检查
# - 自动重启崩溃的服务
# - 记录重启日志
```

### 方法3: Docker Compose启动
```bash
# 1. 构建镜像
make build

# 2. 启动所有服务
make up

# 3. 查看日志
make logs

# 4. 停止服务
make down
```

---

## 启动方式详解

### 1. 手动启动（开发模式）

```bash
# 设置环境变量
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=sitecluster
export DB_PASSWORD=sitecluster123
export DB_NAME=sitecluster
export REDIS_HOST=localhost
export REDIS_PORT=6379
export PORT=9090
export ADMIN_PORT=9999

# 编译程序
go build -o site-cluster cmd/server/main.go

# 运行数据库迁移
./site-cluster migrate

# 启动服务
./site-cluster
```

### 2. 使用start.sh脚本（生产模式）

`start.sh`脚本内容：
```bash
#!/bin/bash

# 设置环境变量
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=sitecluster
export DB_PASSWORD=sitecluster123
export DB_NAME=sitecluster
export REDIS_HOST=localhost
export REDIS_PORT=6379
export PORT=9090
export ADMIN_PORT=9999

# 编译最新代码
echo "正在编译..."
go build -o site-cluster cmd/server/main.go

if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

# 运行迁移
echo "运行数据库迁移..."
./site-cluster migrate

# 启动服务
echo "启动服务..."
echo "前台访问: http://localhost:9090"
echo "后台管理: http://localhost:9999"
echo "默认账号: admin / admin123"

./site-cluster
```

### 3. 使用auto_restart.sh（自动恢复模式）

`auto_restart.sh`脚本内容：
```bash
#!/bin/bash

# 配置
HEALTH_CHECK_URL="http://localhost:9090/health"
CHECK_INTERVAL=30
MAX_RETRIES=3
LOG_FILE="auto_restart.log"

# 健康检查函数
health_check() {
    curl -sf "$HEALTH_CHECK_URL" >/dev/null 2>&1
    return $?
}

# 启动服务函数
start_service() {
    echo "[$(date)] 启动服务..." | tee -a "$LOG_FILE"
    ./start.sh &
    SERVICE_PID=$!
    echo "[$(date)] 服务PID: $SERVICE_PID" | tee -a "$LOG_FILE"
    sleep 10  # 等待服务启动
}

# 停止服务函数
stop_service() {
    echo "[$(date)] 停止服务..." | tee -a "$LOG_FILE"
    if [ ! -z "$SERVICE_PID" ]; then
        kill $SERVICE_PID 2>/dev/null
        wait $SERVICE_PID 2>/dev/null
    fi
    pkill -f site-cluster 2>/dev/null
}

# 清理函数
cleanup() {
    echo "[$(date)] 收到退出信号，正在清理..." | tee -a "$LOG_FILE"
    stop_service
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主循环
echo "[$(date)] 自动恢复监控启动" | tee -a "$LOG_FILE"

start_service

while true; do
    sleep $CHECK_INTERVAL
    
    if ! health_check; then
        echo "[$(date)] 健康检查失败" | tee -a "$LOG_FILE"
        
        # 重试健康检查
        retry_count=0
        while [ $retry_count -lt $MAX_RETRIES ]; do
            sleep 5
            if health_check; then
                echo "[$(date)] 服务恢复正常" | tee -a "$LOG_FILE"
                break
            fi
            retry_count=$((retry_count + 1))
        done
        
        # 如果仍然失败，重启服务
        if [ $retry_count -eq $MAX_RETRIES ]; then
            echo "[$(date)] 多次健康检查失败，重启服务" | tee -a "$LOG_FILE"
            stop_service
            start_service
        fi
    fi
done
```

### 4. 使用系统服务（systemd）

创建服务文件 `/etc/systemd/system/site-cluster.service`：
```ini
[Unit]
Description=Site Cluster Management System
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/site-cluster
Environment="DB_HOST=localhost"
Environment="DB_PORT=5432"
Environment="DB_USER=sitecluster"
Environment="DB_PASSWORD=sitecluster123"
Environment="DB_NAME=sitecluster"
Environment="REDIS_HOST=localhost"
Environment="REDIS_PORT=6379"
Environment="PORT=9090"
Environment="ADMIN_PORT=9999"
ExecStart=/opt/site-cluster/site-cluster
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

管理命令：
```bash
# 启用服务
sudo systemctl enable site-cluster

# 启动服务
sudo systemctl start site-cluster

# 查看状态
sudo systemctl status site-cluster

# 查看日志
sudo journalctl -u site-cluster -f

# 重启服务
sudo systemctl restart site-cluster

# 停止服务
sudo systemctl stop site-cluster
```

---

## 自动恢复机制

系统内置三种自动恢复机制：

### 1. Shell脚本监控（auto_restart.sh）
- **优点**: 简单可靠，无需系统权限
- **功能**: 定期健康检查，自动重启
- **使用**: `./auto_restart.sh`

### 2. 应用内置监控（SelfMonitor）
- **位置**: `/internal/service/self_monitor.go`
- **功能**: 
  - 监控goroutine数量
  - 监控内存使用
  - 自动重启进程
- **配置**: 自动启用

### 3. 健康检查端点
- **端点**: `GET /health`
- **返回**: 
```json
{
    "status": "healthy|degraded|unhealthy",
    "time": "2024-01-01T00:00:00Z",
    "goroutines": 100,
    "memory_mb": 256,
    "issues": []
}
```

---

## 性能优化配置

### 1. 工作池配置
系统使用动态工作池限制并发：
- **最小工作协程**: 5
- **最大工作协程**: 20
- **自动伸缩**: 根据负载自动调整

### 2. HTTP客户端优化
- **连接池大小**: 100
- **每主机最大连接**: 10
- **空闲连接超时**: 90秒

### 3. 数据库连接池
- **最大连接数**: 25（优化后）
- **最大空闲连接**: 10
- **连接生命周期**: 5分钟

### 4. 熔断器配置
- **失败阈值**: 5次
- **恢复时间**: 60秒
- **半开状态测试**: 1次

### 5. 限流配置
- **全局限流**: 1000请求/分钟
- **IP限流**: 100请求/分钟
- **域名限流**: 500请求/分钟

### 6. 缓存清理
- **自动清理间隔**: 24小时
- **过期时间**: 7天
- **大文件阈值**: 10MB

---

## 故障排查

### 1. 服务无法启动

#### 检查数据库连接
```bash
# 测试PostgreSQL连接
psql -h localhost -U sitecluster -d sitecluster -c "SELECT 1"

# 测试Redis连接
redis-cli -h localhost -p 6379 ping
```

#### 检查端口占用
```bash
# 检查9090端口
lsof -i :9090

# 检查9999端口
lsof -i :9999

# 强制释放端口
kill -9 $(lsof -t -i :9090)
kill -9 $(lsof -t -i :9999)
```

#### 查看错误日志
```bash
# 查看程序日志
tail -f logs/app.log

# 查看自动重启日志
tail -f auto_restart.log

# 查看系统日志（如果使用systemd）
journalctl -u site-cluster -n 100
```

### 2. 高并发崩溃问题

#### 症状
- goroutine数量激增
- 内存快速增长
- 网络连接耗尽

#### 解决方案
系统已实施以下优化：
1. **工作池限制**: 限制并发goroutine数量
2. **HTTP连接池**: 复用连接，防止连接耗尽
3. **熔断器**: 自动隔离故障站点
4. **限流器**: 防止请求过载

#### 手动干预
```bash
# 查看goroutine数量
curl http://localhost:9090/health | jq .goroutines

# 查看系统指标
curl http://localhost:9999/api/v1/system/metrics

# 清理缓存
curl -X DELETE http://localhost:9999/api/v1/cache/expired
```

### 3. 内存泄漏问题

#### 监控内存使用
```bash
# 查看进程内存
ps aux | grep site-cluster

# 持续监控
watch -n 1 'ps aux | grep site-cluster'
```

#### 内存分析
```bash
# 获取pprof数据
go tool pprof http://localhost:9999/debug/pprof/heap

# 查看goroutine
go tool pprof http://localhost:9999/debug/pprof/goroutine
```

---

## 监控与健康检查

### 1. 健康检查接口

#### 基础健康检查
```bash
curl http://localhost:9090/health
```

#### 详细系统信息
```bash
curl -H "Cookie: admin_token=YOUR_TOKEN" \
     http://localhost:9999/api/v1/system/info
```

### 2. 性能指标

#### 获取实时指标
```bash
curl -H "Cookie: admin_token=YOUR_TOKEN" \
     http://localhost:9999/api/v1/system/metrics
```

响应示例：
```json
{
    "cpu_usage": 15.5,
    "memory_usage": 256,
    "goroutines": 150,
    "active_connections": 25,
    "cache_size": 1024,
    "request_rate": 100,
    "response_time_avg": 50
}
```

#### 导出指标数据
```bash
curl -H "Cookie: admin_token=YOUR_TOKEN" \
     http://localhost:9999/api/v1/system/metrics/export > metrics.json
```

### 3. 爬虫统计

#### 查看爬虫访问统计
```bash
curl -H "Cookie: admin_token=YOUR_TOKEN" \
     http://localhost:9999/api/v1/spider-stats
```

#### 查看爬虫趋势
```bash
curl -H "Cookie: admin_token=YOUR_TOKEN" \
     "http://localhost:9999/api/v1/spider-stats/trend?period=24h"
```

---

## API接口文档

### 认证接口

#### 登录
```bash
curl -X POST http://localhost:9999/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}'
```

#### 保持会话
```bash
# 保存cookie
curl -c cookies.txt -X POST http://localhost:9999/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "admin123"}'

# 使用cookie访问
curl -b cookies.txt http://localhost:9999/api/v1/sites
```

### 站点管理

#### 获取站点列表
```bash
curl -b cookies.txt \
     "http://localhost:9999/api/v1/sites?page=1&limit=10"
```

#### 创建站点
```bash
curl -b cookies.txt -X POST \
     http://localhost:9999/api/v1/sites \
     -H "Content-Type: application/json" \
     -d '{
         "domain": "example.com",
         "target_url": "https://target.com",
         "crawl_depth": 3,
         "status": "active"
     }'
```

#### 更新站点
```bash
curl -b cookies.txt -X PUT \
     http://localhost:9999/api/v1/sites/1 \
     -H "Content-Type: application/json" \
     -d '{
         "enable_cache": true,
         "cache_ttl": 3600
     }'
```

### 缓存管理

#### 获取缓存统计
```bash
curl -b cookies.txt \
     http://localhost:9999/api/v1/cache/stats
```

#### 清理站点缓存
```bash
curl -b cookies.txt -X DELETE \
     http://localhost:9999/api/v1/cache/site/example.com
```

#### 清理过期缓存
```bash
curl -b cookies.txt -X DELETE \
     http://localhost:9999/api/v1/cache/expired
```

### 系统设置

#### 获取系统设置
```bash
curl -b cookies.txt \
     http://localhost:9999/api/v1/system/settings
```

#### 更新系统设置
```bash
curl -b cookies.txt -X PUT \
     http://localhost:9999/api/v1/system/settings \
     -H "Content-Type: application/json" \
     -d '{
         "max_concurrent_crawls": 10,
         "cache_cleanup_interval": 24,
         "enable_metrics": true
     }'
```

---

## 常见问题

### Q1: 如何修改默认端口？
**A**: 修改环境变量 `PORT` 和 `ADMIN_PORT`：
```bash
export PORT=8080
export ADMIN_PORT=8888
```

### Q2: 如何增加并发处理能力？
**A**: 系统已自动优化，但可通过以下方式调整：
1. 增加工作池大小（修改`worker_pool.go`）
2. 增加数据库连接池（修改`db_optimized.go`）
3. 调整限流参数（修改`rate_limiter.go`）

### Q3: 如何备份数据？
**A**: 
```bash
# 备份数据库
pg_dump -U sitecluster sitecluster > backup.sql

# 备份缓存文件
tar -czf cache_backup.tar.gz cache/

# 恢复数据库
psql -U sitecluster sitecluster < backup.sql
```

### Q4: 如何查看实时日志？
**A**: 
```bash
# 应用日志
tail -f logs/app.log

# 错误日志
tail -f logs/error.log

# 访问日志
tail -f logs/access.log
```

### Q5: 系统崩溃后如何恢复？
**A**: 
1. 使用自动恢复脚本：`./auto_restart.sh`
2. 手动重启：`./start.sh`
3. 清理进程：`pkill -f site-cluster && ./start.sh`

---

## 安全建议

1. **修改默认密码**
   ```bash
   # 登录后修改admin密码
   curl -b cookies.txt -X POST \
        http://localhost:9999/api/v1/auth/change-password \
        -H "Content-Type: application/json" \
        -d '{"old_password": "admin123", "new_password": "NewSecurePass123!"}'
   ```

2. **限制访问IP**
   - 使用防火墙限制9999端口访问
   - 配置Nginx反向代理
   - 启用HTTPS

3. **定期备份**
   - 设置自动备份脚本
   - 异地备份重要数据

4. **监控告警**
   - 配置监控系统（Prometheus/Grafana）
   - 设置异常告警

---

## 性能调优建议

### 1. 数据库优化
```sql
-- 定期分析表
ANALYZE sites;
ANALYZE spider_stats;

-- 重建索引
REINDEX TABLE sites;

-- 清理死元组
VACUUM ANALYZE;
```

### 2. Redis优化
```bash
# 设置最大内存
redis-cli CONFIG SET maxmemory 2gb

# 设置淘汰策略
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

### 3. 系统优化
```bash
# 增加文件描述符限制
ulimit -n 65535

# 优化TCP参数
sysctl -w net.ipv4.tcp_fin_timeout=30
sysctl -w net.ipv4.tcp_keepalive_time=1200
```

---

## 更新日志

### v2.0.0 (2024-01-XX)
- ✅ 实现工作池限制并发
- ✅ 添加HTTP连接池
- ✅ 实现熔断器模式
- ✅ 添加多级限流
- ✅ 优化数据库连接池
- ✅ 实现自动缓存清理
- ✅ 添加自动恢复机制
- ✅ 修复goroutine泄漏
- ✅ 添加context超时控制
- ✅ 完善健康检查

---

## 技术支持

- **项目地址**: [内部Git仓库]
- **问题反馈**: 请提交Issue
- **技术文档**: /docs目录
- **API文档**: http://localhost:9999/api/docs（开发中）

---

**最后更新**: 2024-01-XX
**版本**: 2.0.0
**作者**: 站群管理系统团队