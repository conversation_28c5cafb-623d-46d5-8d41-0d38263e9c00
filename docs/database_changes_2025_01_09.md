# 数据库变更记录 - 2025-01-09

## 关键词库独立选择功能

### 变更说明
为了支持标题、描述、关键词分别使用不同的关键词库，对 `inject_configs` 表进行了扩展。

### 新增字段

**表名：** `inject_configs`

| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title_keyword_library_ids | json | [] | 标题使用的关键词库ID列表 |
| meta_keyword_library_ids | json | [] | 关键词Meta标签使用的关键词库ID列表 |
| desc_keyword_library_ids | json | [] | 描述使用的关键词库ID列表 |

### 兼容性说明
- 保留原有的 `keyword_library_ids` 字段，用于向后兼容
- 如果新字段为空，系统将回退使用 `keyword_library_ids` 字段
- 新建站点时，如果只选择了通用关键词库，会同时设置到所有位置

### 迁移SQL
```sql
-- 添加新字段
ALTER TABLE inject_configs 
ADD COLUMN IF NOT EXISTS title_keyword_library_ids json DEFAULT '[]',
ADD COLUMN IF NOT EXISTS meta_keyword_library_ids json DEFAULT '[]',
ADD COLUMN IF NOT EXISTS desc_keyword_library_ids json DEFAULT '[]';

-- 将现有数据迁移到新字段（可选）
UPDATE inject_configs 
SET title_keyword_library_ids = keyword_library_ids,
    meta_keyword_library_ids = keyword_library_ids,
    desc_keyword_library_ids = keyword_library_ids
WHERE keyword_library_ids IS NOT NULL 
  AND keyword_library_ids != '[]'
  AND title_keyword_library_ids = '[]';
```

### 影响范围
1. 站点配置API需要更新，支持新字段的读写
2. 前端站点设置页面需要增加三个独立的关键词库选择器
3. 关键词注入逻辑需要根据不同位置使用对应的关键词库