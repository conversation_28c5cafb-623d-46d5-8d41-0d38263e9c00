# Docker 部署指南

## 🐳 完全 Docker 化部署

### 系统架构
```
┌─────────────────────────────────────────────────┐
│                   用户访问                        │
└─────────────┬───────────────┬───────────────────┘
              ↓               ↓
        ┌─────────────┐ ┌─────────────┐
        │   前台镜像   │ │  后台管理    │
        │  Port:9090  │ │  Port:9999  │
        └─────┬───────┘ └──────┬──────┘
              ↓                ↓
    ┌─────────────────────────────────┐
    │     site-cluster-app 容器        │
    │    (Go + Gin Web Framework)      │
    └────────┬────────────┬────────────┘
             ↓            ↓
    ┌────────────┐  ┌────────────┐
    │ PostgreSQL │  │   Redis    │
    │  Port:5432 │  │ Port:6380  │
    └────────────┘  └────────────┘
```

### 快速启动

#### 1. 构建并启动所有服务
```bash
# 构建镜像
docker compose build

# 启动所有服务（后台运行）
docker compose up -d

# 查看服务状态
docker compose ps
```

#### 2. 停止服务
```bash
# 停止所有服务
docker compose down

# 停止并删除数据卷（谨慎使用）
docker compose down -v
```

### 服务端口

| 服务 | 端口 | 说明 |
|------|------|------|
| 前台镜像站点 | 9090 | 访问镜像站点 |
| 后台管理系统 | 9999 | 管理员界面 |
| PostgreSQL | 5432 | 数据库 |
| Redis | 6380 | 缓存服务 |
| pgAdmin | 8082 | 数据库管理界面（可选） |
| Redis Commander | 8081 | Redis管理界面（可选） |

### 默认账号

- **后台管理系统**
  - 用户名：admin
  - 密码：wanshun

- **pgAdmin** (http://localhost:8082)
  - 邮箱：<EMAIL>
  - 密码：admin123

### 环境变量配置

编辑 `.env` 文件来修改默认配置：

```env
# Docker Compose 项目名称
COMPOSE_PROJECT_NAME=site-cluster

# 数据库配置
DB_USER=sitecluster
DB_PASSWORD=sitecluster123
DB_NAME=sitecluster

# Redis配置
REDIS_PASSWORD=

# 管理界面配置
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123

# 应用配置
GIN_MODE=release
CACHE_MAX_SIZE=10737418240  # 10GB
CACHE_TTL=86400  # 24小时
```

### 数据持久化

Docker Compose 使用以下卷来持久化数据：

- `postgres_data` - PostgreSQL 数据
- `redis_data` - Redis 持久化数据
- `pgadmin_data` - pgAdmin 配置
- `./cache` - 缓存文件（挂载到宿主机）
- `./data` - 应用数据（挂载到宿主机）

### 常用命令

```bash
# 查看容器日志
docker compose logs -f app

# 进入容器内部
docker exec -it site-cluster-app sh

# 重启单个服务
docker compose restart app

# 查看容器资源使用
docker stats site-cluster-app

# 备份数据库
docker exec site-cluster-postgres pg_dump -U sitecluster sitecluster > backup.sql

# 恢复数据库
docker exec -i site-cluster-postgres psql -U sitecluster sitecluster < backup.sql
```

### 健康检查

系统包含自动健康检查：

```bash
# 检查服务健康状态
docker compose ps

# 手动测试 API
curl http://localhost:9999/api/v1/auth/check

# 测试镜像功能
curl -H "Host: example.com" http://localhost:9090/
```

### 故障排除

1. **容器无法启动**
   ```bash
   # 查看详细日志
   docker compose logs app
   
   # 检查端口占用
   lsof -i :9090
   lsof -i :9999
   ```

2. **数据库连接失败**
   ```bash
   # 确保数据库健康
   docker compose ps postgres
   
   # 查看数据库日志
   docker compose logs postgres
   ```

3. **Redis 连接问题**
   ```bash
   # 测试 Redis 连接
   docker exec site-cluster-redis redis-cli ping
   ```

4. **重新构建镜像**
   ```bash
   # 强制重新构建
   docker compose build --no-cache app
   ```

### 生产环境优化

1. **使用外部数据库**
   - 修改 `docker-compose.yml` 中的环境变量
   - 指向外部 PostgreSQL 和 Redis 实例

2. **启用 HTTPS**
   - 在前面添加 Nginx 反向代理
   - 配置 SSL 证书

3. **资源限制**
   ```yaml
   services:
     app:
       deploy:
         resources:
           limits:
             cpus: '2'
             memory: 4G
           reservations:
             cpus: '1'
             memory: 2G
   ```

4. **日志管理**
   ```yaml
   services:
     app:
       logging:
         driver: "json-file"
         options:
           max-size: "10m"
           max-file: "3"
   ```

### 监控和维护

1. **查看资源使用**
   ```bash
   docker system df
   docker stats
   ```

2. **清理未使用资源**
   ```bash
   docker system prune -a
   ```

3. **备份策略**
   - 定期备份 PostgreSQL 数据
   - 备份 `cache` 和 `data` 目录
   - 保存 `.env` 配置文件

### 更新流程

1. 拉取最新代码
2. 重新构建镜像：`docker compose build app`
3. 停止旧容器：`docker compose stop app`
4. 启动新容器：`docker compose up -d app`
5. 验证服务：`docker compose logs -f app`

---

## 注意事项

- 首次启动时会自动初始化数据库
- 缓存文件存储在宿主机的 `./cache` 目录
- 确保有足够的磁盘空间用于缓存
- 生产环境建议使用独立的数据库和缓存服务

---

*最后更新：2025-08-09*