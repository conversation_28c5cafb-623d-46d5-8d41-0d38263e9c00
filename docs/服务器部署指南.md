# 服务器部署指南

## 📦 部署方式选择

### 方式一：Docker 镜像部署（推荐）

适用于：已安装 Docker 的服务器

### 方式二：二进制文件部署

适用于：不想使用 Docker 的场景

---

## 🐳 方式一：Docker 镜像部署（推荐）

### 1. 本地打包

```bash
# 在开发机器上执行打包脚本
./build.sh
```

打包脚本会自动：
- 构建 Docker 镜像
- 导出镜像文件
- 打包所有必要文件
- 生成部署脚本

生成的文件：`site-cluster-deploy-YYYYMMDD_HHMMSS.tar.gz`

### 2. 上传到服务器

```bash
# 使用 scp 上传
scp site-cluster-deploy-*.tar.gz root@服务器IP:/opt/

# 或使用 rsync
rsync -avz site-cluster-deploy-*.tar.gz root@服务器IP:/opt/
```

### 3. 服务器部署

登录服务器后执行：

```bash
# 1. 进入部署目录
cd /opt

# 2. 解压部署包
tar -xzf site-cluster-deploy-*.tar.gz

# 3. 编辑配置文件
vi .env
# 修改以下重要配置：
# - DB_PASSWORD：数据库密码
# - REDIS_PASSWORD：Redis密码（可选）
# - 其他根据需要调整

# 4. 执行部署
./deploy.sh
```

### 4. 验证部署

```bash
# 查看服务状态
docker compose ps

# 查看应用日志
docker compose logs -f app

# 测试访问
curl http://localhost:9999/api/v1/auth/check
```

### 5. 管理命令

```bash
# 停止服务
./stop.sh

# 重启服务
./restart.sh

# 查看日志
./logs.sh

# 进入容器
docker exec -it site-cluster-app sh
```

---

## 📄 方式二：二进制文件部署

### 1. 编译二进制文件

```bash
# 在开发机器上编译（Linux 64位）
GOOS=linux GOARCH=amd64 go build -o site-cluster cmd/server/main.go

# 编译 ARM 架构（如树莓派）
GOOS=linux GOARCH=arm64 go build -o site-cluster cmd/server/main.go
```

### 2. 准备部署包

创建部署目录结构：

```
site-cluster-binary/
├── site-cluster          # 二进制文件
├── web/                  # Web静态文件
│   ├── static/
│   └── templates/
├── scripts/              # 数据库脚本
│   └── init-db.sql
├── config.env            # 环境变量配置
├── start.sh              # 启动脚本
└── install.sh            # 安装脚本
```

创建 `config.env`：

```bash
# 数据库配置
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=sitecluster
export DB_PASSWORD=your_password
export DB_NAME=sitecluster

# Redis配置
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=

# 端口配置
export PORT=9090
export ADMIN_PORT=9999

# 缓存配置
export CACHE_DIR=/opt/site-cluster/cache
```

创建 `start.sh`：

```bash
#!/bin/bash

# 加载环境变量
source config.env

# 创建必要目录
mkdir -p cache logs data

# 启动服务
nohup ./site-cluster > logs/app.log 2>&1 &

echo "服务已启动，PID: $!"
echo $! > app.pid
```

创建 `install.sh`：

```bash
#!/bin/bash

echo "安装站群系统..."

# 1. 安装 PostgreSQL
apt-get update
apt-get install -y postgresql postgresql-contrib

# 2. 安装 Redis
apt-get install -y redis-server

# 3. 创建数据库
sudo -u postgres psql << EOF
CREATE USER sitecluster WITH PASSWORD 'your_password';
CREATE DATABASE sitecluster OWNER sitecluster;
GRANT ALL PRIVILEGES ON DATABASE sitecluster TO sitecluster;
EOF

# 4. 初始化数据库
PGPASSWORD=your_password psql -h localhost -U sitecluster -d sitecluster < scripts/init-db.sql

# 5. 配置权限
chmod +x site-cluster
chmod +x start.sh

echo "安装完成！"
echo "请编辑 config.env 配置文件后执行 ./start.sh 启动服务"
```

### 3. 打包并上传

```bash
# 打包
tar -czf site-cluster-binary.tar.gz site-cluster-binary/

# 上传
scp site-cluster-binary.tar.gz root@服务器IP:/opt/
```

### 4. 服务器部署

```bash
# 解压
cd /opt
tar -xzf site-cluster-binary.tar.gz
cd site-cluster-binary

# 安装依赖（首次）
./install.sh

# 配置
vi config.env

# 启动
./start.sh

# 查看日志
tail -f logs/app.log
```

### 5. 配置 systemd 服务（可选）

创建 `/etc/systemd/system/site-cluster.service`：

```ini
[Unit]
Description=Site Cluster Service
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/site-cluster-binary
EnvironmentFile=/opt/site-cluster-binary/config.env
ExecStart=/opt/site-cluster-binary/site-cluster
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
systemctl daemon-reload
systemctl enable site-cluster
systemctl start site-cluster
systemctl status site-cluster
```

---

## 🔒 生产环境安全配置

### 1. 防火墙配置

```bash
# 只开放必要端口
ufw allow 9090/tcp  # 前台
ufw allow 9999/tcp  # 后台管理

# 如果使用 Nginx 反向代理，可以关闭直接访问
ufw deny 9090/tcp
ufw deny 9999/tcp
ufw allow 80/tcp
ufw allow 443/tcp
```

### 2. Nginx 反向代理

```nginx
# 前台站点
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:9090;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

# 后台管理
server {
    listen 80;
    server_name admin.your-domain.com;

    # IP 白名单（可选）
    allow *******;  # 你的 IP
    deny all;

    location / {
        proxy_pass http://127.0.0.1:9999;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. SSL 配置

使用 Let's Encrypt：

```bash
# 安装 certbot
apt-get install certbot python3-certbot-nginx

# 获取证书
certbot --nginx -d your-domain.com -d admin.your-domain.com

# 自动续期
crontab -e
0 0 * * * certbot renew --quiet
```

### 4. 数据库安全

```sql
-- 修改默认密码
ALTER USER sitecluster WITH PASSWORD 'strong_password_here';

-- 限制连接
-- 编辑 postgresql.conf
listen_addresses = 'localhost'

-- 编辑 pg_hba.conf
local   all   sitecluster   md5
host    all   all          127.0.0.1/32   md5
```

### 5. Redis 安全

编辑 `/etc/redis/redis.conf`：

```conf
# 设置密码
requirepass your_redis_password

# 绑定本地
bind 127.0.0.1

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG ""
```

---

## 📊 监控和维护

### 1. 健康检查

```bash
# 创建健康检查脚本
cat > /opt/health_check.sh << 'EOF'
#!/bin/bash

# 检查服务
if curl -f http://localhost:9999/api/v1/auth/check > /dev/null 2>&1; then
    echo "服务正常"
else
    echo "服务异常，重启中..."
    docker compose restart app
    # 或 systemctl restart site-cluster
fi
EOF

# 添加到 crontab
*/5 * * * * /opt/health_check.sh
```

### 2. 日志轮转

创建 `/etc/logrotate.d/site-cluster`：

```
/opt/site-cluster/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    postrotate
        docker compose restart app
    endscript
}
```

### 3. 备份策略

```bash
# 数据库备份脚本
cat > /opt/backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/backup/site-cluster"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
docker exec site-cluster-postgres pg_dump -U sitecluster sitecluster \
    > $BACKUP_DIR/db_$DATE.sql

# 备份缓存和数据
tar -czf $BACKUP_DIR/data_$DATE.tar.gz /opt/site-cluster/cache /opt/site-cluster/data

# 删除7天前的备份
find $BACKUP_DIR -mtime +7 -delete

echo "备份完成: $DATE"
EOF

# 每天凌晨2点执行备份
0 2 * * * /opt/backup.sh
```

---

## 🚀 性能优化

### 1. 系统优化

```bash
# 编辑 /etc/sysctl.conf
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
fs.file-max = 65535

# 应用配置
sysctl -p
```

### 2. Docker 优化

```yaml
# docker-compose.yml 添加资源限制
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '2'
          memory: 4G
```

### 3. PostgreSQL 优化

```sql
-- 调整连接池
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '4GB';
ALTER SYSTEM SET effective_cache_size = '12GB';
ALTER SYSTEM SET work_mem = '16MB';

-- 重启生效
SELECT pg_reload_conf();
```

---

## ❓ 常见问题

### 1. 端口被占用

```bash
# 查看端口占用
lsof -i :9090
lsof -i :9999

# 修改端口
vi .env
# 修改 PORT 和 ADMIN_PORT
```

### 2. 数据库连接失败

```bash
# 检查 PostgreSQL 服务
systemctl status postgresql
docker compose ps postgres

# 测试连接
psql -h localhost -U sitecluster -d sitecluster
```

### 3. 缓存目录权限问题

```bash
# 设置权限
chmod -R 755 cache/
chown -R www-data:www-data cache/
```

### 4. 内存不足

```bash
# 查看内存使用
free -h
docker stats

# 清理 Docker
docker system prune -a
```

---

## 📞 支持

- 部署前请确保服务器满足最低要求
- 建议先在测试环境验证
- 生产环境务必修改默认密码
- 定期备份重要数据

---

*最后更新：2025-08-09*