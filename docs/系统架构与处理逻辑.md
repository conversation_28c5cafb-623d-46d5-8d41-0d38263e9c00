# 站群镜像系统架构与处理逻辑

## 🏗️ 站群镜像系统架构

### 📊 系统整体处理流程

```
用户请求
  ├── 域名识别 (Host Header)
  ├── 站点配置加载
  ├── 页面类型判断
  │   ├── 首页 (/)
  │   └── 内页 (其他路径)
  └── 响应处理管道
```

---

## 🏠 首页处理逻辑

```
首页请求 (/)
├── 1️⃣ 缓存检查
│   ├── Redis缓存 (热点数据)
│   ├── 文件缓存 (cache/domain/)
│   └── 内存缓存 (go-cache)
│
├── 2️⃣ 获取原始内容
│   ├── 代理请求目标站点
│   ├── 处理重定向
│   └── 获取HTML内容
│
├── 3️⃣ 基础处理
│   ├── 资源链接处理
│   │   ├── CSS链接转换
│   │   ├── JS链接转换
│   │   ├── 图片链接转换
│   │   └── 内联样式URL处理
│   ├── 导航链接处理
│   │   └── 转换为相对路径
│   └── 删除重复标签
│       ├── 多余的<title>标签
│       ├── 多余的meta keywords
│       └── 多余的meta description
│
├── 4️⃣ 首页特殊配置
│   ├── 自定义标题 (home_title)
│   ├── 自定义关键词 (home_keywords)
│   └── 自定义描述 (home_description)
│
├── 5️⃣ 注入处理（部分功能跳过）
│   ├── ❌ 关键词注入 (首页跳过)
│   ├── ❌ 结构注入 (首页跳过)
│   ├── ❌ 伪原创 (首页跳过)
│   ├── ✅ 隐藏HTML注入
│   │   ├── 零宽字符注入
│   │   ├── 随机HTML标签
│   │   └── 位置：body开始/结束/both
│   └── ✅ 统计代码注入
│
├── 6️⃣ 转换处理
│   ├── 简繁转换
│   │   └── 检测简体 → 转换繁体
│   ├── Unicode编码
│   │   ├── 标题Unicode化
│   │   ├── 关键词Unicode化
│   │   └── 描述Unicode化
│   └── 拼音注入（如启用）
       └── 跳过链接内容，不影响导航
│
├── 7️⃣ 其他功能
│   ├── 随机字符串class属性
│   ├── Sitemap链接（如启用）
│   └── 爬虫检测与屏蔽
│
└── 8️⃣ 缓存更新
    ├── 异步写入文件缓存
    ├── 更新Redis缓存
    └── 预加载关联页面
```

---

## 📄 内页处理逻辑

```
内页请求 (/path/to/page.html)
├── 1️⃣ 缓存检查
│   ├── Redis缓存
│   ├── 文件缓存
│   └── 内存缓存
│
├── 2️⃣ 获取原始内容
│   ├── 代理请求目标站点
│   ├── 处理Cookie和Session
│   └── 获取HTML内容
│
├── 3️⃣ 基础处理
│   ├── 资源链接处理
│   │   ├── CSS/JS/图片链接
│   │   └── 下载外部资源（如启用）
│   ├── 删除重复标签
│   └── 链接路径处理
│
├── 4️⃣ 关键词注入系统 ✅
│   ├── 关键词库加载
│   │   ├── 标题关键词库 (title_keyword_library_ids)
│   │   ├── Meta关键词库 (meta_keyword_library_ids)
│   │   ├── 描述关键词库 (desc_keyword_library_ids)
│   │   └── 正文关键词库 (keyword_library_ids)
│   │
│   ├── 模板替换
│   │   ├── 标题模板 {keyword1}-{original}
│   │   ├── Meta模板 {keyword1},{keyword2},{keyword3}
│   │   └── 描述模板 {keyword1}是专业的{keyword2}服务商
│   │
│   ├── 注入位置
│   │   ├── <title>标签注入
│   │   ├── meta keywords注入（无则创建）
│   │   ├── meta description注入（无则创建）
│   │   ├── H1/H2标题注入
│   │   ├── 图片alt属性注入
│   │   ├── 正文段落注入
│   │   └── 隐藏关键词注入
│   │       └── <div style="visibility:hidden">关键词</div>
│   │
│   └── 注入控制
│       ├── 最大关键词数 (keyword_max_per_page)
│       ├── 注入概率 (keyword_inject_ratio)
│       ├── 最小字数要求 (keyword_min_word_count)
│       └── 关键词密度 (keyword_density)
│
├── 5️⃣ 结构优化注入 ✅
│   ├── 段落结构优化
│   ├── 列表结构注入
│   └── 语义标签优化
│
├── 6️⃣ 伪原创系统 ✅
│   ├── 伪原创词库加载
│   │   └── pseudo_library_ids
│   ├── 同义词替换
│   ├── 句式变换
│   └── 段落重组
│
├── 7️⃣ 隐藏HTML注入 ✅
│   ├── 零宽字符
│   │   ├── \u200B (零宽空格)
│   │   ├── \u200C (零宽非连接符)
│   │   ├── \u200D (零宽连接符)
│   │   ├── \uFEFF (零宽非断空格)
│   │   ├── \u2060 (字词连接符)
│   │   └── \u180E (蒙古元音分隔符)
│   ├── 随机HTML标签 (40+种)
│   ├── 随机ID生成
│   └── 注入位置控制
│
├── 8️⃣ 统计与分析
│   ├── 统计代码注入
│   ├── 爬虫识别统计
│   │   ├── Googlebot
│   │   ├── Baiduspider
│   │   ├── Bingbot
│   │   └── 30+种爬虫UA
│   └── 访问日志记录
│
├── 9️⃣ 转换处理
│   ├── 简繁转换（最后执行）
│   ├── Unicode编码
│   └── 拼音标注
│
├── 🔟 特殊功能
│   ├── 外链过滤
│   ├── 随机字符串注入
│   ├── Sitemap生成
│   └── 爬虫屏蔽返回403
│
└── 1️⃣1️⃣ 缓存更新
    ├── 异步文件缓存
    ├── Redis缓存更新
    └── Sitemap条目更新
```

---

## ⚙️ 配置系统结构

```
站点配置 (sites)
├── 基础配置
│   ├── domain (域名)
│   ├── target_url (目标站点)
│   ├── crawl_depth (爬取深度)
│   ├── status (状态：active/inactive)
│   └── enable_preload (预加载)
│
├── 缓存配置
│   ├── use_global_cache
│   ├── cache_max_size
│   ├── cache_home_ttl (首页TTL)
│   ├── cache_other_ttl (内页TTL)
│   └── enable_redis_cache
│
├── 注入配置 (inject_config)
│   ├── 关键词系统
│   │   ├── enable_keyword
│   │   ├── 各位置注入开关
│   │   │   ├── keyword_inject_title
│   │   │   ├── keyword_inject_meta
│   │   │   ├── keyword_inject_desc
│   │   │   ├── keyword_inject_body
│   │   │   ├── keyword_inject_hidden
│   │   │   ├── keyword_inject_h1
│   │   │   ├── keyword_inject_h2
│   │   │   └── keyword_inject_alt
│   │   ├── 关键词库IDs
│   │   │   ├── keyword_library_ids (通用)
│   │   │   ├── title_keyword_library_ids
│   │   │   ├── meta_keyword_library_ids
│   │   │   └── desc_keyword_library_ids
│   │   └── 模板配置
│   │       ├── keyword_title_template
│   │       ├── keyword_meta_template
│   │       └── keyword_desc_template
│   │
│   ├── 伪原创系统
│   │   ├── enable_pseudo
│   │   └── pseudo_library_ids
│   │
│   ├── 结构优化
│   │   └── enable_structure
│   │
│   ├── 隐藏注入
│   │   ├── enable_hidden_html
│   │   ├── hidden_html_length
│   │   ├── hidden_html_random_id
│   │   └── hidden_html_position
│   │
│   └── 转换功能
│       ├── enable_unicode
│       ├── unicode_scope
│       ├── enable_unicode_title
│       ├── enable_unicode_keywords
│       ├── enable_unicode_desc
│       ├── enable_pinyin
│       ├── enable_random_string
│       └── random_string_length
│
├── SEO配置
│   ├── home_title
│   ├── home_keywords
│   ├── home_description
│   └── filter_external_links
│
├── 爬虫配置
│   ├── enable_spider_block
│   ├── enable_ua_check
│   ├── allowed_ua
│   └── non_spider_html
│
└── Sitemap配置
    ├── enable_sitemap
    ├── sitemap_update_interval
    ├── sitemap_changefreq
    ├── sitemap_priority
    └── sitemap_max_urls
```

---

## 🔄 处理顺序总结

### 首页处理顺序：
1. 获取原始内容
2. 基础HTML处理
3. 首页特殊配置应用
4. 隐藏HTML注入（如启用）
5. 统计代码注入
6. 简繁转换
7. Unicode编码

### 内页处理顺序：
1. 获取原始内容
2. 基础HTML处理
3. **关键词注入**（简体）
4. **结构优化**
5. **伪原创处理**
6. 隐藏HTML注入
7. 统计代码注入
8. **简繁转换**（最后）
9. Unicode编码

---

## 📊 功能对比表

| 功能 | 首页 | 内页 | 说明 |
|------|------|------|------|
| 标题处理 | home_title | 关键词模板注入 | 首页用固定配置，内页用模板 |
| 关键词注入 | ❌ | ✅ | 首页不注入关键词 |
| 伪原创 | ❌ | ✅ | 首页保持原样 |
| 结构注入 | ❌ | ✅ | 首页不修改结构 |
| 隐藏HTML | ✅ | ✅ | 都可以注入 |
| 简繁转换 | ✅ | ✅ | 都在最后执行 |
| Unicode | ✅ | ✅ | 可配置范围 |
| 缓存 | ✅ | ✅ | 不同TTL时间 |
| 统计代码 | ✅ | ✅ | 都会注入 |
| 爬虫处理 | ✅ | ✅ | 统一处理 |

---

## 🎯 关键特性

### 1. **智能缓存系统**
- 三层缓存架构（Redis + 文件 + 内存）
- 异步写入机制
- 预加载策略
- TTL分离控制

### 2. **SEO优化系统**
- 全方位关键词注入
- 独立关键词库
- 模板化配置
- 密度控制

### 3. **内容保护**
- 零宽字符防采集
- 随机HTML混淆
- 外链过滤
- 防镜像技术

### 4. **爬虫友好**
- 识别30+爬虫
- 区别对待策略
- 统计分析
- 自定义返回

### 5. **性能优化**
- 异步处理
- 并发控制
- 资源本地化
- 智能预加载

### 6. **灵活配置**
- 站点级独立配置
- 功能模块化
- 实时生效
- 批量管理

---

## 🔜 待实现功能

### 1. 标题跟随功能
- 自动学习导航标题
- 建立URL->标题映射
- 统一页面标题显示
- 数据库持久化

### 2. 简繁转换独立控制
- 修复配置保存问题
- 站点级别控制
- 实时切换

### 3. 智能SEO优化
- 关键词密度自动调整
- 内容相关性分析
- 竞品分析

### 4. 高级缓存策略
- 智能预热
- 差异化更新
- CDN集成

---

## 📝 注意事项

1. **处理顺序很重要**：简繁转换必须在最后，确保所有注入内容一起转换
2. **首页特殊处理**：首页不进行关键词注入，保持原始结构
3. **重复标签处理**：自动删除多余的SEO标签，避免搜索引擎惩罚
4. **缓存策略**：首页和内页使用不同的缓存时间
5. **爬虫识别**：正确识别搜索引擎爬虫，提供优化内容

---

## 🚀 系统优势

1. **高性能**：多级缓存 + 异步处理
2. **易维护**：模块化设计 + 配置分离
3. **SEO友好**：智能优化 + 爬虫识别
4. **安全可靠**：内容保护 + 防采集
5. **灵活扩展**：插件化架构 + API接口

---

*最后更新：2025-08-09*