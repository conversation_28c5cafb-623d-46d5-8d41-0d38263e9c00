# 权重监测功能文档

## 功能概述
权重监测功能用于自动获取和跟踪所有站点域名的百度权重和流量数据，支持历史趋势分析和数据导出。

## 数据库变更

### 新增数据表

#### 1. weight_monitor_config (权重监测配置表)
```sql
CREATE TABLE weight_monitor_config (
    id SERIAL PRIMARY KEY,
    enabled BOOLEAN DEFAULT false,           -- 是否启用监测
    api_key VARCHAR(255),                   -- 爱站API密钥
    check_interval INTEGER DEFAULT 60,       -- 检查间隔（分钟）
    batch_size INTEGER DEFAULT 5,           -- 每批域名数量
    batch_delay INTEGER DEFAULT 5,          -- 批次间隔（秒）
    cycle_wait INTEGER DEFAULT 24,          -- 循环等待时间（小时）
    last_check_time TIMESTAMP,              -- 最后检查时间
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. weight_history (权重历史记录表)
```sql
CREATE TABLE weight_history (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(255) NOT NULL,           -- 站点域名
    site_id INTEGER,                        -- 关联站点ID
    pc_br INTEGER,                          -- PC端百度权重
    m_br INTEGER,                           -- 移动端百度权重
    ip VARCHAR(50),                         -- 预估总流量
    pc_ip VARCHAR(50),                      -- PC端预估流量
    m_ip VARCHAR(50),                       -- 移动端预估流量
    check_time TIMESTAMP,                   -- 检查时间
    created_at TIMESTAMP DEFAULT NOW(),
    
    INDEX idx_domain (domain),
    INDEX idx_site_id (site_id),
    INDEX idx_check_time (check_time)
);
```

## API接口

### 配置管理
- `GET /api/v1/weight/config` - 获取权重监测配置
- `PUT /api/v1/weight/config` - 更新权重监测配置
- `POST /api/v1/weight/test-api` - 测试API密钥有效性

### 数据查询
- `GET /api/v1/weight/list` - 获取所有域名最新权重
- `GET /api/v1/weight/history` - 获取权重历史记录
- `GET /api/v1/weight/trend` - 获取权重趋势数据
- `GET /api/v1/weight/domain/:domain` - 获取单个域名详情

### 操作管理
- `POST /api/v1/weight/check` - 手动触发权重检查
- `GET /api/v1/weight/export` - 导出权重数据

## 配置说明

### 系统设置页面配置项
1. **功能开关** - 启用/禁用权重监测功能
2. **API密钥** - 爱站API商店密钥
3. **检查间隔** - 定时检查的时间间隔（10-1440分钟）
4. **批次大小** - 每次API请求包含的域名数量（1-20个）
5. **批次间隔** - 批次之间的等待时间（1-60秒）
6. **循环等待** - 完成一轮检查后的等待时间（1-168小时）

## 使用流程

### 初始配置
1. 进入系统设置 -> 权重监测标签
2. 输入爱站API密钥
3. 点击"验证密钥"确认有效性
4. 配置检查频率和批次设置
5. 开启权重监测功能开关
6. 保存设置

### 查看数据
1. 进入权重监测页面
2. 查看权重趋势图表
3. 查看域名列表和权重变化
4. 点击"详情"查看单个域名历史
5. 使用日期筛选查看特定时间段数据

### 手动操作
- 点击"立即执行"手动触发权重检查
- 点击"导出"下载权重数据报表

## 技术实现

### 核心服务
- **MonitorService** - 权重监测服务，管理定时任务和API调用
- **AizhanAPI** - 爱站API客户端，处理API请求和响应
- **WeightRepository** - 数据访问层，处理权重数据存储

### 定时任务
使用 robfig/cron 实现定时调度：
1. 按配置的间隔时间执行检查
2. 获取所有活跃站点的域名
3. 分批调用爱站API
4. 保存权重历史记录
5. 完成后等待循环时间

### 数据处理
- 支持批量域名查询（最多20个/批）
- 自动重试失败的请求
- 记录权重变化趋势
- 计算平均权重和提升数量

## 注意事项

1. **API费用** - 爱站API按调用次数收费，请合理设置检查频率
2. **域名范围** - 监测的是站点配置的域名，不是目标URL
3. **批次限制** - 建议批次大小不超过10个，避免API限流
4. **数据延迟** - 爱站数据可能有1-2天延迟
5. **服务重启** - 修改配置后需要重启服务才能生效

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持自动权重监测
- 支持历史趋势分析
- 支持数据导出功能