# 🛡️ 爬虫屏蔽功能架构说明

## 📋 功能概述

站群系统的爬虫屏蔽功能采用**两级控制架构**，提供灵活的爬虫访问控制策略。

## 🏗️ 架构设计

### 1. 两级开关控制

```
┌─────────────────────────────────────┐
│         全局级别控制                 │
│   (系统设置 - EnableGlobalSpiderBlock)│
└──────────────┬──────────────────────┘
               │
               ▼
┌─────────────────────────────────────┐
│         站点级别控制                 │
│   (站点设置 - EnableSpiderBlock)     │
└──────────────┬──────────────────────┘
               │
               ▼
┌─────────────────────────────────────┐
│         屏蔽规则匹配                 │
│   (/admin/spider-block 管理)         │
└─────────────────────────────────────┘
```

### 2. 优先级规则

1. **站点级别优先**：如果站点启用了爬虫屏蔽，使用站点配置
2. **全局级别兜底**：如果站点未启用，检查全局设置
3. **规则独立管理**：具体的UA匹配规则在独立页面管理

### 3. 数据模型

#### 系统设置 (system_settings)
```go
EnableGlobalSpiderBlock bool   // 全局爬虫屏蔽开关
SpiderBlock403Template  string // 全局403响应模板
DefaultSpiderBlockUA    string // 默认屏蔽UA列表（已废弃）
```

#### 站点设置 (sites)
```go
EnableSpiderBlock      bool   // 站点爬虫屏蔽开关
SpiderBlock403Template string // 站点403响应模板
```

#### 屏蔽规则 (spider_blocks)
```go
UserAgent   string // UA匹配规则（支持正则）
Description string // 规则描述
ReturnCode  int    // 返回状态码（通常403）
Enabled     bool   // 规则启用状态
HitCount    int64  // 命中计数
```

## 🔄 执行流程

```mermaid
graph TD
    A[接收请求] --> B{检查站点屏蔽开关}
    B -->|启用| C[使用站点模板]
    B -->|未启用| D{检查全局屏蔽开关}
    D -->|启用| E[使用全局模板]
    D -->|未启用| F[放行请求]
    C --> G[匹配UA规则]
    E --> G
    G -->|匹配| H[返回403+模板]
    G -->|不匹配| F
```

## 🎯 关键特性

### 1. 动态规则加载
- 规则创建/更新/删除后立即生效
- 使用内存缓存提高匹配性能
- 支持正则表达式匹配

### 2. 命中统计
- 自动记录每个规则的命中次数
- 支持重置单个或全部计数
- 异步更新避免影响响应性能

### 3. 自定义响应模板
- 站点和全局分别设置403模板
- 支持HTML格式自定义
- 可包含变量替换

## 📝 配置示例

### 全局配置（系统设置）
```json
{
  "enable_global_spider_block": true,
  "spider_block_403_template": "<html><body><h1>访问被拒绝</h1></body></html>"
}
```

### 站点配置
```json
{
  "domain": "example.com",
  "enable_spider_block": true,
  "spider_block_403_template": "<html><body><h1>本站禁止爬虫访问</h1></body></html>"
}
```

### 屏蔽规则
```json
{
  "user_agent": "AhrefsBot",
  "description": "Ahrefs SEO爬虫",
  "return_code": 403,
  "enabled": true
}
```

## 🔍 测试验证

### 测试命令
```bash
# 测试被屏蔽的爬虫
curl -H "User-Agent: AhrefsBot/7.0" -H "Host: example.com" http://localhost:9090/

# 测试正常浏览器
curl -H "User-Agent: Mozilla/5.0 Chrome/91.0" -H "Host: example.com" http://localhost:9090/
```

### 预期结果
- 启用屏蔽 + 匹配规则 = 返回403
- 启用屏蔽 + 不匹配规则 = 正常访问
- 未启用屏蔽 = 正常访问（忽略规则）

## ⚠️ 注意事项

1. **性能影响**：大量规则可能影响性能，建议控制在100条以内
2. **正则复杂度**：避免使用过于复杂的正则表达式
3. **缓存更新**：规则变更后会重新加载，短暂影响性能
4. **优先级**：站点配置始终优先于全局配置

## 🐛 已知问题

1. ~~站点级别屏蔽不生效~~ ✅ 已修复
2. ~~新规则不立即生效~~ ✅ 已修复
3. ~~命中统计不准确~~ ✅ 已修复

## 📊 默认屏蔽列表

系统默认包含48个常见的爬虫/扫描器规则，包括：
- SEO爬虫（Ahrefs、Semrush、Moz等）
- 搜索引擎爬虫（Yandex、360、搜狗等）
- 安全扫描器（Nikto、sqlmap、Nmap等）
- 开发工具（curl、wget、python-requests等）

## 🔧 管理界面

- **系统设置**：`/admin/settings` - 全局开关配置
- **站点管理**：`/admin/sites` - 站点级别配置
- **规则管理**：`/admin/spider-block` - 具体规则管理

## 更新日期
2025-08-12