# 🚀 站群系统性能配置指南

## 📊 配置参数说明与建议值

### 1. 数据库连接池配置

| 参数 | 当前硬编码值 | 默认值 | 200站点建议值 | 500站点建议值 | 说明 |
|------|-------------|--------|--------------|--------------|------|
| `db_max_open_conns` | 25 | 100 | 200 | 400 | 数据库最大连接数 |
| `db_max_idle_conns` | 10 | 50 | 100 | 200 | 数据库最大空闲连接数 |
| `db_conn_max_lifetime` | 300秒 | 600秒 | 600秒 | 600秒 | 连接最大生命周期 |
| `db_slave_max_open_conns` | 15 | 50 | 100 | 200 | 从库最大连接数 |
| `db_slave_max_idle_conns` | 5 | 25 | 50 | 100 | 从库最大空闲连接数 |

**配置位置**: 
- 原硬编码位置: `internal/database/db_optimized.go:50-54`
- 现在可在系统设置中配置

### 2. HTTP客户端配置

| 参数 | 当前硬编码值 | 默认值 | 200站点建议值 | 500站点建议值 | 说明 |
|------|-------------|--------|--------------|--------------|------|
| `http_max_idle_conns` | 50 | 200 | 300 | 500 | HTTP最大空闲连接数 |
| `http_max_idle_conns_per_host` | 5 | 50 | 50 | 100 | 每主机最大空闲连接 |
| `http_max_conns_per_host` | 10 | 100 | 100 | 200 | 每主机最大连接数 |
| `http_idle_conn_timeout` | 90秒 | 90秒 | 90秒 | 90秒 | 空闲连接超时 |

**配置位置**:
- 原硬编码位置: `internal/api/handler/mirror.go:290-292`
- 现在可在系统设置中配置

### 3. Redis连接池配置

| 参数 | 当前硬编码值 | 默认值 | 200站点建议值 | 500站点建议值 | 说明 |
|------|-------------|--------|--------------|--------------|------|
| `redis_max_pool_size` | 10 | 30 | 50 | 100 | Redis最大连接数 |
| `redis_connect_timeout` | 5000ms | 5000ms | 5000ms | 5000ms | 连接超时 |
| `redis_read_timeout` | 2000ms | 2000ms | 3000ms | 3000ms | 读超时 |
| `redis_write_timeout` | 3000ms | 3000ms | 5000ms | 5000ms | 写超时 |

**配置位置**:
- 系统设置表中已有配置项

### 4. 任务调度器配置

| 参数 | 当前硬编码值 | 默认值 | 200站点建议值 | 500站点建议值 | 说明 |
|------|-------------|--------|--------------|--------------|------|
| `scheduler_max_workers` | 100 | 200 | 300 | 500 | 最大工作协程数 |
| `scheduler_queue_size` | 1000 | 5000 | 10000 | 20000 | 任务队列大小 |
| `crawler_max_concurrency` | 10 | 50 | 100 | 200 | 爬虫最大并发数 |
| `crawler_rate_limit` | 1000 QPS | 2000 | 3000 | 5000 | 爬虫速率限制 |

**配置位置**:
- 原硬编码位置: `internal/scheduler/scheduler.go`
- 现在可在系统设置中配置

### 5. 缓存锁配置

| 参数 | 当前硬编码值 | 默认值 | 200站点建议值 | 500站点建议值 | 说明 |
|------|-------------|--------|--------------|--------------|------|
| `cache_lock_timeout` | 100ms | 1000ms | 2000ms | 3000ms | 缓存锁超时 |
| `cache_lock_retry_interval` | 10ms | 100ms | 100ms | 100ms | 重试间隔 |

## 🔧 配置方法

### 方法1: 通过SQL直接更新（推荐用于生产环境）

```sql
-- 更新为200站点并发的推荐配置
UPDATE system_settings SET
    db_max_open_conns = 200,
    db_max_idle_conns = 100,
    db_conn_max_lifetime = 600,
    db_slave_max_open_conns = 100,
    db_slave_max_idle_conns = 50,
    http_max_idle_conns = 300,
    http_max_idle_conns_per_host = 50,
    http_max_conns_per_host = 100,
    http_idle_conn_timeout = 90,
    scheduler_max_workers = 300,
    scheduler_queue_size = 10000,
    crawler_max_concurrency = 100,
    crawler_rate_limit = 3000,
    redis_max_pool_size = 50,
    cache_lock_timeout = 2000,
    cache_lock_retry_interval = 100
WHERE id = 1;
```

### 方法2: 通过API更新

```bash
# 登录获取token
curl -c cookies.txt -X POST http://localhost:9999/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# 更新系统设置
curl -b cookies.txt -X PUT http://localhost:9999/api/v1/settings \
  -H "Content-Type: application/json" \
  -d '{
    "db_max_open_conns": 200,
    "db_max_idle_conns": 100,
    "http_max_idle_conns": 300,
    "http_max_idle_conns_per_host": 50,
    "http_max_conns_per_host": 100,
    "scheduler_max_workers": 300,
    "scheduler_queue_size": 10000
  }'
```

### 方法3: 通过后台管理界面（开发中）

访问 http://localhost:9999/admin/settings 进入系统设置页面，在"性能配置"标签页中调整各项参数。

## 📈 性能测试建议

### 测试步骤

1. **基准测试**：使用默认配置测试当前性能
2. **逐步调优**：按建议值调整配置
3. **压力测试**：模拟200个站点并发请求
4. **监控指标**：观察CPU、内存、连接数等

### 测试脚本

```bash
# 并发测试脚本
for i in {1..200}; do
  curl -H "Host: site$i.com" http://localhost:9090/ &
done
wait
```

### 监控命令

```bash
# 监控数据库连接数
docker exec site-cluster-postgres psql -U sitecluster -c "SELECT count(*) FROM pg_stat_activity;"

# 监控系统资源
htop

# 监控网络连接
netstat -an | grep ESTABLISHED | wc -l
```

## ⚠️ 注意事项

1. **硬件要求**：
   - 200站点：最低8核16GB内存
   - 500站点：建议16核32GB内存
   
2. **调整顺序**：
   - 先调整数据库连接池
   - 再调整HTTP客户端
   - 最后调整任务调度器
   
3. **监控重点**：
   - 数据库连接使用率
   - HTTP连接池状态
   - 内存使用情况
   - Goroutine数量

4. **回滚方案**：
   - 保存原始配置
   - 准备快速回滚SQL
   - 监控异常情况

## 🔍 问题排查

### 常见问题

1. **连接池耗尽**
   - 症状：请求超时，日志显示"too many connections"
   - 解决：增加max_open_conns

2. **内存占用过高**
   - 症状：内存持续增长
   - 解决：减少max_idle_conns，检查goroutine泄露

3. **响应缓慢**
   - 症状：延迟增加
   - 解决：增加worker数量，优化缓存策略

### 日志位置

- 系统日志：`/tmp/site-cluster.log`
- 数据库日志：Docker容器日志
- 性能指标：可通过Prometheus/Grafana监控

## 📝 更新记录

- 2024-08-12: 添加性能配置到系统设置表
- 配置项不再硬编码，支持动态调整
- 提供200站点和500站点的建议配置值