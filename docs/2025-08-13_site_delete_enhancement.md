# 站点删除功能增强
时间：2025-08-13
作者：夏天

## 功能概述
增强了站点删除功能，确保删除站点时能够完整清理所有关联数据。

## 修改内容

### 1. 数据库层（repository/gorm/site.go）
- 修改 `Delete` 方法，在事务中删除所有关联数据：
  - 爬虫统计记录（spider_stats, spider_stats_minute）
  - 权重检测记录（weight_history）
  - Sitemap记录（sitemap_entries）
  - 爬取任务日志（crawl_job, task_log）
  - 注入配置（inject_config）
  - 路由规则（route_rules）

### 2. 服务层（service/site.go）
- 在 `DeleteSite` 和 `BatchDeleteSites` 方法中添加：
  - 清理404缓存（Clear404Cache）
  - 重置404统计（Reset404Stats）

### 3. 控制器层（api/handler/site.go）
- 已有缓存文件清理逻辑（ClearSiteCache）

## 清理的数据范围

删除站点时会自动清理：
1. **数据库记录**
   - 站点配置（sites表）
   - 注入配置（inject_config表）
   - 路由规则（route_rules表）
   - 爬虫统计（spider_stats, spider_stats_minute表）
   - 权重历史（weight_history表）
   - Sitemap条目（sitemap_entries表）
   - 爬取任务（crawl_job, task_log表）

2. **缓存数据**
   - 文件缓存（cache/域名/目录下的所有文件）
   - 404统计缓存（Redis中的404相关数据）
   - 404数据库统计（sites表的count404字段重置）

3. **定时任务**
   - 站点相关的爬取定时任务

## 测试脚本
创建了测试脚本 `/test/test_site_delete.sh` 用于验证删除功能的完整性。

## 注意事项
- 删除操作在事务中执行，确保数据一致性
- 缓存清理和404统计重置是异步执行的
- 批量删除时会逐个处理站点，部分失败不影响其他站点的删除