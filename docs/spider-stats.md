# 爬虫统计功能文档

## 功能概述

爬虫统计功能用于跟踪和分析搜索引擎爬虫对站点的访问情况，提供可视化图表和详细的域名级统计数据。

## 主要特性

### 1. 爬虫识别与记录
- 基于 User-Agent 识别主流搜索引擎爬虫
- 异步记录爬虫访问，不影响主请求性能
- 支持分钟级和日级统计数据存储

### 2. 可配置的爬虫管理
- 通过管理界面配置需要统计的爬虫
- 支持启用/禁用特定爬虫的统计
- 自定义爬虫显示名称和颜色

### 3. 数据可视化
- 折线图展示爬虫访问趋势
- 支持按小时、天、周查看数据
- 响应式图表设计，自适应容器大小

### 4. 域名级统计
- 按域名展示爬虫访问数据
- 显示今天、昨天、最近5天的统计
- 支持域名筛选和分页

## 技术实现

### 数据模型
```go
// 爬虫配置
type SpiderConfig struct {
    ID          uint      // 配置ID
    Name        string    // 爬虫标识
    DisplayName string    // 显示名称
    UserAgent   string    // User-Agent 特征
    Enabled     bool      // 是否启用
    Priority    int       // 优先级
    Color       string    // 图表颜色
}

// 分钟级统计
type SpiderStatsMinute struct {
    ID         uint      // 记录ID
    SpiderName string    // 爬虫名称
    Count      int64     // 访问次数
    Timestamp  time.Time // 时间戳（分钟精度）
}

// 日级统计
type SpiderStats struct {
    ID         uint      // 记录ID
    Domain     string    // 域名
    SpiderName string    // 爬虫名称
    Count      int64     // 访问次数
    Date       time.Time // 日期
}
```

### 核心组件

1. **SpiderStatsService** - 爬虫统计服务
   - 识别爬虫类型
   - 记录访问数据
   - 生成统计报表

2. **SpiderStatsHandler** - API处理器
   - 处理统计数据查询请求
   - 管理爬虫配置

3. **数据清理机制**
   - 自动清理超过24小时的分钟级数据
   - 保留日级统计用于长期分析

## 使用说明

### 1. 访问爬虫统计页面
访问 `/admin/spider-stats` 查看爬虫统计信息。

### 2. 配置爬虫
点击"配置爬虫"按钮，可以：
- 查看已配置的爬虫列表
- 启用/禁用特定爬虫的统计
- 添加新的爬虫识别规则

### 3. 查看统计数据
- **图表区域**：显示爬虫访问趋势
- **时间筛选**：切换查看不同时间范围的数据
- **域名列表**：查看各域名的爬虫访问详情

### 4. 数据导出
支持导出统计数据用于进一步分析（功能待实现）。

## 性能优化

1. **异步记录**：使用 goroutine 异步记录爬虫访问，不阻塞主请求
2. **数据聚合**：分钟级数据用于实时展示，日级数据用于历史分析
3. **自动清理**：定期清理过期的分钟级数据，控制数据库大小
4. **缓存优化**：图表数据可配置缓存时间（待实现）

## 默认支持的爬虫

系统默认配置了30+主流搜索引擎爬虫，包括：
- 国际搜索引擎：Google、Bing、Yahoo、DuckDuckGo等
- 国内搜索引擎：百度、360、搜狗、神马等
- 其他爬虫：Facebook、Twitter、LinkedIn等社交媒体爬虫

## 注意事项

1. 爬虫统计在记录访问后才进行屏蔽检查，确保统计数据的完整性
2. 分钟级数据仅保留24小时，请及时查看实时数据
3. 爬虫识别基于 User-Agent，可能存在误判或漏判的情况
4. 建议定期检查和更新爬虫配置以适应新的爬虫特征