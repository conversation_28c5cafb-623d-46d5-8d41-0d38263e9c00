# 404页面缓存功能

## 功能说明

系统现已支持404页面缓存功能，当目标站点返回404状态时：
1. 系统会自动缓存该URL的404状态
2. 后续访问相同URL将直接返回自定义404页面，不再请求目标站点
3. 管理员可以配置自定义404页面内容

## 配置项

在系统设置页面的"请求设置"部分：

### 404页面设置
- **启用404缓存**: 开启/关闭404状态缓存功能
- **缓存时间(秒)**: 404状态的缓存有效期，默认86400秒（24小时）
- **自定义404页面**: 可配置自定义的404页面HTML内容

## 技术实现

### 相关文件
- `internal/service/cache_404.go`: 404缓存服务实现
- `internal/api/handler/mirror.go`: 镜像处理器中的404检测
- `internal/api/handler/mirror_optimized.go`: 优化镜像处理器中的404缓存处理
- `internal/model/system_settings.go`: 系统设置模型（包含404配置字段）

### 工作流程
1. 当访问一个URL时，系统首先检查是否已被缓存为404
2. 如果已缓存，直接返回自定义404页面
3. 如果未缓存，请求目标站点
4. 若目标站点返回404，缓存该状态并返回自定义404页面

### 缓存存储
- 内存缓存：快速访问
- Redis缓存：持久化存储，支持分布式部署

## 使用示例

1. 在后台管理系统中，进入"系统设置"页面
2. 找到"请求设置"部分的"404页面设置"
3. 启用404缓存功能
4. 设置缓存时间（建议保持默认24小时）
5. 自定义404页面HTML内容（可选）

## 测试方法

使用提供的测试脚本进行验证：
```bash
./test/test_404_page.sh
./test/test_404_detailed.sh
```

## 注意事项

- 404缓存会占用一定的内存和Redis存储空间
- 建议定期清理过期的404缓存
- 自定义404页面应保持简洁，避免过大的HTML内容