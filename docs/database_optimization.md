# 数据库优化文档

## 概述
根据代码审计结果，本次优化主要针对数据库查询性能，通过添加合适的索引来提升系统整体性能。

## 优化时间
2025年1月15日

## 索引优化详情

### 1. 站点表 (sites)
- **idx_sites_domain_status**: 复合索引(domain, status)
  - 用途: 加速按域名查询和状态过滤
  - 影响查询: GetSiteByDomain, GetActiveSites
  
- **idx_sites_status**: 部分索引(status WHERE status='active')
  - 用途: 快速获取活跃站点列表
  - 影响查询: 后台管理站点列表
  
- **idx_sites_created_at**: 时间索引(created_at DESC)
  - 用途: 站点列表排序和分页
  - 影响查询: 站点管理页面

### 2. 爬虫统计表 (spider_stats)
- **idx_spider_stats_created_at**: 时间索引(created_at DESC)
  - 用途: 按时间范围查询统计数据
  - 影响查询: 爬虫统计趋势图
  
- **idx_spider_stats_domain_created**: 复合索引(domain, created_at DESC)
  - 用途: 特定站点的爬虫统计
  - 影响查询: 单站点爬虫分析
  
- **idx_spider_stats_spider_name**: 复合索引(spider_name, created_at DESC)
  - 用途: 按爬虫类型统计
  - 影响查询: 爬虫类型分布

### 3. 404缓存表 (cache_404_records)
- **idx_cache_404_domain**: 域名索引(domain)
  - 用途: 快速查找域名的404记录
  - 影响查询: 404页面处理
  
- **idx_cache_404_domain_path**: 复合索引(domain, path)
  - 用途: 精确匹配404缓存
  - 影响查询: 缓存命中判断
  
- **idx_cache_404_created_at**: 时间索引(created_at)
  - 用途: 清理过期记录
  - 影响查询: 定时清理任务

### 4. 其他重要索引
- 关键词库索引: 提升关键词注入性能
- 伪原创库索引: 加速同义词查找
- 登录日志索引: 优化安全审计查询
- Sitemap索引: 提升站点地图生成速度
- 权重历史索引: 优化权重监控查询

## 执行方法

### 应用索引优化
```bash
cd /Users/<USER>/Desktop/站群
./scripts/apply_indexes.sh
```

### 回滚索引（如需要）
```bash
cd /Users/<USER>/Desktop/站群
./scripts/rollback_indexes.sh
```

## 性能预期提升

1. **站点查询**: 预计提升 50-70%
   - 域名查找从全表扫描变为索引查找
   - 活跃站点筛选使用部分索引

2. **爬虫统计**: 预计提升 60-80%
   - 时间范围查询使用索引
   - 避免大表全扫描

3. **404缓存**: 预计提升 40-60%
   - 缓存命中判断更快
   - 减少数据库负载

4. **整体响应时间**: 预计降低 30-50%
   - 减少慢查询
   - 降低数据库CPU使用率

## 监控建议

1. **定期检查索引使用情况**
```sql
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

2. **查看慢查询**
```sql
SELECT 
    query,
    calls,
    mean_exec_time,
    total_exec_time
FROM pg_stat_statements
WHERE mean_exec_time > 100
ORDER BY mean_exec_time DESC;
```

3. **检查索引膨胀**
```sql
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexname::regclass)) AS size
FROM pg_indexes
WHERE schemaname = 'public'
ORDER BY pg_relation_size(indexname::regclass) DESC;
```

## 维护建议

1. **定期执行 VACUUM ANALYZE**
   - 建议每周执行一次
   - 更新表统计信息

2. **监控索引碎片**
   - 使用 pgstattuple 扩展
   - 必要时重建索引

3. **评估索引效果**
   - 记录优化前后的查询时间
   - 调整不常用的索引

## 注意事项

1. 索引会占用额外的磁盘空间
2. 写入操作会稍微变慢（需要维护索引）
3. 建议在低峰期执行索引创建
4. 备份现有索引信息以便回滚