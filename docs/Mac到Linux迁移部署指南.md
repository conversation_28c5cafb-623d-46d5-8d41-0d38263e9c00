# Mac到Linux完整迁移部署指南

## 📋 迁移概述

本指南将帮助您将站群系统从Mac开发环境完整迁移到Linux生产环境，使用Docker容器化部署，实现一键启动。

---

## 🚀 快速迁移步骤

### 第1步：在Mac上备份数据

```bash
# 1. 进入项目目录
cd /Users/<USER>/Desktop/站群

# 2. 运行备份脚本
chmod +x scripts/backup.sh
./scripts/backup.sh

# 备份文件将生成在: ./backup/site-cluster-backup-[时间戳].tar.gz
```

### 第2步：打包项目文件

```bash
# 打包整个项目（包含源码）
tar -czf site-cluster-project.tar.gz \
  --exclude='.git' \
  --exclude='cache/*' \
  --exclude='logs/*' \
  --exclude='*.log' \
  --exclude='site-cluster' \
  .

# 文件大小
ls -lh site-cluster-project.tar.gz
```

### 第3步：传输到Linux服务器

```bash
# 使用scp传输文件
scp site-cluster-project.tar.gz user@linux-server:/tmp/
scp backup/site-cluster-backup-*.tar.gz user@linux-server:/tmp/
```

### 第4步：在Linux服务器上部署

```bash
# SSH登录到Linux服务器
ssh user@linux-server

# 1. 解压项目文件
cd /tmp
tar -xzf site-cluster-project.tar.gz

# 2. 运行部署脚本（全新安装）
chmod +x deploy.sh
sudo ./deploy.sh --init

# 或者从备份恢复
sudo ./deploy.sh --backup /tmp/site-cluster-backup-*.tar.gz --restore-db
```

---

## 📦 Docker环境准备

### 安装Docker（如果未安装）

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com | sudo sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
```

### 安装Docker Compose

```bash
# 方法1：使用包管理器
sudo apt-get install docker-compose  # Ubuntu/Debian
sudo yum install docker-compose       # CentOS/RHEL

# 方法2：直接下载二进制文件
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

---

## 🔧 详细部署流程

### 1. 项目结构说明

```
/opt/sitecluster/
├── docker-compose.yml     # Docker编排文件
├── Dockerfile             # 应用镜像构建文件
├── .env                   # 环境配置
├── cmd/                   # Go源代码
├── internal/              # 内部包
├── web/                   # 前端资源
├── scripts/               # 脚本文件
├── cache/                 # 缓存目录
├── logs/                  # 日志目录
└── backup/                # 备份目录
```

### 2. 环境配置

编辑 `.env` 文件配置环境变量：

```bash
cd /opt/sitecluster
cp .env.example .env
vim .env
```

关键配置项：
```env
# 数据库密码（生产环境必须修改）
DB_PASSWORD=your_secure_password

# Session密钥（必须修改为随机字符串）
SESSION_SECRET=$(openssl rand -base64 32)

# 端口配置
PORT=9090
ADMIN_PORT=9999

# 性能配置
MAX_CONCURRENT_CRAWLS=10
WORKER_POOL_MAX=20
```

### 3. 构建和启动

```bash
cd /opt/sitecluster

# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

### 4. 数据迁移

如果需要从Mac迁移数据：

```bash
# 1. 恢复数据库
docker-compose exec -T postgres psql -U sitecluster -d sitecluster < backup/database.sql

# 2. 恢复缓存（可选）
tar -xzf backup/cache.tar.gz -C /opt/sitecluster/

# 3. 重启应用
docker-compose restart app
```

---

## 🛡️ 生产环境配置

### 1. 使用Nginx反向代理

创建 `nginx.conf`:

```nginx
upstream sitecluster_web {
    server localhost:9090;
}

upstream sitecluster_admin {
    server localhost:9999;
}

server {
    listen 80;
    server_name your-domain.com;

    # 前台
    location / {
        proxy_pass http://sitecluster_web;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 80;
    server_name admin.your-domain.com;

    # 后台管理
    location / {
        proxy_pass http://sitecluster_admin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        
        # IP白名单（可选）
        allow ***********/24;
        allow 10.0.0.0/8;
        deny all;
    }
}
```

启用Nginx：
```bash
docker-compose --profile with-nginx up -d
```

### 2. 配置SSL证书

```bash
# 使用Let's Encrypt
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com -d admin.your-domain.com
```

### 3. 启用监控

```bash
# 启动Prometheus和Grafana
docker-compose --profile monitoring up -d

# 访问监控面板
# Prometheus: http://server-ip:9091
# Grafana: http://server-ip:3000 (admin/admin123)
```

### 4. 配置防火墙

```bash
# Ubuntu/Debian (UFW)
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 9090/tcp  # 仅内网
sudo ufw allow 9999/tcp  # 仅内网

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

---

## 🔄 自动化运维

### 1. 自动备份

创建定时任务：
```bash
# 编辑crontab
crontab -e

# 每天凌晨2点自动备份
0 2 * * * /opt/sitecluster/scripts/backup.sh > /opt/sitecluster/logs/backup.log 2>&1
```

### 2. 日志轮转

创建 `/etc/logrotate.d/sitecluster`:
```
/opt/sitecluster/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 root root
    sharedscripts
    postrotate
        docker-compose -f /opt/sitecluster/docker-compose.yml restart app
    endscript
}
```

### 3. 健康监控

```bash
# 创建健康检查脚本
cat > /opt/sitecluster/health_check.sh << 'EOF'
#!/bin/bash
HEALTH_URL="http://localhost:9090/health"
WEBHOOK_URL="your-webhook-url"  # 钉钉/企业微信告警

STATUS=$(curl -s $HEALTH_URL | jq -r '.status')
if [ "$STATUS" != "healthy" ]; then
    curl -X POST $WEBHOOK_URL \
        -H 'Content-Type: application/json' \
        -d '{"msgtype":"text","text":{"content":"站群系统异常，状态: '$STATUS'"}}'
fi
EOF

chmod +x /opt/sitecluster/health_check.sh

# 添加到crontab（每5分钟检查）
*/5 * * * * /opt/sitecluster/health_check.sh
```

---

## 📊 性能优化

### 1. 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf

# 优化内核参数
cat >> /etc/sysctl.conf << EOF
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_tw_reuse = 1
net.core.somaxconn = 65535
EOF

sysctl -p
```

### 2. Docker优化

编辑 `/etc/docker/daemon.json`:
```json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "10"
  },
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.override_kernel_check=true"
  ]
}
```

重启Docker：
```bash
sudo systemctl restart docker
```

### 3. PostgreSQL优化

连接到数据库容器：
```bash
docker-compose exec postgres psql -U sitecluster

-- 优化配置
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET effective_cache_size = '1GB';

-- 重启生效
docker-compose restart postgres
```

---

## 🔨 常用运维命令

### Docker Compose命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app          # 应用日志
docker-compose logs -f postgres     # 数据库日志
docker-compose logs --tail=100 app  # 最近100行

# 重启服务
docker-compose restart app

# 停止所有服务
docker-compose down

# 停止并删除数据
docker-compose down -v

# 更新镜像并重启
docker-compose pull
docker-compose up -d
```

### 数据库操作

```bash
# 进入PostgreSQL容器
docker-compose exec postgres psql -U sitecluster -d sitecluster

# 备份数据库
docker-compose exec postgres pg_dump -U sitecluster sitecluster > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U sitecluster -d sitecluster < backup.sql

# 查看数据库大小
docker-compose exec postgres psql -U sitecluster -d sitecluster -c "SELECT pg_database_size('sitecluster');"
```

### Redis操作

```bash
# 进入Redis容器
docker-compose exec redis redis-cli

# 清空缓存
docker-compose exec redis redis-cli FLUSHALL

# 查看内存使用
docker-compose exec redis redis-cli INFO memory
```

---

## ❗ 故障处理

### 1. 服务无法启动

```bash
# 检查端口占用
netstat -tlnp | grep -E '9090|9999|5432|6379'

# 查看详细错误
docker-compose logs app | tail -100

# 重建容器
docker-compose down
docker-compose up -d --force-recreate
```

### 2. 数据库连接失败

```bash
# 检查数据库状态
docker-compose exec postgres pg_isready

# 查看数据库日志
docker-compose logs postgres | tail -50

# 重置数据库密码
docker-compose exec postgres psql -U postgres -c "ALTER USER sitecluster PASSWORD 'new_password';"
```

### 3. 内存不足

```bash
# 查看内存使用
docker stats

# 限制容器内存
# 编辑docker-compose.yml，添加：
services:
  app:
    mem_limit: 2g
    memswap_limit: 2g
```

---

## 📝 迁移检查清单

- [ ] Docker和Docker Compose已安装
- [ ] 防火墙规则已配置
- [ ] 数据库备份已完成
- [ ] 环境变量已配置（.env文件）
- [ ] SSL证书已配置（生产环境）
- [ ] 自动备份已设置
- [ ] 监控告警已配置
- [ ] 日志轮转已设置
- [ ] 系统优化已完成
- [ ] 健康检查通过

---

## 🆘 技术支持

如遇到问题，请检查：

1. **查看完整日志**: `docker-compose logs -f`
2. **检查健康状态**: `curl http://localhost:9090/health`
3. **查看系统资源**: `docker stats`
4. **检查网络连接**: `docker-compose exec app ping postgres`

---

**文档版本**: 2.0.0  
**更新时间**: 2024-01-XX  
**适用版本**: 站群系统 v2.0+