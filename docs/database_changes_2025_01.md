# 数据库字段更改记录 - 2025年1月

## 更改日期：2025-01-08

### 1. system_settings 表新增字段

#### 新增字段
- `enable_global_spider_block` (boolean) - 全局蜘蛛屏蔽开关
  - 默认值: false
  - 说明: 控制所有站点的默认蜘蛛屏蔽行为

- `spider_block_403_template` (text) - 蜘蛛屏蔽403响应模板
  - 默认值: NULL
  - 说明: 自定义403错误页面HTML模板，支持变量 {{.UserAgent}} 和 {{.Time}}

#### SQL迁移脚本
```sql
-- 添加全局蜘蛛屏蔽开关
ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS enable_global_spider_block BOOLEAN DEFAULT false;

-- 添加403响应模板
ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS spider_block_403_template TEXT;

-- 添加注释
COMMENT ON COLUMN system_settings.enable_global_spider_block IS '全局蜘蛛屏蔽开关';
COMMENT ON COLUMN system_settings.spider_block_403_template IS '蜘蛛屏蔽403响应模板';
```

### 2. sites 表新增字段

#### 新增字段
- `enable_spider_block` (boolean) - 站点级别蜘蛛屏蔽开关
  - 默认值: false
  - 说明: 覆盖全局设置，独立控制站点的蜘蛛屏蔽

- `spider_block_403_template` (text) - 站点自定义403模板
  - 默认值: NULL
  - 说明: 站点专属的403错误页面模板，优先级高于全局模板

#### SQL迁移脚本
```sql
-- 添加站点级别蜘蛛屏蔽开关
ALTER TABLE sites 
ADD COLUMN IF NOT EXISTS enable_spider_block BOOLEAN DEFAULT false;

-- 添加站点自定义403模板
ALTER TABLE sites 
ADD COLUMN IF NOT EXISTS spider_block_403_template TEXT;

-- 添加注释
COMMENT ON COLUMN sites.enable_spider_block IS '站点级别蜘蛛屏蔽开关（覆盖全局设置）';
COMMENT ON COLUMN sites.spider_block_403_template IS '站点自定义403模板（优先级高于全局模板）';
```

## 功能说明

### 蜘蛛屏蔽优先级逻辑
1. **站点级别优先**: 如果站点启用了 `enable_spider_block=true`，则使用站点设置
2. **全局级别次之**: 如果站点未启用，检查全局 `enable_global_spider_block`
3. **模板选择**: 优先使用站点的 `spider_block_403_template`，若为空则使用全局模板

### 403模板变量
模板中可使用以下变量：
- `{{.UserAgent}}` - 访问者的User-Agent字符串
- `{{.Time}}` - 当前时间

### 默认403模板示例
```html
<!DOCTYPE html>
<html>
<head>
    <title>403 Forbidden</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f0f0f0;
        }
        h1 {
            color: #d32f2f;
            font-size: 48px;
        }
        .message {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .details {
            color: #666;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="message">
        <h1>403 Forbidden</h1>
        <p>Access to this resource is forbidden.</p>
        <div class="details">
            <p>User-Agent: {{.UserAgent}}</p>
            <p>Time: {{.Time}}</p>
        </div>
    </div>
</body>
</html>
```

## 相关文件更改
1. `/internal/model/system_settings.go` - 添加系统设置模型字段
2. `/internal/model/site.go` - 添加站点模型字段
3. `/web/templates/spider-block.html` - 更新前端页面，添加全局开关和403模板编辑
4. `/web/templates/sites.html` - 更新站点编辑表单，添加蜘蛛屏蔽设置
5. `/internal/api/handler/mirror_optimized.go` - 更新镜像处理逻辑，支持站点和全局级别的蜘蛛屏蔽