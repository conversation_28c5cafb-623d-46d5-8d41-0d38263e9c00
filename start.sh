#!/bin/bash

# 站群系统启动脚本

# 设置数据库环境变量
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=sitecluster
export DB_PASSWORD=sitecluster123
export DB_NAME=sitecluster

# Redis环境变量
export REDIS_HOST=localhost
export REDIS_PORT=6379

# 端口环境变量
export PORT=9090
export ADMIN_PORT=9999

# 确保脚本退出时清理进程
trap 'echo "正在停止服务器..."; pkill -f site-cluster; exit' INT TERM

# 检查是否已有进程在运行
if pgrep -f site-cluster > /dev/null; then
    echo "发现已有站群系统在运行，正在停止..."
    pkill -f site-cluster
    sleep 2
fi

# 创建必要的目录
mkdir -p ./data
mkdir -p ./cache
mkdir -p ./logs

# 编译项目
echo "正在编译项目..."
go build -o site-cluster cmd/server/main.go
if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo "编译成功！"

# 清理spider_blocks规则（如果Docker容器正在运行）
if docker ps | grep -q site-cluster-postgres; then
    echo "清理spider_blocks自动规则..."
    chmod +x ./auto_clean_spider_blocks.sh 2>/dev/null
    ./auto_clean_spider_blocks.sh 2>/dev/null || true
fi

# 启动服务器
echo "前台端口: ${PORT:-9090}"
echo "后台端口: ${ADMIN_PORT:-9999}"
./site-cluster

# 服务器启动后会持续运行，直到按 Ctrl+C 停止