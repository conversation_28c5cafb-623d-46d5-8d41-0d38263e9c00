<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词库 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* 词库项样式 - 更紧凑的设计 */
        .library-item {
            padding: 8px 10px;
            margin-bottom: 4px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }
        .library-item:hover {
            background-color: #f9fafb;
            border-color: #3b82f6;
        }
        .library-item.active {
            background-color: #eff6ff;
            border-color: #3b82f6;
        }
        .library-name {
            font-weight: 500;
            color: #1f2937;
            font-size: 14px;
            margin-bottom: 2px;
        }
        .library-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
        }
        .library-type {
            padding: 2px 6px;
            background-color: #f3f4f6;
            border-radius: 4px;
        }
        .library-count::after {
            content: ' 个词';
        }
        .library-actions {
            display: flex;
            gap: 4px;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
        }
        .btn-icon {
            padding: 2px 6px;
            background-color: transparent;
            border: 1px solid #e5e7eb;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;
        }
        .btn-icon:hover {
            background-color: #f3f4f6;
            border-color: #9ca3af;
        }
        .icon-edit::before {
            content: '✏️';
        }
        .icon-trash::before {
            content: '🗑️';
        }
        
        /* 关键词表格样式 */
        .keyword-table {
            width: 100%;
            border-collapse: collapse;
        }
        .keyword-table thead {
            background-color: #f9fafb;
        }
        .keyword-table th {
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        .keyword-table td {
            padding: 12px;
            color: #4b5563;
            border-bottom: 1px solid #f3f4f6;
        }
        .keyword-table tbody tr:hover {
            background-color: #f9fafb;
        }
        
        /* 空状态样式 */
        .empty {
            text-align: center;
            padding: 32px;
            color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">关键词库</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="showAddLibraryModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i>
                            创建词库
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- 左侧：词库列表 -->
                    <div class="lg:col-span-1 bg-white rounded-lg shadow">
                        <div class="px-4 py-3 border-b">
                            <h3 class="text-base font-semibold text-gray-800">词库列表</h3>
                        </div>
                        <div class="p-3 max-h-[700px] overflow-y-auto" id="library-list">
                            <div class="flex justify-center py-8">
                                <div class="text-gray-500">加载中...</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧：关键词列表 -->
                    <div class="lg:col-span-3 bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-800" id="keywords-title">请选择一个词库</h3>
                            <div id="keywords-actions" class="hidden space-x-2">
                                <button onclick="showImportModal()" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
                                    <i class="fas fa-upload mr-1"></i>导入
                                </button>
                                <button onclick="exportKeywords()" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
                                    <i class="fas fa-download mr-1"></i>导出
                                </button>
                                <button onclick="showAddKeywordModal()" class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
                                    <i class="fas fa-plus mr-1"></i>添加关键词
                                </button>
                            </div>
                        </div>
                        <div class="p-6" id="keywords-list">
                            <div class="text-center py-12 text-gray-500">请从左侧选择一个词库</div>
                        </div>
                        <div class="px-6 py-4 border-t flex justify-center" id="keywords-pagination"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <!-- 创建词库模态框 -->
    <div id="library-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold" id="library-modal-title">创建词库</h3>
                    <button onclick="closeLibraryModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="library-form" class="p-6">
                    <input type="hidden" id="library-id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">词库名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="library-name" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：产品词库">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                        <textarea id="library-description" rows="3"
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="词库用途说明..."></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">类型</label>
                        <select id="library-type" onchange="updateLibraryTypeHelp()"
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="main">主词库</option>
                            <option value="synonym">同义词库</option>
                            <option value="pseudo">伪原创词库</option>
                        </select>
                        <div id="library-type-help" class="mt-2 text-sm text-gray-600">
                            <strong>主词库：</strong>用于关键词注入，格式为每行一个关键词。
                        </div>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeLibraryModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveLibrary()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加关键词模态框 -->
    <div id="keyword-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">添加关键词</h3>
                    <button onclick="closeKeywordModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="keyword-form" class="p-6">
                    <div id="keyword-format-help" class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-800">
                        <!-- 格式说明将由 JavaScript 动态更新 -->
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">关键词 <span class="text-red-500">*</span></label>
                        <textarea id="keyword-text" rows="8" required
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="请按照上方格式说明输入"></textarea>
                    </div>
                    
                    <div class="mb-4" id="weight-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">权重</label>
                        <input type="number" id="keyword-weight" min="1" max="10" value="1"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="mt-1 text-sm text-gray-600">权重值越高，在注入时被选中的概率越大（1-10）</p>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeKeywordModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveKeywords()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 导入关键词模态框 -->
    <div id="import-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">导入关键词</h3>
                    <button onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="p-6">
                    <!-- 添加词库选择下拉框 -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择目标词库</label>
                        <select id="import-library-select" 
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                onchange="updateImportFormatHelp()">
                            <option value="">请选择词库...</option>
                        </select>
                    </div>
                    
                    <div id="import-format-help" class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-800">
                        <strong>提示：</strong>请先选择目标词库
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
                        <input type="file" id="import-file" accept=".txt"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="mt-1 text-sm text-gray-600">支持TXT文件，每行一个关键词</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">或直接粘贴</label>
                        <textarea id="import-text" rows="10"
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="每行一个关键词"></textarea>
                    </div>
                    
                    <!-- 导入结果显示区 -->
                    <div id="import-result" class="hidden mb-4 p-4 rounded-lg">
                        <div class="font-medium mb-2">导入结果：</div>
                        <div id="import-result-content"></div>
                    </div>
                    
                    <!-- 显示去重提示 -->
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-info-circle mr-1"></i>
                        系统会自动去除重复的关键词，已存在的关键词将被跳过
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeImportModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="importKeywords()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        导入
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script src="/static/js/admin.js"></script>
    <script>
        let currentLibraryId = null;
        let currentLibraryType = 'main';
        let currentPage = 1;
        const pageSize = 20;
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadLibraries();
        });
        
        // 更新词库类型帮助信息
        function updateLibraryTypeHelp() {
            const type = document.getElementById('library-type').value;
            const helpDiv = document.getElementById('library-type-help');
            
            const helpTexts = {
                'main': '<strong>主词库：</strong>用于关键词注入，格式为每行一个关键词。<br>例如：<br>SEO优化<br>网站建设<br>数字营销',
                'synonym': '<strong>同义词库：</strong>用于同义词替换，格式为"原词|同义词1,同义词2,同义词3"。<br>例如：<br>优秀|卓越,杰出,出色<br>重要|关键,核心,主要',
                'pseudo': '<strong>伪原创词库：</strong>已废弃，请使用独立的伪原创管理功能。'
            };
            
            helpDiv.innerHTML = helpTexts[type] || '';
        }
        
        // 加载词库列表
        async function loadLibraries() {
            try {
                const res = await fetch('/api/v1/keywords/libraries');
                const data = await res.json();
                
                if (data.success) {
                    renderLibraries(data.data);
                }
            } catch (error) {
                showError('加载词库失败: ' + error.message);
            }
        }
        
        // 渲染词库列表
        function renderLibraries(libraries) {
            const container = document.getElementById('library-list');
            
            if (!libraries || libraries.length === 0) {
                container.innerHTML = '<div class="empty">暂无词库</div>';
                return;
            }
            
            container.innerHTML = libraries.map(lib => `
                <div class="library-item ${currentLibraryId === lib.id ? 'active' : ''}" 
                     onclick="selectLibrary(${lib.id}, '${lib.name}')">
                    <div class="flex justify-between items-start pr-20">
                        <div>
                            <div class="library-name">${lib.name}</div>
                            <div class="library-meta">
                                <span class="library-type">${getLibraryTypeText(lib.type)}</span>
                                <span class="library-count" id="count-${lib.id}">-</span>
                            </div>
                        </div>
                    </div>
                    <div class="library-actions">
                        <button class="btn-icon" onclick="editLibrary(${lib.id}, event)" title="编辑">
                            <i class="icon-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="deleteLibrary(${lib.id}, event)" title="删除">
                            <i class="icon-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
            
            // 加载每个词库的关键词数量
            libraries.forEach(lib => {
                loadLibraryCount(lib.id);
            });
        }
        
        // 加载词库关键词数量
        async function loadLibraryCount(libraryId) {
            try {
                const res = await fetch(`/api/v1/keywords/libraries/${libraryId}/keywords?page=1&limit=1`);
                const data = await res.json();
                
                if (data.success) {
                    const countElement = document.getElementById(`count-${libraryId}`);
                    if (countElement) {
                        countElement.textContent = `${data.data.total} 个词`;
                    }
                }
            } catch (error) {
                console.error('加载词库数量失败:', error);
            }
        }
        
        // 选择词库
        async function selectLibrary(libraryId, libraryName) {
            currentLibraryId = libraryId;
            currentPage = 1;
            
            // 获取词库详情以获取类型
            try {
                const res = await fetch('/api/v1/keywords/libraries');
                const data = await res.json();
                if (data.success) {
                    const library = data.data.find(lib => lib.id === libraryId);
                    if (library) {
                        currentLibraryType = library.type;
                    }
                }
            } catch (error) {
                console.error('获取词库类型失败:', error);
            }
            
            // 更新UI
            document.querySelectorAll('.library-item').forEach(item => {
                item.classList.toggle('active', item.onclick.toString().includes(libraryId));
            });
            
            document.getElementById('keywords-title').textContent = libraryName;
            document.getElementById('keywords-actions').style.display = 'block';
            
            loadKeywords();
        }
        
        // 加载关键词列表
        async function loadKeywords(page = 1) {
            if (!currentLibraryId) return;
            
            currentPage = page;
            
            try {
                const res = await fetch(`/api/v1/keywords/libraries/${currentLibraryId}/keywords?page=${page}&limit=${pageSize}`);
                const data = await res.json();
                
                if (data.success) {
                    renderKeywords(data.data.items);
                    renderKeywordsPagination(data.data.total);
                }
            } catch (error) {
                showError('加载关键词失败: ' + error.message);
            }
        }
        
        // 渲染关键词列表
        function renderKeywords(keywords) {
            const container = document.getElementById('keywords-list');
            
            if (!keywords || keywords.length === 0) {
                container.innerHTML = '<div class="empty">暂无关键词</div>';
                return;
            }
            
            container.innerHTML = `
                <table class="keyword-table">
                    <thead>
                        <tr>
                            <th>关键词</th>
                            <th>权重</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${keywords.map(kw => `
                            <tr>
                                <td>${kw.keyword || kw.word || ''}</td>
                                <td>${kw.weight || 1}</td>
                                <td>${formatDate(kw.created_at)}</td>
                                <td>
                                    <button class="btn-icon" onclick="editKeyword(${kw.id})" title="编辑">
                                        <i class="icon-edit"></i>
                                    </button>
                                    <button class="btn-icon" onclick="deleteKeyword(${kw.id})" title="删除">
                                        <i class="icon-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }
        
        // 渲染关键词分页
        function renderKeywordsPagination(total) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = document.getElementById('keywords-pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let html = '<div class="flex items-center justify-between">';
            
            // 左侧统计信息
            const startItem = (currentPage - 1) * pageSize + 1;
            const endItem = Math.min(currentPage * pageSize, total);
            html += `<div class="text-sm text-gray-600">
                        显示 ${startItem} 到 ${endItem} 条，共 ${total} 条记录
                     </div>`;
            
            // 右侧分页按钮
            html += '<div class="flex items-center space-x-1">';
            
            // 上一页
            if (currentPage > 1) {
                html += `<button onclick="loadKeywords(${currentPage - 1})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               <i class="fas fa-chevron-left mr-1"></i>上一页
                         </button>`;
            } else {
                html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-l-md cursor-not-allowed">
                               <i class="fas fa-chevron-left mr-1"></i>上一页
                         </span>`;
            }
            
            // 计算显示的页码范围
            const maxVisible = 5; // 最多显示5个页码，让界面更紧凑
            let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let endPage = Math.min(totalPages, startPage + maxVisible - 1);
            
            // 调整起始页，确保显示足够的页码
            if (endPage - startPage + 1 < maxVisible) {
                startPage = Math.max(1, endPage - maxVisible + 1);
            }
            
            // 如果不是从第1页开始，显示第1页和省略号
            if (startPage > 1) {
                html += `<button onclick="loadKeywords(1)" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               1
                         </button>`;
                if (startPage > 2) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border-t border-b border-gray-300">
                                   <i class="fas fa-ellipsis-h"></i>
                             </span>`;
                }
            }
            
            // 显示页码
            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 border-t border-b border-blue-600">
                                   ${i}
                             </span>`;
                } else {
                    html += `<button onclick="loadKeywords(${i})" 
                                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                                   ${i}
                             </button>`;
                }
            }
            
            // 如果不是到最后一页，显示省略号和最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border-t border-b border-gray-300">
                                   <i class="fas fa-ellipsis-h"></i>
                             </span>`;
                }
                html += `<button onclick="loadKeywords(${totalPages})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               ${totalPages}
                         </button>`;
            }
            
            // 下一页
            if (currentPage < totalPages) {
                html += `<button onclick="loadKeywords(${currentPage + 1})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               下一页<i class="fas fa-chevron-right ml-1"></i>
                         </button>`;
            } else {
                html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-r-md cursor-not-allowed">
                               下一页<i class="fas fa-chevron-right ml-1"></i>
                         </span>`;
            }
            
            html += '</div></div>';
            
            pagination.innerHTML = html;
        }
        
        // 显示创建词库模态框
        function showAddLibraryModal() {
            document.getElementById('library-modal-title').textContent = '创建词库';
            document.getElementById('library-form').reset();
            document.getElementById('library-id').value = '';
            document.getElementById('library-modal').classList.remove('hidden');
        }
        
        // 关闭词库模态框
        function closeLibraryModal() {
            document.getElementById('library-modal').classList.add('hidden');
        }
        
        // 保存词库
        async function saveLibrary() {
            const id = document.getElementById('library-id').value;
            const isEdit = !!id;
            
            const libraryData = {
                name: document.getElementById('library-name').value,
                description: document.getElementById('library-description').value,
                type: document.getElementById('library-type').value
            };
            
            try {
                const url = isEdit ? `/api/v1/keywords/libraries/${id}` : '/api/v1/keywords/libraries';
                const method = isEdit ? 'PUT' : 'POST';
                
                const res = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(libraryData)
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(isEdit ? '更新成功' : '创建成功');
                    closeLibraryModal();
                    loadLibraries();
                } else {
                    showError(data.error || '保存失败');
                }
            } catch (error) {
                showError('保存失败: ' + error.message);
            }
        }
        
        // 编辑词库
        async function editLibrary(id, event) {
            event.stopPropagation();
            
            try {
                const res = await fetch(`/api/v1/keywords/libraries`);
                const data = await res.json();
                
                if (data.success) {
                    const library = data.data.find(lib => lib.id === id);
                    if (library) {
                        document.getElementById('library-modal-title').textContent = '编辑词库';
                        document.getElementById('library-id').value = library.id;
                        document.getElementById('library-name').value = library.name;
                        document.getElementById('library-description').value = library.description || '';
                        document.getElementById('library-type').value = library.type;
                        document.getElementById('library-modal').classList.remove('hidden');
                    }
                }
            } catch (error) {
                showError('加载词库信息失败: ' + error.message);
            }
        }
        
        // 删除词库
        async function deleteLibrary(id, event) {
            event.stopPropagation();
            
            if (!confirm('确定要删除这个词库吗？相关的所有关键词也会被删除。')) return;
            
            try {
                const res = await fetch(`/api/v1/keywords/libraries/${id}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('删除成功');
                    if (currentLibraryId === id) {
                        currentLibraryId = null;
                        document.getElementById('keywords-title').textContent = '请选择一个词库';
                        document.getElementById('keywords-actions').style.display = 'none';
                        document.getElementById('keywords-list').innerHTML = '<div class="empty">请从左侧选择一个词库</div>';
                    }
                    loadLibraries();
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 显示添加关键词模态框
        function showAddKeywordModal() {
            if (!currentLibraryId) {
                showWarning('请先选择一个词库');
                return;
            }
            
            // 根据词库类型更新格式说明
            const formatHelp = document.getElementById('keyword-format-help');
            const weightGroup = document.getElementById('weight-group');
            
            if (currentLibraryType === 'main') {
                formatHelp.innerHTML = `
                    <strong>主词库格式说明：</strong><br>
                    每行输入一个关键词，系统将随机选择关键词进行注入。<br>
                    <strong>示例：</strong><br>
                    SEO优化<br>
                    网站建设<br>
                    数字营销<br>
                    搜索引擎优化
                `;
                weightGroup.style.display = 'block';
                document.getElementById('keyword-text').placeholder = "每行一个关键词\n例如：\nSEO优化\n网站建设\n数字营销";
            } else if (currentLibraryType === 'synonym') {
                formatHelp.innerHTML = `
                    <strong>同义词库格式说明：</strong><br>
                    每行格式为：原词|同义词1,同义词2,同义词3<br>
                    使用竖线"|"分隔原词和同义词，使用逗号","分隔多个同义词。<br>
                    <strong>示例：</strong><br>
                    优秀|卓越,杰出,出色,优异<br>
                    重要|关键,核心,主要,首要<br>
                    提高|提升,增强,改善,优化
                `;
                weightGroup.style.display = 'none';
                document.getElementById('keyword-text').placeholder = "原词|同义词1,同义词2,同义词3\n例如：\n优秀|卓越,杰出,出色\n重要|关键,核心,主要";
            } else {
                formatHelp.innerHTML = '<strong>提示：</strong>伪原创词库请使用独立的伪原创管理功能。';
                weightGroup.style.display = 'none';
            }
            
            document.getElementById('keyword-form').reset();
            document.getElementById('keyword-modal').classList.remove('hidden');
        }
        
        // 关闭关键词模态框
        function closeKeywordModal() {
            document.getElementById('keyword-modal').classList.add('hidden');
        }
        
        // 保存关键词
        async function saveKeywords() {
            const keywordsText = document.getElementById('keyword-text').value;
            const weight = parseInt(document.getElementById('keyword-weight').value);
            
            const lines = keywordsText.split('\n').filter(k => k.trim());
            
            if (lines.length === 0) {
                showWarning('请输入至少一个关键词');
                return;
            }
            
            try {
                let successCount = 0;
                let errorCount = 0;
                
                for (const line of lines) {
                    let keyword = line.trim();
                    let synonyms = [];
                    
                    // 如果是同义词库，解析格式
                    if (currentLibraryType === 'synonym' && keyword.includes('|')) {
                        const parts = keyword.split('|');
                        if (parts.length === 2) {
                            keyword = parts[0].trim();
                            synonyms = parts[1].split(',').map(s => s.trim()).filter(s => s);
                        }
                    }
                    
                    const res = await fetch(`/api/v1/keywords/libraries/${currentLibraryId}/keywords`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            keyword: keyword,
                            synonyms: synonyms,
                            weight: weight
                        })
                    });
                    
                    const data = await res.json();
                    if (data.success) {
                        successCount++;
                    } else {
                        errorCount++;
                        console.error(`添加关键词失败: ${keyword}`, data.error);
                    }
                }
                
                if (successCount > 0) {
                    showSuccess(`成功添加 ${successCount} 个关键词`);
                }
                if (errorCount > 0) {
                    showWarning(`${errorCount} 个关键词添加失败`);
                }
                
                closeKeywordModal();
                loadKeywords(currentPage);
                loadLibraryCount(currentLibraryId);
            } catch (error) {
                showError('添加失败: ' + error.message);
            }
        }
        
        // 删除关键词
        async function deleteKeyword(id) {
            if (!confirm('确定要删除这个关键词吗？')) return;
            
            try {
                const res = await fetch(`/api/v1/keywords/keywords/${id}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('删除成功');
                    loadKeywords(currentPage);
                    loadLibraryCount(currentLibraryId);
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 显示导入模态框
        async function showImportModal() {
            // 加载词库列表到下拉框
            try {
                const res = await fetch('/api/v1/keywords/libraries');
                const data = await res.json();
                
                if (data.success && data.data) {
                    const select = document.getElementById('import-library-select');
                    select.innerHTML = '<option value="">请选择词库...</option>';
                    
                    // 只显示主词库（type === 'main'）
                    const mainLibraries = data.data.filter(lib => lib.type === 'main');
                    
                    mainLibraries.forEach(lib => {
                        const option = document.createElement('option');
                        option.value = lib.id;
                        option.textContent = lib.name;
                        // 如果是当前选中的词库，设置为默认选中
                        if (currentLibraryId && lib.id === currentLibraryId) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                    
                    // 如果有默认选中的词库，更新格式说明
                    if (currentLibraryId && currentLibraryType === 'main') {
                        updateImportFormatHelp();
                    }
                }
            } catch (error) {
                console.error('加载词库列表失败:', error);
                showError('加载词库列表失败');
                return;
            }
            
            document.getElementById('import-file').value = '';
            document.getElementById('import-text').value = '';
            document.getElementById('import-modal').classList.remove('hidden');
        }
        
        // 更新导入格式帮助
        function updateImportFormatHelp() {
            const select = document.getElementById('import-library-select');
            const formatHelp = document.getElementById('import-format-help');
            
            if (!select.value) {
                formatHelp.innerHTML = '<strong>提示：</strong>请先选择目标词库';
                return;
            }
            
            formatHelp.innerHTML = `
                <strong>关键词导入格式：</strong><br>
                每行一个关键词，支持TXT文件批量导入。<br>
                <strong>示例：</strong><br>
                SEO优化<br>
                网站建设<br>
                数字营销
            `;
        }
        
        // 关闭导入模态框
        function closeImportModal() {
            document.getElementById('import-modal').classList.add('hidden');
            // 清理导入结果显示
            document.getElementById('import-result').classList.add('hidden');
            document.getElementById('import-result-content').innerHTML = '';
            // 清空输入
            document.getElementById('import-file').value = '';
            document.getElementById('import-text').value = '';
        }
        
        // 导入关键词
        async function importKeywords() {
            const libraryId = document.getElementById('import-library-select').value;
            
            if (!libraryId) {
                showWarning('请选择目标词库');
                return;
            }
            
            const file = document.getElementById('import-file').files[0];
            const text = document.getElementById('import-text').value;
            
            let keywords = [];
            
            if (file) {
                const fileText = await file.text();
                keywords = fileText.split('\n').filter(k => k.trim());
            } else if (text) {
                keywords = text.split('\n').filter(k => k.trim());
            } else {
                showWarning('请选择文件或输入关键词');
                return;
            }
            
            if (keywords.length === 0) {
                showWarning('没有找到有效的关键词');
                return;
            }
            
            // 获取导入按钮并显示加载状态
            const importBtn = document.querySelector('#import-modal button[onclick="importKeywords()"]');
            const originalText = importBtn.textContent;
            importBtn.disabled = true;
            importBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>导入中...';
            
            try {
                const res = await fetch('/api/v1/keywords/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        library_id: parseInt(libraryId),  // 确保是数字类型
                        keywords: keywords
                    })
                });
                
                const data = await res.json();
                
                if (data.success) {
                    // 显示导入结果
                    const resultDiv = document.getElementById('import-result');
                    const resultContent = document.getElementById('import-result-content');
                    
                    let resultHtml = '<div class="space-y-2">';
                    resultHtml += `<div class="flex justify-between"><span>总计：</span><strong>${data.data.total} 个</strong></div>`;
                    resultHtml += `<div class="flex justify-between text-green-600"><span>成功导入：</span><strong>${data.data.success} 个</strong></div>`;
                    
                    if (data.data.duplicate > 0) {
                        resultHtml += `<div class="flex justify-between text-yellow-600"><span>去重：</span><strong>${data.data.duplicate} 个</strong></div>`;
                    }
                    
                    if (data.data.failed > 0) {
                        resultHtml += `<div class="flex justify-between text-red-600"><span>跳过（已存在）：</span><strong>${data.data.failed} 个</strong></div>`;
                    }
                    
                    resultHtml += '</div>';
                    
                    resultContent.innerHTML = resultHtml;
                    resultDiv.className = 'mb-4 p-4 rounded-lg bg-green-50 border border-green-200';
                    resultDiv.classList.remove('hidden');
                    
                    // 清空输入
                    document.getElementById('import-file').value = '';
                    document.getElementById('import-text').value = '';
                    
                    // 如果导入的是当前查看的词库，刷新列表
                    if (parseInt(libraryId) === currentLibraryId) {
                        loadKeywords(1);
                        loadLibraryCount(currentLibraryId);
                    }
                    
                    // 更新其他词库的数量
                    loadLibraryCount(parseInt(libraryId));
                    
                    // 显示成功消息
                    showSuccess('关键词导入完成');
                } else {
                    // 显示错误结果
                    const resultDiv = document.getElementById('import-result');
                    const resultContent = document.getElementById('import-result-content');
                    
                    resultContent.innerHTML = `<div class="text-red-600">${data.error || '导入失败'}</div>`;
                    resultDiv.className = 'mb-4 p-4 rounded-lg bg-red-50 border border-red-200';
                    resultDiv.classList.remove('hidden');
                    
                    showError(data.error || '导入失败');
                }
            } catch (error) {
                // 显示错误结果
                const resultDiv = document.getElementById('import-result');
                const resultContent = document.getElementById('import-result-content');
                
                resultContent.innerHTML = `<div class="text-red-600">导入失败: ${error.message}</div>`;
                resultDiv.className = 'mb-4 p-4 rounded-lg bg-red-50 border border-red-200';
                resultDiv.classList.remove('hidden');
                
                showError('导入失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                importBtn.disabled = false;
                importBtn.textContent = originalText;
            }
        }
        
        // 导出关键词
        function exportKeywords() {
            if (!currentLibraryId) {
                showWarning('请先选择一个词库');
                return;
            }
            
            window.open(`/api/v1/keywords/export/${currentLibraryId}`, '_blank');
        }
        
        // 获取词库类型文本
        function getLibraryTypeText(type) {
            const typeMap = {
                'main': '主词库',
                'synonym': '同义词库',
                'pseudo': '伪原创词库'
            };
            return typeMap[type] || type;
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.className === 'modal') {
                event.target.style.display = 'none';
            }
        }
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
