<div class="settings-panel p-6" id="system-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">系统信息</h3>
                        
                        <!-- 基础信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-600 mb-1">版本</label>
                                <p class="text-lg font-semibold text-gray-800">1.0.3</p>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-600 mb-1">Go版本</label>
                                <p class="text-lg font-semibold text-gray-800" id="go-version">1.21</p>
                            </div>
                        </div>
                        
                        <!-- 数据库状态 -->
                        <div class="border border-gray-200 rounded-lg p-4 mb-6">
                            <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                                <i class="fas fa-database mr-2 text-blue-600"></i>
                                数据库状态
                            </h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">连接状态</label>
                                    <p class="font-semibold" id="db-status">
                                        <span class="text-gray-400">加载中...</span>
                                    </p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">数据库类型</label>
                                    <p class="font-semibold" id="db-type">PostgreSQL</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">数据库大小</label>
                                    <p class="font-semibold" id="db-size">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">表数量</label>
                                    <p class="font-semibold" id="db-tables">-</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">连接池状态</label>
                                    <p class="text-sm" id="db-connections">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">活动连接</label>
                                    <p class="text-sm" id="db-active">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">空闲连接</label>
                                    <p class="text-sm" id="db-idle">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">活动查询</label>
                                    <p class="text-sm" id="db-queries">-</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Redis状态 -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                                <i class="fas fa-memory mr-2 text-red-600"></i>
                                Redis状态
                            </h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">连接状态</label>
                                    <p class="font-semibold" id="redis-status">
                                        <span class="text-gray-400">加载中...</span>
                                    </p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">版本</label>
                                    <p class="font-semibold" id="redis-version">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">内存使用</label>
                                    <p class="font-semibold" id="redis-memory">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">键数量</label>
                                    <p class="font-semibold" id="redis-keys">-</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">命中率</label>
                                    <p class="text-sm" id="redis-hit-rate">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">连接客户端</label>
                                    <p class="text-sm" id="redis-clients">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">每秒操作</label>
                                    <p class="text-sm" id="redis-ops">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">运行时间</label>
                                    <p class="text-sm" id="redis-uptime">-</p>
                                </div>
                            </div>
                        </div>
                    </div>