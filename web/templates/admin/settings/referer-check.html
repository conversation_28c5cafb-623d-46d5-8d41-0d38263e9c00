<!-- 来源判断设置面板 -->
<div class="settings-panel p-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">全局来源判断设置</h3>
    
    <!-- 紧凑的提示信息 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 flex items-start">
        <i class="fas fa-info-circle text-yellow-600 mt-0.5 mr-2"></i>
        <p class="text-xs text-yellow-800">
            这里是全局默认设置，新建站点时会使用这些默认值。每个站点可以在"站点管理"中单独配置来源判断规则。
        </p>
    </div>
    
    <div class="space-y-4">
        <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-800">启用全局来源判断</h4>
                    <p class="text-xs text-gray-600 mt-0.5">所有站点将默认进行来源检查（站点可单独设置）</p>
                </div>
                <label class="switch switch-sm">
                    <input type="checkbox" id="enable-global-referer-check">
                    <span class="slider round"></span>
                </label>
            </div>
        </div>
        
        <!-- 来源判断规则说明 - 更紧凑的版本 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div class="flex items-start">
                <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                <div class="flex-1">
                    <p class="text-xs font-medium text-blue-800 mb-1">来源判断规则说明：</p>
                    <ul class="text-xs text-blue-700 space-y-0.5">
                        <li>• 全局开关关闭时，不进行来源判断</li>
                        <li>• 全局开关开启后，只有来自白名单域名的访问才被允许</li>
                        <li>• 支持通配符匹配，如 *.google.com 匹配所有谷歌子域名</li>
                        <li>• 无Referer的直接访问默认允许</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">允许的来源域名</label>
            <textarea id="global-allowed-referers" rows="4" 
                      class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                      placeholder="输入允许访问的来源域名，用 | 分隔，例如：
www.baidu.com|*.google.com|www.so.com|*.bing.com
留空则允许所有来源（包括空Referer）"></textarea>
            <p class="mt-1 text-sm text-gray-600">设置允许访问的来源域名，多个域名用 | 分隔，支持通配符(*)。<strong>留空表示不进行来源判断</strong></p>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">拒绝状态码</label>
            <input type="number" id="global-referer-block-code" 
                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                   placeholder="403" min="100" max="599" value="403">
            <p class="mt-1 text-sm text-gray-600">返回的HTTP状态码（100-599）</p>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">自定义拒绝页面HTML</label>
            <div class="mb-2 p-3 bg-gray-50 rounded">
                <p class="text-xs font-medium text-gray-700 mb-2">可用的变量标签：</p>
                <div class="space-y-1">
                    <div><code class="bg-white px-1 py-0.5 rounded">{domain}</code> - 当前域名</div>
                    <div><code class="bg-white px-1 py-0.5 rounded">{referer}</code> - 访问来源</div>
                    <div><code class="bg-white px-1 py-0.5 rounded">{ip}</code> - 访问者IP</div>
                    <div><code class="bg-white px-1 py-0.5 rounded">{year}</code> - 当前年份</div>
                    <div><code class="bg-white px-1 py-0.5 rounded">{date}</code> - 当前日期</div>
                </div>
            </div>
            
            <textarea id="global-referer-block-html" rows="15" 
                      class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                      placeholder="在此输入自定义HTML模板..."></textarea>
        </div>
    </div>
</div>