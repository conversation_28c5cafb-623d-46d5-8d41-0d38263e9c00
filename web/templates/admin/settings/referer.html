<div class="settings-panel p-6" id="referer-check-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">全局来源判断设置</h3>
                        
                        <!-- 紧凑的提示信息 -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2.5 mb-4 flex items-start">
                            <i class="fas fa-info-circle text-yellow-600 mt-0.5 mr-2"></i>
                            <p class="text-xs text-yellow-800">
                                这里是全局默认设置，新建站点时会使用这些默认值。每个站点可以在"站点管理"中单独配置来源判断规则。
                            </p>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- 开关设置 - 使用更紧凑的布局 -->
                            <div class="border border-gray-200 rounded-lg p-3">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-800">启用全局来源判断</h4>
                                        <p class="text-xs text-gray-600 mt-0.5">所有站点将默认进行来源检查（站点可单独设置）</p>
                                    </div>
                                    <label class="switch switch-sm">
                                        <input type="checkbox" id="enable_global_referer_check">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- 来源判断规则说明 - 更紧凑的版本 -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                                    <div class="flex-1">
                                        <p class="text-xs font-medium text-blue-800 mb-1">来源判断规则说明：</p>
                                        <ul class="text-xs text-blue-700 space-y-0.5">
                                            <li>• 全局开关关闭时，不进行来源判断</li>
                                            <li>• 全局开关开启后，只有来自白名单域名的访问才被允许</li>
                                            <li>• 支持通配符匹配，如 *.google.com 匹配所有谷歌子域名</li>
                                            <li>• 空Referer的直接访问默认被允许</li>
                                            <li>• 非白名单访问将返回自定义状态码和页面</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">允许的来源域名</label>
                                <textarea id="global_allowed_referers" rows="4" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="输入允许访问的来源域名，用 | 分隔，例如：
www.baidu.com|*.google.com|www.so.com|*.bing.com
留空则允许所有来源（包括空Referer）"></textarea>
                                <p class="mt-1 text-sm text-gray-600">设置允许访问的来源域名，多个域名用 | 分隔，支持通配符(*)。<strong>留空表示不进行来源判断</strong></p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">拒绝状态码</label>
                                <input type="number" id="global_referer_block_code" 
                                       class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="403" min="100" max="599" value="403">
                                <p class="mt-1 text-sm text-gray-600">当来源不在白名单时返回的HTTP状态码（100-599）</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">拒绝访问页面</label>
                                
                                <!-- 标签说明 -->
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                                    <h5 class="font-semibold text-blue-900 mb-2">
                                        <i class="fas fa-tags mr-1"></i>支持的自定义标签
                                    </h5>
                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                        <div class="space-y-1">
                                            <div><code class="bg-white px-1 py-0.5 rounded">{domain}</code> - 当前域名</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{referer}</code> - 访问来源</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{ip}</code> - 访问者IP</div>
                                        </div>
                                        <div class="space-y-1">
                                            <div><code class="bg-white px-1 py-0.5 rounded">{year}</code> - 当前年份</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{date}</code> - 当前日期</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{time}</code> - 当前时间</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <textarea id="global_referer_block_html" rows="15" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="在此输入自定义HTML模板..."><!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>403 Forbidden</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #d32f2f; }
        p { color: #666; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>403 Forbidden</h1>
        <p>抱歉，您的访问来源未被授权。</p>
        <p>域名：{domain}</p>
        <p>来源：{referer}</p>
        <p>IP：{ip}</p>
    </div>
</body>
</html></textarea>
                                <p class="mt-1 text-sm text-gray-600">当来源不在白名单时显示的HTML页面</p>
                            </div>
                        </div>
                    </div>