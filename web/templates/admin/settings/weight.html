<div class="settings-panel p-6" id="weight-monitor-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">权重监测设置</h3>
                        <div class="space-y-6">
                            <!-- 功能开关 -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="font-medium text-blue-800">权重监测功能</h4>
                                        <p class="text-sm text-blue-600 mt-1">自动获取所有站点域名的百度权重和流量数据</p>
                                    </div>
                                    <label class="switch">
                                        <input type="checkbox" id="weight_monitor_enabled">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                                <p class="text-xs text-blue-700">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    开启后将按照配置定时获取所有活跃站点的权重信息
                                </p>
                            </div>
                            
                            <!-- API配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">爱站API配置</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">API密钥</label>
                                        <input type="text" id="weight_api_key" 
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                                               placeholder="请输入爱站API密钥">
                                        <p class="mt-1 text-sm text-gray-600">
                                            从 <a href="https://apistore.aizhan.com" target="_blank" class="text-blue-600 hover:underline">爱站API商店</a> 获取密钥
                                        </p>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <button onclick="testAPIKey()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm">
                                            <i class="fas fa-check-circle mr-2"></i>验证密钥
                                        </button>
                                        <button onclick="window.open('https://apistore.aizhan.com/detail/23/', '_blank')" 
                                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                            <i class="fas fa-external-link-alt mr-2"></i>查看API文档
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 调度配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">调度配置</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">检查间隔（分钟）</label>
                                        <input type="number" id="weight_check_interval" value="60" min="10" max="1440"
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-600">多久执行一次权重检查</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">批次大小</label>
                                        <input type="number" id="weight_batch_size" value="5" min="1" max="20"
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-600">一次请求获取几个域名</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">批次间隔（秒）</label>
                                        <input type="number" id="weight_batch_delay" value="5" min="1" max="60"
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-600">批次之间的等待时间</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">循环等待（小时）</label>
                                        <input type="number" id="weight_cycle_wait" value="24" min="1" max="168"
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-600">完成后等待下次循环</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 状态信息 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">运行状态</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">服务状态</span>
                                        <span id="weight-service-status" class="text-sm font-medium text-gray-800">未启动</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">最后检查时间</span>
                                        <span id="weight-last-check" class="text-sm font-medium text-gray-800">-</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">下次检查时间</span>
                                        <span id="weight-next-check" class="text-sm font-medium text-gray-800">-</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4 flex space-x-2">
                                    <button onclick="manualCheckWeights()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                                        <i class="fas fa-play mr-2"></i>立即执行
                                    </button>
                                    <button onclick="viewWeightHistory()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                        <i class="fas fa-chart-line mr-2"></i>查看统计
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 说明信息 -->
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <p class="font-medium text-yellow-800 mb-2">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>注意事项：
                                </p>
                                <ul class="space-y-1 text-sm text-yellow-700">
                                    <li>• 监测的是站点配置的域名，不是目标URL</li>
                                    <li>• API调用会产生费用，请合理设置检查频率</li>
                                    <li>• 批次大小建议不超过10个，避免API限流</li>
                                    <li>• 修改配置后需要保存并重启服务才能生效</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    </div> <!-- 关闭可滚动容器 -->

<script>
// 权重监测相关函数

// 页面加载时初始化权重监测状态
document.addEventListener('DOMContentLoaded', function() {
    // 延迟加载，确保页面完全加载
    setTimeout(() => {
        loadWeightMonitorStatus();
    }, 500);
});

// 加载权重监测状态
async function loadWeightMonitorStatus() {
    try {
        const res = await fetch('/api/v1/weight/config');
        const data = await res.json();
        
        if (data.success && data.data) {
            const config = data.data;
            
            // 填充表单字段
            const enabledCheckbox = document.getElementById('weight_monitor_enabled');
            const apiKeyInput = document.getElementById('weight_api_key');
            const checkIntervalInput = document.getElementById('weight_check_interval');
            const batchSizeInput = document.getElementById('weight_batch_size');
            const batchDelayInput = document.getElementById('weight_batch_delay');
            const cycleWaitInput = document.getElementById('weight_cycle_wait');
            
            // 设置表单值
            if (enabledCheckbox) enabledCheckbox.checked = config.enabled || false;
            if (apiKeyInput) apiKeyInput.value = config.api_key || '';
            if (checkIntervalInput) checkIntervalInput.value = config.check_interval || 60;
            if (batchSizeInput) batchSizeInput.value = config.batch_size || 5;
            if (batchDelayInput) batchDelayInput.value = config.batch_delay || 5;
            if (cycleWaitInput) cycleWaitInput.value = config.cycle_wait || 24;
            
            // 更新状态显示
            const statusEl = document.getElementById('weight-service-status');
            const lastCheckEl = document.getElementById('weight-last-check');
            const nextCheckEl = document.getElementById('weight-next-check');
            
            if (!statusEl) return; // 元素不存在则退出
            
            // 更新服务状态
            if (!config.api_key) {
                statusEl.textContent = '未配置';
                statusEl.className = 'text-sm font-medium text-gray-500';
            } else if (config.enabled) {
                statusEl.textContent = '运行中';
                statusEl.className = 'text-sm font-medium text-green-600';
            } else {
                statusEl.textContent = '已停止';
                statusEl.className = 'text-sm font-medium text-yellow-600';
            }
            
            // 更新最后检查时间
            if (lastCheckEl) {
                if (config.last_check_time && !config.last_check_time.startsWith('0001-01-01')) {
                    const date = new Date(config.last_check_time);
                    if (!isNaN(date.getTime())) {
                        lastCheckEl.textContent = date.toLocaleString('zh-CN');
                    } else {
                        lastCheckEl.textContent = '从未执行';
                    }
                } else {
                    lastCheckEl.textContent = '从未执行';
                }
            }
            
            // 计算下次检查时间
            if (nextCheckEl) {
                if (config.enabled && config.last_check_time && !config.last_check_time.startsWith('0001-01-01')) {
                    const lastCheck = new Date(config.last_check_time);
                    if (!isNaN(lastCheck.getTime())) {
                        // 根据check_interval计算下次检查时间
                        const nextCheck = new Date(lastCheck.getTime() + config.check_interval * 60 * 1000);
                        const now = new Date();
                        
                        if (nextCheck > now) {
                            nextCheckEl.textContent = nextCheck.toLocaleString('zh-CN');
                        } else {
                            nextCheckEl.textContent = '即将执行';
                        }
                    } else {
                        nextCheckEl.textContent = '-';
                    }
                } else if (config.enabled && config.api_key) {
                    nextCheckEl.textContent = '等待首次执行';
                } else {
                    nextCheckEl.textContent = '-';
                }
            }
        }
    } catch (error) {
        console.error('加载权重监测状态失败:', error);
    }
}

// 从cookie获取CSRF token
function getCsrfToken() {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrf_token') {
            return value;
        }
    }
    return '';
}

async function manualCheckWeights() {
    if (!confirm('确定要立即执行权重检查吗？')) return;
    
    try {
        // 获取CSRF token
        const csrfToken = getCsrfToken();
        
        const res = await fetch('/api/v1/weight/check', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            credentials: 'same-origin',
            body: JSON.stringify({}) // 发送空对象，避免JSON解析错误
        });
        
        // 检查响应类型
        const contentType = res.headers.get('content-type');
        let data;
        
        if (contentType && contentType.includes('application/json')) {
            data = await res.json();
        } else {
            // 如果不是JSON，读取文本
            const text = await res.text();
            console.error('非JSON响应:', text);
            throw new Error('服务器返回了非JSON响应');
        }
        
        if (data.success) {
            showToast('权重检查已触发，请稍后查看结果', 'success');
        } else {
            showToast(data.error || '触发失败', 'error');
        }
    } catch (error) {
        console.error('执行失败:', error);
        showToast('执行失败: ' + error.message, 'error');
    }
}

async function testAPIKey() {
    const apiKey = document.getElementById('weight_api_key').value;
    if (!apiKey) {
        showToast('请先输入API密钥', 'warning');
        return;
    }
    
    try {
        const csrfToken = getCsrfToken();
        
        const res = await fetch('/api/v1/weight/test-api', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            credentials: 'same-origin',
            body: JSON.stringify({ api_key: apiKey })
        });
        
        const data = await res.json();
        if (data.success) {
            showToast('API密钥验证成功', 'success');
        } else {
            showToast(data.error || '验证失败', 'error');
        }
    } catch (error) {
        showToast('验证失败: ' + error.message, 'error');
    }
}

function viewWeightHistory() {
    window.location.href = '/admin/weight-monitor';
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 检查是否有全局的showToast函数，并且不是当前函数
    if (window.showToast && window.showToast !== showToast) {
        window.showToast(message, type);
    } else {
        // 创建简单的toast提示
        const colors = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'warning': 'bg-yellow-500',
            'info': 'bg-blue-500'
        };
        
        const icons = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-times-circle',
            'warning': 'fas fa-exclamation-circle',
            'info': 'fas fa-info-circle'
        };
        
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 ${colors[type] || colors['info']} text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-3`;
        toast.innerHTML = `
            <i class="${icons[type] || icons['info']}"></i>
            <span>${message}</span>
        `;
        document.body.appendChild(toast);
        
        // 3秒后自动消失
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.3s';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
}
</script>