<div class="settings-panel p-6" id="performance-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">连接池配置</h3>
                        
                        <!-- 配置说明 -->
                        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 mb-4">
                            <p class="font-medium text-indigo-800 mb-2">
                                <i class="fas fa-layer-group mr-2"></i>连接池 vs 并发限制的区别：
                            </p>
                            <ul class="text-sm text-indigo-700 ml-4 space-y-1">
                                <li><strong>• 连接池（本页配置）</strong>：控制实际的物理连接数，是系统资源的硬性限制</li>
                                <li><strong>• 并发限制（资源限流页）</strong>：通过信号量控制同时执行的操作数，是逻辑层面的限制</li>
                                <li class="text-orange-600">⚠️ 重要：连接池大小必须 ≥ 并发限制数，建议设为并发限制的 2 倍</li>
                            </ul>
                        </div>
                        
                        <!-- 性能优化提示 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <p class="font-medium text-blue-800 mb-2">
                                <i class="fas fa-info-circle mr-2"></i>性能优化建议：
                            </p>
                            <ul class="text-sm text-blue-700 ml-4 space-y-1">
                                <li>• 200个站点并发：数据库连接池200，并发限制100</li>
                                <li>• 500个站点并发：数据库连接池400，并发限制200</li>
                                <li>• 修改后需重启服务生效</li>
                                <li>• 建议配合8核16GB以上硬件配置</li>
                            </ul>
                        </div>
                        
                        <div class="space-y-6">
                            <!-- 数据库连接池配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">
                                    <i class="fas fa-database mr-2"></i>数据库连接池配置
                                </h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最大连接数</label>
                                        <input type="number" id="db_max_open_conns" name="db_max_open_conns" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐200">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：25，推荐值：200-400</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最大空闲连接数</label>
                                        <input type="number" id="db_max_idle_conns" name="db_max_idle_conns"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐100">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：10，推荐值：100-200</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">连接最大生命周期（秒）</label>
                                        <input type="number" id="db_conn_max_lifetime" name="db_conn_max_lifetime"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="600">
                                        <p class="text-xs text-gray-500 mt-1">推荐值：600秒（10分钟）</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">从库最大连接数</label>
                                        <input type="number" id="db_slave_max_open_conns" name="db_slave_max_open_conns"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐100">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：15，推荐值：100</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- HTTP客户端配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">
                                    <i class="fas fa-globe mr-2"></i>HTTP客户端配置
                                </h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最大空闲连接数</label>
                                        <input type="number" id="http_max_idle_conns" name="http_max_idle_conns"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐300">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：50，推荐值：300-500</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">每主机最大空闲连接</label>
                                        <input type="number" id="http_max_idle_conns_per_host" name="http_max_idle_conns_per_host"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐50">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：5，推荐值：50-100</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">每主机最大连接数</label>
                                        <input type="number" id="http_max_conns_per_host" name="http_max_conns_per_host"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐100">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：10，推荐值：100-200</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">空闲连接超时（秒）</label>
                                        <input type="number" id="http_idle_conn_timeout" name="http_idle_conn_timeout"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="90">
                                        <p class="text-xs text-gray-500 mt-1">推荐值：90秒</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 迁移提示 -->
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <p class="text-sm text-yellow-800">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    <strong>重要提示：</strong>并发控制和超时设置已移至「资源限流」页面统一管理
                                </p>
                                <p class="text-xs text-yellow-700 mt-2">
                                    本页专注于连接池的详细配置，如最大连接数、空闲连接、生命周期等
                                </p>
                            </div>
                            
                            <!-- 缓存锁配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">
                                    <i class="fas fa-lock mr-2"></i>缓存锁配置
                                </h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">缓存锁超时（毫秒）</label>
                                        <input type="number" id="cache_lock_timeout" name="cache_lock_timeout"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐2000">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：100，推荐值：2000-3000</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">缓存锁重试间隔（毫秒）</label>
                                        <input type="number" id="cache_lock_retry_interval" name="cache_lock_retry_interval"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="100">
                                        <p class="text-xs text-gray-500 mt-1">推荐值：100毫秒</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 快速配置按钮 -->
                            <div class="flex justify-center space-x-4 pt-4">
                                <button onclick="applyPerformancePreset('default')" 
                                        class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                                    <i class="fas fa-undo mr-2"></i>恢复默认值
                                </button>
                                <button onclick="applyPerformancePreset('200sites')" 
                                        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-rocket mr-2"></i>应用200站点优化
                                </button>
                                <button onclick="applyPerformancePreset('500sites')" 
                                        class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-fire mr-2"></i>应用500站点优化
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计设置面板 -->