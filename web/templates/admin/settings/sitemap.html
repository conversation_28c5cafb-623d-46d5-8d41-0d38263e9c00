<!-- 站点地图设置部分 -->
<div class="space-y-6">
    <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">自动刷新间隔（分钟）</label>
        <input type="number" id="sitemap-refresh-interval" min="60" max="10080" value="720"
               class="w-32 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
        <p class="mt-1 text-sm text-gray-600">系统将根据设定间隔自动刷新所有站点地图。建议设置为720分钟（12小时），最小60分钟，最大10080分钟（7天）</p>
    </div>
    
    <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">上次刷新时间</label>
        <p class="text-sm text-gray-600" id="last-sitemap-refresh-time">尚未刷新</p>
    </div>
    
    <div class="flex justify-between items-center">
        <div class="flex gap-2">
            <button onclick="refreshAllSitemaps()" id="refresh-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-sitemap mr-2"></i>
                立即刷新所有站点地图
            </button>
            <button onclick="clearSitemapCache()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                <i class="fas fa-trash mr-2"></i>
                清空站点地图缓存
            </button>
        </div>
        <span class="text-sm text-gray-600" id="sitemap-refresh-status"></span>
    </div>
    
    <!-- 进度条区域 -->
    <div id="refresh-progress-container" class="hidden mt-4 bg-gray-100 rounded-lg p-4">
        <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-700">刷新进度</span>
            <span class="text-sm text-gray-600" id="progress-text">0/0</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div id="progress-bar" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
        </div>
        <div class="mt-2 text-xs text-gray-600" id="current-site">准备开始...</div>
    </div>
    
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 class="font-medium text-blue-800 mb-2">站点地图统计</h4>
        <div class="grid grid-cols-3 gap-4 text-sm">
            <div>
                <label class="text-gray-600">已启用站点地图</label>
                <p class="font-semibold text-blue-600" id="sitemap-enabled-count">0</p>
            </div>
            <div>
                <label class="text-gray-600">总URL数量</label>
                <p class="font-semibold text-blue-600" id="sitemap-total-urls">0</p>
            </div>
            <div>
                <label class="text-gray-600">待刷新数量</label>
                <p class="font-semibold text-orange-600" id="sitemap-pending-count">0</p>
            </div>
        </div>
    </div>
</div>

<script>
// 初始化站点地图设置页面
function initSitemapSettings() {
    // 加载站点地图设置（使用独立的API）
    fetch('/api/v1/sitemap/settings')
        .then(res => res.json())
        .then(data => {
            if (data.success && data.data) {
                document.getElementById('sitemap-refresh-interval').value = data.data.sitemap_refresh_interval || 720;
                
                // 显示上次刷新时间
                if (data.data.last_sitemap_refresh_time) {
                    const lastRefreshTime = new Date(data.data.last_sitemap_refresh_time);
                    document.getElementById('last-sitemap-refresh-time').textContent = 
                        lastRefreshTime.toLocaleString('zh-CN');
                } else {
                    document.getElementById('last-sitemap-refresh-time').textContent = '尚未刷新';
                }
            }
        })
        .catch(error => console.error('加载站点地图设置失败:', error));
    
    // 加载站点地图统计
    loadSitemapStats();
}

// 页面加载时自动执行初始化
// 对于动态加载的HTML片段，直接执行初始化
(function() {
    console.log('[Sitemap] 初始化sitemap设置页面');
    initSitemapSettings();
})();

// 刷新所有站点地图（模拟进度显示）
async function refreshAllSitemaps() {
    const statusEl = document.getElementById('sitemap-refresh-status');
    const progressContainer = document.getElementById('refresh-progress-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const currentSiteEl = document.getElementById('current-site');
    const refreshBtn = document.getElementById('refresh-btn');
    
    // 禁用刷新按钮
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }
    
    // 显示进度条
    if (progressContainer) {
        progressContainer.classList.remove('hidden');
    }
    
    try {
        // 先获取所有站点（设置大的page_size以获取所有站点）
        const sitesRes = await fetch('/api/v1/sites?page_size=1000');
        const sitesData = await sitesRes.json();
        
        if (!sitesData.success) {
            throw new Error('获取站点列表失败');
        }
        
        // 筛选启用了sitemap的站点（注意：sites数组在data.sites中）
        const allSites = sitesData.data?.sites || [];
        console.log('[Sitemap] 获取到的站点总数:', allSites.length);
        console.log('[Sitemap] 前3个站点的enable_sitemap状态:', 
            allSites.slice(0, 3).map(s => ({domain: s.domain, enable_sitemap: s.enable_sitemap})));
        
        const enabledSites = allSites.filter(site => site.enable_sitemap);
        const totalSites = enabledSites.length;
        console.log('[Sitemap] 启用sitemap的站点数:', totalSites);
        
        if (totalSites === 0) {
            if (statusEl) {
                statusEl.textContent = '没有启用站点地图的站点';
                statusEl.className = 'text-sm text-orange-600';
            }
            return;
        }
        
        // 显示初始进度
        if (progressText) {
            progressText.textContent = `0/${totalSites}`;
        }
        if (currentSiteEl) {
            currentSiteEl.textContent = '正在批量刷新所有站点...';
        }
        
        // 创建一个定时器来模拟进度（因为批量接口不返回实时进度）
        let currentIndex = 0;
        const estimatedTimePerSite = 300; // 估计每个站点300ms
        const progressInterval = setInterval(() => {
            if (currentIndex < totalSites) {
                currentIndex++;
                if (progressText) {
                    progressText.textContent = `${currentIndex}/${totalSites}`;
                }
                if (progressBar) {
                    const percentage = (currentIndex / totalSites) * 100;
                    progressBar.style.width = percentage + '%';
                }
                if (currentSiteEl && enabledSites[currentIndex - 1]) {
                    currentSiteEl.textContent = `正在处理: ${enabledSites[currentIndex - 1].domain}`;
                }
            } else {
                clearInterval(progressInterval);
            }
        }, estimatedTimePerSite);
        
        // 使用现有的批量刷新接口
        const response = await fetch('/api/v1/sitemap/refresh-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        // 清除进度定时器
        clearInterval(progressInterval);
        
        const result = await response.json();
        
        // 立即更新为完成状态
        if (progressBar) {
            progressBar.style.width = '100%';
        }
        if (progressText) {
            progressText.textContent = `${totalSites}/${totalSites}`;
        }
        
        if (result.success) {
            const successCount = result.data?.success_count || 0;
            const failedCount = result.data?.failed_count || 0;
            
            if (currentSiteEl) {
                currentSiteEl.textContent = '刷新完成！';
            }
            
            const message = `刷新完成：成功 ${successCount} 个，失败 ${failedCount} 个`;
            if (statusEl) {
                statusEl.textContent = message;
                statusEl.className = failedCount > 0 ? 'text-sm text-orange-600' : 'text-sm text-green-600';
            }
            
            if (window.showNotification) {
                window.showNotification(message, failedCount > 0 ? 'warning' : 'success');
            }
            
            // 如果有错误，显示错误详情
            if (result.data?.errors && result.data.errors.length > 0) {
                console.error('刷新失败的站点:', result.data.errors);
            }
            
            // 重新加载统计数据
            loadSitemapStats();
            // 更新刷新时间
            updateLastRefreshTime();
        } else {
            throw new Error(result.message || '刷新失败');
        }
        
    } catch (error) {
        console.error('刷新站点地图失败:', error);
        if (statusEl) {
            statusEl.textContent = '刷新失败: ' + error.message;
            statusEl.className = 'text-sm text-red-600';
        }
        if (window.showNotification) {
            window.showNotification('刷新失败: ' + error.message, 'error');
        }
    } finally {
        // 3秒后恢复界面状态
        setTimeout(() => {
            // 恢复刷新按钮
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
            
            // 隐藏进度条
            if (progressContainer) {
                progressContainer.classList.add('hidden');
            }
            
            // 重置进度条
            if (progressBar) {
                progressBar.style.width = '0%';
            }
            if (progressText) {
                progressText.textContent = '0/0';
            }
            if (currentSiteEl) {
                currentSiteEl.textContent = '准备开始...';
            }
        }, 3000);
    }
}

// 清空站点地图缓存
async function clearSitemapCache() {
    if (!confirm('确定要清空所有站点地图缓存吗？')) return;
    
    const statusEl = document.getElementById('sitemap-refresh-status');
    if (statusEl) {
        statusEl.textContent = '正在清空缓存...';
        statusEl.className = 'text-sm text-blue-600';
    }
    
    try {
        const response = await fetch('/api/v1/sitemap/clear-cache', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': getCSRFToken()
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            const message = `成功清空 ${result.data?.cleared_count || 0} 个站点地图缓存！`;
            if (statusEl) {
                statusEl.textContent = message;
                statusEl.className = 'text-sm text-green-600';
            }
            if (window.showNotification) {
                window.showNotification(message, 'success');
            }
            // 重新加载统计数据
            loadSitemapStats();
        } else {
            throw new Error(result.message || '清空失败');
        }
    } catch (error) {
        console.error('清空站点地图缓存失败:', error);
        if (statusEl) {
            statusEl.textContent = '清空失败: ' + error.message;
            statusEl.className = 'text-sm text-red-600';
        }
        if (window.showNotification) {
            window.showNotification('清空失败: ' + error.message, 'error');
        }
    }
}

// 加载站点地图统计
async function loadSitemapStats() {
    try {
        const response = await fetch('/api/v1/sitemap/stats');
        const result = await response.json();
        
        if (result.success && result.data) {
            const stats = result.data;
            
            // 更新统计数据
            document.getElementById('sitemap-enabled-count').textContent = stats.enabled_count || 0;
            document.getElementById('sitemap-total-urls').textContent = stats.total_urls || 0;
            document.getElementById('sitemap-pending-count').textContent = stats.pending_count || 0;
        }
    } catch (error) {
        console.error('加载站点地图统计失败:', error);
    }
}

// 更新上次刷新时间
async function updateLastRefreshTime() {
    try {
        const response = await fetch('/api/v1/sitemap/settings');
        const result = await response.json();
        
        if (result.success && result.data) {
            const timeEl = document.getElementById('last-sitemap-refresh-time');
            if (timeEl) {
                if (result.data.last_sitemap_refresh_time) {
                    const lastRefreshTime = new Date(result.data.last_sitemap_refresh_time);
                    timeEl.textContent = lastRefreshTime.toLocaleString('zh-CN');
                } else {
                    timeEl.textContent = '尚未刷新';
                }
            }
        }
    } catch (error) {
        console.error('更新刷新时间失败:', error);
    }
}

// 获取CSRF Token
function getCSRFToken() {
    // 先尝试从meta标签获取
    const metaToken = document.querySelector('meta[name="csrf-token"]')?.content;
    if (metaToken) return metaToken;
    
    // 再尝试从localStorage获取  
    const localToken = localStorage.getItem('csrf_token');
    if (localToken) return localToken;
    
    // 如果在iframe中，且父页面不是自己，尝试使用父页面的函数
    if (window.parent && window.parent !== window && typeof window.parent.getCSRFToken === 'function') {
        try {
            return window.parent.getCSRFToken();
        } catch (e) {
            // 跨域情况下会抛出异常，忽略
        }
    }
    
    // 最后从cookie中获取
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrf_token') {
            return decodeURIComponent(value);
        }
    }
    return '';
}

// 保存sitemap设置（独立保存）
window.saveSitemapSettings = async function() {
    const settings = {
        sitemap_refresh_interval: parseInt(document.getElementById('sitemap-refresh-interval')?.value) || 720
    };
    
    console.log('保存站点地图设置:', settings);
    
    try {
        const response = await fetch('/api/v1/sitemap/settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': getCSRFToken()
            },
            body: JSON.stringify(settings)
        });
        
        const result = await response.json();
        
        if (result.success) {
            if (window.showNotification) {
                window.showNotification('站点地图设置保存成功', 'success');
            }
            return true;
        } else {
            throw new Error(result.message || '保存失败');
        }
    } catch (error) {
        console.error('保存站点地图设置失败:', error);
        if (window.showNotification) {
            window.showNotification('保存失败: ' + error.message, 'error');
        }
        return false;
    }
};

// 监听设置变化
if (document.getElementById('sitemap-refresh-interval')) {
    document.getElementById('sitemap-refresh-interval').addEventListener('change', function() {
        console.log('Sitemap刷新间隔变化:', this.value);
    });
}
</script>