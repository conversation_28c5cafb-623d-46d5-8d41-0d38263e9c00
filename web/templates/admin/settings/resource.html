<!-- 资源限流配置 -->
<div class="space-y-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">资源限流与超时配置</h3>
    
    <!-- 一键优化按钮 -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
            <div>
                <h4 class="font-medium text-blue-800 mb-1">
                    <i class="fas fa-magic mr-2"></i>智能优化
                </h4>
                <p class="text-sm text-blue-600">根据当前服务器配置自动优化参数</p>
            </div>
            <button onclick="autoOptimizeSettings()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-bolt mr-2"></i>一键优化
            </button>
        </div>
    </div>

    <!-- 并发限制配置 -->
    <div class="border border-gray-200 rounded-lg p-4">
        <h4 class="font-medium text-gray-800 mb-4 flex items-center">
            <i class="fas fa-users mr-2 text-green-600"></i>
            并发限制配置
        </h4>
        <p class="text-sm text-gray-600 mb-4">控制不同类型操作的最大并发数，防止资源耗尽</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    数据库并发连接数
                    <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×10)</span>
                </label>
                <input type="number" id="max-database-conn" min="10" max="500" step="10"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="80">
                <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-database-conn">80</span></p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Redis并发连接数
                    <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×20)</span>
                </label>
                <input type="number" id="max-redis-conn" min="20" max="1000" step="20"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="160">
                <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-redis-conn">160</span></p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    HTTP并发请求数
                    <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×5)</span>
                </label>
                <input type="number" id="max-http-requests" min="5" max="200" step="5"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="40">
                <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-http-requests">40</span></p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    文件并发操作数
                    <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×3)</span>
                </label>
                <input type="number" id="max-file-ops" min="3" max="100" step="3"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="24">
                <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-file-ops">24</span></p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    爬虫并发任务数
                    <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×2)</span>
                </label>
                <input type="number" id="max-crawler-tasks" min="2" max="100" step="2"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="16">
                <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-crawler-tasks">16</span></p>
            </div>
        </div>
    </div>

    <!-- 统一超时配置 -->
    <div class="border border-gray-200 rounded-lg p-4">
        <h4 class="font-medium text-gray-800 mb-4 flex items-center">
            <i class="fas fa-clock mr-2 text-orange-600"></i>
            统一超时配置
        </h4>
        <p class="text-sm text-gray-600 mb-4">设置各类操作的超时时间，避免请求堆积（单位：毫秒）</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    路由处理总超时
                    <span class="text-xs text-gray-500 ml-1">(推荐: 800-1500ms)</span>
                </label>
                <input type="number" id="route-timeout" min="500" max="5000" step="100"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="1200">
                <p class="mt-1 text-xs text-gray-600">整个请求的最大处理时间</p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    数据库查询超时
                    <span class="text-xs text-gray-500 ml-1">(推荐: 500-1000ms)</span>
                </label>
                <input type="number" id="database-query-timeout" min="200" max="3000" step="100"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="800">
                <p class="mt-1 text-xs text-gray-600">单个数据库查询超时</p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    Redis操作超时
                    <span class="text-xs text-gray-500 ml-1">(推荐: 100-300ms)</span>
                </label>
                <input type="number" id="redis-op-timeout" min="50" max="1000" step="50"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="200">
                <p class="mt-1 text-xs text-gray-600">Redis读写操作超时</p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    HTTP请求超时
                    <span class="text-xs text-gray-500 ml-1">(推荐: 300-800ms)</span>
                </label>
                <input type="number" id="http-request-timeout" min="200" max="3000" step="100"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="500">
                <p class="mt-1 text-xs text-gray-600">出站HTTP请求超时</p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    文件操作超时
                    <span class="text-xs text-gray-500 ml-1">(推荐: 500-2000ms)</span>
                </label>
                <input type="number" id="file-op-timeout" min="200" max="5000" step="100"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="1000">
                <p class="mt-1 text-xs text-gray-600">文件读写操作超时</p>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                    爬虫任务超时
                    <span class="text-xs text-gray-500 ml-1">(推荐: 5000-15000ms)</span>
                </label>
                <input type="number" id="crawler-task-timeout" min="3000" max="30000" step="1000"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                       placeholder="10000">
                <p class="mt-1 text-xs text-gray-600">爬虫抓取页面超时</p>
            </div>
        </div>
    </div>

    <!-- 工作池配置 -->
    <div class="border border-gray-200 rounded-lg p-4">
        <h4 class="font-medium text-gray-800 mb-4 flex items-center">
            <i class="fas fa-cogs mr-2 text-purple-600"></i>
            工作池配置
        </h4>
        <p class="text-sm text-gray-600 mb-4">配置任务处理工作池，建议根据系统并发数自动调整</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 工作池模式选择 -->
            <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">工作池模式</label>
                <div class="flex space-x-4">
                    <label class="flex items-center">
                        <input type="radio" name="worker-pool-mode" value="auto" checked 
                               class="mr-2 text-blue-600 focus:ring-blue-500"
                               onchange="updateWorkerPoolMode('auto')">
                        <span class="text-sm">
                            <strong>自动模式</strong>
                            <span class="text-gray-500 ml-1">(推荐)</span>
                        </span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="worker-pool-mode" value="fixed" 
                               class="mr-2 text-blue-600 focus:ring-blue-500"
                               onchange="updateWorkerPoolMode('fixed')">
                        <span class="text-sm">
                            <strong>固定模式</strong>
                            <span class="text-gray-500 ml-1">(手动设置)</span>
                        </span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="worker-pool-mode" value="dynamic" 
                               class="mr-2 text-blue-600 focus:ring-blue-500"
                               onchange="updateWorkerPoolMode('dynamic')">
                        <span class="text-sm">
                            <strong>动态模式</strong>
                            <span class="text-gray-500 ml-1">(自动伸缩)</span>
                        </span>
                    </label>
                </div>
            </div>
            
            <!-- 自动模式配置 -->
            <div id="auto-mode-config" class="col-span-2">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <p class="text-sm text-blue-800 mb-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        <strong>自动计算规则：</strong>
                    </p>
                    <p class="text-sm text-blue-700">
                        工作池大小 = (数据库并发 + Redis并发 + HTTP并发 + 文件并发 + 爬虫并发) × 缩放比例
                    </p>
                    <div class="mt-3">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            缩放比例
                            <span class="text-xs text-gray-500 ml-1">(默认1.2，范围1.0-2.0)</span>
                        </label>
                        <input type="number" id="worker-pool-scale-ratio" min="1.0" max="2.0" step="0.1"
                               class="w-full md:w-1/3 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="1.2" value="1.2" onchange="calculateWorkerPoolSize()">
                    </div>
                    <div class="mt-3 p-3 bg-white rounded border border-blue-200">
                        <p class="text-sm font-medium text-gray-800">预计工作池大小：
                            <span id="calculated-pool-size" class="text-lg text-blue-600 ml-2">计算中...</span>
                        </p>
                        <p class="text-xs text-gray-600 mt-1">
                            基于当前并发配置：<span id="total-concurrency">0</span> × <span id="scale-ratio-display">1.2</span> = <span id="final-pool-size">0</span>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 固定模式配置 -->
            <div id="fixed-mode-config" class="col-span-2 hidden">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        固定工作池大小
                        <span class="text-xs text-gray-500 ml-1">(范围: 100-5000)</span>
                    </label>
                    <input type="number" id="worker-pool-size" min="100" max="5000" step="50"
                           class="w-full md:w-1/2 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="350">
                    <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-pool-size">350</span></p>
                </div>
            </div>
            
            <!-- 动态模式配置 -->
            <div id="dynamic-mode-config" class="col-span-2 hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            最小工作池大小
                            <span class="text-xs text-gray-500 ml-1">(默认: 100)</span>
                        </label>
                        <input type="number" id="worker-pool-min-size" min="50" max="1000" step="50"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            最大工作池大小
                            <span class="text-xs text-gray-500 ml-1">(默认: 2000)</span>
                        </label>
                        <input type="number" id="worker-pool-max-size" min="200" max="5000" step="100"
                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="2000">
                    </div>
                </div>
                <div class="mt-3 p-3 bg-amber-50 rounded border border-amber-200">
                    <p class="text-xs text-amber-800">
                        <i class="fas fa-info-circle mr-1"></i>
                        动态模式会根据负载自动在最小值和最大值之间调整工作池大小
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示信息 -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 class="font-medium text-yellow-800 mb-2">
            <i class="fas fa-info-circle mr-2"></i>配置建议
        </h4>
        <ul class="space-y-1 text-sm text-yellow-700">
            <li>• <strong>高配服务器（16GB+内存）：</strong>可适当增加并发数，缩短超时时间</li>
            <li>• <strong>中配服务器（8-16GB内存）：</strong>使用默认配置即可</li>
            <li>• <strong>低配服务器（8GB以下）：</strong>降低并发数，适当延长超时时间</li>
            <li>• <strong>SSD存储：</strong>文件操作超时可设置较短（500-1000ms）</li>
            <li>• <strong>HDD存储：</strong>文件操作超时建议设置较长（1000-3000ms）</li>
            <li>• 修改配置后需要重启服务才能生效</li>
        </ul>
    </div>
</div>

<script>
// 一键优化功能
async function autoOptimizeSettings() {
    try {
        // 获取系统信息
        const response = await fetch('/api/v1/system/info', {
            headers: {
                'X-CSRF-Token': getCSRFToken()
            }
        });
        
        if (!response.ok) throw new Error('获取系统信息失败');
        
        const data = await response.json();
        const systemInfo = data.data;
        
        // 根据系统信息计算优化值
        const cpuCores = systemInfo.cpu_cores || 8;
        const memoryGB = Math.floor((systemInfo.total_memory || 8589934592) / 1024 / 1024 / 1024);
        const isSSD = systemInfo.storage_type === 'SSD' || true; // 默认假设SSD
        
        // 计算并发限制
        let multiplier = 1;
        if (memoryGB >= 16) {
            multiplier = 1.5; // 高配
        } else if (memoryGB >= 8) {
            multiplier = 1.0; // 中配
        } else {
            multiplier = 0.5; // 低配
        }
        
        // 设置并发数
        document.getElementById('max-database-conn').value = Math.floor(cpuCores * 10 * multiplier);
        document.getElementById('max-redis-conn').value = Math.floor(cpuCores * 20 * multiplier);
        document.getElementById('max-http-requests').value = Math.floor(cpuCores * 5 * multiplier);
        document.getElementById('max-file-ops').value = Math.floor(cpuCores * 3 * multiplier);
        document.getElementById('max-crawler-tasks').value = Math.floor(cpuCores * 2 * multiplier);
        
        // 计算工作池大小
        calculateWorkerPoolSize();
        
        // 设置超时时间
        if (isSSD) {
            // SSD优化配置
            document.getElementById('route-timeout').value = 1000;
            document.getElementById('database-query-timeout').value = 600;
            document.getElementById('redis-op-timeout').value = 150;
            document.getElementById('http-request-timeout').value = 400;
            document.getElementById('file-op-timeout').value = 500;
            document.getElementById('crawler-task-timeout').value = 8000;
        } else {
            // HDD配置
            document.getElementById('route-timeout').value = 1500;
            document.getElementById('database-query-timeout').value = 1000;
            document.getElementById('redis-op-timeout').value = 300;
            document.getElementById('http-request-timeout').value = 800;
            document.getElementById('file-op-timeout').value = 2000;
            document.getElementById('crawler-task-timeout').value = 15000;
        }
        
        // 显示优化信息
        showNotification(`已根据系统配置自动优化参数：${cpuCores}核CPU, ${memoryGB}GB内存, ${isSSD ? 'SSD' : 'HDD'}存储`, 'success');
        
    } catch (error) {
        console.error('自动优化失败:', error);
        showNotification('自动优化失败：' + error.message, 'error');
    }
}

// 更新工作池模式
function updateWorkerPoolMode(mode) {
    // 隐藏所有配置区域
    document.getElementById('auto-mode-config').classList.add('hidden');
    document.getElementById('fixed-mode-config').classList.add('hidden');
    document.getElementById('dynamic-mode-config').classList.add('hidden');
    
    // 显示对应的配置区域
    if (mode === 'auto') {
        document.getElementById('auto-mode-config').classList.remove('hidden');
        calculateWorkerPoolSize();
    } else if (mode === 'fixed') {
        document.getElementById('fixed-mode-config').classList.remove('hidden');
    } else if (mode === 'dynamic') {
        document.getElementById('dynamic-mode-config').classList.remove('hidden');
    }
}

// 计算工作池大小
function calculateWorkerPoolSize() {
    // 获取各个并发数
    const dbConn = parseInt(document.getElementById('max-database-conn').value) || 80;
    const redisConn = parseInt(document.getElementById('max-redis-conn').value) || 160;
    const httpReq = parseInt(document.getElementById('max-http-requests').value) || 40;
    const fileOps = parseInt(document.getElementById('max-file-ops').value) || 24;
    const crawlerTasks = parseInt(document.getElementById('max-crawler-tasks').value) || 16;
    
    // 获取缩放比例
    const scaleRatio = parseFloat(document.getElementById('worker-pool-scale-ratio').value) || 1.2;
    
    // 计算总并发数
    const totalConcurrency = dbConn + redisConn + httpReq + fileOps + crawlerTasks;
    
    // 计算工作池大小
    let poolSize = Math.floor(totalConcurrency * scaleRatio);
    
    // 限制范围
    if (poolSize < 100) poolSize = 100;
    if (poolSize > 5000) poolSize = 5000;
    
    // 更新显示
    document.getElementById('total-concurrency').textContent = totalConcurrency;
    document.getElementById('scale-ratio-display').textContent = scaleRatio;
    document.getElementById('final-pool-size').textContent = poolSize;
    document.getElementById('calculated-pool-size').textContent = poolSize;
    
    return poolSize;
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 监听并发数变化
    const concurrencyInputs = [
        'max-database-conn',
        'max-redis-conn', 
        'max-http-requests',
        'max-file-ops',
        'max-crawler-tasks'
    ];
    
    concurrencyInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('change', calculateWorkerPoolSize);
        }
    });
    
    // 初始计算
    calculateWorkerPoolSize();
});

// 获取CSRF令牌
function getCSRFToken() {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrf_token') {
            return value;
        }
    }
    return '';
}

// 显示通知
function showNotification(message, type = 'info') {
    // 使用父页面的showToast函数（如果存在）
    if (window.parent && window.parent.showToast) {
        window.parent.showToast(message, type === 'error' ? 'error' : 'success');
    } else if (window.showToast) {
        window.showToast(message, type === 'error' ? 'error' : 'success');
    } else {
        // 简单的alert作为后备
        alert(message);
    }
}
</script>