<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        body {
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        
        .login-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 24px;
            margin: 0 0 10px 0;
        }
        
        .login-header p {
            color: #666;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .error-message {
            color: #f44336;
            font-size: 14px;
            margin-top: 10px;
            display: none;
        }
        
        .submit-btn {
            width: 100%;
            padding: 12px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .submit-btn:hover {
            background: #45a049;
        }
        
        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .footer-text {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
        
        .captcha-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .captcha-input {
            flex: 1;
        }
        
        .captcha-image {
            min-width: 120px;
            max-width: 180px;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background: #f5f5f5;
            display: inline-block;
        }
        
        .captcha-image img {
            width: auto;
            height: 100%;
            display: block;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>站群管理系统</h1>
          
        </div>
        
        <form id="login-form" onsubmit="handleLogin(event)">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required autofocus>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group" id="captcha-container" style="display: none;">
                <label for="captcha">验证码</label>
                <div class="captcha-group">
                    <input type="text" id="captcha" name="captcha" class="captcha-input" placeholder="请输入验证码">
                    <div class="captcha-image" onclick="refreshCaptcha()" title="点击刷新">
                        <img id="captcha-img" src="" alt="验证码">
                    </div>
                </div>
            </div>
            
            <div id="error-message" class="error-message"></div>
            
            <button type="submit" class="submit-btn" id="submit-btn">登录</button>
        </form>
        
    </div>
    
    <script>
        let captchaId = '';
        let captchaEnabled = false;
        
        // 检查验证码配置
        async function checkCaptchaConfig() {
            try {
                const response = await fetch('/api/v1/auth/captcha-config');
                const data = await response.json();
                
                if (data.success && data.data) {
                    captchaEnabled = data.data.enabled;
                    if (captchaEnabled) {
                        document.getElementById('captcha-container').style.display = 'block';
                        document.getElementById('captcha').required = true;
                        await refreshCaptcha();
                    }
                }
            } catch (error) {
                console.error('获取验证码配置失败:', error);
            }
        }
        
        // 获取验证码
        async function refreshCaptcha() {
            if (!captchaEnabled) return;
            
            try {
                const response = await fetch('/api/v1/auth/captcha');
                const data = await response.json();
                
                if (data.success) {
                    captchaId = data.data.captcha_id;
                    document.getElementById('captcha-img').src = data.data.image;
                }
            } catch (error) {
                console.error('获取验证码失败:', error);
            }
        }
        
        async function handleLogin(event) {
            event.preventDefault();
            
            const submitBtn = document.getElementById('submit-btn');
            const errorDiv = document.getElementById('error-message');
            
            // 禁用按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '登录中...';
            errorDiv.style.display = 'none';
            
            const formData = {
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };
            
            // 只在验证码启用时添加验证码字段
            if (captchaEnabled) {
                formData.captcha_id = captchaId;
                formData.captcha_code = document.getElementById('captcha').value;
            }
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 登录成功，跳转到管理后台
                    window.location.href = '/admin/';
                } else {
                    // 显示错误信息
                    errorDiv.textContent = data.error || '登录失败';
                    errorDiv.style.display = 'block';
                    // 刷新验证码
                    if (captchaEnabled) {
                        refreshCaptcha();
                        document.getElementById('captcha').value = '';
                    }
                }
            } catch (error) {
                errorDiv.textContent = '网络错误，请稍后重试';
                errorDiv.style.display = 'block';
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.textContent = '登录';
            }
        }
        
        // 页面加载时检查验证码配置
        window.onload = function() {
            checkCaptchaConfig();
        };
        
        // 如果已登录，跳转到管理后台
        fetch('/api/v1/auth/current')
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    window.location.href = '/admin/';
                }
            });
    </script>
</body>
</html>