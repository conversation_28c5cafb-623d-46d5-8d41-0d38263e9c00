<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量替换 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* 行号样式 */
        .line-numbers {
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            color: #6c757d;
            font-family: 'Courier New', monospace;
            line-height: 24px;
            overflow-y: hidden;
            padding-top: 8px;
            padding-bottom: 8px;
        }
        
        /* 文本域样式 */
        .code-textarea {
            font-family: 'Courier New', monospace;
            line-height: 24px;
            tab-size: 4;
            padding-top: 8px;
            padding-bottom: 8px;
        }
        
        /* 同步滚动 */
        .sync-scroll {
            overflow-y: auto;
            overflow-x: hidden;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
            <nav class="mt-8">
                <!-- 菜单将由 menu-groups.js 动态生成 -->
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部导航 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-6 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">批量替换</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600">管理员</span>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-8">
                    <!-- 页面说明 -->
                    <div class="mb-6">
                        <p class="text-gray-600">通过域名查找站点，批量替换对应的目标站地址</p>
                        <p class="text-sm text-gray-500 mt-1">左侧输入域名列表，右侧输入对应的新目标站地址，一行对应一行</p>
                    </div>
                    
                    <!-- 替换输入区 -->
                    <div class="bg-white rounded-lg shadow p-6 mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-lg font-semibold">替换规则设置</h2>
                            <div class="flex gap-2">
                                <button onclick="clearAll()" class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                                    <i class="fas fa-trash mr-1"></i>清空
                                </button>
                                <button onclick="executeReplace()" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    <i class="fas fa-exchange-alt mr-2"></i>执行替换
                                </button>
                            </div>
                        </div>
                        
                        <!-- 左右对应输入框 -->
                        <div class="grid grid-cols-2 gap-4">
                            <!-- 左侧：域名列表 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    域名列表（每行一个）
                                </label>
                                <div class="relative border border-gray-300 rounded-md overflow-hidden">
                                    <div class="flex">
                                        <div id="leftNumbers" class="line-numbers w-12 p-2 text-xs text-right select-none"></div>
                                        <textarea 
                                            id="leftInput" 
                                            class="code-textarea flex-1 p-2 border-0 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                            rows="15"
                                            placeholder="example.com&#10;test.com&#10;demo.com&#10;site1.com&#10;..."
                                            oninput="updateLineNumbers(); checkMatching()"
                                            onscroll="syncScroll('left')"></textarea>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">
                                    <span id="leftCount">0</span> 行
                                </p>
                            </div>
                            
                            <!-- 右侧：新目标站地址 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    新目标站地址（对应左边每个域名）
                                </label>
                                <div class="relative border border-gray-300 rounded-md overflow-hidden">
                                    <div class="flex">
                                        <div id="rightNumbers" class="line-numbers w-12 p-2 text-xs text-right select-none"></div>
                                        <textarea 
                                            id="rightInput" 
                                            class="code-textarea flex-1 p-2 border-0 focus:outline-none focus:ring-2 focus:ring-green-500 resize-none"
                                            rows="15"
                                            placeholder="https://new-example.com&#10;https://new-test.com&#10;https://new-demo.com&#10;https://new-site1.com&#10;..."
                                            oninput="processRightInput(); updateLineNumbers(); checkMatching()"
                                            onpaste="handlePaste(event)"
                                            onscroll="syncScroll('right')"></textarea>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">
                                    <span id="rightCount">0</span> 行
                                </p>
                            </div>
                        </div>
                        
                        <!-- 匹配提示 -->
                        <div id="matchingAlert" class="mt-4 p-3 rounded-md hidden">
                            <p class="text-sm flex items-center">
                                <i class="fas fa-info-circle mr-2"></i>
                                <span id="matchingMessage"></span>
                            </p>
                        </div>
                    </div>
                    
                    <!-- 替换结果 -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                            <h2 class="text-lg font-semibold">替换结果</h2>
                            <div class="flex gap-2">
                                <button id="copyFailedBtn" onclick="copyFailedDomains()" class="px-4 py-2 text-sm bg-orange-600 text-white rounded-md hover:bg-orange-700 hidden">
                                    <i class="fas fa-copy mr-2"></i>复制失败的域名
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-3 py-3">
                                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行号</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">域名</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原目标站</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">新目标站</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">说明</th>
                                    </tr>
                                </thead>
                                <tbody id="resultTableBody" class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            暂无替换记录
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <script>
        // 处理URL，只保留协议和域名部分
        function cleanUrl(url) {
            if (!url || typeof url !== 'string') return url;
            
            // 去除首尾空格
            url = url.trim();
            
            // 如果URL为空，直接返回
            if (!url) return url;
            
            try {
                // 如果没有协议，先添加一个临时协议
                let tempUrl = url;
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    tempUrl = 'https://' + url;
                }
                
                // 使用URL对象解析
                const urlObj = new URL(tempUrl);
                
                // 只保留协议和域名（包括端口，如果有的话）
                let cleanedUrl = urlObj.protocol + '//' + urlObj.hostname;
                if (urlObj.port) {
                    cleanedUrl += ':' + urlObj.port;
                }
                
                // 如果原始URL没有协议，返回时也不带协议
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    cleanedUrl = cleanedUrl.replace(/^https?:\/\//, '');
                }
                
                return cleanedUrl;
            } catch (e) {
                // 如果URL解析失败，尝试手动处理
                // 移除路径部分（从第一个/开始）
                if (url.includes('://')) {
                    const parts = url.split('://');
                    if (parts.length === 2) {
                        const domainPart = parts[1].split('/')[0];
                        return parts[0] + '://' + domainPart;
                    }
                } else {
                    // 没有协议的情况
                    return url.split('/')[0];
                }
                return url;
            }
        }
        
        // 处理右侧输入框的输入
        function processRightInput() {
            const rightInput = document.getElementById('rightInput');
            const lines = rightInput.value.split('\n');
            const cleanedLines = lines.map(line => {
                // 如果行是空的或只有空格，保持原样
                if (!line.trim()) return line;
                // 否则清理URL
                return cleanUrl(line);
            });
            
            // 只有当内容真的改变时才更新
            const newValue = cleanedLines.join('\n');
            if (newValue !== rightInput.value) {
                // 保存光标位置
                const selectionStart = rightInput.selectionStart;
                const selectionEnd = rightInput.selectionEnd;
                
                rightInput.value = newValue;
                
                // 尝试恢复光标位置
                try {
                    rightInput.setSelectionRange(selectionStart, selectionEnd);
                } catch (e) {
                    // 如果恢复失败，将光标移到末尾
                    rightInput.setSelectionRange(newValue.length, newValue.length);
                }
            }
        }
        
        // 处理粘贴事件
        function handlePaste(event) {
            event.preventDefault();
            
            const pasteData = (event.clipboardData || window.clipboardData).getData('text');
            const rightInput = document.getElementById('rightInput');
            
            // 处理粘贴的数据
            const lines = pasteData.split('\n');
            const cleanedLines = lines.map(line => {
                if (!line.trim()) return line;
                return cleanUrl(line);
            });
            
            // 获取当前光标位置
            const start = rightInput.selectionStart;
            const end = rightInput.selectionEnd;
            const currentValue = rightInput.value;
            
            // 插入处理后的文本
            const newValue = currentValue.substring(0, start) + cleanedLines.join('\n') + currentValue.substring(end);
            rightInput.value = newValue;
            
            // 设置光标位置到粘贴内容的末尾
            const newCursorPos = start + cleanedLines.join('\n').length;
            rightInput.setSelectionRange(newCursorPos, newCursorPos);
            
            // 触发更新
            updateLineNumbers();
            checkMatching();
        }
        
        // 更新行号
        function updateLineNumbers() {
            const leftTextarea = document.getElementById('leftInput');
            const rightTextarea = document.getElementById('rightInput');
            const leftNumbers = document.getElementById('leftNumbers');
            const rightNumbers = document.getElementById('rightNumbers');
            
            // 获取实际的内容行数
            const leftValue = leftTextarea.value;
            const rightValue = rightTextarea.value;
            const leftLines = leftValue === '' ? 0 : leftValue.split('\n').length;
            const rightLines = rightValue === '' ? 0 : rightValue.split('\n').length;
            
            // 显示的最小行数为15行
            const minDisplayLines = 15;
            
            // 更新左侧行号
            let leftNumbersHtml = '';
            const leftDisplayCount = Math.max(leftLines, minDisplayLines);
            for (let i = 1; i <= leftDisplayCount; i++) {
                leftNumbersHtml += `<div style="height: 24px; line-height: 24px;">${i}</div>`;
            }
            leftNumbers.innerHTML = leftNumbersHtml;
            
            // 更新右侧行号
            let rightNumbersHtml = '';
            const rightDisplayCount = Math.max(rightLines, minDisplayLines);
            for (let i = 1; i <= rightDisplayCount; i++) {
                rightNumbersHtml += `<div style="height: 24px; line-height: 24px;">${i}</div>`;
            }
            rightNumbers.innerHTML = rightNumbersHtml;
            
            // 更新计数
            document.getElementById('leftCount').textContent = leftLines;
            document.getElementById('rightCount').textContent = rightLines;
            
            // 调整高度 - 根据内容动态调整
            const maxLines = Math.max(leftLines, rightLines, minDisplayLines);
            const lineHeight = 24; // 每行24px
            const newHeight = maxLines * lineHeight;
            
            leftTextarea.style.height = newHeight + 'px';
            rightTextarea.style.height = newHeight + 'px';
            leftNumbers.style.height = newHeight + 'px';
            rightNumbers.style.height = newHeight + 'px';
        }
        
        // 同步滚动
        function syncScroll(source) {
            const leftTextarea = document.getElementById('leftInput');
            const rightTextarea = document.getElementById('rightInput');
            const leftNumbers = document.getElementById('leftNumbers');
            const rightNumbers = document.getElementById('rightNumbers');
            
            if (source === 'left') {
                rightTextarea.scrollTop = leftTextarea.scrollTop;
                leftNumbers.scrollTop = leftTextarea.scrollTop;
                rightNumbers.scrollTop = leftTextarea.scrollTop;
            } else {
                leftTextarea.scrollTop = rightTextarea.scrollTop;
                leftNumbers.scrollTop = rightTextarea.scrollTop;
                rightNumbers.scrollTop = rightTextarea.scrollTop;
            }
        }
        
        // 检查匹配
        function checkMatching() {
            const leftLines = document.getElementById('leftInput').value.split('\n').filter(line => line.trim());
            const rightLines = document.getElementById('rightInput').value.split('\n').filter(line => line.trim());
            const alert = document.getElementById('matchingAlert');
            const message = document.getElementById('matchingMessage');
            
            if (leftLines.length === 0 && rightLines.length === 0) {
                alert.classList.add('hidden');
                return;
            }
            
            if (leftLines.length !== rightLines.length) {
                alert.classList.remove('hidden');
                alert.className = 'mt-4 p-3 rounded-md bg-yellow-50 border border-yellow-200';
                message.className = 'text-yellow-700';
                message.textContent = `左右两边行数不匹配：左边 ${leftLines.length} 行，右边 ${rightLines.length} 行`;
            } else {
                alert.classList.remove('hidden');
                alert.className = 'mt-4 p-3 rounded-md bg-green-50 border border-green-200';
                message.className = 'text-green-700';
                message.textContent = `匹配成功：共 ${leftLines.length} 条替换规则`;
            }
        }
        
        // 清空所有
        function clearAll() {
            if (confirm('确定要清空所有输入吗？')) {
                document.getElementById('leftInput').value = '';
                document.getElementById('rightInput').value = '';
                updateLineNumbers();
                checkMatching();
            }
        }
        
        // 执行替换
        async function executeReplace() {
            const leftLines = document.getElementById('leftInput').value.split('\n').filter(line => line.trim());
            const rightLines = document.getElementById('rightInput').value.split('\n').filter(line => line.trim());
            
            if (leftLines.length === 0) {
                showToast('请输入域名列表', 'error');
                return;
            }
            
            if (leftLines.length !== rightLines.length) {
                showToast('左右两边行数必须相同', 'error');
                return;
            }
            
            if (!confirm(`确定要为 ${leftLines.length} 个域名更新目标站地址吗？`)) {
                return;
            }
            
            // 显示处理中状态
            showToast('正在检测协议并处理...', 'info');
            
            // 构建替换规则：通过域名查找站点，替换其目标站地址
            const replacements = [];
            const protocolCheckResults = [];
            
            for (let i = 0; i < leftLines.length; i++) {
                const domain = leftLines[i].trim();
                let newTargetUrl = rightLines[i].trim();
                
                // 验证域名格式（不应该包含http://或https://）
                if (domain.startsWith('http://') || domain.startsWith('https://')) {
                    showToast(`第 ${i + 1} 行：域名不应包含协议前缀`, 'error');
                    return;
                }
                
                // 如果目标站地址没有协议，则自动检测
                if (!newTargetUrl.startsWith('http://') && !newTargetUrl.startsWith('https://')) {
                    // 尝试检测协议
                    const detectedProtocol = await detectProtocol(newTargetUrl);
                    if (detectedProtocol) {
                        newTargetUrl = detectedProtocol + newTargetUrl;
                        protocolCheckResults.push({
                            domain: domain,
                            url: newTargetUrl,
                            protocol: detectedProtocol.replace('://', ''),
                            status: 'success'
                        });
                    } else {
                        protocolCheckResults.push({
                            domain: domain,
                            url: newTargetUrl,
                            protocol: 'none',
                            status: 'failed',
                            message: '无法检测到可用协议'
                        });
                        continue; // 跳过这个域名
                    }
                }
                
                // 通过域名查找并替换目标站地址
                replacements.push({
                    domain: domain,
                    new_target_url: newTargetUrl
                });
            }
            
            try {
                const response = await fetch('/api/v1/sites/batch-replace', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': window.csrfToken || ''
                    },
                    body: JSON.stringify({ replacements })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showToast('替换成功', 'success');
                    displayResults(data.results || []);
                } else {
                    showToast(data.error || '替换失败', 'error');
                }
            } catch (error) {
                showToast('网络错误：' + error.message, 'error');
            }
        }
        
        // 显示结果
        function displayResults(results) {
            const tbody = document.getElementById('resultTableBody');
            
            if (results.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                            暂无替换记录
                        </td>
                    </tr>
                `;
                document.getElementById('copyFailedBtn').classList.add('hidden');
                return;
            }
            
            // 检查是否有失败的记录
            const hasFailures = results.some(r => r.status === 'failed');
            if (hasFailures) {
                document.getElementById('copyFailedBtn').classList.remove('hidden');
            } else {
                document.getElementById('copyFailedBtn').classList.add('hidden');
            }
            
            tbody.innerHTML = results.map((result, index) => `
                <tr data-domain="${result.domain || ''}" data-status="${result.status}">
                    <td class="px-3 py-4">
                        <input type="checkbox" class="row-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                               data-domain="${result.domain || ''}" ${result.status === 'failed' ? 'checked' : ''}>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${index + 1}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">${result.domain || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${result.old_value || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${result.new_value || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            result.status === 'success' ? 'bg-green-100 text-green-800' : 
                            result.status === 'failed' ? 'bg-red-100 text-red-800' : 
                            'bg-gray-100 text-gray-800'
                        }">
                            ${result.status === 'success' ? '成功' : 
                              result.status === 'failed' ? '失败' : '跳过'}
                        </span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">${result.message || '-'}</td>
                </tr>
            `).join('');
        }
        
        // 显示提示
        function showToast(message, type = 'info') {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `mb-2 px-4 py-3 rounded-lg shadow-lg text-white transform transition-all duration-300 translate-x-full ${
                type === 'success' ? 'bg-green-500' : 
                type === 'error' ? 'bg-red-500' : 
                type === 'warning' ? 'bg-yellow-500' : 
                'bg-blue-500'
            }`;
            
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${
                        type === 'success' ? 'fa-check-circle' : 
                        type === 'error' ? 'fa-times-circle' : 
                        type === 'warning' ? 'fa-exclamation-triangle' : 
                        'fa-info-circle'
                    } mr-2"></i>
                    ${message}
                </div>
            `;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 退出登录
        function logout() {
            fetch('/api/v1/auth/logout', { method: 'POST' })
                .then(() => {
                    window.location.href = '/login';
                });
        }
        
        // 检测协议
        async function detectProtocol(url) {
            try {
                // 首先尝试 https
                const httpsResponse = await fetch('/api/v1/sites/check-protocol', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': window.csrfToken || ''
                    },
                    body: JSON.stringify({ url: 'https://' + url })
                });
                
                const httpsData = await httpsResponse.json();
                if (httpsData.success && httpsData.accessible) {
                    return 'https://';
                }
                
                // 如果 https 失败，尝试 http
                const httpResponse = await fetch('/api/v1/sites/check-protocol', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': window.csrfToken || ''
                    },
                    body: JSON.stringify({ url: 'http://' + url })
                });
                
                const httpData = await httpResponse.json();
                if (httpData.success && httpData.accessible) {
                    return 'http://';
                }
            } catch (error) {
                console.error('协议检测失败:', error);
            }
            
            return null; // 两个协议都不可用
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.row-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }
        
        // 复制失败的域名
        function copyFailedDomains() {
            const failedRows = document.querySelectorAll('tr[data-status="failed"]');
            const domains = Array.from(failedRows).map(row => row.getAttribute('data-domain')).filter(d => d);
            
            if (domains.length === 0) {
                showToast('没有失败的域名', 'warning');
                return;
            }
            
            const text = domains.join('\n');
            
            // 复制到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showToast(`已复制 ${domains.length} 个失败的域名`, 'success');
                }).catch(err => {
                    // 降级方案
                    fallbackCopyToClipboard(text);
                });
            } else {
                // 降级方案
                fallbackCopyToClipboard(text);
            }
        }
        
        // 降级复制方案
        function fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                showToast('已复制失败的域名', 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }
            
            document.body.removeChild(textArea);
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateLineNumbers();
            checkMatching();
        });
    </script>
    
    <!-- 加载动态菜单 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>