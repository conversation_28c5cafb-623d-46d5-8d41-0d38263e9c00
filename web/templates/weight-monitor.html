<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权重监测 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
            <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">权重监测</h1>
                    <div class="flex items-center space-x-4">
                        <!-- 日期筛选 -->
                        <div class="flex items-center space-x-2">
                            <input type="date" id="start-date" class="border border-gray-300 rounded px-3 py-1 text-sm">
                            <span class="text-gray-500">至</span>
                            <input type="date" id="end-date" class="border border-gray-300 rounded px-3 py-1 text-sm">
                            <button onclick="filterByDate()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-1 rounded text-sm">
                                <i class="fas fa-filter mr-1"></i>筛选
                            </button>
                        </div>
                        <button onclick="exportData()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-1 rounded text-sm">
                            <i class="fas fa-download mr-1"></i>导出
                        </button>
                        <button onclick="location.reload()" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-6">
                <!-- 快速操作栏 -->
                <div class="bg-white rounded-lg shadow mb-6 p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <h2 class="text-lg font-semibold text-gray-800">权重监测状态</h2>
                            <span id="monitor-status" class="px-3 py-1 text-sm rounded-full bg-gray-100 text-gray-600">
                                <i class="fas fa-circle text-gray-400 mr-1"></i>未配置
                            </span>
                            <span class="text-sm text-gray-500">
                                最后检查: <span id="last-check-time">-</span>
                            </span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="manualCheck()" 
                                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded text-sm">
                                <i class="fas fa-play mr-1"></i>立即执行
                            </button>
                            <button onclick="clearWeightData()" 
                                    class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm">
                                <i class="fas fa-trash-alt mr-1"></i>清空数据
                            </button>
                            <a href="/admin/settings" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm">
                                <i class="fas fa-cog mr-1"></i>配置设置
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">监测域名数</p>
                                <p class="text-2xl font-bold text-gray-800" id="total-domains">0</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-globe text-blue-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">平均PC权重</p>
                                <p class="text-2xl font-bold text-gray-800" id="avg-pc-weight">0</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-desktop text-green-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">平均移动权重</p>
                                <p class="text-2xl font-bold text-gray-800" id="avg-mobile-weight">0</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-mobile-alt text-purple-600"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500">权重提升</p>
                                <p class="text-2xl font-bold text-green-600" id="weight-increase">
                                    <i class="fas fa-arrow-up mr-1"></i><span>0</span>
                                </p>
                            </div>
                            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-orange-600"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 趋势图表 -->
                <div class="bg-white rounded-lg shadow mb-6">
                    <div class="px-6 py-4 border-b flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-800">权重趋势图</h2>
                        <div class="flex items-center space-x-2">
                            <!-- 平台选择按钮 -->
                            <div class="flex items-center gap-1 bg-gray-100 rounded p-1">
                                <button onclick="setChartPlatform('mobile')" id="chart-platform-mobile" 
                                        class="px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors">
                                    <i class="fas fa-mobile-alt mr-1"></i>移动端
                                </button>
                                <button onclick="setChartPlatform('pc')" id="chart-platform-pc" 
                                        class="px-3 py-1 text-sm rounded bg-transparent text-gray-700 hover:bg-gray-200 transition-colors">
                                    <i class="fas fa-desktop mr-1"></i>PC端
                                </button>
                            </div>
                            <select id="trend-domain" onchange="updateTrendChart()" class="border border-gray-300 rounded px-3 py-1 text-sm">
                                <option value="">所有域名</option>
                            </select>
                            <select id="trend-days" onchange="updateTrendChart()" class="border border-gray-300 rounded px-3 py-1 text-sm">
                                <option value="7" selected>最近7天</option>
                                <option value="30">最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </div>
                    </div>
                    <div class="p-6" style="height: 320px;">
                        <canvas id="weightTrendChart"></canvas>
                    </div>
                </div>
                
                <!-- 域名列表 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <div class="flex items-center justify-between mb-3">
                            <h2 class="text-lg font-semibold text-gray-800">域名权重列表</h2>
                            <input type="text" id="search-domain" placeholder="搜索域名..." 
                                   class="border border-gray-300 rounded px-3 py-1 text-sm"
                                   onkeyup="filterWeightList()">
                        </div>
                        
                        <!-- 权重筛选标签 -->
                        <div class="space-y-3">
                            <!-- 平台选择 -->
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-gray-600">平台:</span>
                                <button onclick="setPlatformFilter('all')" id="platform-all" 
                                        class="px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors">
                                    全部
                                </button>
                                <button onclick="setPlatformFilter('pc')" id="platform-pc" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    <i class="fas fa-desktop mr-1"></i>PC端
                                </button>
                                <button onclick="setPlatformFilter('mobile')" id="platform-mobile" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    <i class="fas fa-mobile-alt mr-1"></i>移动端
                                </button>
                            </div>
                            
                            <!-- 权重等级筛选 -->
                            <div class="flex flex-wrap items-center gap-2">
                                <span class="text-sm text-gray-600">权重:</span>
                                <button onclick="setWeightFilter('all')" id="filter-all" 
                                        class="px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors">
                                    全部 <span class="font-semibold ml-1 bg-white/20 px-1.5 py-0.5 rounded">0</span>
                                </button>
                                <!-- 权重 1-5 使用渐变绿色 -->
                                <button onclick="setWeightFilter('1')" id="filter-1" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重1 <span class="font-semibold ml-1 text-green-600">0</span>
                                </button>
                                <button onclick="setWeightFilter('2')" id="filter-2" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重2 <span class="font-semibold ml-1 text-green-600">0</span>
                                </button>
                                <button onclick="setWeightFilter('3')" id="filter-3" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重3 <span class="font-semibold ml-1 text-green-600">0</span>
                                </button>
                                <button onclick="setWeightFilter('4')" id="filter-4" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重4 <span class="font-semibold ml-1 text-yellow-600">0</span>
                                </button>
                                <button onclick="setWeightFilter('5')" id="filter-5" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重5 <span class="font-semibold ml-1 text-yellow-600">0</span>
                                </button>
                                <!-- 权重 6-10 使用渐变橙红色 -->
                                <button onclick="setWeightFilter('6')" id="filter-6" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重6 <span class="font-semibold ml-1 text-orange-600">0</span>
                                </button>
                                <button onclick="setWeightFilter('7')" id="filter-7" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重7 <span class="font-semibold ml-1 text-orange-600">0</span>
                                </button>
                                <button onclick="setWeightFilter('8')" id="filter-8" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重8 <span class="font-semibold ml-1 text-red-600">0</span>
                                </button>
                                <button onclick="setWeightFilter('9')" id="filter-9" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重9 <span class="font-semibold ml-1 text-red-600">0</span>
                                </button>
                                <button onclick="setWeightFilter('10')" id="filter-10" 
                                        class="px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                    权重10 <span class="font-semibold ml-1 text-purple-600">0</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">域名</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('pc_br')">
                                        PC权重 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('m_br')">
                                        移动权重 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('total_ip')">
                                        预估流量 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('pc_ip')">
                                        PC流量 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('m_ip')">
                                        移动流量 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">变化</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="weight-list" class="bg-white divide-y divide-gray-200">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控件 -->
                    <div class="px-6 py-4 border-t">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-700">每页显示</span>
                                <select id="page-size" onchange="changePageSize()" class="border border-gray-300 rounded px-2 py-1 text-sm">
                                    <option value="10">10</option>
                                    <option value="20" selected>20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="text-sm text-gray-700">条</span>
                                <span class="text-sm text-gray-600 ml-4">
                                    共 <span id="total-count">0</span> 条记录
                                </span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="prev-page" onclick="prevPage()" disabled 
                                        class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div id="page-numbers" class="flex items-center space-x-1">
                                    <!-- 页码按钮将动态生成 -->
                                </div>
                                <button id="next-page" onclick="nextPage()" disabled
                                        class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- 详情弹窗 -->
    <div id="detail-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto">
                <div class="px-6 py-4 border-b flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800" id="modal-title">域名权重详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    <!-- 平台切换按钮 -->
                    <div class="mb-4 flex items-center gap-2">
                        <span class="text-sm text-gray-600">选择平台:</span>
                        <div class="flex items-center gap-1 bg-gray-100 rounded p-1">
                            <button onclick="setDetailChartPlatform('pc')" id="detail-platform-pc" 
                                    class="px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors">
                                <i class="fas fa-desktop mr-1"></i>PC端
                            </button>
                            <button onclick="setDetailChartPlatform('mobile')" id="detail-platform-mobile" 
                                    class="px-3 py-1 text-sm rounded bg-transparent text-gray-700 hover:bg-gray-200 transition-colors">
                                <i class="fas fa-mobile-alt mr-1"></i>移动端
                            </button>
                        </div>
                    </div>
                    <div class="mb-6" style="height: 240px;">
                        <canvas id="detailChart"></canvas>
                    </div>
                    <div id="detail-content">
                        <!-- 详细数据将动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        let weightData = [];
        let filteredData = [];  // 筛选后的数据
        let currentPage = 1;
        let pageSize = 20;
        let trendChart = null;
        let detailChart = null;
        let detailChartData = null; // 存储详情图表数据
        let chartPlatform = 'mobile'; // 默认显示移动端
        let detailChartPlatform = 'pc'; // 详情图表默认显示PC端
        let sortColumn = ''; // 当前排序列
        let sortOrder = 'desc'; // 排序方向：asc或desc
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', async function() {
            // 设置默认日期范围（改为7天）
            const today = new Date();
            const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            document.getElementById('end-date').value = formatDate(today);
            document.getElementById('start-date').value = formatDate(sevenDaysAgo);
            
            // 加载状态
            loadMonitorStatus();
            
            // 先加载权重数据，再加载图表
            await loadWeightData();
            // 延迟加载图表，确保数据已准备好
            setTimeout(async () => {
                console.log('开始初始化趋势图表');
                await loadTrendChart();
            }, 500);
        });
        
        // 格式化日期
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 加载权重数据
        async function loadWeightData() {
            try {
                const res = await fetch('/api/v1/weight/list');
                const data = await res.json();
                
                if (data.success) {
                    weightData = data.data || [];
                    filteredData = [...weightData];  // 初始化筛选数据
                    currentPage = 1;  // 重置到第一页
                    updateStatistics();
                    updateWeightStats();  // 更新权重统计
                    renderWeightList();
                    updateDomainSelect();
                }
            } catch (error) {
                console.error('加载权重数据失败:', error);
                showToast('加载数据失败', 'error');
            }
        }
        
        // 分页相关函数
        function updatePagination() {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            const prevBtn = document.getElementById('prev-page');
            const nextBtn = document.getElementById('next-page');
            const pageNumbers = document.getElementById('page-numbers');
            
            // 更新上一页按钮
            prevBtn.disabled = currentPage <= 1;
            
            // 更新下一页按钮
            nextBtn.disabled = currentPage >= totalPages;
            
            // 生成页码按钮
            let pageHtml = '';
            const maxVisible = 5; // 最多显示5个页码
            let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let endPage = Math.min(totalPages, startPage + maxVisible - 1);
            
            if (endPage - startPage < maxVisible - 1) {
                startPage = Math.max(1, endPage - maxVisible + 1);
            }
            
            // 添加第一页
            if (startPage > 1) {
                pageHtml += `
                    <button onclick="goToPage(1)" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">1</button>
                `;
                if (startPage > 2) {
                    pageHtml += `<span class="px-2 text-gray-500">...</span>`;
                }
            }
            
            // 添加中间页码
            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                pageHtml += `
                    <button onclick="goToPage(${i})" 
                            class="px-3 py-1 border ${isActive ? 'bg-blue-500 text-white border-blue-500' : 'border-gray-300 hover:bg-gray-50'} rounded text-sm">
                        ${i}
                    </button>
                `;
            }
            
            // 添加最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pageHtml += `<span class="px-2 text-gray-500">...</span>`;
                }
                pageHtml += `
                    <button onclick="goToPage(${totalPages})" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">${totalPages}</button>
                `;
            }
            
            pageNumbers.innerHTML = pageHtml || '<span class="text-sm text-gray-500">1</span>';
        }
        
        function changePageSize() {
            pageSize = parseInt(document.getElementById('page-size').value);
            currentPage = 1;
            renderWeightList();
        }
        
        function prevPage() {
            if (currentPage > 1) {
                currentPage--;
                renderWeightList();
            }
        }
        
        function nextPage() {
            const totalPages = Math.ceil(filteredData.length / pageSize);
            if (currentPage < totalPages) {
                currentPage++;
                renderWeightList();
            }
        }
        
        function goToPage(page) {
            currentPage = page;
            renderWeightList();
        }
        
        // 更新统计信息
        function updateStatistics() {
            const totalDomains = weightData.length;
            let totalPCBR = 0;
            let totalMobileBR = 0;
            let increaseCount = 0;
            
            weightData.forEach(item => {
                totalPCBR += item.current_pc_br || 0;
                totalMobileBR += item.current_m_br || 0;
                if (item.pc_br_change > 0 || item.m_br_change > 0) {
                    increaseCount++;
                }
            });
            
            document.getElementById('total-domains').textContent = totalDomains;
            document.getElementById('avg-pc-weight').textContent = 
                totalDomains > 0 ? (totalPCBR / totalDomains).toFixed(2) : '0';
            document.getElementById('avg-mobile-weight').textContent = 
                totalDomains > 0 ? (totalMobileBR / totalDomains).toFixed(2) : '0';
            document.getElementById('weight-increase').querySelector('span').textContent = increaseCount;
        }
        
        // 排序函数
        function sortByColumn(column) {
            // 如果点击同一列，切换排序方向
            if (sortColumn === column) {
                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortOrder = 'desc'; // 新列默认降序
            }
            
            // 更新表头图标
            updateSortIcons(column);
            
            // 执行排序
            weightData.sort((a, b) => {
                let valueA, valueB;
                
                switch(column) {
                    case 'pc_br':
                        valueA = a.current_pc_br || 0;
                        valueB = b.current_pc_br || 0;
                        break;
                    case 'm_br':
                        valueA = a.current_m_br || 0;
                        valueB = b.current_m_br || 0;
                        break;
                    case 'total_ip':
                        valueA = parseFloat(a.current_ip) || 0;
                        valueB = parseFloat(b.current_ip) || 0;
                        break;
                    case 'pc_ip':
                        valueA = parseFloat(a.current_pc_ip) || 0;
                        valueB = parseFloat(b.current_pc_ip) || 0;
                        break;
                    case 'm_ip':
                        valueA = parseFloat(a.current_m_ip) || 0;
                        valueB = parseFloat(b.current_m_ip) || 0;
                        break;
                    default:
                        return 0;
                }
                
                if (sortOrder === 'asc') {
                    return valueA - valueB;
                } else {
                    return valueB - valueA;
                }
            });
            
            // 同步更新筛选后的数据（考虑当前筛选条件）
            filterWeightList();
        }
        
        // 更新排序图标
        function updateSortIcons(column) {
            // 重置所有图标
            document.querySelectorAll('th[onclick^="sortByColumn"] i').forEach(icon => {
                icon.className = 'fas fa-sort text-xs ml-1';
            });
            
            // 设置当前列的图标
            const th = document.querySelector(`th[onclick="sortByColumn('${column}')"]`);
            if (th) {
                const icon = th.querySelector('i');
                if (icon) {
                    if (sortOrder === 'asc') {
                        icon.className = 'fas fa-sort-up text-xs ml-1 text-blue-600';
                    } else {
                        icon.className = 'fas fa-sort-down text-xs ml-1 text-blue-600';
                    }
                }
            }
        }
        
        // 格式化流量数值（添加千位分隔符）
        function formatTraffic(value) {
            if (!value || value === '-') return '-';
            const num = parseFloat(value);
            if (isNaN(num)) return '-';
            return num.toLocaleString('zh-CN');
        }
        
        // 渲染权重列表
        function renderWeightList() {
            const tbody = document.getElementById('weight-list');
            
            // 获取当前页的数据
            const start = (currentPage - 1) * pageSize;
            const end = start + pageSize;
            const pageData = filteredData.slice(start, end);
            
            // 更新总记录数
            document.getElementById('total-count').textContent = filteredData.length;
            
            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                            暂无数据
                        </td>
                    </tr>
                `;
                updatePagination();
                return;
            }
            
            tbody.innerHTML = pageData.map(item => {
                const pcChange = item.pc_br_change || 0;
                const mobileChange = item.m_br_change || 0;
                const changeClass = (pcChange > 0 || mobileChange > 0) ? 'text-green-600' : 
                                   (pcChange < 0 || mobileChange < 0) ? 'text-red-600' : 'text-gray-600';
                const changeIcon = (pcChange > 0 || mobileChange > 0) ? 'fa-arrow-up' : 
                                  (pcChange < 0 || mobileChange < 0) ? 'fa-arrow-down' : 'fa-minus';
                
                const lastCheck = new Date(item.last_check_time);
                const checkTime = lastCheck.toLocaleString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-blue-600 hover:text-blue-800 cursor-pointer" onclick="filterTrendChart('${item.domain}')">${item.domain}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                                ${item.current_pc_br}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                                ${item.current_m_br}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">
                            ${formatTraffic(item.current_ip)}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">
                            ${formatTraffic(item.current_pc_ip)}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">
                            ${formatTraffic(item.current_m_ip)}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <span class="${changeClass}">
                                <i class="fas ${changeIcon} text-xs"></i>
                                PC: ${pcChange > 0 ? '+' : ''}${pcChange}
                                M: ${mobileChange > 0 ? '+' : ''}${mobileChange}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">
                            ${checkTime}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            <button onclick="showDetail('${item.domain}')" 
                                    class="text-blue-600 hover:text-blue-900 text-sm mr-2">
                                <i class="fas fa-chart-line mr-1"></i>详情
                            </button>
                            <button onclick="deleteDomainWeight('${item.domain}')" 
                                    class="text-red-600 hover:text-red-900 text-sm">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
            
            // 更新分页控件
            updatePagination();
        }
        
        // 更新域名选择列表
        function updateDomainSelect() {
            const select = document.getElementById('trend-domain');
            const domains = weightData.map(item => item.domain);
            
            select.innerHTML = '<option value="">所有域名</option>' + 
                domains.map(domain => `<option value="${domain}">${domain}</option>`).join('');
        }
        
        // 加载趋势图表
        async function loadTrendChart() {
            const domain = document.getElementById('trend-domain').value;
            const days = parseInt(document.getElementById('trend-days').value || '7');  // 默认7天
            
            console.log('加载趋势图表，域名:', domain, '天数:', days);
            
            // 计算日期范围
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);
            
            const startDateStr = formatDate(startDate);
            const endDateStr = formatDate(endDate);
            
            try {
                let url;
                if (domain) {
                    // 单个域名用trend接口
                    url = `/api/v1/weight/trend?domain=${domain}&days=${days}`;
                } else {
                    // 所有域名用history接口，传入日期范围
                    url = `/api/v1/weight/history?start_date=${startDateStr}&end_date=${endDateStr}`;
                }
                
                console.log('请求URL:', url);
                const res = await fetch(url);
                const data = await res.json();
                
                console.log('趋势数据响应:', data);
                
                if (data.success && data.data) {
                    drawTrendChart(data.data);
                } else {
                    console.log('无趋势数据');
                    drawTrendChart([]);  // 显示空图表
                }
            } catch (error) {
                console.error('加载趋势数据失败:', error);
                drawTrendChart([]);  // 显示空图表
            }
        }
        
        // 设置图表平台
        function setChartPlatform(platform) {
            chartPlatform = platform;
            
            // 更新按钮样式
            if (platform === 'mobile') {
                document.getElementById('chart-platform-mobile').className = 'px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors';
                document.getElementById('chart-platform-pc').className = 'px-3 py-1 text-sm rounded bg-transparent text-gray-700 hover:bg-gray-200 transition-colors';
            } else {
                document.getElementById('chart-platform-pc').className = 'px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors';
                document.getElementById('chart-platform-mobile').className = 'px-3 py-1 text-sm rounded bg-transparent text-gray-700 hover:bg-gray-200 transition-colors';
            }
            
            // 重新绘制图表
            loadTrendChart();
        }
        
        // 绘制趋势图表
        function drawTrendChart(data) {
            const ctx = document.getElementById('weightTrendChart').getContext('2d');
            
            if (trendChart) {
                trendChart.destroy();
            }
            
            // 如果有历史数据，显示趋势图
            if (data && data.length > 0) {
                console.log('绘制权重趋势图，数据点数:', data.length, '平台:', chartPlatform);
                
                // 按时间点统计各权重级别的域名数量
                const timePointStats = {};
                const allTimePoints = new Set();
                const domainLatestData = {}; // 存储每个域名在每个时间点的最新数据
                
                // 首先整理数据，确保每个域名在每个时间点只保留最新的记录
                data.forEach(item => {
                    const checkTime = new Date(item.check_time);
                    const domain = item.domain;
                    
                    // 格式化为小时级别的时间点（月-日 时点）
                    const timePoint = `${(checkTime.getMonth() + 1).toString().padStart(2, '0')}-${checkTime.getDate().toString().padStart(2, '0')} ${checkTime.getHours().toString().padStart(2, '0')}时`;
                    
                    allTimePoints.add(timePoint);
                    
                    const key = `${domain}_${timePoint}`;
                    if (!domainLatestData[key] || new Date(domainLatestData[key].check_time) < checkTime) {
                        domainLatestData[key] = item;
                    }
                });
                
                // 统计每个时间点各权重的域名数量
                Object.values(domainLatestData).forEach(item => {
                    const checkTime = new Date(item.check_time);
                    const timePoint = `${(checkTime.getMonth() + 1).toString().padStart(2, '0')}-${checkTime.getDate().toString().padStart(2, '0')} ${checkTime.getHours().toString().padStart(2, '0')}时`;
                    
                    if (!timePointStats[timePoint]) {
                        timePointStats[timePoint] = {
                            weights: {} // 只统计选中平台的权重
                        };
                    }
                    
                    // 根据选中平台统计权重
                    const weight = chartPlatform === 'mobile' ? (item.m_br || 0) : (item.pc_br || 0);
                    
                    if (!timePointStats[timePoint].weights[weight]) {
                        timePointStats[timePoint].weights[weight] = 0;
                    }
                    timePointStats[timePoint].weights[weight]++;
                });
                
                // 排序时间点
                const sortedTimePoints = Array.from(allTimePoints).sort((a, b) => {
                    const parseTime = (str) => {
                        const [date, hour] = str.split(' ');
                        const [month, day] = date.split('-');
                        return new Date(2024, parseInt(month) - 1, parseInt(day), parseInt(hour));
                    };
                    return parseTime(a) - parseTime(b);
                });
                
                // 权重级别的颜色方案（从低到高渐变）
                const weightColors = {
                    0: '#9CA3AF',  // 灰色
                    1: '#FCA5A5',  // 浅红
                    2: '#FBBF24',  // 黄色
                    3: '#FDE047',  // 亮黄
                    4: '#A3E635',  // 黄绿
                    5: '#4ADE80',  // 绿色
                    6: '#34D399',  // 青绿
                    7: '#2DD4BF',  // 青色
                    8: '#22D3EE',  // 天蓝
                    9: '#3B82F6',  // 蓝色
                    10: '#6366F1'  // 紫色
                };
                
                // 创建数据集 - 为每个权重级别创建线条
                const datasets = [];
                
                // 为每个权重级别创建数据集（0-10）
                for (let weight = 0; weight <= 10; weight++) {
                    // 根据选中平台获取数据
                    const weightData = sortedTimePoints.map(time => {
                        return timePointStats[time]?.weights[weight] || 0;
                    });
                    
                    // 只添加有数据的权重级别
                    if (weightData.some(v => v > 0)) {
                        datasets.push({
                            label: `权重${weight}`,
                            data: weightData,
                            borderColor: weightColors[weight],
                            backgroundColor: weightColors[weight] + '20',
                            borderWidth: 2,
                            tension: 0.3,
                            fill: false,
                            pointRadius: 1,
                            pointHoverRadius: 3
                        });
                    }
                }
                
                trendChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: sortedTimePoints,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: `${chartPlatform === 'mobile' ? '移动端' : 'PC端'}权重分布`,
                                position: 'top',
                                font: {
                                    size: 14
                                }
                            },
                            legend: {
                                display: true,
                                position: 'right',
                                labels: {
                                    boxWidth: 15,
                                    padding: 8,
                                    font: {
                                        size: 10
                                    }
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    title: function(context) {
                                        return '时间: ' + context[0].label;
                                    },
                                    label: function(context) {
                                        const value = context.parsed.y;
                                        if (value === 0) return null;
                                        return context.dataset.label + ': ' + value + '个域名';
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    maxRotation: 45,
                                    minRotation: 45,
                                    maxTicksLimit: 24, // 最多显示24个时间点
                                    callback: function(value, index, values) {
                                        // 根据数据点数量决定显示频率
                                        const skipInterval = Math.ceil(values.length / 24);
                                        if (index % skipInterval === 0) {
                                            return this.getLabelForValue(value);
                                        }
                                        return null;
                                    }
                                }
                            },
                            y: {
                                display: true,
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1,
                                    callback: function(value) {
                                        return value + '个';
                                    }
                                },
                                title: {
                                    display: true,
                                    text: '域名数量'
                                }
                            }
                        }
                    }
                });
            } else {
                // 无数据时显示空图表
                console.log('暂无趋势数据');
                trendChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['暂无数据'],
                        datasets: [{
                            label: 'PC权重',
                            data: [0],
                            borderColor: '#3B82F6',
                            backgroundColor: '#3B82F620'
                        }, {
                            label: '移动权重',
                            data: [0],
                            borderColor: '#10B981',
                            backgroundColor: '#10B98120',
                            borderDash: [5, 5]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            title: {
                                display: true,
                                text: '暂无权重数据，请先添加域名并执行检测',
                                font: {
                                    size: 14
                                },
                                color: '#6B7280'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 10,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }
        }
        
        // 处理权重数据，统计分布
        function processWeightData(data) {
            const stats = {};
            const uniqueDomains = new Map(); // 用于去重
            
            if (data && data.length > 0) {
                // 首先按域名去重，保留每个域名的最新记录
                data.forEach(item => {
                    const domain = item.domain;
                    if (!uniqueDomains.has(domain) || 
                        new Date(item.check_time) > new Date(uniqueDomains.get(domain).check_time)) {
                        uniqueDomains.set(domain, item);
                    }
                });
                
                // 统计去重后的数据
                uniqueDomains.forEach(item => {
                    const pcBR = item.pc_br || 0;
                    const mobileBR = item.m_br || item.mobile_br || 0;
                    
                    if (!stats[pcBR]) {
                        stats[pcBR] = { pc: 0, mobile: 0 };
                    }
                    if (!stats[mobileBR]) {
                        stats[mobileBR] = { pc: 0, mobile: 0 };
                    }
                    
                    stats[pcBR].pc++;
                    stats[mobileBR].mobile++;
                });
            }
            return stats;
        }
        
        // 更新趋势图表
        function updateTrendChart() {
            loadTrendChart();
        }
        
        // 显示详情
        async function showDetail(domain) {
            try {
                const res = await fetch(`/api/v1/weight/domain/${domain}`);
                const data = await res.json();
                
                if (data.success) {
                    document.getElementById('modal-title').textContent = `${domain} - 权重详情`;
                    
                    // 保存数据供切换平台时使用
                    if (data.data.history && data.data.history.length > 0) {
                        detailChartData = data.data.history;
                        // 默认显示PC端
                        detailChartPlatform = 'pc';
                        drawDetailChart(detailChartData);
                    } else if (data.data.trends && data.data.trends.length > 0) {
                        detailChartData = data.data.trends;
                        drawDetailChart(detailChartData);
                    } else {
                        console.log('无详情数据可用于绘制图表');
                        detailChartData = null;
                    }
                    
                    // 显示详细信息
                    const stats = data.data.stats || {};
                    const history = data.data.history || [];
                    
                    document.getElementById('detail-content').innerHTML = `
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="bg-gray-50 rounded p-4">
                                <h4 class="font-semibold text-gray-800 mb-2">当前权重</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">PC权重:</span>
                                        <span class="font-medium">${stats.current_pc_br || 0}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">移动权重:</span>
                                        <span class="font-medium">${stats.current_m_br || stats.current_mobile_br || 0}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">预估流量:</span>
                                        <span class="font-medium">${stats.current_ip || '-'}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-gray-50 rounded p-4">
                                <h4 class="font-semibold text-gray-800 mb-2">变化趋势</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">PC变化:</span>
                                        <span class="font-medium ${(stats.pc_br_change || 0) > 0 ? 'text-green-600' : (stats.pc_br_change || 0) < 0 ? 'text-red-600' : ''}">
                                            ${(stats.pc_br_change || 0) > 0 ? '+' : ''}${stats.pc_br_change || 0}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">移动变化:</span>
                                        <span class="font-medium ${(stats.m_br_change || stats.mobile_br_change || 0) > 0 ? 'text-green-600' : (stats.m_br_change || stats.mobile_br_change || 0) < 0 ? 'text-red-600' : ''}">
                                            ${(stats.m_br_change || stats.mobile_br_change || 0) > 0 ? '+' : ''}${stats.m_br_change || stats.mobile_br_change || 0}
                                        </span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">最后更新:</span>
                                        <span class="font-medium">${stats.last_check_time ? new Date(stats.last_check_time).toLocaleString('zh-CN') : '-'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <h4 class="font-semibold text-gray-800 mb-2">历史记录</h4>
                        <div class="max-h-64 overflow-y-auto">
                            <table class="w-full text-sm">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-2 text-left">时间</th>
                                        <th class="px-4 py-2 text-center">PC权重</th>
                                        <th class="px-4 py-2 text-center">移动权重</th>
                                        <th class="px-4 py-2 text-center">预估流量</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    ${history.map(item => `
                                        <tr>
                                            <td class="px-4 py-2">${new Date(item.check_time).toLocaleString('zh-CN')}</td>
                                            <td class="px-4 py-2 text-center">${item.pc_br || 0}</td>
                                            <td class="px-4 py-2 text-center">${item.m_br || item.mobile_br || 0}</td>
                                            <td class="px-4 py-2 text-center">${item.ip || '-'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                    
                    document.getElementById('detail-modal').classList.remove('hidden');
                }
            } catch (error) {
                console.error('加载详情失败:', error);
                showToast('加载详情失败', 'error');
            }
        }
        
        // 筛选趋势图表（域名点击筛选）
        function filterTrendChart(domain) {
            // 更新趋势图表的域名选择
            const trendDomainSelect = document.getElementById('trend-domain');
            trendDomainSelect.value = domain;
            
            // 刷新趋势图表
            loadTrendChart();
            
            // 显示提示
            showToast(`已筛选域名: ${domain}`, 'success');
        }
        
        // 删除域名权重数据
        async function deleteDomainWeight(domain) {
            if (!confirm(`确定要删除域名 "${domain}" 的所有权重数据吗？\n\n此操作将删除：\n- 历史权重记录\n- 趋势图表数据\n\n此操作不可恢复！`)) {
                return;
            }
            
            try {
                const res = await fetch(`/api/v1/weight/domain/${encodeURIComponent(domain)}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showToast(`已删除域名 ${domain} 的权重数据`, 'success');
                    
                    // 重新加载数据
                    await loadWeightData();
                    
                    // 如果趋势图表正在显示该域名，重置为显示所有域名
                    const trendDomainSelect = document.getElementById('trend-domain');
                    if (trendDomainSelect.value === domain) {
                        trendDomainSelect.value = '';
                        loadTrendChart();
                    }
                    
                    // 如果详情弹窗正在显示该域名，关闭弹窗
                    const modalTitle = document.getElementById('modal-title');
                    if (modalTitle && modalTitle.textContent.includes(domain)) {
                        closeDetailModal();
                    }
                } else {
                    showToast(data.error || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除域名权重数据失败:', error);
                showToast('删除失败', 'error');
            }
        }
        
        // 设置详情图表平台
        function setDetailChartPlatform(platform) {
            detailChartPlatform = platform;
            
            // 更新按钮样式
            if (platform === 'pc') {
                document.getElementById('detail-platform-pc').className = 'px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors';
                document.getElementById('detail-platform-mobile').className = 'px-3 py-1 text-sm rounded bg-transparent text-gray-700 hover:bg-gray-200 transition-colors';
            } else {
                document.getElementById('detail-platform-mobile').className = 'px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors';
                document.getElementById('detail-platform-pc').className = 'px-3 py-1 text-sm rounded bg-transparent text-gray-700 hover:bg-gray-200 transition-colors';
            }
            
            // 重新绘制图表
            if (detailChartData) {
                drawDetailChart(detailChartData);
            }
        }
        
        // 绘制详情图表
        function drawDetailChart(data) {
            const ctx = document.getElementById('detailChart').getContext('2d');
            
            if (detailChart) {
                detailChart.destroy();
            }
            
            // 处理数据，确保格式正确
            let processedData = [];
            if (Array.isArray(data)) {
                processedData = data;
            } else if (data && typeof data === 'object') {
                // 如果是单个对象，转换为数组
                processedData = [data];
            }
            
            if (processedData.length === 0) {
                console.log('详情图表无数据');
                return;
            }
            
            // 按时间排序数据（确保时间顺序正确）
            processedData.sort((a, b) => {
                const dateA = new Date(a.date || a.check_time);
                const dateB = new Date(b.date || b.check_time);
                return dateA - dateB;
            });
            
            // 只保留最近7天的数据
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            processedData = processedData.filter(item => {
                const itemDate = new Date(item.date || item.check_time);
                return itemDate >= sevenDaysAgo;
            });
            
            // 按小时格式化时间标签
            const labels = processedData.map(item => {
                const date = new Date(item.date || item.check_time);
                return `${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}时`;
            });
            
            // 根据选中平台提取权重数据
            const weightData = processedData.map(item => {
                if (detailChartPlatform === 'pc') {
                    return item.pc_br || 0;
                } else {
                    return item.m_br || item.mobile_br || 0;
                }
            });
            
            detailChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: detailChartPlatform === 'pc' ? 'PC权重' : '移动权重',
                        data: weightData,
                        borderColor: detailChartPlatform === 'pc' ? '#3B82F6' : '#10B981',
                        backgroundColor: detailChartPlatform === 'pc' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: `${detailChartPlatform === 'pc' ? 'PC端' : '移动端'}权重趋势（最近7天）`,
                            position: 'top',
                            font: {
                                size: 14
                            }
                        },
                        legend: {
                            display: false // 只有一条线，不需要图例
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return '时间: ' + context[0].label;
                                },
                                label: function(context) {
                                    return context.dataset.label + ': 权重' + context.parsed.y;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                maxTicksLimit: 12, // 最多显示12个时间点
                                callback: function(value, index, values) {
                                    // 根据数据点数量决定显示频率
                                    const skipInterval = Math.ceil(values.length / 12);
                                    if (index % skipInterval === 0) {
                                        return this.getLabelForValue(value);
                                    }
                                    return null;
                                }
                            }
                        },
                        y: {
                            display: true,
                            beginAtZero: true,
                            max: 10,
                            ticks: {
                                stepSize: 1,
                                callback: function(value) {
                                    return '权重' + value;
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // 关闭详情弹窗
        function closeDetailModal() {
            document.getElementById('detail-modal').classList.add('hidden');
        }
        
        // 按日期筛选
        async function filterByDate() {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            
            if (!startDate || !endDate) {
                showToast('请选择日期范围', 'warning');
                return;
            }
            
            try {
                const res = await fetch(`/api/v1/weight/history?start_date=${startDate}&end_date=${endDate}`);
                const data = await res.json();
                
                if (data.success) {
                    // 处理筛选后的数据
                    showToast('数据已更新', 'success');
                    loadWeightData();
                }
            } catch (error) {
                console.error('筛选失败:', error);
                showToast('筛选失败', 'error');
            }
        }
        
        // 当前选中的筛选条件
        let currentWeightFilter = 'all';
        let currentPlatformFilter = 'all';
        
        // 设置平台筛选
        function setPlatformFilter(filter) {
            currentPlatformFilter = filter;
            
            // 更新按钮样式
            document.querySelectorAll('[id^="platform-"]').forEach(btn => {
                btn.className = 'px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors';
            });
            document.getElementById('platform-' + filter).className = 'px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors';
            
            updateWeightStats();  // 更新统计
            filterWeightList();
        }
        
        // 设置权重筛选
        function setWeightFilter(filter) {
            currentWeightFilter = filter;
            
            // 更新标签样式
            document.querySelectorAll('[id^="filter-"]').forEach(btn => {
                btn.className = 'px-3 py-1 text-sm rounded bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors';
            });
            document.getElementById('filter-' + filter).className = 'px-3 py-1 text-sm rounded bg-indigo-500 text-white transition-colors';
            
            filterWeightList();
        }
        
        // 更新权重统计
        function updateWeightStats() {
            // 根据当前平台筛选统计
            const stats = {
                all: 0,
                1: 0, 2: 0, 3: 0, 4: 0, 5: 0,
                6: 0, 7: 0, 8: 0, 9: 0, 10: 0
            };
            
            weightData.forEach(item => {
                const pcBR = item.current_pc_br || 0;
                const mobileBR = item.current_m_br || 0;
                
                let shouldCount = false;
                let weightValue = 0;
                
                if (currentPlatformFilter === 'all') {
                    // 全部平台：取最大值
                    weightValue = Math.max(pcBR, mobileBR);
                    shouldCount = true;
                } else if (currentPlatformFilter === 'pc') {
                    // 仅PC端
                    weightValue = pcBR;
                    shouldCount = pcBR > 0;
                } else if (currentPlatformFilter === 'mobile') {
                    // 仅移动端
                    weightValue = mobileBR;
                    shouldCount = mobileBR > 0;
                }
                
                if (shouldCount) {
                    stats.all++;
                    if (weightValue >= 1 && weightValue <= 10) {
                        stats[weightValue]++;
                    }
                }
            });
            
            // 更新标签上的数字
            document.querySelector('#filter-all span').textContent = stats.all;
            for (let i = 1; i <= 10; i++) {
                const span = document.querySelector(`#filter-${i} span`);
                if (span) {
                    span.textContent = stats[i];
                }
            }
        }
        
        // 筛选权重列表
        function filterWeightList() {
            const keyword = document.getElementById('search-domain').value.toLowerCase();
            
            let filtered = weightData.filter(item => {
                // 域名搜索
                if (keyword && !item.domain.toLowerCase().includes(keyword)) {
                    return false;
                }
                
                const pcBR = item.current_pc_br || 0;
                const mobileBR = item.current_m_br || 0;
                
                // 平台筛选
                if (currentPlatformFilter === 'pc') {
                    // 仅显示有PC权重的
                    if (pcBR === 0) return false;
                    // 权重筛选（基于PC权重）
                    if (currentWeightFilter !== 'all') {
                        const targetWeight = parseInt(currentWeightFilter);
                        if (pcBR !== targetWeight) return false;
                    }
                } else if (currentPlatformFilter === 'mobile') {
                    // 仅显示有移动权重的
                    if (mobileBR === 0) return false;
                    // 权重筛选（基于移动权重）
                    if (currentWeightFilter !== 'all') {
                        const targetWeight = parseInt(currentWeightFilter);
                        if (mobileBR !== targetWeight) return false;
                    }
                } else {
                    // 全部平台：基于最大权重
                    if (currentWeightFilter !== 'all') {
                        const maxBR = Math.max(pcBR, mobileBR);
                        const targetWeight = parseInt(currentWeightFilter);
                        if (maxBR !== targetWeight) return false;
                    }
                }
                
                return true;
            });
            
            // 更新筛选后的数据
            filteredData = filtered;
            currentPage = 1;  // 重置到第一页
            renderWeightList();
        }
        
        // 保留旧函数名以兼容
        function searchDomains() {
            filterWeightList();
        }
        
        // 导出数据
        async function exportData() {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            
            try {
                const url = `/api/v1/weight/export?format=csv&start_date=${startDate}&end_date=${endDate}`;
                window.open(url, '_blank');
            } catch (error) {
                console.error('导出失败:', error);
                showToast('导出功能开发中', 'info');
            }
        }
        
        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }
        
        // 加载监测状态
        async function loadMonitorStatus() {
            try {
                const res = await fetch('/api/v1/weight/config');
                const data = await res.json();
                
                if (data.success && data.data) {
                    const config = data.data;
                    const statusEl = document.getElementById('monitor-status');
                    const lastCheckEl = document.getElementById('last-check-time');
                    
                    // 更新状态显示
                    if (!config.api_key) {
                        statusEl.innerHTML = '<i class="fas fa-circle text-gray-400 mr-1"></i>未配置';
                        statusEl.className = 'px-3 py-1 text-sm rounded-full bg-gray-100 text-gray-600';
                    } else if (config.enabled) {
                        statusEl.innerHTML = '<i class="fas fa-circle text-green-400 mr-1"></i>运行中';
                        statusEl.className = 'px-3 py-1 text-sm rounded-full bg-green-100 text-green-600';
                    } else {
                        statusEl.innerHTML = '<i class="fas fa-circle text-yellow-400 mr-1"></i>已停止';
                        statusEl.className = 'px-3 py-1 text-sm rounded-full bg-yellow-100 text-yellow-600';
                    }
                    
                    // 更新最后检查时间
                    if (config.last_check_time && config.last_check_time !== '0001-01-01T08:05:43+08:05') {
                        const date = new Date(config.last_check_time);
                        lastCheckEl.textContent = date.toLocaleString();
                    } else {
                        lastCheckEl.textContent = '从未执行';
                    }
                }
            } catch (error) {
                console.error('加载监测状态失败:', error);
            }
        }
        
        // 手动执行检测
        async function manualCheck() {
            try {
                // 先检查是否已配置
                const configRes = await fetch('/api/v1/weight/config');
                const configData = await configRes.json();
                
                if (!configData.success || !configData.data || !configData.data.api_key) {
                    showToast('请先在系统设置中配置API密钥', 'warning');
                    return;
                }
                
                showToast('正在执行权重检测...', 'info');
                
                const res = await fetch('/api/v1/weight/check', {
                    method: 'POST'
                });
                
                const data = await res.json();
                if (data.success) {
                    showToast('权重检测已开始执行', 'success');
                    // 等待几秒后刷新数据
                    setTimeout(() => {
                        loadMonitorStatus();
                        loadWeightData();
                    }, 3000);
                } else {
                    showToast(data.error || '执行失败', 'error');
                }
            } catch (error) {
                console.error('执行权重检测失败:', error);
                showToast('执行权重检测失败', 'error');
            }
        }
        
        // 清空权重数据
        async function clearWeightData() {
            if (!confirm('确定要清空所有权重历史数据吗？此操作不可恢复！')) {
                return;
            }
            
            try {
                showToast('正在清空权重数据...', 'info');
                
                const res = await fetch('/api/v1/weight/clear', {
                    method: 'DELETE'
                });
                
                const data = await res.json();
                if (data.success) {
                    showToast('权重数据已清空', 'success');
                    // 重新加载数据
                    weightData = [];
                    updateStatistics();
                    updateWeightStats();
                    renderWeightList();
                    loadTrendChart();
                } else {
                    showToast(data.error || '清空失败', 'error');
                }
            } catch (error) {
                console.error('清空权重数据失败:', error);
                showToast('清空权重数据失败', 'error');
            }
        }
        
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
