<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>脏数据管理 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* 加载动画 */
        .loader {
            border-top-color: #3B82F6;
            -webkit-animation: spinner 1.5s linear infinite;
            animation: spinner 1.5s linear infinite;
        }
        @-webkit-keyframes spinner {
            0% { -webkit-transform: rotate(0deg); }
            100% { -webkit-transform: rotate(360deg); }
        }
        @keyframes spinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
            <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">脏数据管理</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="location.reload()" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 页面操作栏 -->
                <div class="mb-6 flex justify-between items-center">
                    <div class="flex space-x-4">
                        <button onclick="loadOrphanedData()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-search mr-2"></i>
                            检测脏数据
                        </button>
                        <button onclick="cleanupAllOrphanedData()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-broom mr-2"></i>
                            清理全部
                        </button>
                    </div>
                    <div class="text-sm text-gray-600">
                        <span id="orphaned-count">未检测</span>
                    </div>
                </div>

                <!-- 说明区域 -->
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">关于脏数据</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>脏数据是指在数据库中存在但没有对应站点记录的孤立数据，通常由以下情况产生：</p>
                                <ul class="mt-2 list-disc list-inside">
                                    <li>站点创建过程中发生异常，部分数据插入成功但整体创建失败</li>
                                    <li>站点删除时某些关联数据未被正确清理</li>
                                    <li>数据库约束异常或事务回滚不完整</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">脏数据列表</h3>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">记录ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">站点ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述信息</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                </tr>
                            </thead>
                            <tbody id="orphaned-data-tbody" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">点击"检测脏数据"开始检测</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 加载中 -->
                <div id="loading" class="hidden fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                        <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-8 w-8"></div>
                        <span class="text-lg text-gray-700">处理中...</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/js/menu-groups.js?v=1756618802"></script>
    <script>
        // 更新系统时间
        function updateTime() {
            const now = new Date();
            document.getElementById('system-time').textContent = now.toLocaleString('zh-CN');
        }
        setInterval(updateTime, 1000);
        updateTime();

        // 显示toast消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' : 
                           type === 'error' ? 'bg-red-500' : 'bg-blue-500';
            
            toast.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300`;
            toast.textContent = message;
            toast.style.transform = 'translateX(100%)';
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // 显示加载中
        function showLoading() {
            document.getElementById('loading').classList.remove('hidden');
        }

        // 隐藏加载中
        function hideLoading() {
            document.getElementById('loading').classList.add('hidden');
        }

        // 加载脏数据
        async function loadOrphanedData() {
            try {
                showLoading();
                const response = await fetch('/api/v1/sites/orphaned-data');
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data || [];
                    renderOrphanedData(data);
                    document.getElementById('orphaned-count').textContent = `发现 ${data.length} 条脏数据`;
                    if (data.length === 0) {
                        showToast('未发现脏数据，系统数据完整', 'success');
                    }
                } else {
                    showToast('检测失败: ' + result.error, 'error');
                }
            } catch (error) {
                showToast('检测失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // 渲染脏数据表格
        function renderOrphanedData(data) {
            const tbody = document.getElementById('orphaned-data-tbody');
            
            if (data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            未发现脏数据，系统数据完整
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            data.forEach(item => {
                const typeText = item.type === 'inject_config' ? '注入配置' : 
                               item.type === 'route_rule' ? '路由规则' : item.type;
                const typeColor = item.type === 'inject_config' ? 'text-yellow-600' : 'text-red-600';
                
                html += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeColor} bg-gray-100">
                                ${typeText}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.id}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.site_id}</td>
                        <td class="px-6 py-4 text-sm text-gray-900">${item.description}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.created_at}</td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
        }

        // 清理所有脏数据
        async function cleanupAllOrphanedData() {
            if (!confirm('确定要清理所有脏数据吗？此操作不可逆！')) {
                return;
            }

            try {
                showLoading();
                const response = await fetch('/api/v1/sites/cleanup-all-orphaned', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();
                
                if (result.success) {
                    showToast(result.message || '清理完成', 'success');
                    // 重新检测
                    setTimeout(() => loadOrphanedData(), 1000);
                } else {
                    showToast('清理失败: ' + result.error, 'error');
                }
            } catch (error) {
                showToast('清理失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // 页面加载时自动检测
        document.addEventListener('DOMContentLoaded', function() {
            loadOrphanedData();
        });
    </script>
</body>
</html>