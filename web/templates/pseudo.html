<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>伪原创库 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* 紧凑的词库卡片样式 */
        .library-card {
            padding: 8px 10px;
            margin-bottom: 4px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }
        .library-card:hover {
            background-color: #f9fafb;
            border-color: #3b82f6;
        }
        .library-card.active {
            background-color: #eff6ff;
            border-color: #3b82f6;
        }
        .library-actions {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 4px;
        }
        .library-actions button {
            padding: 2px 6px;
            font-size: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 3px;
            background: white;
            transition: all 0.2s;
        }
        .library-actions button:hover {
            background-color: #f3f4f6;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">伪原创库</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="showAddLibraryModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i>
                            创建词库
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- 词库列表面板 -->
                    <div class="lg:col-span-1 bg-white rounded-lg shadow">
                        <div class="px-4 py-3 border-b">
                            <h3 class="text-base font-semibold text-gray-800">词库列表</h3>
                        </div>
                        <div class="p-3 max-h-[700px] overflow-y-auto" id="libraries-list">
                            <div class="flex justify-center py-8">
                                <div class="text-gray-500">加载中...</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 词条管理面板 -->
                    <div class="lg:col-span-3 bg-white rounded-lg shadow">
                        <div id="library-detail" class="hidden">
                            <div class="px-6 py-4 border-b flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-800" id="library-name">选择词库查看词条</h3>
                                <div class="flex space-x-2">
                                    <button onclick="showAddWordModal()" class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
                                        <i class="fas fa-plus mr-1"></i>添加词条
                                    </button>
                                    <button onclick="showImportWordsModal()" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
                                        <i class="fas fa-upload mr-1"></i>批量导入
                                    </button>
                                </div>
                            </div>
                            <div class="p-4 border-b">
                                <div class="relative">
                                    <input type="text" id="search-words" placeholder="搜索词条..." onkeyup="searchWords(event)"
                                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                                </div>
                            </div>
                            <div class="p-6" id="words-list">
                                <div class="text-center py-12 text-gray-500">暂无词条</div>
                            </div>
                            <!-- 分页 -->
                            <div class="px-6 py-4 border-t" id="words-pagination-container" style="display: none;">
                                <div id="words-pagination"></div>
                            </div>
                        </div>
                        <div id="no-library-selected" class="p-12 text-center text-gray-500">
                            <i class="fas fa-folder-open text-6xl mb-4 text-gray-300"></i>
                            <p>请选择一个词库查看词条</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <!-- 创建/编辑词库模态框 -->
    <div id="library-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold" id="library-modal-title">创建词库</h3>
                    <button onclick="closeLibraryModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="library-form" class="p-6">
                    <input type="hidden" id="library-id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">词库名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="library-name-input" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：基础同义词库">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">词库描述</label>
                        <textarea id="library-description" rows="3"
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="描述词库的用途和特点"></textarea>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeLibraryModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveLibrary()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑词条模态框 -->
    <div id="word-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold" id="word-modal-title">添加词条</h3>
                    <button onclick="closeWordModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="word-form" class="p-6">
                    <input type="hidden" id="word-id">
                    <input type="hidden" id="word-library-id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">原词 <span class="text-red-500">*</span></label>
                        <input type="text" id="word-original" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：优秀">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">同义词 <span class="text-red-500">*</span></label>
                        <textarea id="word-synonyms" rows="4" required
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="每行一个同义词，例如：&#10;卓越&#10;杰出&#10;出色"></textarea>
                        <p class="mt-1 text-xs text-gray-600">请输入同义词，每行一个</p>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeWordModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveWord()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 批量导入词条模态框 -->
    <div id="import-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">批量导入词条</h3>
                    <button onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="import-form" class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">导入格式说明</label>
                        <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-800">
                            每行一个词条，格式：原词|同义词1,同义词2,同义词3<br>
                            例如：<br>
                            优秀|卓越,杰出,出色<br>
                            重要|关键,核心,主要
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">词条内容 <span class="text-red-500">*</span></label>
                        <textarea id="import-content" rows="10" required
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="优秀|卓越,杰出,出色&#10;重要|关键,核心,主要"></textarea>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeImportModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="importWords()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        导入
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        let currentLibraryId = null;
        let currentPage = 1;
        let pageSize = 20;
        let totalWords = 0;
        let currentSearch = '';
        
        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 兼容旧的通知函数
        function showSuccess(message) { showToast(message, 'success'); }
        function showError(message) { showToast(message, 'error'); }
        function showWarning(message) { showToast(message, 'warning'); }
        function showInfo(message) { showToast(message, 'info'); }

        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadLibraries();
        });
        
        // 加载词库列表
        async function loadLibraries() {
            try {
                const res = await fetch('/api/v1/pseudo/libraries');
                const data = await res.json();
                
                if (data.success) {
                    renderLibraries(data.data || []);
                } else {
                    showError(data.error || '加载词库失败');
                }
            } catch (error) {
                showError('加载词库失败: ' + error.message);
            }
        }
        
        // 渲染词库列表
        function renderLibraries(libraries) {
            const container = document.getElementById('libraries-list');
            
            if (!libraries || libraries.length === 0) {
                container.innerHTML = '<div class="text-center py-8 text-gray-500">暂无词库</div>';
                return;
            }
            
            container.innerHTML = libraries.map(lib => `
                <div class="library-card ${lib.id === currentLibraryId ? 'active' : ''}" 
                     onclick="selectLibrary(${lib.id})">
                    <div class="pr-20">
                        <h4 class="text-sm font-medium text-gray-900">${lib.name}</h4>
                        <p class="text-xs text-gray-500 mt-1 truncate">${lib.description || '暂无描述'}</p>
                    </div>
                    <div class="library-actions">
                        <button onclick="editLibrary(event, ${lib.id})" title="编辑">
                            <i class="fas fa-edit text-xs"></i>
                        </button>
                        <button onclick="deleteLibrary(event, ${lib.id})" title="删除">
                            <i class="fas fa-trash text-xs"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        // 选择词库
        async function selectLibrary(libraryId) {
            currentLibraryId = libraryId;
            currentPage = 1; // 重置分页
            
            // 更新UI
            document.querySelectorAll('[onclick*="selectLibrary"]').forEach(item => {
                if (item.onclick.toString().includes(libraryId)) {
                    item.classList.add('bg-blue-50', 'border-blue-500');
                    item.classList.remove('border-gray-200');
                } else {
                    item.classList.remove('bg-blue-50', 'border-blue-500');
                    item.classList.add('border-gray-200');
                }
            });
            
            document.getElementById('library-detail').classList.remove('hidden');
            document.getElementById('no-library-selected').classList.add('hidden');
            
            // 加载词库详情
            try {
                const res = await fetch(`/api/v1/pseudo/libraries/${libraryId}`);
                const data = await res.json();
                
                if (data.success) {
                    document.getElementById('library-name').textContent = data.data.name;
                    document.getElementById('word-library-id').value = libraryId;
                    
                    // 加载词条
                    loadWords(libraryId);
                }
            } catch (error) {
                showError('加载词库详情失败: ' + error.message);
            }
        }
        
        // 加载词条列表
        async function loadWords(libraryId, page = 1, search = '') {
            try {
                const params = new URLSearchParams({
                    page: page,
                    limit: pageSize
                });
                
                if (search) {
                    params.append('search', search);
                }
                
                const res = await fetch(`/api/v1/pseudo/libraries/${libraryId}/words?${params}`);
                const data = await res.json();
                
                if (data.success) {
                    const words = data.data || [];
                    totalWords = data.pagination?.total || 0;
                    currentPage = page;
                    currentSearch = search;
                    renderWords(words);
                }
            } catch (error) {
                showError('加载词条失败: ' + error.message);
            }
        }
        
        // 渲染词条列表
        function renderWords(words) {
            const container = document.getElementById('words-list');
            const paginationContainer = document.getElementById('words-pagination-container');
            
            if (!words || words.length === 0) {
                container.innerHTML = '<div class="text-center py-12 text-gray-500">暂无词条</div>';
                paginationContainer.style.display = 'none';
                return;
            }
            
            container.innerHTML = `
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead class="bg-gray-50 border-b">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原词</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">同义词</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${words.map(word => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${word.original}</td>
                                    <td class="px-6 py-4 text-sm text-gray-500">${word.synonyms.join('、')}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <button class="text-blue-600 hover:text-blue-800 mr-3" onclick="editWord(${word.id})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="text-red-600 hover:text-red-800" onclick="deleteWord(${word.id})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            // 显示分页
            const totalPages = Math.ceil(totalWords / pageSize);
            if (totalPages > 1) {
                renderWordsPagination(totalWords);
                paginationContainer.style.display = 'block';
            } else {
                paginationContainer.style.display = 'none';
            }
        }
        
        // 渲染词条分页
        function renderWordsPagination(total) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = document.getElementById('words-pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let html = '<div class="flex items-center justify-between">';
            
            // 左侧统计信息
            const startItem = (currentPage - 1) * pageSize + 1;
            const endItem = Math.min(currentPage * pageSize, total);
            html += `<div class="text-sm text-gray-600">
                        显示 ${startItem} 到 ${endItem} 条，共 ${total} 条记录
                     </div>`;
            
            // 右侧分页按钮
            html += '<div class="flex items-center">';
            
            // 上一页
            if (currentPage > 1) {
                html += `<button onclick="changeWordsPage(${currentPage - 1})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               <i class="fas fa-chevron-left mr-1"></i>上一页
                         </button>`;
            } else {
                html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-l-md cursor-not-allowed">
                               <i class="fas fa-chevron-left mr-1"></i>上一页
                         </span>`;
            }
            
            // 计算显示的页码范围
            const maxVisible = 5; // 最多显示5个页码
            let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let endPage = Math.min(totalPages, startPage + maxVisible - 1);
            
            // 调整起始页，确保显示足够的页码
            if (endPage - startPage + 1 < maxVisible) {
                startPage = Math.max(1, endPage - maxVisible + 1);
            }
            
            // 如果不是从第1页开始，显示第1页和省略号
            if (startPage > 1) {
                html += `<button onclick="changeWordsPage(1)" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               1
                         </button>`;
                if (startPage > 2) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border-t border-b border-gray-300">
                                   <i class="fas fa-ellipsis-h"></i>
                             </span>`;
                }
            }
            
            // 显示页码
            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 border-t border-b border-blue-600">
                                   ${i}
                             </span>`;
                } else {
                    html += `<button onclick="changeWordsPage(${i})" 
                                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                                   ${i}
                             </button>`;
                }
            }
            
            // 如果不是到最后一页，显示省略号和最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border-t border-b border-gray-300">
                                   <i class="fas fa-ellipsis-h"></i>
                             </span>`;
                }
                html += `<button onclick="changeWordsPage(${totalPages})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               ${totalPages}
                         </button>`;
            }
            
            // 下一页
            if (currentPage < totalPages) {
                html += `<button onclick="changeWordsPage(${currentPage + 1})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               下一页<i class="fas fa-chevron-right ml-1"></i>
                         </button>`;
            } else {
                html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-r-md cursor-not-allowed">
                               下一页<i class="fas fa-chevron-right ml-1"></i>
                         </span>`;
            }
            
            html += '</div></div>';
            
            pagination.innerHTML = html;
        }
        
        // 切换页面
        function changeWordsPage(page) {
            if (currentLibraryId) {
                loadWords(currentLibraryId, page, currentSearch);
            }
        }
        
        // 搜索词条
        function searchWords(event) {
            const keyword = event.target.value.trim();
            
            // 重置到第一页
            currentPage = 1;
            currentSearch = keyword;
            
            // 使用服务器端分页和搜索
            if (currentLibraryId) {
                loadWords(currentLibraryId, 1, keyword);
            }
        }
        
        // 显示添加词库模态框
        function showAddLibraryModal() {
            document.getElementById('library-modal-title').textContent = '创建词库';
            document.getElementById('library-form').reset();
            document.getElementById('library-id').value = '';
            document.getElementById('library-modal').classList.remove('hidden');
        }
        
        // 编辑词库
        async function editLibrary(event, libraryId) {
            event.stopPropagation();
            
            try {
                const res = await fetch(`/api/v1/pseudo/libraries/${libraryId}`);
                const data = await res.json();
                
                if (data.success) {
                    document.getElementById('library-modal-title').textContent = '编辑词库';
                    document.getElementById('library-id').value = libraryId;
                    document.getElementById('library-name-input').value = data.data.name;
                    document.getElementById('library-description').value = data.data.description || '';
                    document.getElementById('library-modal').classList.remove('hidden');
                }
            } catch (error) {
                showError('加载词库信息失败: ' + error.message);
            }
        }
        
        // 保存词库
        async function saveLibrary() {
            const id = document.getElementById('library-id').value;
            const isEdit = !!id;
            
            const libraryData = {
                name: document.getElementById('library-name-input').value,
                description: document.getElementById('library-description').value
            };
            
            try {
                const url = isEdit ? `/api/v1/pseudo/libraries/${id}` : '/api/v1/pseudo/libraries';
                const method = isEdit ? 'PUT' : 'POST';
                
                const res = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(libraryData)
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(isEdit ? '更新成功' : '创建成功');
                    closeLibraryModal();
                    loadLibraries();
                } else {
                    showError(data.error || '保存失败');
                }
            } catch (error) {
                showError('保存失败: ' + error.message);
            }
        }
        
        // 删除词库
        async function deleteLibrary(event, libraryId) {
            event.stopPropagation();
            
            if (!confirm('确定要删除这个词库吗？删除后将无法恢复。')) return;
            
            try {
                const res = await fetch(`/api/v1/pseudo/libraries/${libraryId}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('删除成功');
                    if (currentLibraryId === libraryId) {
                        currentLibraryId = null;
                        document.getElementById('library-detail').classList.add('hidden');
                        document.getElementById('no-library-selected').classList.remove('hidden');
                    }
                    loadLibraries();
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 显示添加词条模态框
        function showAddWordModal() {
            document.getElementById('word-modal-title').textContent = '添加词条';
            document.getElementById('word-form').reset();
            document.getElementById('word-id').value = '';
            document.getElementById('word-library-id').value = currentLibraryId;
            document.getElementById('word-modal').classList.remove('hidden');
        }
        
        // 编辑词条
        async function editWord(wordId) {
            try {
                // 通过API获取词条详情
                const response = await fetch(`/api/v1/pseudo/words/${wordId}`);
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || '获取词条失败');
                }
                
                const result = await response.json();
                const word = result.data;
                
                document.getElementById('word-modal-title').textContent = '编辑词条';
                document.getElementById('word-id').value = wordId;
                document.getElementById('word-original').value = word.original;
                document.getElementById('word-synonyms').value = word.synonyms.join('\n');
                document.getElementById('word-modal').classList.remove('hidden');
            } catch (error) {
                console.error('编辑词条失败:', error);
                showToast('获取词条信息失败', 'error');
            }
        }
        
        // 保存词条
        async function saveWord() {
            const wordId = document.getElementById('word-id').value;
            const isEdit = !!wordId;
            
            const synonymsText = document.getElementById('word-synonyms').value;
            const synonyms = synonymsText.split('\n').map(s => s.trim()).filter(s => s);
            
            if (synonyms.length === 0) {
                showError('请至少输入一个同义词');
                return;
            }
            
            const wordData = {
                original: document.getElementById('word-original').value,
                synonyms: synonyms
            };
            
            try {
                let url, method;
                if (isEdit) {
                    url = `/api/v1/pseudo/words/${wordId}`;
                    method = 'PUT';
                } else {
                    url = `/api/v1/pseudo/libraries/${currentLibraryId}/words`;
                    method = 'POST';
                }
                
                const res = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(wordData)
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(isEdit ? '更新成功' : '添加成功');
                    closeWordModal();
                    loadWords(currentLibraryId);
                } else {
                    showError(data.error || '保存失败');
                }
            } catch (error) {
                showError('保存失败: ' + error.message);
            }
        }
        
        // 删除词条
        async function deleteWord(wordId) {
            if (!confirm('确定要删除这个词条吗？')) return;
            
            try {
                const res = await fetch(`/api/v1/pseudo/words/${wordId}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('删除成功');
                    loadWords(currentLibraryId);
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 显示批量导入模态框
        function showImportWordsModal() {
            document.getElementById('import-form').reset();
            document.getElementById('import-modal').classList.remove('hidden');
        }
        
        // 批量导入词条
        async function importWords() {
            const content = document.getElementById('import-content').value;
            
            if (!content.trim()) {
                showError('请输入要导入的词条');
                return;
            }
            
            try {
                const res = await fetch(`/api/v1/pseudo/libraries/${currentLibraryId}/import`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ content: content })
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(`成功导入 ${data.data.imported_count} 个词条`);
                    closeImportModal();
                    loadWords(currentLibraryId);
                } else {
                    showError(data.error || '导入失败');
                }
            } catch (error) {
                showError('导入失败: ' + error.message);
            }
        }
        
        // 关闭模态框
        function closeLibraryModal() {
            document.getElementById('library-modal').classList.add('hidden');
        }
        
        function closeWordModal() {
            document.getElementById('word-modal').classList.add('hidden');
        }
        
        function closeImportModal() {
            document.getElementById('import-modal').classList.add('hidden');
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.id === 'library-modal' || event.target.id === 'word-modal' || event.target.id === 'import-modal') {
                event.target.classList.add('hidden');
            }
        }
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
