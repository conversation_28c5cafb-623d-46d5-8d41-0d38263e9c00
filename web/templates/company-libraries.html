<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业库 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* 词库项样式 - 更紧凑的设计 */
        .library-item {
            padding: 8px 10px;
            margin-bottom: 4px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }
        .library-item:hover {
            background-color: #f9fafb;
            border-color: #3b82f6;
        }
        .library-item.active {
            background-color: #eff6ff;
            border-color: #3b82f6;
        }
        .library-name {
            font-weight: 500;
            color: #1f2937;
            font-size: 14px;
            margin-bottom: 2px;
        }
        .library-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
        }
        .library-type {
            padding: 2px 6px;
            background-color: #f3f4f6;
            border-radius: 4px;
        }
        .library-count::after {
            content: ' 个企业';
        }
        .library-actions {
            display: flex;
            gap: 4px;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
        }
        .btn-icon {
            padding: 2px 6px;
            background-color: transparent;
            border: 1px solid #e5e7eb;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;
        }
        .btn-icon:hover {
            background-color: #f3f4f6;
            border-color: #9ca3af;
        }
        .icon-edit::before {
            content: '✏️';
        }
        .icon-trash::before {
            content: '🗑️';
        }
        
        /* 企业名称表格样式 */
        .company-table {
            width: 100%;
            border-collapse: collapse;
        }
        .company-table thead {
            background-color: #f9fafb;
        }
        .company-table th {
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        .company-table td {
            padding: 12px;
            color: #4b5563;
            border-bottom: 1px solid #f3f4f6;
        }
        .company-table tbody tr:hover {
            background-color: #f9fafb;
        }
        
        /* 空状态样式 */
        .empty {
            text-align: center;
            padding: 32px;
            color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">企业库</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="showAddLibraryModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i>
                            创建词库
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- 左侧：词库列表 -->
                    <div class="lg:col-span-1 bg-white rounded-lg shadow">
                        <div class="px-4 py-3 border-b">
                            <h3 class="text-base font-semibold text-gray-800">词库列表</h3>
                        </div>
                        <div class="p-3 max-h-[700px] overflow-y-auto" id="library-list">
                            <div class="flex justify-center py-8">
                                <div class="text-gray-500">加载中...</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧：企业名称列表 -->
                    <div class="lg:col-span-3 bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-800" id="company-title">请选择一个词库</h3>
                            <div id="company-actions" class="hidden space-x-2">
                                <button onclick="showImportModal()" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
                                    <i class="fas fa-upload mr-1"></i>导入
                                </button>
                                <button onclick="exportCompanies()" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
                                    <i class="fas fa-download mr-1"></i>导出
                                </button>
                                <button onclick="showAddCompanyModal()" class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600">
                                    <i class="fas fa-plus mr-1"></i>添加企业
                                </button>
                            </div>
                        </div>
                        <div class="p-6" id="company-list">
                            <div class="text-center py-12 text-gray-500">请从左侧选择一个词库</div>
                        </div>
                        <div class="px-6 py-4 border-t flex justify-center" id="company-pagination"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <!-- 创建词库模态框 -->
    <div id="library-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold" id="library-modal-title">创建词库</h3>
                    <button onclick="closeLibraryModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="library-form" class="p-6">
                    <input type="hidden" id="library-id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">词库名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="library-name" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：科技企业库">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                        <textarea id="library-description" rows="3"
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="词库用途说明..."></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">类型</label>
                        <select id="library-type" onchange="updateLibraryTypeHelp()"
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="general">通用</option>
                            <option value="tech">科技</option>
                            <option value="edu">教育</option>
                            <option value="trade">商贸</option>
                            <option value="service">服务</option>
                            <option value="finance">金融</option>
                            <option value="medical">医疗</option>
                            <option value="other">其他</option>
                        </select>
                        <div id="library-type-help" class="mt-2 text-sm text-gray-600">
                            <strong>通用：</strong>适用于各行业的企业名称。
                        </div>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeLibraryModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveLibrary()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加企业名称模态框 -->
    <div id="company-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">添加企业名称</h3>
                    <button onclick="closeCompanyModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="company-form" class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">企业名称 <span class="text-red-500">*</span></label>
                        <textarea id="company-text" rows="8" required
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="每行输入一个企业名称，例如：&#10;深圳创新科技有限公司&#10;北京智慧教育有限公司&#10;上海云计算服务有限公司"></textarea>
                        <p class="mt-1 text-sm text-gray-600">支持批量添加，每行一个企业名称</p>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">行业</label>
                            <input type="text" id="company-industry"
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="例如：科技">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">地区</label>
                            <input type="text" id="company-region"
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="例如：深圳">
                        </div>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeCompanyModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveCompanies()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 导入企业名称模态框 -->
    <div id="import-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">导入企业名称</h3>
                    <button onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="p-6">
                    <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg text-sm text-blue-800">
                        <strong>导入格式说明：</strong><br>
                        每行一个企业名称，支持TXT文件批量导入。<br>
                        <strong>示例：</strong><br>
                        深圳创新科技有限公司<br>
                        北京智慧教育有限公司<br>
                        上海云计算服务有限公司
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
                        <input type="file" id="import-file" accept=".txt"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="mt-1 text-sm text-gray-600">支持TXT文件，格式见上方说明</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">或直接粘贴</label>
                        <textarea id="import-text" rows="10"
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="请按照上方格式说明粘贴内容"></textarea>
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeImportModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="importCompanies()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        导入
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script src="/static/js/admin.js"></script>
    <script>
        let currentLibraryId = null;
        let currentLibraryType = 'general';
        let currentPage = 1;
        const pageSize = 20;
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadLibraries();
            updateSystemTime();
        });
        
        // 更新系统时间
        function updateSystemTime() {
            const timeElement = document.getElementById('system-time');
            if (timeElement) {
                const updateTime = () => {
                    const now = new Date();
                    timeElement.textContent = now.toLocaleString('zh-CN');
                };
                updateTime();
                setInterval(updateTime, 1000);
            }
        }
        
        // 更新词库类型帮助信息
        function updateLibraryTypeHelp() {
            const type = document.getElementById('library-type').value;
            const helpDiv = document.getElementById('library-type-help');
            
            const helpTexts = {
                'general': '<strong>通用：</strong>适用于各行业的企业名称。',
                'tech': '<strong>科技：</strong>适用于科技类企业名称。',
                'edu': '<strong>教育：</strong>适用于教育培训类企业名称。',
                'trade': '<strong>商贸：</strong>适用于商贸类企业名称。',
                'service': '<strong>服务：</strong>适用于服务类企业名称。',
                'finance': '<strong>金融：</strong>适用于金融类企业名称。',
                'medical': '<strong>医疗：</strong>适用于医疗健康类企业名称。',
                'other': '<strong>其他：</strong>适用于其他行业企业名称。'
            };
            
            helpDiv.innerHTML = helpTexts[type] || '';
        }
        
        // 加载词库列表
        async function loadLibraries() {
            try {
                const res = await fetch('/api/v1/company/libraries');
                const data = await res.json();
                
                if (data.success) {
                    renderLibraries(data.data);
                }
            } catch (error) {
                showError('加载词库失败: ' + error.message);
            }
        }
        
        // 渲染词库列表
        function renderLibraries(libraries) {
            const container = document.getElementById('library-list');
            
            if (!libraries || libraries.length === 0) {
                container.innerHTML = '<div class="empty">暂无词库</div>';
                return;
            }
            
            container.innerHTML = libraries.map(lib => `
                <div class="library-item ${currentLibraryId === lib.id ? 'active' : ''}" 
                     onclick="selectLibrary(${lib.id}, '${lib.name}', event)">
                    <div class="flex justify-between items-start pr-20">
                        <div>
                            <div class="library-name">${lib.name}</div>
                            <div class="library-meta">
                                <span class="library-type">${getLibraryTypeText(lib.type)}</span>
                                <span class="library-count" id="count-${lib.id}">${lib.company_count || 0}</span>
                            </div>
                        </div>
                    </div>
                    <div class="library-actions">
                        <button class="btn-icon" onclick="editLibrary(${lib.id}, event)" title="编辑">
                            <i class="icon-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="deleteLibrary(${lib.id}, event)" title="删除">
                            <i class="icon-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }
        
        // 选择词库
        async function selectLibrary(libraryId, libraryName, event) {
            // 阻止事件冒泡
            if (event) {
                event.stopPropagation();
            }
            
            currentLibraryId = libraryId;
            currentPage = 1;
            
            // 获取词库详情以获取类型
            try {
                const res = await fetch('/api/v1/company/libraries');
                const data = await res.json();
                if (data.success) {
                    const library = data.data.find(lib => lib.id === libraryId);
                    if (library) {
                        currentLibraryType = library.type;
                    }
                }
            } catch (error) {
                console.error('获取词库类型失败:', error);
            }
            
            // 更新UI
            document.querySelectorAll('.library-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 找到当前点击的元素并添加active类
            const clickedItem = event ? event.currentTarget : document.querySelector(`.library-item[onclick*="${libraryId}"]`);
            if (clickedItem) {
                clickedItem.classList.add('active');
            }
            
            document.getElementById('company-title').textContent = libraryName;
            document.getElementById('company-actions').classList.remove('hidden');
            
            loadCompanies();
        }
        
        // 加载企业名称列表
        async function loadCompanies(page = 1) {
            if (!currentLibraryId) return;
            
            currentPage = page;
            
            try {
                const res = await fetch(`/api/v1/company/libraries/${currentLibraryId}/names?page=${page}&limit=${pageSize}`);
                const data = await res.json();
                
                if (data.success) {
                    renderCompanies(data.data);
                    renderPagination(data.total);
                }
            } catch (error) {
                showError('加载企业名称失败: ' + error.message);
            }
        }
        
        // 渲染企业名称列表
        function renderCompanies(companies) {
            const container = document.getElementById('company-list');
            
            if (!companies || companies.length === 0) {
                container.innerHTML = '<div class="empty">暂无企业名称</div>';
                return;
            }
            
            container.innerHTML = `
                <table class="company-table">
                    <thead>
                        <tr>
                            <th>企业名称</th>
                            <th>行业</th>
                            <th>地区</th>
                            <th>使用次数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${companies.map(company => `
                            <tr>
                                <td>${company.name}</td>
                                <td>${company.industry || '-'}</td>
                                <td>${company.region || '-'}</td>
                                <td>${company.use_count || 0}</td>
                                <td>${formatDate(company.created_at)}</td>
                                <td>
                                    <button class="btn-icon" onclick="deleteCompany(${company.id})" title="删除">
                                        <i class="icon-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }
        
        // 渲染分页
        function renderPagination(total) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = document.getElementById('company-pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let html = '<div class="flex space-x-2">';
            
            if (currentPage > 1) {
                html += `<button onclick="loadCompanies(${currentPage - 1})" class="px-3 py-1 border rounded hover:bg-gray-100">上一页</button>`;
            }
            
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    const active = i === currentPage ? 'bg-blue-500 text-white' : 'hover:bg-gray-100';
                    html += `<button onclick="loadCompanies(${i})" class="px-3 py-1 border rounded ${active}">${i}</button>`;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    html += '<span class="px-2">...</span>';
                }
            }
            
            if (currentPage < totalPages) {
                html += `<button onclick="loadCompanies(${currentPage + 1})" class="px-3 py-1 border rounded hover:bg-gray-100">下一页</button>`;
            }
            
            html += '</div>';
            pagination.innerHTML = html;
        }
        
        // 显示创建词库模态框
        function showAddLibraryModal() {
            document.getElementById('library-modal-title').textContent = '创建词库';
            document.getElementById('library-form').reset();
            document.getElementById('library-id').value = '';
            document.getElementById('library-modal').classList.remove('hidden');
        }
        
        // 关闭词库模态框
        function closeLibraryModal() {
            document.getElementById('library-modal').classList.add('hidden');
        }
        
        // 保存词库
        async function saveLibrary() {
            const id = document.getElementById('library-id').value;
            const isEdit = !!id;
            
            const libraryData = {
                name: document.getElementById('library-name').value,
                description: document.getElementById('library-description').value,
                type: document.getElementById('library-type').value
            };
            
            try {
                const url = isEdit ? `/api/v1/company/libraries/${id}` : '/api/v1/company/libraries';
                const method = isEdit ? 'PUT' : 'POST';
                
                const res = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(libraryData)
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(isEdit ? '更新成功' : '创建成功');
                    closeLibraryModal();
                    loadLibraries();
                } else {
                    showError(data.error || '保存失败');
                }
            } catch (error) {
                showError('保存失败: ' + error.message);
            }
        }
        
        // 编辑词库
        async function editLibrary(id, event) {
            event.stopPropagation();
            
            try {
                const res = await fetch(`/api/v1/company/libraries/${id}`);
                const data = await res.json();
                
                if (data.success) {
                    const library = data.data;
                    document.getElementById('library-modal-title').textContent = '编辑词库';
                    document.getElementById('library-id').value = library.id;
                    document.getElementById('library-name').value = library.name;
                    document.getElementById('library-description').value = library.description || '';
                    document.getElementById('library-type').value = library.type;
                    updateLibraryTypeHelp();
                    document.getElementById('library-modal').classList.remove('hidden');
                }
            } catch (error) {
                showError('加载词库信息失败: ' + error.message);
            }
        }
        
        // 删除词库
        async function deleteLibrary(id, event) {
            event.stopPropagation();
            
            if (!confirm('确定要删除这个词库吗？相关的所有企业名称也会被删除。')) return;
            
            try {
                const res = await fetch(`/api/v1/company/libraries/${id}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('删除成功');
                    if (currentLibraryId === id) {
                        currentLibraryId = null;
                        document.getElementById('company-title').textContent = '请选择一个词库';
                        document.getElementById('company-actions').classList.add('hidden');
                        document.getElementById('company-list').innerHTML = '<div class="empty">请从左侧选择一个词库</div>';
                    }
                    loadLibraries();
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 显示添加企业名称模态框
        function showAddCompanyModal() {
            if (!currentLibraryId) {
                showWarning('请先选择一个词库');
                return;
            }
            
            document.getElementById('company-form').reset();
            document.getElementById('company-modal').classList.remove('hidden');
        }
        
        // 关闭企业名称模态框
        function closeCompanyModal() {
            document.getElementById('company-modal').classList.add('hidden');
        }
        
        // 保存企业名称
        async function saveCompanies() {
            const companiesText = document.getElementById('company-text').value;
            const industry = document.getElementById('company-industry').value;
            const region = document.getElementById('company-region').value;
            
            const lines = companiesText.split('\n').filter(c => c.trim());
            
            if (lines.length === 0) {
                showWarning('请输入至少一个企业名称');
                return;
            }
            
            try {
                // 批量添加
                const res = await fetch(`/api/v1/company/libraries/${currentLibraryId}/batch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        names: companiesText,
                        industry: industry,
                        region: region
                    })
                });
                
                const data = await res.json();
                if (data.success) {
                    showSuccess(`成功添加企业名称`);
                    closeCompanyModal();
                    loadCompanies(currentPage);
                    loadLibraries(); // 更新计数
                } else {
                    showError(data.error || '添加失败');
                }
            } catch (error) {
                showError('添加失败: ' + error.message);
            }
        }
        
        // 删除企业名称
        async function deleteCompany(id) {
            if (!confirm('确定要删除这个企业名称吗？')) return;
            
            try {
                const res = await fetch(`/api/v1/company/names/${id}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('删除成功');
                    loadCompanies(currentPage);
                    loadLibraries(); // 更新计数
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 显示导入模态框
        function showImportModal() {
            if (!currentLibraryId) {
                showWarning('请先选择一个词库');
                return;
            }
            
            document.getElementById('import-file').value = '';
            document.getElementById('import-text').value = '';
            document.getElementById('import-modal').classList.remove('hidden');
        }
        
        // 关闭导入模态框
        function closeImportModal() {
            document.getElementById('import-modal').classList.add('hidden');
        }
        
        // 导入企业名称
        async function importCompanies() {
            const file = document.getElementById('import-file').files[0];
            const text = document.getElementById('import-text').value;
            
            let companies = [];
            
            if (file) {
                const fileText = await file.text();
                companies = fileText.split('\n').filter(c => c.trim());
            } else if (text) {
                companies = text.split('\n').filter(c => c.trim());
            } else {
                showWarning('请选择文件或输入企业名称');
                return;
            }
            
            if (companies.length === 0) {
                showWarning('没有找到有效的企业名称');
                return;
            }
            
            try {
                const res = await fetch(`/api/v1/company/libraries/${currentLibraryId}/batch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        names: companies.join('\n')
                    })
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(`成功导入企业名称`);
                    closeImportModal();
                    loadCompanies(1);
                    loadLibraries(); // 更新计数
                } else {
                    showError(data.error || '导入失败');
                }
            } catch (error) {
                showError('导入失败: ' + error.message);
            }
        }
        
        // 导出企业名称
        function exportCompanies() {
            if (!currentLibraryId) {
                showWarning('请先选择一个词库');
                return;
            }
            
            window.open(`/api/v1/company/libraries/${currentLibraryId}/export`, '_blank');
        }
        
        // 获取词库类型文本
        function getLibraryTypeText(type) {
            const typeMap = {
                'general': '通用',
                'tech': '科技',
                'edu': '教育',
                'trade': '商贸',
                'service': '服务',
                'finance': '金融',
                'medical': '医疗',
                'other': '其他'
            };
            return typeMap[type] || type;
        }
        
        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleDateString('zh-CN');
        }
        
        // Toast 通知函数
        function showSuccess(message) {
            showToast(message, 'success');
        }
        
        function showError(message) {
            showToast(message, 'error');
        }
        
        function showWarning(message) {
            showToast(message, 'warning');
        }
        
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColors = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            };
            
            toast.className = `${bgColors[type]} text-white px-6 py-3 rounded-lg shadow-lg mb-2 animate-fade-in`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('animate-fade-out');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
