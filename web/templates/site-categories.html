<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站点分类管理 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* 加载动画 */
        .loader {
            border-top-color: #3B82F6;
            -webkit-animation: spinner 1.5s linear infinite;
            animation: spinner 1.5s linear infinite;
        }
        @-webkit-keyframes spinner {
            0% { -webkit-transform: rotate(0deg); }
            100% { -webkit-transform: rotate(360deg); }
        }
        @keyframes spinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
            <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
            <!-- 底部管理员信息 -->
            <div class="absolute bottom-0 w-64 p-4 border-t border-gray-700">
                <div class="flex items-center justify-between">
                    <span class="text-sm">管理员</span>
                    <button onclick="logout()" class="text-sm text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>退出
                    </button>
                </div>
            </div>
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部栏 -->
            <header class="bg-white shadow-sm">
                <div class="px-8 py-4 flex items-center justify-between">
                    <h1 class="text-2xl font-semibold text-gray-800">站点分类管理</h1>
                    <span class="text-sm text-gray-500" id="system-time"></span>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 操作栏 -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-700">分类列表</h2>
                            <p class="text-sm text-gray-500 mt-1">管理站点分类，支持自定义图标和颜色</p>
                        </div>
                        <button onclick="showAddModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            添加分类
                        </button>
                    </div>
                </div>

                <!-- 分类列表 -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <table class="min-w-full">
                        <thead class="bg-gray-50 border-b">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排序</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">图标</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">站点数</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">独立统计</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">颜色</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="categoryList" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="8" class="px-6 py-12 text-center">
                                    <div class="flex justify-center">
                                        <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-12 w-12"></div>
                                    </div>
                                    <p class="text-gray-500 mt-4">加载中...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- 添加/编辑分类模态框 -->
    <div id="categoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-lg flex flex-col" style="max-height: 90vh;">
            <!-- Header -->
            <div class="px-6 py-4 border-b flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">添加分类</h3>
                <button onclick="hideModal()" class="text-gray-400 hover:text-gray-600 text-2xl">&times;</button>
            </div>
            
            <!-- Scrollable Content -->
            <div class="p-6 overflow-y-auto">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId">
                    
                    <div class="space-y-5">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">分类名称 *</label>
                            <input type="text" id="categoryName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                            <textarea id="categoryDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">图标</label>
                                <div class="flex items-center space-x-2">
                                    <select id="categoryIcon" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="fa-folder">📁 文件夹</option>
                                        <option value="fa-building">🏢 企业</option>
                                        <option value="fa-shopping-cart">🛒 购物车</option>
                                        <option value="fa-newspaper">📰 新闻</option>
                                        <option value="fa-comments">💬 评论</option>
                                        <option value="fa-graduation-cap">🎓 教育</option>
                                        <option value="fa-globe">🌍 全球</option>
                                        <option value="fa-star">⭐ 星标</option>
                                        <option value="fa-heart">❤️ 爱心</option>
                                        <option value="fa-tag">🏷️ 标签</option>
                                    </select>
                                    <span id="iconPreview" class="text-2xl">
                                        <i class="fas fa-folder"></i>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">颜色</label>
                                <select id="categoryColor" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="gray">灰色</option>
                                    <option value="red">红色</option>
                                    <option value="yellow">黄色</option>
                                    <option value="green">绿色</option>
                                    <option value="blue">蓝色</option>
                                    <option value="indigo">靛蓝</option>
                                    <option value="purple">紫色</option>
                                    <option value="pink">粉色</option>
                                    <option value="teal">青色</option>
                                    <option value="orange">橙色</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">排序</label>
                            <input type="number" id="categorySort" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">数字越小，排序越靠前</p>
                        </div>

                        <!-- 独立统计配置 -->
                        <div class="border-t pt-4">
                            <div class="flex items-center mb-3">
                                <input type="checkbox" id="useIndependentAnalytics" class="mr-2 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="useIndependentAnalytics" class="text-sm font-medium text-gray-700">使用独立统计代码</label>
                            </div>
                            <div id="analyticsCodeSection" class="hidden">
                                <label class="block text-sm font-medium text-gray-700 mb-1">统计代码</label>
                                <textarea id="categoryAnalyticsCode" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入分类独立的统计代码，如百度统计、Google Analytics等"></textarea>
                                <p class="text-xs text-gray-500 mt-1">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    启用后，该分类下的所有站点将使用此统计代码，而不使用全局统计设置
                                </p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Footer -->
            <div class="px-6 py-4 bg-gray-50 border-t rounded-b-lg flex justify-end space-x-3">
                <button type="button" onclick="hideModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
                    取消
                </button>
                <button type="submit" form="categoryForm" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div id="toast" class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg hidden transition-all transform translate-y-full">
        <span id="toastMessage"></span>
    </div>

    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadCategories();
        updateTime();
        setInterval(updateTime, 1000);
        
        // 图标预览
        document.getElementById('categoryIcon').addEventListener('change', function() {
            document.getElementById('iconPreview').innerHTML = `<i class="fas ${this.value}"></i>`;
        });
        
        // 独立统计复选框切换
        document.getElementById('useIndependentAnalytics').addEventListener('change', function() {
            const analyticsSection = document.getElementById('analyticsCodeSection');
            if (this.checked) {
                analyticsSection.classList.remove('hidden');
            } else {
                analyticsSection.classList.add('hidden');
            }
        });
        
        // 表单提交
        document.getElementById('categoryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveCategory();
        });
    });

    // 更新时间
    function updateTime() {
        const now = new Date();
        const timeStr = now.toLocaleString('zh-CN');
        document.getElementById('system-time').textContent = timeStr;
    }

    // 显示通知
    function showNotification(message, type = 'success') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        
        toast.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg transition-all transform text-white`;
        if (type === 'success') {
            toast.classList.add('bg-green-500');
        } else if (type === 'error') {
            toast.classList.add('bg-red-500');
        } else if (type === 'info') {
            toast.classList.add('bg-blue-500');
        }
        toastMessage.textContent = message;
        toast.classList.remove('hidden', 'translate-y-full');
        
        setTimeout(() => {
            toast.classList.add('translate-y-full');
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 300);
        }, 3000);
    }

    // 加载分类列表
    async function loadCategories() {
        try {
            const response = await fetchWithAuth('/api/v1/site-categories');
            const data = await response.json();
            
            if (data.success) {
                displayCategories(data.data || []);
            }
        } catch (error) {
            console.error('加载分类失败:', error);
            showNotification('加载分类失败', 'error');
        }
    }

    // 显示分类列表
    function displayCategories(categories) {
        const tbody = document.getElementById('categoryList');
        
        if (categories.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                        <i class="fas fa-folder-open text-4xl text-gray-300 mb-4"></i>
                        <p>暂无分类数据</p>
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = categories.map(category => {
            const colorMap = {
                'gray': 'text-gray-500',
                'red': 'text-red-500',
                'yellow': 'text-yellow-500',
                'green': 'text-green-500',
                'blue': 'text-blue-500',
                'indigo': 'text-indigo-500',
                'purple': 'text-purple-500',
                'pink': 'text-pink-500',
                'teal': 'text-teal-500',
                'orange': 'text-orange-500'
            };
            
            const bgColorMap = {
                'gray': 'bg-gray-100 text-gray-800',
                'red': 'bg-red-100 text-red-800',
                'yellow': 'bg-yellow-100 text-yellow-800',
                'green': 'bg-green-100 text-green-800',
                'blue': 'bg-blue-100 text-blue-800',
                'indigo': 'bg-indigo-100 text-indigo-800',
                'purple': 'bg-purple-100 text-purple-800',
                'pink': 'bg-pink-100 text-pink-800',
                'teal': 'bg-teal-100 text-teal-800',
                'orange': 'bg-orange-100 text-orange-800'
            };
            
            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${category.sort}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="${colorMap[category.color]} text-2xl">
                            <i class="fas ${category.icon}"></i>
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm font-medium text-gray-900">${escapeHtml(category.name)}</span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                        ${escapeHtml(category.description || '-')}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                            ${category.site_count || 0} 个站点
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${category.use_independent_analytics ? `
                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800" title="已配置独立统计代码">
                                <i class="fas fa-chart-bar mr-1"></i>已启用
                            </span>
                        ` : `
                            <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                                <i class="fas fa-globe mr-1"></i>全局
                            </span>
                        `}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs rounded-full ${bgColorMap[category.color]}">
                            ${category.color}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <button onclick="editCategory(${category.id})" class="text-blue-600 hover:text-blue-900 mr-3" title="编辑分类">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        ${category.use_independent_analytics || category.site_count > 0 ? `
                            <button onclick="refreshCategoryAnalytics(${category.id}, '${escapeHtml(category.name)}')" class="text-green-600 hover:text-green-900 mr-3" title="刷新该分类下所有站点的统计JS">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        ` : ''}
                        ${category.site_count === 0 ? `
                            <button onclick="deleteCategory(${category.id}, '${escapeHtml(category.name)}')" class="text-red-600 hover:text-red-900" title="删除分类">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        ` : `
                            <span class="text-gray-400" title="该分类下有站点，无法删除">
                                <i class="fas fa-trash"></i> 删除
                            </span>
                        `}
                    </td>
                </tr>
            `;
        }).join('');
    }

    // 显示添加模态框
    function showAddModal() {
        document.getElementById('modalTitle').textContent = '添加分类';
        document.getElementById('categoryId').value = '';
        document.getElementById('categoryForm').reset();
        document.getElementById('iconPreview').innerHTML = '<i class="fas fa-folder"></i>';
        document.getElementById('useIndependentAnalytics').checked = false;
        document.getElementById('analyticsCodeSection').classList.add('hidden');
        document.getElementById('categoryModal').classList.remove('hidden');
    }

    // 编辑分类
    async function editCategory(id) {
        try {
            const response = await fetchWithAuth(`/api/v1/site-categories/${id}`);
            const data = await response.json();
            
            if (data.success) {
                const category = data.data;
                document.getElementById('modalTitle').textContent = '编辑分类';
                document.getElementById('categoryId').value = category.id;
                document.getElementById('categoryName').value = category.name;
                document.getElementById('categoryDescription').value = category.description || '';
                document.getElementById('categoryIcon').value = category.icon;
                document.getElementById('categoryColor').value = category.color;
                document.getElementById('categorySort').value = category.sort;
                document.getElementById('iconPreview').innerHTML = `<i class="fas ${category.icon}"></i>`;
                
                // 设置独立统计配置
                document.getElementById('useIndependentAnalytics').checked = category.use_independent_analytics || false;
                document.getElementById('categoryAnalyticsCode').value = category.analytics_code || '';
                if (category.use_independent_analytics) {
                    document.getElementById('analyticsCodeSection').classList.remove('hidden');
                } else {
                    document.getElementById('analyticsCodeSection').classList.add('hidden');
                }
                
                document.getElementById('categoryModal').classList.remove('hidden');
            }
        } catch (error) {
            console.error('获取分类详情失败:', error);
            showNotification('获取分类详情失败', 'error');
        }
    }

    // 保存分类
    async function saveCategory() {
        const id = document.getElementById('categoryId').value;
        const categoryData = {
            name: document.getElementById('categoryName').value,
            description: document.getElementById('categoryDescription').value,
            icon: document.getElementById('categoryIcon').value,
            color: document.getElementById('categoryColor').value,
            sort: parseInt(document.getElementById('categorySort').value) || 0,
            use_independent_analytics: document.getElementById('useIndependentAnalytics').checked,
            analytics_code: document.getElementById('categoryAnalyticsCode').value
        };
        
        try {
            const url = id ? `/api/v1/site-categories/${id}` : '/api/v1/site-categories';
            const method = id ? 'PUT' : 'POST';
            
            const response = await fetchWithAuth(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(categoryData)
            });
            
            const data = await response.json();
            
            if (data.success) {
                showNotification(id ? '分类更新成功' : '分类添加成功', 'success');
                hideModal();
                loadCategories();
            } else {
                showNotification(data.error || '操作失败', 'error');
            }
        } catch (error) {
            console.error('保存分类失败:', error);
            showNotification('保存分类失败', 'error');
        }
    }

    // 刷新分类统计JS
    async function refreshCategoryAnalytics(id, name) {
        if (!confirm(`确定要刷新分类"${name}"下所有站点的统计JS吗？`)) {
            return;
        }
        
        // 显示加载提示
        showNotification('正在刷新统计JS，请稍候...', 'info');
        
        try {
            const response = await fetchWithAuth(`/api/v1/site-categories/${id}/refresh-analytics`, {
                method: 'POST'
            });
            
            const data = await response.json();
            
            if (data.success) {
                showNotification(data.message || '刷新成功', 'success');
            } else {
                showNotification(data.error || '刷新失败', 'error');
            }
        } catch (error) {
            console.error('刷新统计JS失败:', error);
            showNotification('刷新统计JS失败', 'error');
        }
    }

    // 删除分类
    async function deleteCategory(id, name) {
        if (!confirm(`确定要删除分类"${name}"吗？`)) {
            return;
        }
        
        try {
            const response = await fetchWithAuth(`/api/v1/site-categories/${id}`, {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                showNotification('分类删除成功', 'success');
                loadCategories();
            } else {
                showNotification(data.error || '删除失败', 'error');
            }
        } catch (error) {
            console.error('删除分类失败:', error);
            showNotification('删除分类失败', 'error');
        }
    }

    // 隐藏模态框
    function hideModal() {
        document.getElementById('categoryModal').classList.add('hidden');
    }

    // 转义HTML
    function escapeHtml(text) {
        if (!text) return '';
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    // 退出登录
    async function logout() {
        try {
            const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
            if (res.ok) {
                window.location.href = '/login';
            }
        } catch (error) {
            alert('退出登录失败');
        }
    }
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
