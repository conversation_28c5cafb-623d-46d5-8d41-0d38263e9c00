<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存管理 - 站群管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">缓存管理</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="clearExpiredCache()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-trash mr-2"></i>
                            清理过期缓存
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 批量操作栏 -->
                <div id="bulk-actions" class="hidden mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-blue-800">
                                已选择 <span id="selected-count" class="font-semibold">0</span> 个站点
                            </span>
                            <button onclick="clearSelection()" class="text-sm text-blue-600 hover:text-blue-800">
                                取消选择
                            </button>
                        </div>
                        <button onclick="bulkDeleteCache()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-trash mr-2"></i>
                            批量清除缓存
                        </button>
                    </div>
                </div>
                
                <!-- 缓存统计 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">总缓存大小</p>
                                <p class="text-3xl font-bold text-gray-800 mt-2" id="total-size">-</p>
                            </div>
                            <div class="p-3 bg-blue-100 rounded-full">
                                <i class="fas fa-hdd text-blue-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">文件数量</p>
                                <p class="text-3xl font-bold text-gray-800 mt-2" id="files-count">-</p>
                            </div>
                            <div class="p-3 bg-green-100 rounded-full">
                                <i class="fas fa-file text-green-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">过期文件</p>
                                <p class="text-3xl font-bold text-gray-800 mt-2" id="expired-count">-</p>
                            </div>
                            <div class="p-3 bg-yellow-100 rounded-full">
                                <i class="fas fa-clock text-yellow-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">站点数量</p>
                                <p class="text-3xl font-bold text-gray-800 mt-2" id="sites-count">-</p>
                            </div>
                            <div class="p-3 bg-purple-100 rounded-full">
                                <i class="fas fa-globe text-purple-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 站点缓存列表 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-800">站点缓存详情</h2>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50 border-b">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input type="checkbox" id="select-all" onchange="toggleSelectAll()" 
                                               class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('domain')">
                                        域名 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('size')">
                                        缓存大小 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('count')">
                                        文件数量 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('time')">
                                        最后更新 <i class="fas fa-sort text-xs ml-1"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="sites-tbody" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                        <div class="flex justify-center">
                                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                                        </div>
                                        <p class="mt-4">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页控件 -->
                    <div class="px-6 py-4 border-t">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-700">每页显示</span>
                                <select id="page-size" onchange="changePageSize()" class="border border-gray-300 rounded px-2 py-1 text-sm">
                                    <option value="10">10</option>
                                    <option value="20" selected>20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="text-sm text-gray-700">条</span>
                                <span class="text-sm text-gray-600 ml-4">
                                    共 <span id="total-count">0</span> 条记录
                                </span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="prev-page" onclick="prevPage()" disabled 
                                        class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <div id="page-numbers" class="flex items-center space-x-1">
                                    <!-- 页码按钮将动态生成 -->
                                </div>
                                <button id="next-page" onclick="nextPage()" disabled
                                        class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        // 分页和排序相关变量
        let cacheData = [];
        let currentPage = 1;
        let pageSize = 20;
        let sortColumn = 'size';  // 默认按大小排序
        let sortOrder = 'desc';
        let selectedDomains = new Set();  // 存储选中的域名
        
        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 兼容旧的通知函数
        function showSuccess(message) { showToast(message, 'success'); }
        function showError(message) { showToast(message, 'error'); }
        function showWarning(message) { showToast(message, 'warning'); }
        function showInfo(message) { showToast(message, 'info'); }

        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }

        // 格式化日期
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN');
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadCacheStats();
        });
        
        // 加载缓存统计
        async function loadCacheStats() {
            try {
                const res = await fetch('/api/v1/cache/stats');
                const data = await res.json();
                
                if (data.success) {
                    renderStats(data.data);
                    // 保存原始数据
                    cacheData = data.data.sites || [];
                    currentPage = 1;
                    // 默认按大小降序排序
                    if (cacheData.length > 0) {
                        sortByColumn('size', false);
                    } else {
                        renderSites();
                    }
                }
            } catch (error) {
                showError('加载缓存统计失败: ' + error.message);
            }
        }
        
        // 渲染统计信息
        function renderStats(stats) {
            document.getElementById('total-size').textContent = formatBytes(stats.total_size || 0);
            document.getElementById('files-count').textContent = stats.files_count || 0;
            document.getElementById('expired-count').textContent = stats.expired_count || 0;
            document.getElementById('sites-count').textContent = stats.sites_count || 0;
        }
        
        // 渲染站点列表（支持分页）
        function renderSites() {
            const tbody = document.getElementById('sites-tbody');
            
            // 获取当前页数据
            const start = (currentPage - 1) * pageSize;
            const end = start + pageSize;
            const pageData = cacheData.slice(start, end);
            
            // 更新总记录数
            document.getElementById('total-count').textContent = cacheData.length;
            
            if (pageData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                            暂无缓存数据
                        </td>
                    </tr>
                `;
                updatePagination();
                return;
            }
            
            tbody.innerHTML = pageData.map(site => `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" value="${site.domain}" onchange="toggleSelect('${site.domain}')" 
                               ${selectedDomains.has(site.domain) ? 'checked' : ''}
                               class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${site.domain}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${formatBytes(site.size)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${site.files_count}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${formatDate(site.last_updated)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <button onclick="viewSiteCache('${site.domain}')" class="text-blue-600 hover:text-blue-800 mr-3">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <button onclick="clearSiteCache('${site.domain}')" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i> 清除
                        </button>
                    </td>
                </tr>
            `).join('');
            
            // 更新分页控件
            updatePagination();
            // 更新批量操作栏
            updateBulkActions();
        }
        
        // 排序函数
        function sortByColumn(column, toggleOrder = true) {
            // 如果点击同一列，切换排序顺序
            if (toggleOrder && sortColumn === column) {
                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                if (!toggleOrder) {
                    sortOrder = 'desc'; // 默认降序
                }
            }
            
            // 执行排序
            cacheData.sort((a, b) => {
                let valueA, valueB;
                
                switch(column) {
                    case 'domain':
                        valueA = a.domain.toLowerCase();
                        valueB = b.domain.toLowerCase();
                        break;
                    case 'size':
                        valueA = a.size || 0;
                        valueB = b.size || 0;
                        break;
                    case 'count':
                        valueA = a.files_count || 0;
                        valueB = b.files_count || 0;
                        break;
                    case 'time':
                        valueA = new Date(a.last_update || 0).getTime();
                        valueB = new Date(b.last_update || 0).getTime();
                        break;
                    default:
                        return 0;
                }
                
                // 字符串比较
                if (typeof valueA === 'string') {
                    return sortOrder === 'asc' ? 
                        valueA.localeCompare(valueB) : 
                        valueB.localeCompare(valueA);
                }
                
                // 数字比较
                if (sortOrder === 'asc') {
                    return valueA - valueB;
                } else {
                    return valueB - valueA;
                }
            });
            
            // 重置到第一页并重新渲染
            currentPage = 1;
            renderSites();
        }
        
        // 分页相关函数
        function updatePagination() {
            const totalPages = Math.ceil(cacheData.length / pageSize);
            const prevBtn = document.getElementById('prev-page');
            const nextBtn = document.getElementById('next-page');
            const pageNumbers = document.getElementById('page-numbers');
            
            // 更新上一页按钮
            prevBtn.disabled = currentPage <= 1;
            
            // 更新下一页按钮
            nextBtn.disabled = currentPage >= totalPages;
            
            // 生成页码按钮
            let pageHtml = '';
            const maxVisible = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let endPage = Math.min(totalPages, startPage + maxVisible - 1);
            
            if (endPage - startPage < maxVisible - 1) {
                startPage = Math.max(1, endPage - maxVisible + 1);
            }
            
            // 添加第一页
            if (startPage > 1) {
                pageHtml += `<button onclick="goToPage(1)" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">1</button>`;
                if (startPage > 2) {
                    pageHtml += `<span class="px-2 text-gray-500">...</span>`;
                }
            }
            
            // 添加中间页码
            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                pageHtml += `
                    <button onclick="goToPage(${i})" 
                            class="px-3 py-1 border ${isActive ? 'bg-blue-500 text-white border-blue-500' : 'border-gray-300 hover:bg-gray-50'} rounded text-sm">
                        ${i}
                    </button>
                `;
            }
            
            // 添加最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pageHtml += `<span class="px-2 text-gray-500">...</span>`;
                }
                pageHtml += `<button onclick="goToPage(${totalPages})" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50">${totalPages}</button>`;
            }
            
            pageNumbers.innerHTML = pageHtml || '<span class="text-sm text-gray-500">1</span>';
        }
        
        function changePageSize() {
            pageSize = parseInt(document.getElementById('page-size').value);
            currentPage = 1;
            renderSites();
        }
        
        function prevPage() {
            if (currentPage > 1) {
                currentPage--;
                renderSites();
            }
        }
        
        function nextPage() {
            const totalPages = Math.ceil(cacheData.length / pageSize);
            if (currentPage < totalPages) {
                currentPage++;
                renderSites();
            }
        }
        
        function goToPage(page) {
            currentPage = page;
            renderSites();
        }
        
        // 清理过期缓存
        async function clearExpiredCache() {
            if (!confirm('确定要清理所有过期缓存吗？')) return;
            
            try {
                const res = await fetch('/api/v1/cache/expired', { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(`成功清理 ${data.data.deleted_count} 个过期文件`);
                    loadCacheStats();
                } else {
                    showError(data.error || '清理失败');
                }
            } catch (error) {
                showError('清理失败: ' + error.message);
            }
        }
        
        // 清除站点缓存
        async function clearSiteCache(domain) {
            if (!confirm(`确定要清除 ${domain} 的所有缓存吗？`)) return;
            
            try {
                const res = await fetch(`/api/v1/cache/site/${encodeURIComponent(domain)}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(`成功清理 ${data.data.deleted_count} 个文件`);
                    loadCacheStats();
                } else {
                    showError(data.error || '清理失败');
                }
            } catch (error) {
                showError('清理失败: ' + error.message);
            }
        }
        
        // 查看站点缓存
        function viewSiteCache(domain) {
            // TODO: 实现查看站点缓存详情
            showWarning('查看缓存详情功能开发中...');
        }
        
        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 切换选中状态
        function toggleSelect(domain) {
            if (selectedDomains.has(domain)) {
                selectedDomains.delete(domain);
            } else {
                selectedDomains.add(domain);
            }
            updateBulkActions();
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const pageData = getPageData();
            
            if (selectAll.checked) {
                pageData.forEach(site => selectedDomains.add(site.domain));
            } else {
                pageData.forEach(site => selectedDomains.delete(site.domain));
            }
            
            renderSites();  // 修正：使用正确的函数名
            updateBulkActions();
        }
        
        // 清空选择
        function clearSelection() {
            selectedDomains.clear();
            document.getElementById('select-all').checked = false;
            renderSites();  // 修正：使用正确的函数名
            updateBulkActions();
        }
        
        // 更新批量操作栏
        function updateBulkActions() {
            const bulkActions = document.getElementById('bulk-actions');
            const selectedCount = document.getElementById('selected-count');
            
            if (selectedDomains.size > 0) {
                bulkActions.classList.remove('hidden');
                selectedCount.textContent = selectedDomains.size;
            } else {
                bulkActions.classList.add('hidden');
            }
            
            // 更新全选复选框状态
            const selectAll = document.getElementById('select-all');
            const pageData = getPageData();
            const pageDomainsCount = pageData.filter(site => selectedDomains.has(site.domain)).length;
            
            if (pageDomainsCount === 0) {
                selectAll.checked = false;
                selectAll.indeterminate = false;
            } else if (pageDomainsCount === pageData.length) {
                selectAll.checked = true;
                selectAll.indeterminate = false;
            } else {
                selectAll.checked = false;
                selectAll.indeterminate = true;
            }
        }
        
        // 批量删除缓存
        async function bulkDeleteCache() {
            const count = selectedDomains.size;
            if (count === 0) return;
            
            if (!confirm(`确定要清除选中的 ${count} 个站点的缓存吗？`)) return;
            
            const domains = Array.from(selectedDomains);
            let successCount = 0;
            let errorCount = 0;
            
            // 显示进度提示
            showInfo(`正在清除 ${count} 个站点的缓存...`);
            
            // 并发删除，但限制并发数
            const batchSize = 5;
            for (let i = 0; i < domains.length; i += batchSize) {
                const batch = domains.slice(i, i + batchSize);
                const promises = batch.map(domain => 
                    fetch(`/api/v1/cache/site/${encodeURIComponent(domain)}`, { method: 'DELETE' })
                        .then(res => res.json())
                        .then(data => {
                            if (data.success) {
                                successCount++;
                            } else {
                                errorCount++;
                            }
                        })
                        .catch(() => errorCount++)
                );
                
                await Promise.all(promises);
            }
            
            // 清空选择
            selectedDomains.clear();
            updateBulkActions();
            
            // 显示结果
            if (errorCount === 0) {
                showSuccess(`成功清除 ${successCount} 个站点的缓存`);
            } else {
                showWarning(`清除完成：成功 ${successCount} 个，失败 ${errorCount} 个`);
            }
            
            // 重新加载数据
            loadCacheStats();
        }
        
        // 获取当前页数据
        function getPageData() {
            const start = (currentPage - 1) * pageSize;
            const end = start + pageSize;
            return cacheData.slice(start, end);
        }
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
