// 管理界面专用JavaScript

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化侧边栏状态
    initSidebar();
    
    // 初始化页面
    initCurrentPage();
    
    // 绑定全局事件
    bindGlobalEvents();
});

// 初始化侧边栏
function initSidebar() {
    // 获取当前页面路径
    const currentPath = window.location.pathname;
    
    // 设置当前菜单项为活动状态
    document.querySelectorAll('.nav-link').forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// 初始化当前页面
function initCurrentPage() {
    const path = window.location.pathname;
    
    // 根据不同页面执行不同的初始化逻辑
    switch(path) {
        case '/admin/':
            initDashboard();
            break;
        case '/admin/sites':
            initSitesPage();
            break;
        case '/admin/tasks':
            initTasksPage();
            break;
        case '/admin/keywords':
            initKeywordsPage();
            break;
        case '/admin/cache':
            initCachePage();
            break;
        case '/admin/settings':
            initSettingsPage();
            break;
    }
}

// 绑定全局事件
function bindGlobalEvents() {
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
    
    // 点击模态框外部关闭
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });
}

// 关闭所有模态框
function closeAllModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.style.display = 'none';
    });
}

// Dashboard页面初始化
function initDashboard() {
    console.log('Dashboard page initialized');
}

// 站点管理页面初始化
function initSitesPage() {
    console.log('Sites page initialized');
}

// 任务管理页面初始化
function initTasksPage() {
    // 自动刷新任务状态
    setInterval(() => {
        if (typeof loadTasks === 'function') {
            loadTasks();
        }
    }, 10000);
}

// 关键词管理页面初始化
function initKeywordsPage() {
    console.log('Keywords page initialized');
}

// 缓存管理页面初始化
function initCachePage() {
    console.log('Cache page initialized');
}

// 设置页面初始化
function initSettingsPage() {
    console.log('Settings page initialized');
}

// 表单验证
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
        } else {
            input.classList.remove('error');
        }
    });
    
    return isValid;
}

// 批量操作
function handleBatchAction(action, selectedItems) {
    if (selectedItems.length === 0) {
        showWarning('请先选择要操作的项目');
        return;
    }
    
    confirmAction(`确定要对 ${selectedItems.length} 个项目执行 ${action} 操作吗？`, () => {
        // 执行批量操作
        console.log('Batch action:', action, selectedItems);
    });
}

// 搜索功能
const searchInput = debounce(function(callback) {
    callback();
}, 500);

// 导出数据
function exportData(type) {
    const exportUrl = `/api/v1/export/${type}`;
    downloadFile(exportUrl, `${type}_export_${Date.now()}.csv`);
}

// 导入数据
function importData(type) {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv,.txt';
    
    input.onchange = async (e) => {
        const file = e.target.files[0];
        if (!file) return;
        
        const formData = new FormData();
        formData.append('file', file);
        
        try {
            const response = await fetch(`/api/v1/import/${type}`, {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                showSuccess('导入成功');
                location.reload();
            } else {
                showError(data.error || '导入失败');
            }
        } catch (error) {
            console.error('Import error:', error);
            showError('导入失败');
        }
    };
    
    input.click();
}

// 实时数据更新
class DataUpdater {
    constructor(updateFunction, interval = 30000) {
        this.updateFunction = updateFunction;
        this.interval = interval;
        this.timer = null;
    }
    
    start() {
        this.updateFunction();
        this.timer = setInterval(() => {
            this.updateFunction();
        }, this.interval);
    }
    
    stop() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
}

// 图表初始化（如果需要）
function initCharts() {
    // 这里可以集成图表库如 Chart.js
    console.log('Charts initialized');
}

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + S 保存
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        const saveButton = document.querySelector('.btn-primary');
        if (saveButton) saveButton.click();
    }
    
    // Ctrl/Cmd + N 新建
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        const addButton = document.querySelector('[onclick*="showAdd"]');
        if (addButton) addButton.click();
    }
});

// 响应式菜单切换
function toggleMobileMenu() {
    const sidebar = document.querySelector('.admin-sidebar');
    sidebar.classList.toggle('mobile-open');
}

// 打印功能
function printContent(elementId) {
    const content = document.getElementById(elementId);
    if (!content) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>打印</title>
            <link rel="stylesheet" href="/static/css/style.css">
            <link rel="stylesheet" href="/static/css/admin.css">
        </head>
        <body>
            ${content.innerHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}

// 全屏切换
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

// 页面加载进度
function showPageLoading() {
    const loader = document.createElement('div');
    loader.className = 'page-loader';
    loader.innerHTML = '<div class="spinner"></div>';
    document.body.appendChild(loader);
}

function hidePageLoading() {
    const loader = document.querySelector('.page-loader');
    if (loader) {
        loader.remove();
    }
}

// 导出工具函数
window.validateForm = validateForm;
window.handleBatchAction = handleBatchAction;
window.searchInput = searchInput;
window.exportData = exportData;
window.importData = importData;
window.DataUpdater = DataUpdater;
window.toggleMobileMenu = toggleMobileMenu;
window.printContent = printContent;
window.toggleFullscreen = toggleFullscreen;