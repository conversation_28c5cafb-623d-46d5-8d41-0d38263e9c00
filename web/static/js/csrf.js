// CSRF Token 管理
(function() {
    let csrfToken = null;
    let tokenExpiry = null;
    
    // 获取CSRF Token
    async function getCsrfToken() {
        // 如果token还有效，直接返回
        if (csrfToken && tokenExpiry && Date.now() < tokenExpiry) {
            return csrfToken;
        }
        
        try {
            // 从服务器获取新token
            const response = await fetch('/api/v1/auth/csrf-token', {
                method: 'GET',
                credentials: 'same-origin'
            });
            
            if (response.ok) {
                const data = await response.json();
                csrfToken = data.token;
                // Token有效期设为55分钟（服务器是60分钟）
                tokenExpiry = Date.now() + (55 * 60 * 1000);
                return csrfToken;
            }
        } catch (error) {
            console.error('Failed to get CSRF token:', error);
        }
        
        return null;
    }
    
    // 增强的fetch函数，自动添加CSRF token
    const originalFetch = window.fetch;
    window.fetch = async function(url, options = {}) {
        // 只对API请求添加CSRF token
        if (url.startsWith('/api/v1/')) {
            const method = (options.method || 'GET').toUpperCase();
            
            // 对于非GET请求，添加CSRF token
            if (method !== 'GET' && method !== 'HEAD') {
                const token = await getCsrfToken();
                if (token) {
                    // 添加到请求头
                    options.headers = options.headers || {};
                    options.headers['X-CSRF-Token'] = token;
                }
            }
            
            // 确保发送cookie
            options.credentials = options.credentials || 'same-origin';
        }
        
        // 调用原始fetch
        const response = await originalFetch(url, options);
        
        // 如果收到403且是CSRF错误，尝试刷新token并重试一次
        if (response.status === 403) {
            try {
                const data = await response.clone().json();
                if (data.error && data.error.includes('CSRF')) {
                    // 清除缓存的token
                    csrfToken = null;
                    tokenExpiry = null;
                    
                    // 获取新token
                    const newToken = await getCsrfToken();
                    if (newToken && options.headers) {
                        options.headers['X-CSRF-Token'] = newToken;
                        // 重试请求
                        return originalFetch(url, options);
                    }
                }
            } catch (e) {
                // 忽略JSON解析错误
            }
        }
        
        return response;
    };
    
    // 为jQuery AJAX添加CSRF支持（如果存在jQuery）
    if (typeof $ !== 'undefined' && $.ajax) {
        $.ajaxSetup({
            beforeSend: async function(xhr, settings) {
                const method = (settings.type || 'GET').toUpperCase();
                if (method !== 'GET' && method !== 'HEAD' && settings.url.startsWith('/api/v1/')) {
                    const token = await getCsrfToken();
                    if (token) {
                        xhr.setRequestHeader('X-CSRF-Token', token);
                    }
                }
            }
        });
    }
    
    // 导出函数供其他脚本使用
    window.CsrfManager = {
        getToken: getCsrfToken,
        clearToken: function() {
            csrfToken = null;
            tokenExpiry = null;
        }
    };
})();