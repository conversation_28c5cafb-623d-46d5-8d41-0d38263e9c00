/**
 * 动态菜单分组组件
 * 用于替换所有页面的静态菜单为分组菜单
 * @version 1.3.4
 * @date 2025-09-02
 * @changes 添加UA统计分析菜单项
 */

(function() {
    // 菜单分组配置
    const menuGroups = [
        {
            id: 'dashboard',
            title: '仪表盘',
            icon: 'fas fa-tachometer-alt',
            items: [
                { id: 'console', title: '控制台', icon: 'fas fa-chart-line', url: '/admin/', active: 'dashboard' },
                { id: 'monitor', title: '系统监控', icon: 'fas fa-desktop', url: '/admin/monitor', active: 'monitor' }
            ]
        },
        {
            id: 'site-management',
            title: '站点管理',
            icon: 'fas fa-globe',
            items: [
                { id: 'batch-add', title: '批量添加', icon: 'fas fa-plus-circle', url: '/admin/batch-add', active: 'batch-add' },
                { id: 'sites', title: '站点列表', icon: 'fas fa-sitemap', url: '/admin/sites', active: 'sites' },
                { id: 'categories', title: '分类管理', icon: 'fas fa-folder-tree', url: '/admin/site-categories', active: 'site-categories' },
                { id: 'batch-replace', title: '批量替换', icon: 'fas fa-exchange-alt', url: '/admin/batch-replace', active: 'batch-replace' },
                { id: 'orphaned-data', title: '脏数据管理', icon: 'fas fa-broom', url: '/admin/orphaned-data', active: 'orphaned-data' }
            ]
        },
        {
            id: 'content-optimization',
            title: '内容优化',
            icon: 'fas fa-edit',
            items: [
                { id: 'keywords', title: '关键词库', icon: 'fas fa-tags', url: '/admin/keywords', active: 'keywords' },
                { id: 'pseudo', title: '伪原创库', icon: 'fas fa-pen-fancy', url: '/admin/pseudo', active: 'pseudo' },
                { id: 'company', title: '企业库', icon: 'fas fa-building', url: '/admin/company-libraries', active: 'company-libraries' },
                { id: 'settings-content', title: '特殊符管理', icon: 'fas fa-asterisk', url: '/admin/settings/content', active: 'settings-content' }
            ]
        },
        {
            id: 'spider-management',
            title: '爬虫管理',
            icon: 'fas fa-spider',
            items: [
                { id: 'spider-stats', title: '爬虫统计', icon: 'fas fa-chart-bar', url: '/admin/spider-stats', active: 'spider-stats' },
                { id: 'spider-block', title: '蜘蛛屏蔽', icon: 'fas fa-shield-alt', url: '/admin/spider-block', active: 'spider-block' },
                { id: 'ua-stats', title: 'UA统计分析', icon: 'fas fa-chart-pie', url: '/admin/ua-stats', active: 'ua-stats' }
            ]
        },
        {
            id: 'system-management',
            title: '系统管理',
            icon: 'fas fa-cogs',
            items: [
                { id: 'cache', title: '缓存管理', icon: 'fas fa-database', url: '/admin/cache', active: 'cache' },
                { id: 'admins', title: '管理员管理', icon: 'fas fa-users-cog', url: '/admin/admins', active: 'admins' },
                { id: 'login-logs', title: '登录日志', icon: 'fas fa-history', url: '/admin/login-logs', active: 'login-logs' }
            ]
        },
        {
            id: 'system-settings',
            title: '系统设置',
            icon: 'fas fa-sliders-h',
            items: [
                // 核心功能配置（最重要）
                { id: 'settings-basic', title: '基础设置', icon: 'fas fa-cog', url: '/admin/settings/basic', active: 'settings-basic' },
                { id: 'settings-resource', title: '资源限流', icon: 'fas fa-tachometer-alt', url: '/admin/settings/resource', active: 'settings-resource' },
                { id: 'settings-performance', title: '连接池设置', icon: 'fas fa-network-wired', url: '/admin/settings/performance', active: 'settings-performance' },
                
                // 访问控制（重要）
                { id: 'settings-ua', title: 'UA判断设置', icon: 'fas fa-user-shield', url: '/admin/settings/ua', active: 'settings-ua' },
                { id: 'settings-referer-check', title: '来源判断', icon: 'fas fa-link', url: '/admin/settings/referer-check', active: 'settings-referer-check' },
                { id: 'settings-request', title: '请求设置', icon: 'fas fa-exchange-alt', url: '/admin/settings/request', active: 'settings-request' },
                
                // SEO与监控（次要）
                { id: 'settings-sitemap', title: 'Sitemap设置', icon: 'fas fa-sitemap', url: '/admin/settings/sitemap', active: 'settings-sitemap' },
                { id: 'settings-weight', title: '权重监测', icon: 'fas fa-weight', url: '/admin/settings/weight', active: 'settings-weight' },
                { id: 'settings-stats', title: '统计设置', icon: 'fas fa-chart-pie', url: '/admin/settings/stats', active: 'settings-stats' },
                
                // 其他配置
                { id: 'settings-error', title: '错误页面', icon: 'fas fa-exclamation-triangle', url: '/admin/settings/error', active: 'settings-error' },
                { id: 'settings-system', title: '系统信息', icon: 'fas fa-info-circle', url: '/admin/settings/system', active: 'settings-system' }
            ]
        },
        {
            id: 'performance',
            title: 'SEO工具',
            icon: 'fas fa-search',
            items: [
                { id: 'weight-monitor', title: '权重查询', icon: 'fas fa-balance-scale', url: '/admin/weight-monitor', active: 'weight-monitor' }
            ]
        }
    ];

    // 获取当前页面的活跃菜单项
    function getActiveMenuItem() {
        const path = window.location.pathname;
        
        // 特殊处理控制台
        if (path === '/admin/' || path === '/admin') {
            return 'dashboard';
        }
        
        // 处理系统设置子页面
        if (path.startsWith('/admin/settings/')) {
            const settingType = path.split('/').pop();
            return 'settings-' + settingType;
        }
        
        // 兼容旧的settings路径和带参数的路径
        if (path === '/admin/settings' || window.location.search.includes('tab=')) {
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get('tab') || 'basic';
            return 'settings-' + tab;
        }
        
        const pageName = path.split('/').pop() || 'dashboard';
        return pageName;
    }

    // 切换菜单分组的展开/折叠状态
    function toggleMenuGroup(groupId) {
        const group = document.querySelector(`[data-group="${groupId}"]`);
        if (group) {
            const isCollapsed = group.classList.contains('collapsed');
            const itemsContainer = group.querySelector('.menu-group-items');
            
            if (isCollapsed) {
                group.classList.remove('collapsed');
                // 先设置为auto来获取实际高度
                itemsContainer.style.maxHeight = 'none';
                const actualHeight = itemsContainer.scrollHeight;
                itemsContainer.style.maxHeight = '0';
                // 强制重排
                itemsContainer.offsetHeight;
                // 设置为实际高度
                itemsContainer.style.maxHeight = actualHeight + 'px';
                group.querySelector('.menu-group-arrow').style.transform = 'rotate(0deg)';
            } else {
                group.classList.add('collapsed');
                itemsContainer.style.maxHeight = '0';
                group.querySelector('.menu-group-arrow').style.transform = 'rotate(-90deg)';
            }
            
            // 保存状态到localStorage
            saveMenuGroupState(groupId, !isCollapsed);
        }
    }

    // 保存菜单分组状态
    function saveMenuGroupState(groupId, collapsed) {
        const states = JSON.parse(localStorage.getItem('menuGroupStates') || '{}');
        states[groupId] = collapsed;
        localStorage.setItem('menuGroupStates', JSON.stringify(states));
    }

    // 恢复菜单分组状态
    function restoreMenuGroupStates() {
        const states = JSON.parse(localStorage.getItem('menuGroupStates') || '{}');
        Object.keys(states).forEach(groupId => {
            const group = document.querySelector(`[data-group="${groupId}"]`);
            if (group && states[groupId]) {
                group.classList.add('collapsed');
                group.querySelector('.menu-group-items').style.maxHeight = '0';
                group.querySelector('.menu-group-arrow').style.transform = 'rotate(-90deg)';
            } else if (group && !states[groupId]) {
                // 确保展开的菜单有正确的高度
                const itemsContainer = group.querySelector('.menu-group-items');
                if (itemsContainer) {
                    itemsContainer.style.maxHeight = itemsContainer.scrollHeight + 'px';
                }
            }
        });
    }

    // 生成分组菜单HTML
    function generateGroupedMenu() {
        const activeItem = getActiveMenuItem();
        const savedStates = JSON.parse(localStorage.getItem('menuGroupStates') || '{}');
        let menuHTML = '';

        menuGroups.forEach(group => {
            // 检查该分组是否包含活跃项
            const hasActive = group.items.some(item => item.active === activeItem);
            // 确定初始状态：如果包含活跃项则展开，否则根据保存的状态
            const isCollapsed = hasActive ? false : (savedStates[group.id] || false);
            
            menuHTML += `
                <div class="menu-group ${hasActive ? 'has-active' : ''} ${isCollapsed ? 'collapsed' : ''}" data-group="${group.id}">
                    <div class="menu-group-title flex items-center justify-between px-4 py-2 text-gray-400 hover:text-white hover:bg-gray-800 cursor-pointer select-none transition-colors"
                         onclick="window.toggleMenuGroup('${group.id}')">
                        <div class="flex items-center">
                            <i class="${group.icon} w-4 mr-2 text-sm"></i>
                            <span class="text-base font-semibold uppercase tracking-wider">${group.title}</span>
                        </div>
                        <i class="fas fa-chevron-down menu-group-arrow text-xs transition-transform duration-200" style="${isCollapsed ? 'transform: rotate(-90deg)' : ''}"></i>
                    </div>
                    <div class="menu-group-items overflow-hidden transition-all duration-300" style="max-height: ${isCollapsed ? '0' : 'none'};">
            `;

            group.items.forEach(item => {
                const isActive = item.active === activeItem;
                menuHTML += `
                    <a href="${item.url}" 
                       class="flex items-center px-6 py-3 ${
                           isActive 
                           ? 'bg-gray-800 border-l-4 border-blue-500 text-white' 
                           : 'hover:bg-gray-800 hover:border-l-4 hover:border-gray-600 transition-colors text-gray-300'
                       }">
                        <i class="${item.icon} w-5"></i>
                        <span class="ml-3 text-base">${item.title}</span>
                    </a>
                `;
            });

            menuHTML += `
                    </div>
                </div>
            `;
        });

        // 添加退出登录按钮
        menuHTML += `
            <div class="mt-4 pt-4 border-t border-gray-800">
                <button onclick="logout()" class="flex items-center px-6 py-3 w-full text-left hover:bg-gray-800 hover:border-l-4 hover:border-red-600 transition-colors text-gray-300">
                    <i class="fas fa-sign-out-alt w-5"></i>
                    <span class="ml-3 text-base">退出登录</span>
                </button>
            </div>
        `;

        return menuHTML;
    }

    // 替换现有菜单
    function replaceMenu() {
        // 查找nav元素
        const navElement = document.querySelector('aside nav, nav.mt-8');
        
        if (navElement) {
            // 添加必要的样式（先添加样式避免闪烁）
            addMenuStyles();
            
            // 生成新的分组菜单（已包含正确的初始状态）
            const groupedMenuHTML = generateGroupedMenu();
            
            // 替换nav的内容
            navElement.innerHTML = groupedMenuHTML;
            
            // 设置所有展开菜单的正确高度
            setTimeout(() => {
                document.querySelectorAll('.menu-group:not(.collapsed) .menu-group-items').forEach(items => {
                    items.style.maxHeight = items.scrollHeight + 'px';
                });
            }, 0);
        }
    }

    // 添加必要的样式
    function addMenuStyles() {
        // 检查是否已经添加过样式
        if (document.getElementById('menu-group-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'menu-group-styles';
        style.innerHTML = `
            .menu-group {
                margin-bottom: 0.5rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }
            
            .menu-group:last-child {
                border-bottom: none;
            }
            
            .menu-group-title {
                position: relative;
            }
            
            .menu-group.has-active .menu-group-title {
                color: #60a5fa !important;
            }
            
            .menu-group.collapsed .menu-group-items {
                max-height: 0 !important;
            }
            
            .menu-group:not(.collapsed) .menu-group-items {
                max-height: none !important;
            }
            
            .menu-group-items {
                transition: max-height 0.3s ease-in-out;
            }
            
            .menu-group-arrow {
                transition: transform 0.2s ease;
            }
            
            .menu-group.collapsed .menu-group-arrow {
                transform: rotate(-90deg);
            }
            
            /* 调整菜单项的缩进 */
            .menu-group-items a {
                padding-left: 2.5rem;
            }
            
            /* 活跃分组的标题颜色 */
            .menu-group.has-active .menu-group-title {
                background-color: rgba(59, 130, 246, 0.1);
            }
        `;
        document.head.appendChild(style);
    }

    // 退出登录函数
    function logout() {
        fetch('/api/v1/auth/logout', { method: 'POST' })
            .then(res => {
                if (res.ok) {
                    window.location.href = '/login';
                }
            })
            .catch(error => {
                alert('退出登录失败');
            });
    }

    // 将函数暴露到全局
    window.toggleMenuGroup = toggleMenuGroup;
    window.logout = logout;

    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', replaceMenu);
    } else {
        // 如果页面已经加载完成，直接执行
        replaceMenu();
    }
})();