/* 管理界面布局 */
.admin-container {
    display: flex;
    height: 100vh;
    background-color: #f5f5f5;
}

/* 侧边栏 */
.admin-sidebar {
    width: 240px;
    background-color: #2c3e50;
    color: #ecf0f1;
    overflow-y: auto;
    position: relative;
    padding-bottom: 110px; /* 增加底部间距，确保内容不被遮挡 */
}

/* 确保所有子元素不显示白色背景 */
.admin-sidebar * {
    background-color: transparent;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
}

.admin-sidebar .nav-link.active {
    background-color: #3498db;
}

.logo {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

/* 菜单分组样式 */
.nav-menu-groups {
    padding: 10px 0;
    padding-bottom: 10px; /* 减少底部间距，因为侧边栏已有padding-bottom */
}

.menu-group {
    margin-bottom: 5px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.menu-group-title {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: #bdc3c7;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.menu-group-title:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: #ecf0f1;
}

.menu-group-icon {
    width: 16px;
    margin-right: 10px;
    font-size: 14px;
}

.menu-group-title span {
    flex: 1;
}

.menu-group-arrow {
    font-size: 10px;
    transition: transform 0.3s ease;
}

.menu-group.collapsed .menu-group-arrow {
    transform: rotate(-90deg);
}

.menu-group-items {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 500px;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: transparent;
}

.menu-group.collapsed .menu-group-items {
    max-height: 0;
}

.menu-group .nav-item {
    border-bottom: none;
    background-color: transparent;
}

.menu-group .nav-link {
    padding-left: 44px;
    font-size: 14px;
    background-color: transparent;
}

.menu-group .nav-link i {
    width: 16px;
    margin-right: 10px;
    font-size: 14px;
    text-align: center;
}

/* 活跃分组样式 */
.menu-group.has-active .menu-group-title {
    color: #3498db;
}

.menu-group.has-active .menu-group-icon {
    color: #3498db;
}

.nav-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    background-color: transparent !important;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.3s ease;
    background-color: transparent;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background-color: #3498db;
}

.nav-link i {
    margin-right: 10px;
}

/* 主内容区 */
.admin-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.admin-header {
    background-color: #fff;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-header h1 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.system-time {
    font-size: 14px;
    color: #666;
}

/* 导航栏底部管理员信息 */
.nav-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 240px; /* 与侧边栏宽度保持一致 */
    height: 100px; /* 增加高度确保完全覆盖 */
    padding: 15px;
    border-top: 1px solid #34495e;
    background-color: #2c3e50; /* 与侧边栏背景色一致 */
    z-index: 100; /* 提高z-index确保显示在最上层 */
    box-sizing: border-box;
}

.admin-info {
    text-align: center;
}

.admin-info .admin-name {
    color: #fff;
    font-size: 14px;
    margin-bottom: 10px;
}

.admin-info .logout-btn {
    background: transparent;
    border: 1px solid #fff;
    color: #fff;
    padding: 5px 15px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s;
}

.admin-info .logout-btn:hover {
    background: #fff;
    color: #2c3e50;
}

.admin-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* Dashboard 样式 */
.dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.stat-icon.sites {
    background-color: #3498db;
    color: #fff;
}

.stat-icon.tasks {
    background-color: #2ecc71;
    color: #fff;
}

.stat-icon.cache {
    background-color: #e74c3c;
    color: #fff;
}

.stat-icon.keywords {
    background-color: #f39c12;
    color: #fff;
}

.stat-content h3 {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.stat-number {
    font-size: 28px;
    font-weight: 600;
    color: #333;
}

.dashboard-panels {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.panel {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.panel.full-width {
    grid-column: 1 / -1;
}

.panel h2 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #333;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.header-left h2 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.header-right {
    display: flex;
    gap: 10px;
}

/* 过滤栏 */
.filter-bar {
    background-color: #fff;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-item label {
    font-weight: 500;
    color: #555;
}

.filter-item select,
.filter-item input {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 快速操作区域 */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.quick-action-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.quick-action-card:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.quick-action-card i {
    font-size: 32px;
    margin-bottom: 10px;
}

.quick-action-card span {
    font-size: 14px;
    font-weight: 500;
}

.action-links {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.action-link {
    color: #007bff;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s;
}

.action-link:hover {
    background-color: #f0f0f0;
    text-decoration: none;
}

.action-link.text-danger {
    color: #dc3545;
}

.separator {
    color: #ccc;
    user-select: none;
}

/* 系统信息样式 */
.system-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-section {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.info-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-section h3 {
    font-size: 14px;
    color: #666;
    margin: 0 0 10px 0;
    font-weight: 600;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    font-size: 13px;
}

.info-item .label {
    color: #888;
}

.info-item .value {
    color: #333;
    font-weight: 500;
}

.info-item .status-ok {
    color: #4caf50;
}

.info-item .status-error {
    color: #f44336;
}

/* 站点管理样式 */
.sites-page {
    background-color: transparent;
}

.table-container {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table thead {
    background-color: #f8f9fa;
}

.data-table th {
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

.data-table .loading,
.data-table .empty {
    text-align: center;
    color: #6c757d;
    padding: 40px;
}

/* 站点状态样式 */
.status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status.active {
    background-color: #d4edda;
    color: #155724;
}

.status.paused {
    background-color: #fff3cd;
    color: #856404;
}

.status.error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 帮助文本样式 */
.help-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
    margin-bottom: 0;
    line-height: 1.5;
}

/* 复选框列表样式 */
.checkbox-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.5rem;
    background-color: #f8f9fa;
}

.checkbox-item {
    display: block;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.checkbox-item:hover {
    background-color: #e9ecef;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 0.5rem;
}

.checkbox-item small {
    display: block;
    color: #6c757d;
    margin-left: 1.5rem;
    font-size: 0.8rem;
}

/* 活跃站点列表样式 */
.active-sites {
    max-height: 300px;
    overflow-y: auto;
}

.site-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.site-item:hover {
    background-color: #f8f9fa;
}

.site-item:last-child {
    border-bottom: none;
}

.site-domain {
    font-weight: 600;
    color: #007bff;
    flex: 1;
}

.site-target {
    color: #6c757d;
    font-size: 14px;
    flex: 2;
    margin: 0 10px;
}

.site-status {
    font-size: 12px;
    font-weight: 500;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-top: 20px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background-color: #fff;
    color: #007bff;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
}

.pagination button:hover {
    background-color: #007bff;
    color: #fff;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination .current {
    padding: 8px 12px;
    background-color: #007bff;
    color: #fff;
    border-radius: 4px;
}

/* 任务管理样式 */
.task-stats {
    display: flex;
    gap: 30px;
    padding: 15px 20px;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 20px;
}

.task-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.task-stats .stat-label {
    color: #666;
}

.task-stats .stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.tasks-table {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.batch-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* 关键词管理样式 */
.keywords-page {
    height: 100%;
}

.keywords-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 20px;
    height: calc(100% - 80px);
}

.libraries-panel,
.keywords-panel {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.libraries-panel h3,
.keywords-panel h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #333;
}

.library-item {
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.library-item:hover {
    background-color: #f8f9fa;
}

.library-item.active {
    background-color: #e3f2fd;
    border-color: #3498db;
}

.library-info h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #333;
}

.library-info p {
    margin: 0 0 5px 0;
    font-size: 13px;
    color: #666;
}

.library-type {
    display: inline-block;
    padding: 2px 8px;
    font-size: 12px;
    background-color: #e9ecef;
    border-radius: 4px;
}

.library-actions {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.keywords-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.keywords-search {
    margin-bottom: 20px;
}

.keywords-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* 缓存管理样式 */
.cache-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.cache-sites {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.site-cache-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.site-cache-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.site-cache-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.site-cache-header h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.cache-size {
    font-size: 14px;
    font-weight: 600;
    color: #3498db;
}

.site-cache-info {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    font-size: 13px;
}

.site-cache-actions {
    display: flex;
    gap: 10px;
}

.cache-config {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-form {
    max-width: 500px;
}

/* 设置页面样式 */
.settings-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.tab-button {
    padding: 10px 20px;
    background: none;
    border: none;
    font-size: 16px;
    color: #666;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: #333;
}

.tab-button.active {
    color: #3498db;
    border-bottom-color: #3498db;
}

.settings-panel {
    display: none;
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-panel.active {
    display: block;
}

.settings-panel h3 {
    margin: 0 0 20px 0;
    font-size: 20px;
    color: #333;
}

.about-info {
    margin-bottom: 30px;
}

.info-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item label {
    width: 150px;
    font-weight: 500;
    color: #555;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.system-stats {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

/* 提示框样式 */
.alert {
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.6;
}

.alert.alert-info {
    background-color: #e3f2fd;
    border: 1px solid #90caf9;
    color: #0d47a1;
}

.alert strong {
    display: block;
    margin-bottom: 8px;
    font-size: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-sidebar {
        display: none;
    }
    
    .admin-main {
        width: 100%;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-panels {
        grid-template-columns: 1fr;
    }
    
    .keywords-container {
        grid-template-columns: 1fr;
    }
}