# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# env file
.env

# Cache directory
cache/

# Build output
site-cluster

# IDE
.idea/
.vscode/

# OS files
.DS_Store
Thumbs.db

# PID files
site-cluster.pid

# Logs
logs/

# Test results
test_results/

# Backup files
*.bak
*_backup_*.html
settings_test.html
test_*.html

# Deploy binaries
deploy/site-cluster

# Debug files
debug_*.go