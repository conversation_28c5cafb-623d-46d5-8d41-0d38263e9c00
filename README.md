# 站群管理系统

一个功能完善的站群管理系统，支持多站点镜像、内容处理、SEO优化和爬虫统计等功能。

## 技术栈

- **后端**: Go 1.23.0 + Gin + GORM + PostgreSQL + Redis
- **前端**: 原生JavaScript + Tailwind CSS
- **爬虫**: Colly v2.2.0
- **缓存**: Redis + 文件缓存 + 内存缓存

## 主要功能

### 站点管理
- 多站点配置管理
- 站点分类和标签
- 批量操作支持

### 内容处理
- 关键词注入
- 伪原创处理
- 简繁体转换
- 拼音标注
- Unicode编码
- 结构优化

### SEO优化
- H1标签优化
- Meta标签管理
- Sitemap生成
- 404页面定制

### 爬虫功能
- 爬虫识别（30+种爬虫）
- 爬虫统计和分析
- UA判断和屏蔽
- 爬虫行为监控

### 权重监测
- 站长之家权重监测
- PC端和移动端权重
- 历史趋势分析
- 批量检测

### 性能优化
- 三层缓存架构
- 异步处理
- 并发控制
- 智能预加载

## 快速开始

### 环境要求
- Go 1.23+
- PostgreSQL 12+
- Redis 6+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd site-cluster
```

2. 安装依赖
```bash
go mod download
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和Redis连接
```

4. 初始化数据库
```bash
createdb sitecluster
psql -U sitecluster -d sitecluster < init.sql
```

5. 编译运行
```bash
./start.sh
```

### 访问地址
- 前台镜像: http://localhost:9090
- 后台管理: http://localhost:9999
- 默认账号: admin / admin123

## 目录结构

```
.
├── cmd/              # 程序入口
├── internal/         # 内部代码
│   ├── api/         # HTTP处理器
│   ├── service/     # 业务逻辑
│   ├── repository/  # 数据访问
│   ├── crawler/     # 爬虫模块
│   ├── injector/    # 内容注入
│   └── scheduler/   # 任务调度
├── web/             # 前端资源
│   ├── templates/   # HTML模板
│   └── static/      # 静态文件
├── scripts/         # 数据库脚本
├── logs/            # 日志文件
└── cache/           # 缓存文件
```

## 配置说明

### 环境变量
- `DB_HOST`: PostgreSQL主机
- `DB_PORT`: PostgreSQL端口
- `DB_USER`: 数据库用户
- `DB_PASSWORD`: 数据库密码
- `DB_NAME`: 数据库名称
- `REDIS_HOST`: Redis主机
- `REDIS_PORT`: Redis端口
- `PORT`: 前台端口（默认9090）
- `ADMIN_PORT`: 后台端口（默认9999）

### 自动重启
系统包含自动重启脚本，可监控进程并在崩溃时自动重启：
```bash
./auto_restart.sh
```

## 开发说明

### 编译
```bash
go build -o site-cluster cmd/server/main.go
```

### 测试
```bash
go test ./...
```

### Docker部署
```bash
docker-compose up -d
```

## 许可证

私有项目，保留所有权利。

## 联系方式

如有问题，请联系开发团队。