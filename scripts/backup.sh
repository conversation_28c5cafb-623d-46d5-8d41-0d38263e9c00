#!/bin/bash

# 数据库备份脚本
# 用于备份PostgreSQL数据库和应用数据

set -e

# 配置
BACKUP_DIR="./backup"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="site-cluster-backup-${TIMESTAMP}"

# 数据库配置（从环境变量读取）
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_USER=${DB_USER:-sitecluster}
DB_NAME=${DB_NAME:-sitecluster}
DB_PASSWORD=${DB_PASSWORD:-sitecluster123}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
mkdir -p "${BACKUP_DIR}"

log_info "开始备份站群系统..."
log_info "备份名称: ${BACKUP_NAME}"

# 1. 备份数据库
log_info "备份PostgreSQL数据库..."
export PGPASSWORD="${DB_PASSWORD}"

# 创建临时目录
TEMP_DIR="${BACKUP_DIR}/${BACKUP_NAME}"
mkdir -p "${TEMP_DIR}"

# 备份数据库结构和数据
# 检查pg_dump是否存在
if command -v pg_dump &> /dev/null; then
    pg_dump -h "${DB_HOST}" \
            -p "${DB_PORT}" \
            -U "${DB_USER}" \
            -d "${DB_NAME}" \
            -f "${TEMP_DIR}/database.sql" \
            --verbose \
            --clean \
            --if-exists \
            --no-owner \
            --no-acl
else
    log_warn "pg_dump未找到，尝试使用docker容器备份"
    # 尝试通过docker备份
    if command -v docker &> /dev/null; then
        docker run --rm \
            -e PGPASSWORD="${DB_PASSWORD}" \
            postgres:15-alpine \
            pg_dump -h host.docker.internal \
                    -p "${DB_PORT}" \
                    -U "${DB_USER}" \
                    -d "${DB_NAME}" \
                    --clean \
                    --if-exists \
                    --no-owner \
                    --no-acl > "${TEMP_DIR}/database.sql"
    else
        log_warn "无法备份数据库（pg_dump和docker都不可用）"
        echo "-- 数据库备份失败，请在Linux服务器上手动备份" > "${TEMP_DIR}/database.sql"
    fi
fi

if [ $? -eq 0 ]; then
    log_info "数据库备份成功"
else
    log_error "数据库备份失败"
    exit 1
fi

# 2. 备份缓存数据（可选）
if [ -d "./cache" ]; then
    log_info "备份缓存数据..."
    tar -czf "${TEMP_DIR}/cache.tar.gz" -C . cache 2>/dev/null || log_warn "缓存目录为空或备份失败"
else
    log_warn "缓存目录不存在，跳过"
fi

# 3. 备份日志（可选）
if [ -d "./logs" ]; then
    log_info "备份日志文件..."
    tar -czf "${TEMP_DIR}/logs.tar.gz" -C . logs 2>/dev/null || log_warn "日志目录为空或备份失败"
else
    log_warn "日志目录不存在，跳过"
fi

# 4. 备份配置文件
log_info "备份配置文件..."
cp .env "${TEMP_DIR}/.env" 2>/dev/null || log_warn ".env文件不存在"
cp docker-compose.yml "${TEMP_DIR}/docker-compose.yml" 2>/dev/null || log_warn "docker-compose.yml不存在"

# 5. 导出当前系统设置（通过API）
if command -v curl &> /dev/null; then
    log_info "导出系统设置..."
    
    # 尝试登录获取token
    TOKEN=$(curl -s -X POST "http://localhost:9999/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"admin123"}' | \
        grep -o '"token":"[^"]*' | cut -d'"' -f4)
    
    if [ ! -z "$TOKEN" ]; then
        # 导出站点配置
        curl -s -H "Authorization: Bearer ${TOKEN}" \
            "http://localhost:9999/api/v1/sites?limit=1000" \
            > "${TEMP_DIR}/sites.json" 2>/dev/null
        
        # 导出系统设置
        curl -s -H "Authorization: Bearer ${TOKEN}" \
            "http://localhost:9999/api/v1/system/settings" \
            > "${TEMP_DIR}/system_settings.json" 2>/dev/null
        
        log_info "系统设置导出完成"
    else
        log_warn "无法获取API token，跳过系统设置导出"
    fi
fi

# 6. 创建备份信息文件
cat > "${TEMP_DIR}/backup_info.txt" << EOF
站群系统备份信息
================
备份时间: $(date)
备份名称: ${BACKUP_NAME}
数据库: ${DB_NAME}@${DB_HOST}:${DB_PORT}
备份内容:
- database.sql: PostgreSQL数据库完整备份
- cache.tar.gz: 缓存文件（可选）
- logs.tar.gz: 日志文件（可选）
- sites.json: 站点配置（可选）
- system_settings.json: 系统设置（可选）

恢复说明:
1. 恢复数据库: psql -U ${DB_USER} -d ${DB_NAME} < database.sql
2. 恢复缓存: tar -xzf cache.tar.gz
3. 恢复日志: tar -xzf logs.tar.gz
EOF

# 7. 压缩整个备份
log_info "压缩备份文件..."
cd "${BACKUP_DIR}"
tar -czf "${BACKUP_NAME}.tar.gz" "${BACKUP_NAME}"

# 计算文件大小
SIZE=$(du -h "${BACKUP_NAME}.tar.gz" | cut -f1)

# 8. 清理临时文件
rm -rf "${BACKUP_NAME}"

# 9. 清理旧备份（保留最近7个）
log_info "清理旧备份..."
ls -t *.tar.gz 2>/dev/null | tail -n +8 | xargs -r rm -f

log_info "备份完成！"
log_info "备份文件: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
log_info "文件大小: ${SIZE}"

echo ""
echo "================== 备份摘要 =================="
echo "备份文件: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
echo "文件大小: ${SIZE}"
echo "备份时间: $(date)"
echo ""
echo "迁移到Linux服务器步骤:"
echo "1. 将备份文件传输到Linux服务器:"
echo "   scp ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz user@server:/path/to/destination/"
echo ""
echo "2. 在Linux服务器上解压:"
echo "   tar -xzf ${BACKUP_NAME}.tar.gz"
echo ""
echo "3. 恢复数据库:"
echo "   psql -U ${DB_USER} -d ${DB_NAME} < ${BACKUP_NAME}/database.sql"
echo ""
echo "4. 使用Docker Compose启动服务"
echo "=============================================="