-- 数据库迁移脚本：移除重复的配置字段
-- 执行时间：2025-08-15
-- 目的：清理资源限流优化后的重复字段

-- 移除已被资源限流替代的字段
ALTER TABLE system_settings DROP COLUMN IF EXISTS crawler_concurrency;
ALTER TABLE system_settings DROP COLUMN IF EXISTS crawler_timeout;
ALTER TABLE system_settings DROP COLUMN IF EXISTS http_client_timeout;
ALTER TABLE system_settings DROP COLUMN IF EXISTS scheduler_max_workers;
ALTER TABLE system_settings DROP COLUMN IF EXISTS scheduler_queue_size;

-- 移除已被统一超时管理替代的Redis超时字段
ALTER TABLE system_settings DROP COLUMN IF EXISTS redis_connect_timeout;
ALTER TABLE system_settings DROP COLUMN IF EXISTS redis_read_timeout;
ALTER TABLE system_settings DROP COLUMN IF EXISTS redis_write_timeout;
ALTER TABLE system_settings DROP COLUMN IF EXISTS redis_pool_timeout;

-- 添加说明注释
COMMENT ON COLUMN system_settings.max_http_requests IS '最大HTTP并发请求数（替代原crawler_concurrency）';
COMMENT ON COLUMN system_settings.crawler_task_timeout IS '爬虫任务超时毫秒（替代原crawler_timeout）';
COMMENT ON COLUMN system_settings.http_request_timeout IS 'HTTP请求超时毫秒（替代原http_client_timeout）';
COMMENT ON COLUMN system_settings.redis_op_timeout IS 'Redis操作超时毫秒（替代原redis_*_timeout系列）';

-- 显示迁移结果
SELECT 
    column_name,
    data_type,
    column_default,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'system_settings'
    AND column_name IN (
        'max_database_conn',
        'max_redis_conn', 
        'max_http_requests',
        'max_file_ops',
        'max_crawler_tasks',
        'route_timeout',
        'database_query_timeout',
        'redis_op_timeout',
        'http_request_timeout',
        'file_op_timeout',
        'crawler_task_timeout'
    )
ORDER BY ordinal_position;