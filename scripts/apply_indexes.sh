#!/bin/bash

# 数据库索引应用脚本
# 用于执行索引优化

echo "====================================="
echo "数据库索引优化脚本"
echo "====================================="

# 数据库连接参数
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_USER="${DB_USER:-sitecluster}"
DB_PASSWORD="${DB_PASSWORD:-sitecluster123}"
DB_NAME="${DB_NAME:-sitecluster}"

# 设置密码环境变量
export PGPASSWORD=$DB_PASSWORD

# 获取脚本目录
SCRIPT_DIR=$(dirname "$0")

echo ""
echo "数据库连接信息："
echo "- 主机: $DB_HOST"
echo "- 端口: $DB_PORT"
echo "- 数据库: $DB_NAME"
echo "- 用户: $DB_USER"
echo ""

# 检查 psql 是否可用
if ! command -v psql &> /dev/null; then
    echo "错误: psql 命令未找到。请确保已安装 PostgreSQL 客户端。"
    echo ""
    echo "可以通过以下方式安装："
    echo "  macOS: brew install postgresql"
    echo "  Linux: apt-get install postgresql-client 或 yum install postgresql"
    exit 1
fi

# 测试数据库连接
echo "测试数据库连接..."
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "错误: 无法连接到数据库。请检查连接参数。"
    exit 1
fi
echo "数据库连接成功。"
echo ""

# 备份当前索引信息
echo "备份当前索引信息..."
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\di" > "$SCRIPT_DIR/indexes_backup_$(date +%Y%m%d_%H%M%S).txt"

# 执行索引创建脚本
echo "开始创建索引..."
echo "-----------------------------------"

psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f "$SCRIPT_DIR/add_indexes.sql"

if [ $? -eq 0 ]; then
    echo ""
    echo "====================================="
    echo "索引创建成功！"
    echo "====================================="
    
    # 显示创建的索引统计
    echo ""
    echo "索引统计信息："
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
    SELECT 
        tablename AS \"表名\",
        COUNT(*) AS \"索引数量\"
    FROM pg_indexes
    WHERE schemaname = 'public'
    GROUP BY tablename
    ORDER BY tablename;"
    
    echo ""
    echo "索引大小信息："
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
    SELECT 
        schemaname AS \"模式\",
        tablename AS \"表名\",
        indexname AS \"索引名\",
        pg_size_pretty(pg_relation_size(indexname::regclass)) AS \"索引大小\"
    FROM pg_indexes
    WHERE schemaname = 'public'
    ORDER BY pg_relation_size(indexname::regclass) DESC
    LIMIT 20;"
    
else
    echo ""
    echo "====================================="
    echo "索引创建失败！"
    echo "====================================="
    echo "如需回滚，请执行: psql -f $SCRIPT_DIR/rollback_indexes.sql"
    exit 1
fi

# 清理环境变量
unset PGPASSWORD

echo ""
echo "优化完成。如需回滚索引，请执行："
echo "  ./scripts/rollback_indexes.sh"