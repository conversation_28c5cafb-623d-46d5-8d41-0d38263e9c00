-- 添加日志配置字段到系统设置表
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS log_enabled BOOLEAN DEFAULT TRUE;
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS log_level VARCHAR(10) DEFAULT 'info';
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS log_storage VARCHAR(10) DEFAULT 'file';
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS log_retention_days INTEGER DEFAULT 7;
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS log_max_size INTEGER DEFAULT 100;
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS log_max_backups INTEGER DEFAULT 10;
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS log_access_enabled BOOLEAN DEFAULT TRUE;
ALTER TABLE system_settings ADD COLUMN IF NOT EXISTS log_error_enabled BOOLEAN DEFAULT TRUE;

-- 添加注释
COMMENT ON COLUMN system_settings.log_enabled IS '是否启用日志';
COMMENT ON COLUMN system_settings.log_level IS '日志级别 (debug, info, warn, error)';
COMMENT ON COLUMN system_settings.log_storage IS '存储方式 (file, database, both)';
COMMENT ON COLUMN system_settings.log_retention_days IS '日志保留天数';
COMMENT ON COLUMN system_settings.log_max_size IS '单个日志文件最大大小(MB)';
COMMENT ON COLUMN system_settings.log_max_backups IS '最大备份文件数';
COMMENT ON COLUMN system_settings.log_access_enabled IS '是否记录访问日志';
COMMENT ON COLUMN system_settings.log_error_enabled IS '是否记录错误日志';