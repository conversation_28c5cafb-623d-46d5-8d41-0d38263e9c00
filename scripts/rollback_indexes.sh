#!/bin/bash

# 数据库索引回滚脚本
# 用于删除添加的索引

echo "====================================="
echo "数据库索引回滚脚本"
echo "====================================="

# 数据库连接参数
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_USER="${DB_USER:-sitecluster}"
DB_PASSWORD="${DB_PASSWORD:-sitecluster123}"
DB_NAME="${DB_NAME:-sitecluster}"

# 设置密码环境变量
export PGPASSWORD=$DB_PASSWORD

# 获取脚本目录
SCRIPT_DIR=$(dirname "$0")

echo ""
echo "警告：此操作将删除所有优化索引！"
echo "数据库连接信息："
echo "- 主机: $DB_HOST"
echo "- 端口: $DB_PORT"
echo "- 数据库: $DB_NAME"
echo "- 用户: $DB_USER"
echo ""

# 确认操作
read -p "确定要回滚索引吗？(yes/no): " confirm
if [ "$confirm" != "yes" ]; then
    echo "操作已取消。"
    exit 0
fi

# 检查 psql 是否可用
if ! command -v psql &> /dev/null; then
    echo "错误: psql 命令未找到。请确保已安装 PostgreSQL 客户端。"
    exit 1
fi

# 测试数据库连接
echo "测试数据库连接..."
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "错误: 无法连接到数据库。请检查连接参数。"
    exit 1
fi
echo "数据库连接成功。"
echo ""

# 执行索引删除脚本
echo "开始删除索引..."
echo "-----------------------------------"

psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f "$SCRIPT_DIR/rollback_indexes.sql"

if [ $? -eq 0 ]; then
    echo ""
    echo "====================================="
    echo "索引回滚成功！"
    echo "====================================="
    
    # 显示剩余的索引
    echo ""
    echo "剩余索引信息："
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
    SELECT 
        tablename AS \"表名\",
        COUNT(*) AS \"索引数量\"
    FROM pg_indexes
    WHERE schemaname = 'public'
    GROUP BY tablename
    ORDER BY tablename;"
    
else
    echo ""
    echo "====================================="
    echo "索引回滚失败！"
    echo "====================================="
    exit 1
fi

# 清理环境变量
unset PGPASSWORD

echo ""
echo "回滚完成。如需重新创建索引，请执行："
echo "  ./scripts/apply_indexes.sh"