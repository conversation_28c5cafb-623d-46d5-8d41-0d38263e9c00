-- 数据库索引优化脚本
-- 基于代码审计建议添加索引以提升查询性能
-- 执行时间：2025-01-15

-- =====================================================
-- 1. 站点相关索引
-- =====================================================

-- 站点域名和状态索引（加速按域名查询和状态过滤）
CREATE INDEX IF NOT EXISTS idx_sites_domain_status 
ON sites(domain, status);

-- 站点状态索引（加速获取活跃站点列表）
CREATE INDEX IF NOT EXISTS idx_sites_status 
ON sites(status) 
WHERE status = 'active';

-- 站点创建时间索引（用于排序和分页）
CREATE INDEX IF NOT EXISTS idx_sites_created_at 
ON sites(created_at DESC);

-- =====================================================
-- 2. 爬虫统计相关索引
-- =====================================================

-- 爬虫统计时间索引（加速按时间范围查询）
CREATE INDEX IF NOT EXISTS idx_spider_stats_created_at 
ON spider_stats(created_at DESC);

-- 爬虫统计复合索引（域名+时间，用于特定站点的统计查询）
CREATE INDEX IF NOT EXISTS idx_spider_stats_domain_created 
ON spider_stats(domain, created_at DESC);

-- 爬虫统计爬虫名称索引（用于按爬虫类型统计）
CREATE INDEX IF NOT EXISTS idx_spider_stats_spider_name 
ON spider_stats(spider_name, created_at DESC);

-- =====================================================
-- 3. 404缓存相关索引
-- =====================================================

-- 404缓存域名索引（加速域名查询）
CREATE INDEX IF NOT EXISTS idx_cache_404_domain 
ON cache_404_records(domain);

-- 404缓存复合索引（域名+路径，用于精确查找）
CREATE INDEX IF NOT EXISTS idx_cache_404_domain_path 
ON cache_404_records(domain, path);

-- 404缓存时间索引（用于清理过期记录）
CREATE INDEX IF NOT EXISTS idx_cache_404_created_at 
ON cache_404_records(created_at);

-- =====================================================
-- 4. 关键词库相关索引
-- =====================================================

-- 关键词库状态索引
CREATE INDEX IF NOT EXISTS idx_keyword_libraries_status 
ON keyword_libraries(status) 
WHERE status = 'active';

-- 关键词权重索引（用于按权重排序）
CREATE INDEX IF NOT EXISTS idx_keywords_weight 
ON keywords(weight DESC);

-- 关键词库ID索引（加速关联查询）
CREATE INDEX IF NOT EXISTS idx_keywords_library_id 
ON keywords(library_id);

-- =====================================================
-- 5. 伪原创库相关索引
-- =====================================================

-- 伪原创库状态索引
CREATE INDEX IF NOT EXISTS idx_pseudo_libraries_status 
ON pseudo_libraries(status) 
WHERE status = 'active';

-- 伪原创词库ID索引
CREATE INDEX IF NOT EXISTS idx_pseudo_words_library_id 
ON pseudo_words(library_id);

-- =====================================================
-- 6. 系统日志相关索引
-- =====================================================

-- 登录日志时间索引
CREATE INDEX IF NOT EXISTS idx_login_logs_created_at 
ON login_logs(created_at DESC);

-- 登录日志用户名索引
CREATE INDEX IF NOT EXISTS idx_login_logs_username 
ON login_logs(username);

-- 操作日志时间索引
CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at 
ON operation_logs(created_at DESC);

-- =====================================================
-- 7. Sitemap相关索引
-- =====================================================

-- Sitemap条目域名索引
CREATE INDEX IF NOT EXISTS idx_sitemap_entries_domain 
ON sitemap_entries(domain);

-- Sitemap条目更新时间索引
CREATE INDEX IF NOT EXISTS idx_sitemap_entries_updated_at 
ON sitemap_entries(updated_at DESC);

-- =====================================================
-- 8. 权重历史相关索引
-- =====================================================

-- 权重历史站点ID和时间索引
CREATE INDEX IF NOT EXISTS idx_weight_histories_site_created 
ON weight_histories(site_id, created_at DESC);

-- =====================================================
-- 9. 爬虫屏蔽规则索引
-- =====================================================

-- 爬虫屏蔽规则状态索引
CREATE INDEX IF NOT EXISTS idx_spider_blocks_status 
ON spider_blocks(status) 
WHERE status = 'active';

-- =====================================================
-- 10. 站点分类索引
-- =====================================================

-- 站点分类排序索引
CREATE INDEX IF NOT EXISTS idx_site_categories_sort_order 
ON site_categories(sort_order);

-- =====================================================
-- 统计信息更新
-- =====================================================

-- 更新表统计信息以优化查询计划
ANALYZE sites;
ANALYZE spider_stats;
ANALYZE cache_404_records;
ANALYZE keyword_libraries;
ANALYZE keywords;
ANALYZE pseudo_libraries;
ANALYZE pseudo_words;
ANALYZE login_logs;
ANALYZE operation_logs;
ANALYZE sitemap_entries;
ANALYZE weight_histories;
ANALYZE spider_blocks;
ANALYZE site_categories;

-- =====================================================
-- 查看索引创建结果
-- =====================================================
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
ORDER BY tablename, indexname;