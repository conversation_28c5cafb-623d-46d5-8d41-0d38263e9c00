-- 添加站点统计设置字段的数据库迁移脚本
-- 创建时间: 2024-08-24
-- 说明: 将统计设置从全局改为每个站点独立配置

BEGIN;

-- 在sites表中添加统计配置字段
ALTER TABLE sites ADD COLUMN IF NOT EXISTS use_global_analytics BOOLEAN DEFAULT NULL;
ALTER TABLE sites ADD COLUMN IF NOT EXISTS enable_analytics BOOLEAN DEFAULT FALSE;

-- 添加注释
COMMENT ON COLUMN sites.use_global_analytics IS '是否使用全局统计设置(null=使用全局,true=使用全局,false=使用站点独立设置)';
COMMENT ON COLUMN sites.enable_analytics IS '站点独立的统计开关';

COMMIT;

-- 记录迁移
INSERT INTO migrations (version, description, executed_at) 
VALUES ('add_site_analytics_20240824', '添加站点统计设置字段', NOW())
ON CONFLICT (version) DO NOTHING;