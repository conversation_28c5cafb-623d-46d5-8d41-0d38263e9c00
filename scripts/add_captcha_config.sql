-- 添加验证码配置字段到系统设置表
-- 执行时间: 2025-01-15

-- 检查并添加验证码配置字段
DO $$
BEGIN
    -- 添加是否启用验证码字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'system_settings' 
                   AND column_name = 'enable_captcha') THEN
        ALTER TABLE system_settings ADD COLUMN enable_captcha BOOLEAN DEFAULT FALSE;
        COMMENT ON COLUMN system_settings.enable_captcha IS '是否启用登录验证码';
    END IF;

    -- 添加验证码长度字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'system_settings' 
                   AND column_name = 'captcha_length') THEN
        ALTER TABLE system_settings ADD COLUMN captcha_length INTEGER DEFAULT 4;
        COMMENT ON COLUMN system_settings.captcha_length IS '验证码长度（3-8位）';
    END IF;

    -- 添加验证码过期时间字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'system_settings' 
                   AND column_name = 'captcha_expiry') THEN
        ALTER TABLE system_settings ADD COLUMN captcha_expiry INTEGER DEFAULT 300;
        COMMENT ON COLUMN system_settings.captcha_expiry IS '验证码过期时间（秒）';
    END IF;
END $$;

-- 输出完成信息
SELECT '验证码配置字段添加完成' AS message;