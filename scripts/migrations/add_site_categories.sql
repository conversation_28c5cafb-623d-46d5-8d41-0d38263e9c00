-- 创建站点分类表
CREATE TABLE IF NOT EXISTS site_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(50),
    sort INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 给sites表添加分类字段
ALTER TABLE sites ADD COLUMN IF NOT EXISTS category_id INTEGER REFERENCES site_categories(id);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sites_category_id ON sites(category_id);

-- 插入默认分类
INSERT INTO site_categories (name, description, icon, color, sort) VALUES 
    ('默认分类', '未分类的站点', 'fa-folder', 'gray', 0),
    ('企业站点', '企业官网类站点', 'fa-building', 'blue', 1),
    ('电商站点', '电子商务类站点', 'fa-shopping-cart', 'green', 2),
    ('新闻媒体', '新闻资讯类站点', 'fa-newspaper', 'red', 3),
    ('博客论坛', '博客和论坛类站点', 'fa-comments', 'purple', 4),
    ('教育培训', '教育培训类站点', 'fa-graduation-cap', 'yellow', 5)
ON CONFLICT (name) DO NOTHING;

-- 将现有站点设置为默认分类
UPDATE sites SET category_id = (SELECT id FROM site_categories WHERE name = '默认分类' LIMIT 1) WHERE category_id IS NULL;