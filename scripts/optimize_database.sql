-- 优化数据库索引和查询性能
-- 执行方式: psql -U sitecluster -d sitecluster -f optimize_database.sql

-- ========================================
-- 1. Sites表索引优化
-- ========================================
-- 域名查询索引（最常用）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sites_domain ON sites(domain);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sites_enabled ON sites(enabled);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sites_created_at ON sites(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sites_cache_status ON sites(cache_status);
-- 复合索引用于分页查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sites_enabled_created ON sites(enabled, created_at DESC);

-- ========================================
-- 2. Spider Block表索引优化
-- ========================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_blocks_enabled ON spider_blocks(enabled);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_blocks_hit_count ON spider_blocks(hit_count DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_blocks_user_agent ON spider_blocks(user_agent);
-- 复合索引用于查询活跃规则
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_blocks_enabled_hit ON spider_blocks(enabled, hit_count DESC);

-- ========================================
-- 3. UA统计表索引优化
-- ========================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ua_stats_ua_hash ON ua_stats_summaries(ua_hash);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ua_stats_hit_count ON ua_stats_summaries(hit_count DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ua_stats_last_seen ON ua_stats_summaries(last_seen_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ua_stats_visitor_type ON ua_stats_summaries(visitor_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ua_stats_is_spider ON ua_stats_summaries(is_spider);
-- 复合索引用于筛选查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ua_stats_type_hits ON ua_stats_summaries(visitor_type, hit_count DESC);

-- ========================================
-- 4. 关键词库表索引优化
-- ========================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keywords_library_id ON keywords(library_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_keyword_libraries_is_active ON keyword_libraries(is_active);

-- ========================================
-- 5. Spider统计表索引优化
-- ========================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_stats_domain ON spider_stats(domain);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_stats_spider_name ON spider_stats(spider_name);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_stats_visit_time ON spider_stats(visit_time DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_stats_created_at ON spider_stats(created_at DESC);
-- 复合索引用于域名+时间查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_stats_domain_time ON spider_stats(domain, visit_time DESC);

-- ========================================
-- 6. 权重监控表索引优化
-- ========================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_weight_records_domain ON weight_records(domain);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_weight_records_created_at ON weight_records(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_weight_records_platform ON weight_records(platform);
-- 复合索引用于域名+平台查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_weight_records_domain_platform ON weight_records(domain, platform);

-- ========================================
-- 7. 登录日志表索引优化
-- ========================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_login_logs_username ON login_logs(username);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_login_logs_created_at ON login_logs(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_login_logs_success ON login_logs(success);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_login_logs_ip ON login_logs(ip);

-- ========================================
-- 8. 清理和优化
-- ========================================
-- 更新统计信息
ANALYZE sites;
ANALYZE spider_blocks;
ANALYZE ua_stats_summaries;
ANALYZE spider_stats;
ANALYZE keywords;
ANALYZE keyword_libraries;
ANALYZE weight_records;
ANALYZE login_logs;

-- 清理死元组
VACUUM ANALYZE;

-- ========================================
-- 9. 查看索引使用情况
-- ========================================
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- ========================================
-- 10. 查看表大小
-- ========================================
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
    pg_total_relation_size(schemaname||'.'||tablename) AS size_bytes
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;

-- 完成消息
SELECT '数据库索引优化完成！' as message;