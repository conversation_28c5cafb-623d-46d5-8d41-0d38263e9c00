-- PostgreSQL 初始化脚本 - 包含性能优化索引

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- 用于模糊搜索

-- 站点表索引
CREATE INDEX IF NOT EXISTS idx_sites_domain ON sites (domain);
CREATE INDEX IF NOT EXISTS idx_sites_status ON sites (status);
CREATE INDEX IF NOT EXISTS idx_sites_last_crawl_at ON sites (last_crawl_at DESC);
CREATE INDEX IF NOT EXISTS idx_sites_status_last_crawl ON sites (status, last_crawl_at DESC);

-- 注入配置表索引
CREATE INDEX IF NOT EXISTS idx_inject_configs_site_id ON inject_configs (site_id);
CREATE INDEX IF NOT EXISTS idx_inject_configs_enable_keyword ON inject_configs (enable_keyword) WHERE enable_keyword = true;
CREATE INDEX IF NOT EXISTS idx_inject_configs_enable_pseudo ON inject_configs (enable_pseudo) WHERE enable_pseudo = true;

-- 爬取任务表索引
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_site_id ON crawl_jobs (site_id);
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_status ON crawl_jobs (status);
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_created_at ON crawl_jobs (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_status_created ON crawl_jobs (status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_crawl_jobs_type_status ON crawl_jobs (type, status);

-- 任务日志表索引
CREATE INDEX IF NOT EXISTS idx_task_logs_task_id ON task_logs (task_id);
CREATE INDEX IF NOT EXISTS idx_task_logs_level ON task_logs (level);
CREATE INDEX IF NOT EXISTS idx_task_logs_timestamp ON task_logs (timestamp DESC);

-- 关键词库表索引
CREATE INDEX IF NOT EXISTS idx_keyword_libraries_name ON keyword_libraries (name);
CREATE INDEX IF NOT EXISTS idx_keyword_libraries_is_active ON keyword_libraries (is_active) WHERE is_active = true;

-- 关键词表索引
CREATE INDEX IF NOT EXISTS idx_keywords_library_id ON keywords (library_id);
CREATE INDEX IF NOT EXISTS idx_keywords_keyword ON keywords (keyword);
CREATE INDEX IF NOT EXISTS idx_keywords_keyword_trgm ON keywords USING gin (keyword gin_trgm_ops); -- 支持模糊搜索

-- 管理员表索引
CREATE INDEX IF NOT EXISTS idx_admins_username ON admins (username);
CREATE INDEX IF NOT EXISTS idx_admins_is_active ON admins (is_active) WHERE is_active = true;

-- 会话表索引
CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions (token);
CREATE INDEX IF NOT EXISTS idx_sessions_admin_id ON sessions (admin_id);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions (expires_at);

-- 伪原创词库表索引
CREATE INDEX IF NOT EXISTS idx_pseudo_libraries_name ON pseudo_libraries (name);
CREATE INDEX IF NOT EXISTS idx_pseudo_libraries_is_active ON pseudo_libraries (is_active) WHERE is_active = true;

-- 伪原创词表索引
CREATE INDEX IF NOT EXISTS idx_pseudo_words_library_id ON pseudo_words (library_id);
CREATE INDEX IF NOT EXISTS idx_pseudo_words_original ON pseudo_words (original);
CREATE INDEX IF NOT EXISTS idx_pseudo_words_original_trgm ON pseudo_words USING gin (original gin_trgm_ops); -- 支持模糊搜索

-- 蜘蛛拦截规则表索引
CREATE INDEX IF NOT EXISTS idx_spider_blocks_user_agent ON spider_blocks (user_agent);
CREATE INDEX IF NOT EXISTS idx_spider_blocks_name ON spider_blocks (name);
CREATE INDEX IF NOT EXISTS idx_spider_blocks_is_active ON spider_blocks (is_active) WHERE is_active = true;

-- 路由规则表索引
CREATE INDEX IF NOT EXISTS idx_route_rules_site_id ON route_rules (site_id);
CREATE INDEX IF NOT EXISTS idx_route_rules_path ON route_rules (path);
CREATE INDEX IF NOT EXISTS idx_route_rules_priority ON route_rules (priority DESC);

-- 创建物化视图用于缓存统计
CREATE MATERIALIZED VIEW IF NOT EXISTS site_stats AS
SELECT 
    s.id,
    s.domain,
    COUNT(DISTINCT cj.id) as total_crawl_jobs,
    COUNT(DISTINCT CASE WHEN cj.status = 'completed' THEN cj.id END) as completed_jobs,
    COUNT(DISTINCT CASE WHEN cj.status = 'failed' THEN cj.id END) as failed_jobs,
    MAX(cj.created_at) as last_job_at
FROM sites s
LEFT JOIN crawl_jobs cj ON s.id = cj.site_id
GROUP BY s.id, s.domain;

-- 创建物化视图的索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_site_stats_id ON site_stats (id);
CREATE INDEX IF NOT EXISTS idx_site_stats_domain ON site_stats (domain);

-- 创建刷新物化视图的函数
CREATE OR REPLACE FUNCTION refresh_site_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY site_stats;
END;
$$ LANGUAGE plpgsql;

-- 设置表的自动清理参数（针对大表）
ALTER TABLE crawl_jobs SET (autovacuum_vacuum_scale_factor = 0.1);
ALTER TABLE task_logs SET (autovacuum_vacuum_scale_factor = 0.1);

-- 创建分区表示例（如果日志量很大）
-- CREATE TABLE task_logs_2024_01 PARTITION OF task_logs
-- FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');