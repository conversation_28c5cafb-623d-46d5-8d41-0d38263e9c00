-- 添加首页关键词注入相关字段到 inject_configs 表

-- 添加首页关键词注入开关
ALTER TABLE inject_configs 
ADD COLUMN IF NOT EXISTS enable_home_keyword_inject BOOLEAN DEFAULT FALSE;

-- 添加首页关键词库ID列表
ALTER TABLE inject_configs 
ADD COLUMN IF NOT EXISTS home_keyword_library_ids JSON DEFAULT '[]';

-- 添加首页关键词注入数量
ALTER TABLE inject_configs 
ADD COLUMN IF NOT EXISTS home_keyword_inject_count INTEGER DEFAULT 10;

-- 添加首页关键词Unicode转码开关
ALTER TABLE inject_configs 
ADD COLUMN IF NOT EXISTS enable_home_keyword_unicode BOOLEAN DEFAULT FALSE;

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_inject_configs_home_keyword_inject 
ON inject_configs(enable_home_keyword_inject);

-- 添加注释说明字段用途
COMMENT ON COLUMN inject_configs.enable_home_keyword_inject IS '是否启用首页关键词注入';
COMMENT ON COLUMN inject_configs.home_keyword_library_ids IS '首页关键词库ID列表（JSON格式）';
COMMENT ON COLUMN inject_configs.home_keyword_inject_count IS '首页关键词注入数量';
COMMENT ON COLUMN inject_configs.enable_home_keyword_unicode IS '是否对首页注入的关键词进行Unicode转码';