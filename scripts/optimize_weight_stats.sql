-- 权重统计性能优化索引
-- 执行方式: psql -U sitecluster -d sitecluster < scripts/optimize_weight_stats.sql

-- 1. 创建domain和id的复合索引
-- 这个索引对于DISTINCT ON (domain) ORDER BY domain, id DESC 查询非常重要
CREATE INDEX IF NOT EXISTS idx_weight_history_domain_id 
ON weight_history(domain, id DESC);

-- 2. 创建部分索引，只索引有权重的记录
-- 这个索引可以加速筛选有权重的记录
CREATE INDEX IF NOT EXISTS idx_weight_history_weights 
ON weight_history(pc_br, mobile_br) 
WHERE pc_br > 0 OR mobile_br > 0;

-- 3. 分析表以更新统计信息
ANALYZE weight_history;

-- 4. 查看表的索引情况
\di weight_history*

-- 5. 查看表的大小和行数
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
    n_live_tup AS row_count
FROM pg_stat_user_tables 
WHERE tablename = 'weight_history';

-- 6. 测试优化后的查询性能
EXPLAIN ANALYZE
WITH latest_per_domain AS (
    SELECT DISTINCT ON (domain) 
        domain,
        pc_br,
        mobile_br
    FROM weight_history
    ORDER BY domain, id DESC
)
SELECT 
    COUNT(*) as total_domains,
    COUNT(CASE WHEN pc_br > 0 OR mobile_br > 0 THEN 1 END) as weighted_count
FROM latest_per_domain;

-- 提示：如果查询仍然很慢，可以考虑：
-- 1. 增加 work_mem 设置: SET work_mem = '256MB';
-- 2. 创建物化视图来缓存最新权重数据
-- 3. 定期清理历史数据，只保留最近N天的记录