-- 蜘蛛屏蔽功能数据库迁移脚本
-- 执行日期：2025-01-08

-- 1. 更新 system_settings 表
ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS enable_global_spider_block BOOLEAN DEFAULT false;

ALTER TABLE system_settings 
ADD COLUMN IF NOT EXISTS spider_block_403_template TEXT;

-- 添加字段注释
COMMENT ON COLUMN system_settings.enable_global_spider_block IS '全局蜘蛛屏蔽开关';
COMMENT ON COLUMN system_settings.spider_block_403_template IS '蜘蛛屏蔽403响应模板';

-- 2. 更新 sites 表
ALTER TABLE sites 
ADD COLUMN IF NOT EXISTS enable_spider_block BOOLEAN DEFAULT false;

ALTER TABLE sites 
ADD COLUMN IF NOT EXISTS spider_block_403_template TEXT;

-- 添加字段注释
COMMENT ON COLUMN sites.enable_spider_block IS '站点级别蜘蛛屏蔽开关（覆盖全局设置）';
COMMENT ON COLUMN sites.spider_block_403_template IS '站点自定义403模板（优先级高于全局模板）';

-- 3. 设置默认值（可选）
-- 如果需要默认启用全局蜘蛛屏蔽，取消下面的注释
-- UPDATE system_settings SET enable_global_spider_block = true WHERE id = 1;

-- 输出迁移完成信息
SELECT '数据库迁移完成' as message,
       '- system_settings 表已添加: enable_global_spider_block, spider_block_403_template' as detail1,
       '- sites 表已添加: enable_spider_block, spider_block_403_template' as detail2;