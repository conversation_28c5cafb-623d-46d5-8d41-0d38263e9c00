-- 移除端口设置字段的数据库迁移脚本
-- 创建时间: 2024-08-24
-- 说明: 从system_settings表中删除admin_port和web_port字段，因为端口配置应该通过配置文件管理

BEGIN;

-- 删除admin_port字段
ALTER TABLE system_settings DROP COLUMN IF EXISTS admin_port;

-- 删除web_port字段  
ALTER TABLE system_settings DROP COLUMN IF EXISTS web_port;

COMMIT;

-- 记录迁移
INSERT INTO migrations (version, description, executed_at) 
VALUES ('remove_port_settings_20240824', '移除system_settings表中的端口设置字段', NOW())
ON CONFLICT (version) DO NOTHING;