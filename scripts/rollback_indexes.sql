-- 索引回滚脚本
-- 用于在需要时删除添加的索引

-- =====================================================
-- 1. 站点相关索引
-- =====================================================
DROP INDEX IF EXISTS idx_sites_domain_status;
DROP INDEX IF EXISTS idx_sites_status;
DROP INDEX IF EXISTS idx_sites_created_at;

-- =====================================================
-- 2. 爬虫统计相关索引
-- =====================================================
DROP INDEX IF EXISTS idx_spider_stats_created_at;
DROP INDEX IF EXISTS idx_spider_stats_domain_created;
DROP INDEX IF EXISTS idx_spider_stats_spider_name;

-- =====================================================
-- 3. 404缓存相关索引
-- =====================================================
DROP INDEX IF EXISTS idx_cache_404_domain;
DROP INDEX IF EXISTS idx_cache_404_domain_path;
DROP INDEX IF EXISTS idx_cache_404_created_at;

-- =====================================================
-- 4. 关键词库相关索引
-- =====================================================
DROP INDEX IF EXISTS idx_keyword_libraries_status;
DROP INDEX IF EXISTS idx_keywords_weight;
DROP INDEX IF EXISTS idx_keywords_library_id;

-- =====================================================
-- 5. 伪原创库相关索引
-- =====================================================
DROP INDEX IF EXISTS idx_pseudo_libraries_status;
DROP INDEX IF EXISTS idx_pseudo_words_library_id;

-- =====================================================
-- 6. 系统日志相关索引
-- =====================================================
DROP INDEX IF EXISTS idx_login_logs_created_at;
DROP INDEX IF EXISTS idx_login_logs_username;
DROP INDEX IF EXISTS idx_operation_logs_created_at;

-- =====================================================
-- 7. Sitemap相关索引
-- =====================================================
DROP INDEX IF EXISTS idx_sitemap_entries_domain;
DROP INDEX IF EXISTS idx_sitemap_entries_updated_at;

-- =====================================================
-- 8. 权重历史相关索引
-- =====================================================
DROP INDEX IF EXISTS idx_weight_histories_site_created;

-- =====================================================
-- 9. 爬虫屏蔽规则索引
-- =====================================================
DROP INDEX IF EXISTS idx_spider_blocks_status;

-- =====================================================
-- 10. 站点分类索引
-- =====================================================
DROP INDEX IF EXISTS idx_site_categories_sort_order;