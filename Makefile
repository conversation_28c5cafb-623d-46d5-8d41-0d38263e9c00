.PHONY: help build up down restart logs clean migrate test

# 默认目标
help:
	@echo "站群系统 Docker 管理命令:"
	@echo "  make build    - 构建Docker镜像"
	@echo "  make up       - 启动所有服务"
	@echo "  make down     - 停止所有服务"
	@echo "  make restart  - 重启所有服务"
	@echo "  make logs     - 查看日志"
	@echo "  make clean    - 清理容器和卷"
	@echo "  make migrate  - 运行数据库迁移"
	@echo "  make test     - 运行测试"
	@echo "  make shell    - 进入应用容器"
	@echo "  make psql     - 连接PostgreSQL"
	@echo "  make redis    - 连接Redis CLI"

# 构建镜像
build:
	docker-compose build

# 启动服务
up:
	docker-compose up -d
	@echo "服务已启动:"
	@echo "  - 应用: http://localhost:8080"
	@echo "  - Redis Commander: http://localhost:8081"
	@echo "  - pgAdmin: http://localhost:8082"

# 停止服务
down:
	docker-compose down

# 重启服务
restart:
	docker-compose restart

# 查看日志
logs:
	docker-compose logs -f app

# 查看所有日志
logs-all:
	docker-compose logs -f

# 清理
clean:
	docker-compose down -v
	docker system prune -f

# 数据库迁移
migrate:
	docker-compose exec app ./main migrate

# 运行测试
test:
	go test ./...

# 进入应用容器
shell:
	docker-compose exec app sh

# 连接PostgreSQL
psql:
	docker-compose exec postgres psql -U sitecluster -d sitecluster

# 连接Redis
redis:
	docker-compose exec redis redis-cli

# 备份数据库
backup:
	@mkdir -p backups
	docker-compose exec postgres pg_dump -U sitecluster sitecluster > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "数据库已备份到 backups/"

# 恢复数据库
restore:
	@echo "请指定备份文件: make restore FILE=backups/backup_xxx.sql"
ifdef FILE
	docker-compose exec -T postgres psql -U sitecluster sitecluster < $(FILE)
	@echo "数据库已从 $(FILE) 恢复"
endif

# 查看容器状态
status:
	docker-compose ps

# 性能监控
monitor:
	docker stats --no-stream

# 开发模式（实时重载）
dev:
	air -c .air.toml