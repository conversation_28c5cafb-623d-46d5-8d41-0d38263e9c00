#!/bin/bash

# 自动重启脚本 - 最简单的方案
# 不需要系统权限，可以在任何环境运行

LOG_FILE="./logs/auto_restart.log"
PID_FILE="./site-cluster.pid"
MAX_RESTARTS=10
RESTART_COUNT=0
RESTART_DELAY=10

# 创建日志目录
mkdir -p ./logs

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 健康检查函数
health_check() {
    # 检查进程是否存在
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ! kill -0 "$PID" 2>/dev/null; then
            return 1
        fi
    else
        return 1
    fi
    
    # 检查HTTP健康端点
    if command -v curl >/dev/null 2>&1; then
        curl -sf http://localhost:9090/health >/dev/null 2>&1
        return $?
    fi
    
    return 0
}

# 启动服务函数
start_service() {
    log "启动站群服务..."
    
    # 设置环境变量
    export DB_HOST=localhost
    export DB_PORT=5432
    export DB_USER=sitecluster
    export DB_PASSWORD=sitecluster123
    export DB_NAME=sitecluster
    export REDIS_HOST=localhost
    export REDIS_PORT=6379
    export PORT=9090
    export ADMIN_PORT=9999
    
    # 编译（如果需要）
    if [ ! -f "./site-cluster" ] || [ "./site-cluster" -ot "cmd/server/main.go" ]; then
        log "编译项目..."
        go build -o site-cluster cmd/server/main.go
        if [ $? -ne 0 ]; then
            log "编译失败！"
            exit 1
        fi
    fi
    
    # 启动服务并保存PID
    nohup ./site-cluster > ./logs/site-cluster.log 2>&1 &
    echo $! > "$PID_FILE"
    
    log "服务已启动，PID: $(cat $PID_FILE)"
    
    # 等待服务完全启动
    sleep 5
}

# 停止服务函数
stop_service() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        log "停止服务 PID: $PID"
        
        # 先尝试优雅关闭
        kill -TERM "$PID" 2>/dev/null
        
        # 等待最多30秒
        for i in {1..30}; do
            if ! kill -0 "$PID" 2>/dev/null; then
                break
            fi
            sleep 1
        done
        
        # 如果还在运行，强制杀死
        if kill -0 "$PID" 2>/dev/null; then
            log "强制杀死进程"
            kill -9 "$PID" 2>/dev/null
        fi
        
        rm -f "$PID_FILE"
    fi
}

# 清理函数
cleanup() {
    log "收到退出信号，正在清理..."
    stop_service
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主循环
log "=== 站群系统自动重启服务 ==="
log "最大重启次数: $MAX_RESTARTS"
log "健康检查间隔: 30秒"

# 初始启动
start_service

# 监控循环
while true; do
    sleep 30
    
    # 健康检查
    if ! health_check; then
        log "健康检查失败！"
        
        RESTART_COUNT=$((RESTART_COUNT + 1))
        
        if [ $RESTART_COUNT -gt $MAX_RESTARTS ]; then
            log "重启次数超过限制($MAX_RESTARTS)，退出"
            exit 1
        fi
        
        log "准备重启... (第 $RESTART_COUNT 次)"
        
        # 停止服务
        stop_service
        
        # 等待
        log "等待 $RESTART_DELAY 秒..."
        sleep $RESTART_DELAY
        
        # 增加延迟（指数退避）
        RESTART_DELAY=$((RESTART_DELAY * 2))
        if [ $RESTART_DELAY -gt 300 ]; then
            RESTART_DELAY=300
        fi
        
        # 重启服务
        start_service
    else
        # 健康检查通过，重置计数器
        if [ $RESTART_COUNT -gt 0 ]; then
            log "服务恢复正常"
            RESTART_COUNT=0
            RESTART_DELAY=10
        fi
    fi
done