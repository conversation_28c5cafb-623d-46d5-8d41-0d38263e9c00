package crawler

import (
	"net/url"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"
)

// ContentProcessor 处理HTML内容
type ContentProcessor struct {
	logger *zap.Logger
}

// NewContentProcessor 创建内容处理器
func NewContentProcessor(logger *zap.Logger) *ContentProcessor {
	return &ContentProcessor{
		logger: logger,
	}
}

// ProcessHTML 处理HTML内容 - 保持原始编码
func (cp *ContentProcessor) ProcessHTML(html, pageURL, domain string) string {
	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		cp.logger.Error("解析HTML失败", zap.Error(err))
		return html
	}

	// 解析基础URL
	baseURL, err := url.Parse(pageURL)
	if err != nil {
		cp.logger.Error("解析URL失败", zap.Error(err))
		return html
	}

	// 处理链接和资源
	cp.processLinks(doc, baseURL, domain)

	// 返回处理后的HTML - 保持原始编码
	result, err := doc.Html()
	if err != nil {
		cp.logger.Error("生成HTML失败", zap.Error(err))
		return html
	}

	return result
}

// processLinks 处理所有链接和资源
func (cp *ContentProcessor) processLinks(doc *goquery.Document, baseURL *url.URL, domain string) {
	// 处理a标签的href
	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		if href, exists := s.Attr("href"); exists {
			newHref := cp.resolveURL(href, baseURL)
			s.SetAttr("href", newHref)
		}
	})

	// 处理img标签的src
	doc.Find("img[src]").Each(func(i int, s *goquery.Selection) {
		if src, exists := s.Attr("src"); exists {
			newSrc := cp.resolveURL(src, baseURL)
			s.SetAttr("src", newSrc)
		}
	})

	// 处理link标签的href（CSS）
	doc.Find("link[href]").Each(func(i int, s *goquery.Selection) {
		if href, exists := s.Attr("href"); exists {
			newHref := cp.resolveURL(href, baseURL)
			s.SetAttr("href", newHref)
		}
	})

	// 处理script标签的src
	doc.Find("script[src]").Each(func(i int, s *goquery.Selection) {
		if src, exists := s.Attr("src"); exists {
			newSrc := cp.resolveURL(src, baseURL)
			s.SetAttr("src", newSrc)
		}
	})

	// 处理form标签的action
	doc.Find("form[action]").Each(func(i int, s *goquery.Selection) {
		if action, exists := s.Attr("action"); exists {
			newAction := cp.resolveURL(action, baseURL)
			s.SetAttr("action", newAction)
		}
	})
}

// resolveURL 解析URL为相对路径
func (cp *ContentProcessor) resolveURL(href string, baseURL *url.URL) string {
	// 解析链接
	u, err := url.Parse(href)
	if err != nil {
		return href
	}

	// 如果是绝对URL，转换为相对路径
	absoluteURL := baseURL.ResolveReference(u)
	
	// 如果是同域名，使用相对路径
	if absoluteURL.Host == baseURL.Host {
		// 返回路径部分
		path := absoluteURL.Path
		if absoluteURL.RawQuery != "" {
			path += "?" + absoluteURL.RawQuery
		}
		if absoluteURL.Fragment != "" {
			path += "#" + absoluteURL.Fragment
		}
		return path
	}

	// 外部链接保持原样
	return absoluteURL.String()
}