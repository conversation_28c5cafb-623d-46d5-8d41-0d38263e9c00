package crawler

import (
	"net/url"
	"time"

	"sync"
	"site-cluster/internal/model"
	"site-cluster/internal/cache"

	"github.com/gocolly/colly/v2"
	"github.com/gocolly/colly/v2/extensions"
	"go.uber.org/zap"
)

type CollyCrawler struct {
	logger       *zap.Logger
	cacheService cache.Service
	downloader   *ResourceDownloader
	processor    *ContentProcessor
	systemSettingsService SystemSettingsService
	sitemapService SitemapService
}

type SitemapService interface {
	AddURL(siteID uint, domain, url string) error
}

type SystemSettingsService interface {
	GetCrawlerConcurrency() int
	GetCrawlerTimeout() int
	GetRandomUserAgent() string
}

func NewCollyCrawler(
	logger *zap.Logger,
	cacheService cache.Service,
	systemSettingsService SystemSettingsService,
) *CollyCrawler {
	downloader := NewResourceDownloader(logger, cacheService)
	downloader.SetSystemSettingsService(systemSettingsService)
	
	return &CollyCrawler{
		logger:       logger,
		cacheService: cacheService,
		downloader:   downloader,
		processor:    NewContentProcessor(logger),
		systemSettingsService: systemSettingsService,
	}
}

// SetSitemapService 设置sitemap服务
func (cc *CollyCrawler) SetSitemapService(sitemapService SitemapService) {
	cc.sitemapService = sitemapService
}

// CrawlSite 爬取整个站点
func (cc *CollyCrawler) CrawlSite(site *model.Site, job *model.CrawlJob) error {
	// 获取系统设置的随机 User-Agent
	userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
	if cc.systemSettingsService != nil {
		userAgent = cc.systemSettingsService.GetRandomUserAgent()
	}
	
	c := colly.NewCollector(
		colly.MaxDepth(site.CrawlDepth),
		colly.Async(true),
		colly.UserAgent(userAgent),
	)

	// 设置并发限制
	concurrency := 10
	if cc.systemSettingsService != nil {
		concurrency = cc.systemSettingsService.GetCrawlerConcurrency()
	}
	c.Limit(&colly.LimitRule{
		DomainGlob:  "*",
		Parallelism: concurrency,
		Delay:       100 * time.Millisecond,
	})

	// 设置请求前的钩子，每次请求都使用新的随机 User-Agent
	c.OnRequest(func(r *colly.Request) {
		if cc.systemSettingsService != nil {
			r.Headers.Set("User-Agent", cc.systemSettingsService.GetRandomUserAgent())
		}
	})

	// 不使用 colly 的内置随机UA扩展，因为我们要使用系统设置的UA列表
	// extensions.RandomUserAgent(c) // 注释掉，使用我们自己的实现
	extensions.Referer(c)

	// 访问统计
	visited := make(map[string]bool)
	visitedMutex := &sync.RWMutex{}

	// HTML页面处理
	c.OnHTML("*", func(e *colly.HTMLElement) {
		pageURL := e.Request.URL.String()
		
		// 记录访问
		visitedMutex.Lock()
		if !visited[pageURL] {
			visited[pageURL] = true
			job.ProcessedPages++
		}
		visitedMutex.Unlock()

		// 处理页面内容
		html, err := e.DOM.Html()
		if err != nil {
			cc.logger.Error("获取HTML失败", zap.Error(err))
			return
		}

		// 处理内容（替换链接等）
		processedHTML := cc.processor.ProcessHTML(html, e.Request.URL.String(), site.Domain)

		// 保存到缓存
		content := &model.CachedContent{
			URL:         pageURL,
			ContentType: "text/html; charset=utf-8",
			Data:        []byte(processedHTML),
			Headers:     make(map[string]string),
			CachedAt:    time.Now(),
			ExpiresAt:   time.Now().Add(24 * time.Hour),
		}
		// 添加Colly处理标记
		content.Headers["X-Processed-By"] = "colly"

		if err := cc.cacheService.SaveContent(site.Domain, content); err != nil {
			cc.logger.Error("保存页面失败", zap.Error(err))
		}

		// 提取所有链接
		e.ForEach("a[href]", func(_ int, el *colly.HTMLElement) {
			link := el.Attr("href")
			absoluteURL := e.Request.AbsoluteURL(link)
			
			// 只爬取同域名下的链接
			if cc.isSameDomain(absoluteURL, site.TargetURL) {
				e.Request.Visit(absoluteURL)
			}
		})

		// 提取所有资源
		// CSS
		e.ForEach("link[rel='stylesheet']", func(_ int, el *colly.HTMLElement) {
			cc.downloadResource(el.Attr("href"), e.Request.URL, site.Domain, "text/css")
		})

		// JS
		e.ForEach("script[src]", func(_ int, el *colly.HTMLElement) {
			cc.downloadResource(el.Attr("src"), e.Request.URL, site.Domain, "application/javascript")
		})

		// 图片
		e.ForEach("img[src]", func(_ int, el *colly.HTMLElement) {
			cc.downloadResource(el.Attr("src"), e.Request.URL, site.Domain, "image/*")
		})
	})

	// 错误处理
	c.OnError(func(r *colly.Response, err error) {
		cc.logger.Error("爬取错误",
			zap.String("url", r.Request.URL.String()),
			zap.Error(err),
		)
	})

	// 开始爬取
	return c.Visit(site.TargetURL)
}

// CrawlPage 爬取单个页面 (接口实现)
func (cc *CollyCrawler) CrawlPage(pageURL, domain string, depth int) error {
	// 这里可以根据 domain 查找 site 配置
	// 暂时简化处理
	site := &model.Site{
		Domain:     domain,
		TargetURL:  pageURL,
		CrawlDepth: depth,
	}
	return cc.CrawlPageWithSite(pageURL, site)
}

// CrawlPageWithSite 爬取单个页面
func (cc *CollyCrawler) CrawlPageWithSite(pageURL string, site *model.Site) error {
	c := colly.NewCollector(
		colly.UserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"),
		colly.AllowURLRevisit(), // 允许重新访问
	)

	var crawlErr error

	c.OnHTML("html", func(e *colly.HTMLElement) {
		html, err := e.DOM.Html()
		if err != nil {
			crawlErr = err
			return
		}

		// 处理并保存
		processedHTML := cc.processor.ProcessHTML(html, pageURL, site.Domain)
		
		content := &model.CachedContent{
			URL:         pageURL,
			ContentType: "text/html; charset=utf-8",
			Data:        []byte(processedHTML),
			Headers:     make(map[string]string),
			CachedAt:    time.Now(),
			ExpiresAt:   time.Now().Add(24 * time.Hour),
		}
		// 添加Colly处理标记
		content.Headers["X-Processed-By"] = "colly"

		if err := cc.cacheService.SaveContent(site.Domain, content); err != nil {
			crawlErr = err
			return
		}

		// 添加到sitemap（预加载的页面也应该被记录）
		if cc.sitemapService != nil && site.EnableSitemap {
			// 解析URL获取路径
			if parsedURL, err := url.Parse(pageURL); err == nil {
				// 异步记录sitemap，避免阻塞爬虫
				go func() {
					if err := cc.sitemapService.AddURL(site.ID, site.Domain, parsedURL.Path); err != nil {
						cc.logger.Error("预加载页面记录sitemap失败", 
							zap.String("domain", site.Domain),
							zap.String("url", parsedURL.Path),
							zap.Error(err))
					} else {
						cc.logger.Debug("预加载页面已记录到sitemap", 
							zap.String("domain", site.Domain),
							zap.String("url", parsedURL.Path))
					}
				}()
			}
		}

		// 如果启用预加载，收集下一层链接
		if site.EnablePreload {
			links := cc.extractLinks(e)
			go cc.preloadLinks(links, site)
		}
	})

	c.OnError(func(r *colly.Response, err error) {
		crawlErr = err
	})

	if err := c.Visit(pageURL); err != nil {
		return err
	}

	c.Wait()
	return crawlErr
}

// extractLinks 提取页面中的链接
func (cc *CollyCrawler) extractLinks(e *colly.HTMLElement) []string {
	var links []string
	seen := make(map[string]bool)

	e.ForEach("a[href]", func(_ int, el *colly.HTMLElement) {
		link := el.Attr("href")
		absoluteURL := e.Request.AbsoluteURL(link)
		
		if !seen[absoluteURL] {
			seen[absoluteURL] = true
			links = append(links, absoluteURL)
		}
	})

	return links
}

// preloadLinks 预加载链接
func (cc *CollyCrawler) preloadLinks(links []string, site *model.Site) {
	for _, link := range links {
		if cc.isSameDomain(link, site.TargetURL) {
			// 检查是否已缓存
			if _, found := cc.cacheService.GetContent(site.Domain, link, 24*time.Hour); !found {
				// 异步爬取
				go func(url string) {
					if err := cc.CrawlPageWithSite(url, site); err != nil {
						cc.logger.Error("预加载失败", zap.String("url", url), zap.Error(err))
					}
				}(link)
			}
		}
	}
}

// downloadResource 下载资源
func (cc *CollyCrawler) downloadResource(resourceURL string, baseURL *url.URL, domain, contentType string) {
	if resourceURL == "" {
		return
	}

	absoluteURL := cc.resolveURL(resourceURL, baseURL)
	if absoluteURL == "" {
		return
	}

	// 异步下载
	go func() {
		if err := cc.downloader.Download(absoluteURL, domain, contentType); err != nil {
			cc.logger.Error("下载资源失败",
				zap.String("url", absoluteURL),
				zap.Error(err),
			)
		}
	}()
}

// isSameDomain 检查是否同域名
func (cc *CollyCrawler) isSameDomain(checkURL, baseURL string) bool {
	u1, err := url.Parse(checkURL)
	if err != nil {
		return false
	}

	u2, err := url.Parse(baseURL)
	if err != nil {
		return false
	}

	return u1.Host == u2.Host
}

// resolveURL 解析URL
func (cc *CollyCrawler) resolveURL(href string, base *url.URL) string {
	u, err := url.Parse(href)
	if err != nil {
		return ""
	}

	return base.ResolveReference(u).String()
}