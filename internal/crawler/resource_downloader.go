package crawler

import (
	"io"
	"net/http"
	"time"

	"site-cluster/internal/cache"
	"site-cluster/internal/model"

	"go.uber.org/zap"
)

// ResourceDownloader 资源下载器
type ResourceDownloader struct {
	logger                *zap.Logger
	cacheService          cache.Service
	client                *http.Client
	systemSettingsService SystemSettingsService
}

// NewResourceDownloader 创建资源下载器
func NewResourceDownloader(logger *zap.Logger, cacheService cache.Service) *ResourceDownloader {
	return &ResourceDownloader{
		logger:       logger,
		cacheService: cacheService,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SetSystemSettingsService 设置系统设置服务
func (rd *ResourceDownloader) SetSystemSettingsService(service SystemSettingsService) {
	rd.systemSettingsService = service
}

// Download 下载资源
func (rd *ResourceDownloader) Download(resourceURL, domain, contentType string) error {
	// 创建请求
	req, err := http.NewRequest("GET", resourceURL, nil)
	if err != nil {
		return err
	}

	// 设置请求头，使用系统设置的随机 User-Agent
	userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
	if rd.systemSettingsService != nil {
		userAgent = rd.systemSettingsService.GetRandomUserAgent()
	}
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Accept", "*/*")

	// 发送请求
	resp, err := rd.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 读取内容
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// 获取实际的内容类型
	actualContentType := resp.Header.Get("Content-Type")
	if actualContentType != "" {
		contentType = actualContentType
	}

	// 保存到缓存
	content := &model.CachedContent{
		URL:         resourceURL,
		ContentType: contentType,
		Data:        data,
		Headers:     make(map[string]string),
		CachedAt:    time.Now(),
		ExpiresAt:   time.Now().Add(7 * 24 * time.Hour), // 资源缓存7天
	}

	// 保存响应头
	for k, v := range resp.Header {
		if len(v) > 0 {
			content.Headers[k] = v[0]
		}
	}

	return rd.cacheService.SaveContent(domain, content)
}

// DownloadBatch 批量下载资源
func (rd *ResourceDownloader) DownloadBatch(resources []ResourceInfo, domain string) {
	for _, res := range resources {
		go func(r ResourceInfo) {
			if err := rd.Download(r.URL, domain, r.ContentType); err != nil {
				rd.logger.Error("下载资源失败",
					zap.String("url", r.URL),
					zap.Error(err),
				)
			}
		}(res)
	}
}

// ResourceInfo 资源信息
type ResourceInfo struct {
	URL         string
	ContentType string
}