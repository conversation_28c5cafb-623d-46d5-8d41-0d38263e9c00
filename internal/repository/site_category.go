package repository

import (
	"site-cluster/internal/model"
	"gorm.io/gorm"
)

// SiteCategoryRepository 站点分类仓储接口
type SiteCategoryRepository interface {
	GetAll() ([]*model.SiteCategory, error)
	GetByID(id uint) (*model.SiteCategory, error)
	Create(category *model.SiteCategory) error
	Update(category *model.SiteCategory) error
	Delete(id uint) error
}

// siteCategoryRepository 站点分类仓储实现
type siteCategoryRepository struct {
	db *gorm.DB
}

// NewSiteCategoryRepository 创建站点分类仓储
func NewSiteCategoryRepository(db *gorm.DB) SiteCategoryRepository {
	return &siteCategoryRepository{db: db}
}

// GetAll 获取所有分类
func (r *siteCategoryRepository) GetAll() ([]*model.SiteCategory, error) {
	var categories []*model.SiteCategory
	err := r.db.Order("sort ASC, id ASC").Find(&categories).Error
	return categories, err
}

// GetByID 根据ID获取分类
func (r *siteCategoryRepository) GetByID(id uint) (*model.SiteCategory, error) {
	var category model.SiteCategory
	err := r.db.First(&category, id).Error
	if err != nil {
		return nil, err
	}
	return &category, nil
}

// Create 创建分类
func (r *siteCategoryRepository) Create(category *model.SiteCategory) error {
	return r.db.Create(category).Error
}

// Update 更新分类（确保更新所有字段包括新增的统计字段）
func (r *siteCategoryRepository) Update(category *model.SiteCategory) error {
	// 使用Updates确保所有字段都被更新
	return r.db.Model(category).Updates(map[string]interface{}{
		"name":                      category.Name,
		"description":               category.Description,
		"icon":                      category.Icon,
		"color":                     category.Color,
		"sort":                      category.Sort,
		"use_independent_analytics": category.UseIndependentAnalytics,
		"analytics_code":            category.AnalyticsCode,
	}).Error
}

// Delete 删除分类
func (r *siteCategoryRepository) Delete(id uint) error {
	return r.db.Delete(&model.SiteCategory{}, id).Error
}