package repository

import (
	"site-cluster/internal/model"
)

// AdminRepository 管理员仓库接口
type AdminRepository interface {
	// Admin 相关
	CreateAdmin(admin *model.Admin) error
	UpdateAdmin(admin *model.Admin) error
	DeleteAdmin(id uint) error
	GetAdminByID(id uint) (*model.Admin, error)
	GetAdminByUsername(username string) (*model.Admin, error)
	GetAdmins(page, pageSize int) ([]*model.Admin, int64, error)
	CountAdmins() (int64, error)
	
	// Session 相关
	CreateSession(session *model.Session) error
	DeleteSession(token string) error
	DeleteExpiredSessions() error
	GetSessionByToken(token string) (*model.Session, error)
	GetSessionsByAdminID(adminID uint) ([]*model.Session, error)
}