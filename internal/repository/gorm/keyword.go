package gorm

import (
	"site-cluster/internal/model"
	"site-cluster/internal/repository"
	
	"gorm.io/gorm"
)

// KeywordRepository 基于GORM的关键词仓储实现
type KeywordRepository struct {
	db *gorm.DB
}

func NewKeywordRepository(db *gorm.DB) repository.KeywordRepository {
	return &KeywordRepository{
		db: db,
	}
}

func (r *KeywordRepository) CreateLibrary(library *model.KeywordLibrary) error {
	return r.db.Create(library).Error
}

func (r *KeywordRepository) GetLibraryByID(id uint) (*model.KeywordLibrary, error) {
	var library model.KeywordLibrary
	err := r.db.Preload("Keywords").First(&library, id).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &library, err
}

func (r *KeywordRepository) GetLibraryByName(name string) (*model.KeywordLibrary, error) {
	var library model.KeywordLibrary
	err := r.db.Preload("Keywords").Where("name = ?", name).First(&library).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &library, err
}

func (r *KeywordRepository) UpdateLibrary(library *model.KeywordLibrary) error {
	return r.db.Save(library).Error
}

func (r *KeywordRepository) DeleteLibrary(id uint) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 删除关键词
		if err := tx.Where("library_id = ?", id).Delete(&model.Keyword{}).Error; err != nil {
			return err
		}
		
		// 删除库
		return tx.Delete(&model.KeywordLibrary{}, id).Error
	})
}

func (r *KeywordRepository) ListLibraries(offset, limit int) ([]*model.KeywordLibrary, error) {
	var libraries []*model.KeywordLibrary
	err := r.db.Offset(offset).Limit(limit).Find(&libraries).Error
	return libraries, err
}

func (r *KeywordRepository) CountLibraries() (int64, error) {
	var count int64
	err := r.db.Model(&model.KeywordLibrary{}).Count(&count).Error
	return count, err
}

func (r *KeywordRepository) CreateKeyword(keyword *model.Keyword) error {
	return r.db.Create(keyword).Error
}

func (r *KeywordRepository) GetKeywordByID(id uint) (*model.Keyword, error) {
	var keyword model.Keyword
	err := r.db.First(&keyword, id).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &keyword, err
}

func (r *KeywordRepository) UpdateKeyword(keyword *model.Keyword) error {
	return r.db.Save(keyword).Error
}

func (r *KeywordRepository) DeleteKeyword(id uint) error {
	return r.db.Delete(&model.Keyword{}, id).Error
}

func (r *KeywordRepository) DeleteKeywordsByLibrary(libraryID uint) error {
	return r.db.Where("library_id = ?", libraryID).Delete(&model.Keyword{}).Error
}

func (r *KeywordRepository) GetKeywordsByLibraryID(libraryID uint, offset, limit int) ([]*model.Keyword, error) {
	var keywords []*model.Keyword
	err := r.db.Where("library_id = ?", libraryID).Offset(offset).Limit(limit).Find(&keywords).Error
	return keywords, err
}

func (r *KeywordRepository) CountKeywordsByLibraryID(libraryID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.Keyword{}).Where("library_id = ?", libraryID).Count(&count).Error
	return count, err
}

func (r *KeywordRepository) SearchKeywords(libraryID uint, query string) ([]*model.Keyword, error) {
	var keywords []*model.Keyword
	q := r.db.Where("library_id = ?", libraryID)
	if query != "" {
		q = q.Where("word LIKE ?", "%"+query+"%")
	}
	err := q.Find(&keywords).Error
	return keywords, err
}

func (r *KeywordRepository) CountLibrariesByType() (map[string]int64, error) {
	stats := make(map[string]int64)
	
	var results []struct {
		Type  string
		Count int64
	}
	
	err := r.db.Model(&model.KeywordLibrary{}).
		Select("type, count(*) as count").
		Group("type").
		Find(&results).Error
		
	if err != nil {
		return nil, err
	}
	
	for _, result := range results {
		stats[result.Type] = result.Count
	}
	
	return stats, nil
}

func (r *KeywordRepository) GetKeywordByText(libraryID uint, text string) (*model.Keyword, error) {
	var keyword model.Keyword
	err := r.db.Where("library_id = ? AND keyword = ?", libraryID, text).First(&keyword).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &keyword, err
}

func (r *KeywordRepository) ListKeywords(libraryID uint, offset, limit int, search string) ([]*model.Keyword, error) {
	var keywords []*model.Keyword
	q := r.db.Where("library_id = ?", libraryID)
	if search != "" {
		q = q.Where("keyword LIKE ?", "%"+search+"%")
	}
	err := q.Offset(offset).Limit(limit).Find(&keywords).Error
	return keywords, err
}

func (r *KeywordRepository) CountKeywords(libraryID uint, search string) (int64, error) {
	var count int64
	q := r.db.Model(&model.Keyword{}).Where("library_id = ?", libraryID)
	if search != "" {
		q = q.Where("keyword LIKE ?", "%"+search+"%")
	}
	err := q.Count(&count).Error
	return count, err
}

func (r *KeywordRepository) CountKeywordsByLibrary(libraryID uint) (int64, error) {
	var count int64
	err := r.db.Model(&model.Keyword{}).Where("library_id = ?", libraryID).Count(&count).Error
	return count, err
}

func (r *KeywordRepository) GetAllKeywordsByLibrary(libraryID uint) ([]*model.Keyword, error) {
	var keywords []*model.Keyword
	err := r.db.Where("library_id = ?", libraryID).Find(&keywords).Error
	return keywords, err
}

// GetRandomKeywordsByLibrary 随机获取指定数量的关键词
func (r *KeywordRepository) GetRandomKeywordsByLibrary(libraryID uint, limit int) ([]*model.Keyword, error) {
	var keywords []*model.Keyword
	// PostgreSQL使用RANDOM()，MySQL使用RAND()
	err := r.db.Where("library_id = ?", libraryID).
		Order("RANDOM()").
		Limit(limit).
		Find(&keywords).Error
	return keywords, err
}

func (r *KeywordRepository) CountAllKeywords() (int64, error) {
	var count int64
	err := r.db.Model(&model.Keyword{}).Count(&count).Error
	return count, err
}