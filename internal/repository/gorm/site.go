package gorm

import (
	"fmt"
	"site-cluster/internal/model"
	"site-cluster/internal/repository"
	"strconv"
	"strings"
	"time"
	
	"gorm.io/gorm"
)

// SiteRepository 基于GORM的站点仓储实现
type SiteRepository struct {
	db *gorm.DB
}

func NewSiteRepository(db *gorm.DB) repository.SiteRepository {
	return &SiteRepository{
		db: db,
	}
}

func (r *SiteRepository) Create(site *model.Site) error {
	// 使用事务确保数据一致性
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 创建站点记录
		if err := tx.Create(site).Error; err != nil {
			return err
		}
		
		// 如果有子域名，创建子域名记录
		if len(site.Aliases) > 0 {
			for i := range site.Aliases {
				site.Aliases[i].SiteID = int(site.ID)
				if err := tx.Create(&site.Aliases[i]).Error; err != nil {
					return err
				}
			}
		}
		
		return nil
	})
}

func (r *SiteRepository) GetByID(id uint) (*model.Site, error) {
	var site model.Site
	err := r.db.Preload("InjectConfig").Preload("RouteRules").Preload("Category").Preload("Aliases").First(&site, id).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &site, err
}

func (r *SiteRepository) GetByDomain(domain string) (*model.Site, error) {
	var site model.Site
	err := r.db.Preload("InjectConfig").Preload("RouteRules").Preload("Category").Preload("Aliases").Where("domain = ?", domain).First(&site).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &site, err
}

// UpdateCacheStatus 更新缓存状态
func (r *SiteRepository) UpdateCacheStatus(siteID uint, status string, errMsg string, updateTime time.Time) error {
	updates := map[string]interface{}{
		"cache_status": status,
		"cache_error": errMsg,
		"cache_update_at": updateTime,
	}
	return r.db.Model(&model.Site{}).Where("id = ?", siteID).Updates(updates).Error
}

func (r *SiteRepository) Update(site *model.Site) error {
	// 使用事务确保数据一致性
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 使用Updates而不是Save，只更新非零值字段
		// 但是CategoryID是指针类型，需要特殊处理
		updates := map[string]interface{}{
			"domain": site.Domain,
			"target_url": site.TargetURL,
			"category_id": site.CategoryID,
			"crawl_depth": site.CrawlDepth,
			"status": site.Status,
			"enable_cache": site.EnableCache,
			"enable_preload": site.EnablePreload,
			"download_external_resources": site.DownloadExternalResources,
			"enable_https_check": site.EnableHTTPSCheck,
			"enable_traditional_convert": site.EnableTraditionalConvert,
			"use_global_ua_check": site.UseGlobalUACheck,  // 添加这个字段！
			"enable_ua_check": site.EnableUACheck,
			"allowed_ua": site.AllowedUA,
			"non_spider_html": site.NonSpiderHTML,
			"enable_spider_block": site.EnableSpiderBlock,
			"use_global_spider_ua": site.UseGlobalSpiderUA,
			"custom_spider_ua": site.CustomSpiderUA,
			"spider_block_403_template": site.SpiderBlock403Template,
			"use_global_cache": site.UseGlobalCache,
			"cache_max_size": site.CacheMaxSize,
			"cache_home_ttl": site.CacheHomeTTL,
			"cache_other_ttl": site.CacheOtherTTL,
			"enable_redis_cache": site.EnableRedisCache,
			"enable_sitemap": site.EnableSitemap,
			"sitemap_update_interval": site.SitemapUpdateInterval,
			"sitemap_changefreq": site.SitemapChangefreq,
			"sitemap_priority": site.SitemapPriority,
			"sitemap_max_urls": site.SitemapMaxUrls,
			"redirect_www": site.RedirectWWW,
			"enable_company_name": site.EnableCompanyName,
			"company_library_id": site.CompanyLibraryID,
			"company_name": site.CompanyName,
			// 来源判断配置
			"use_global_referer_check": site.UseGlobalRefererCheck,
			"enable_referer_check": site.EnableRefererCheck,
			"allowed_referers": site.AllowedReferers,
			"referer_block_code": site.RefererBlockCode,
			"referer_block_html": site.RefererBlockHTML,
			// 统计设置配置
			"use_global_analytics": site.UseGlobalAnalytics,
			"enable_analytics": site.EnableAnalytics,
			// 缓存状态字段
			"cache_status": site.CacheStatus,
			"cache_error": site.CacheError,
			"cache_update_at": site.CacheUpdateAt,
			"updated_at": time.Now(),
		}
		
		if err := tx.Model(&model.Site{}).Where("id = ?", site.ID).Updates(updates).Error; err != nil {
			return err
		}
		
		// 更新子域名
		// 先删除现有的所有子域名
		if err := tx.Where("site_id = ?", site.ID).Delete(&model.SiteAlias{}).Error; err != nil {
			return err
		}
		
		// 然后添加新的子域名
		if len(site.Aliases) > 0 {
			for i := range site.Aliases {
				site.Aliases[i].SiteID = int(site.ID)
				if err := tx.Create(&site.Aliases[i]).Error; err != nil {
					return err
				}
			}
		}
		
		// 如果有注入配置，也更新它
		if site.InjectConfig != nil {
			site.InjectConfig.SiteID = site.ID
			
			// 先查询现有的inject_config
			var existingConfig model.InjectConfig
			err := tx.Where("site_id = ?", site.ID).First(&existingConfig).Error
			if err == nil {
			// 如果存在，构建更新map，包含所有需要更新的字段
			updates := make(map[string]interface{})
			
			// 处理所有布尔类型字段
			updates["enable_keyword"] = site.InjectConfig.EnableKeyword
			updates["enable_structure"] = site.InjectConfig.EnableStructure
			updates["enable_pseudo"] = site.InjectConfig.EnablePseudo
			updates["filter_external_links"] = site.InjectConfig.FilterExternalLinks
			updates["enable_unicode"] = site.InjectConfig.EnableUnicode
			updates["enable_random_string"] = site.InjectConfig.EnableRandomString
			updates["enable_pinyin"] = site.InjectConfig.EnablePinyin
			// 拼音特殊字符配置
			updates["use_global_pinyin"] = site.InjectConfig.UseGlobalPinyin
			updates["enable_pinyin_special_chars"] = site.InjectConfig.EnablePinyinSpecialChars
			updates["pinyin_special_chars_ratio"] = site.InjectConfig.PinyinSpecialCharsRatio
			updates["pinyin_special_chars"] = site.InjectConfig.PinyinSpecialChars
			updates["enable_hidden_html"] = site.InjectConfig.EnableHiddenHTML
			updates["enable_h1_tag"] = site.InjectConfig.EnableH1Tag
			updates["enable_unicode_title"] = site.InjectConfig.EnableUnicodeTitle
			updates["enable_unicode_keywords"] = site.InjectConfig.EnableUnicodeKeywords
			updates["enable_unicode_desc"] = site.InjectConfig.EnableUnicodeDesc
			updates["hidden_html_random_id"] = site.InjectConfig.HiddenHTMLRandomID
			updates["keyword_inject_title"] = site.InjectConfig.KeywordInjectTitle
			updates["keyword_inject_meta"] = site.InjectConfig.KeywordInjectMeta
			updates["keyword_inject_desc"] = site.InjectConfig.KeywordInjectDesc
			updates["keyword_inject_body"] = site.InjectConfig.KeywordInjectBody
			updates["keyword_inject_hidden"] = site.InjectConfig.KeywordInjectHidden
			updates["keyword_inject_h1"] = site.InjectConfig.KeywordInjectH1
			updates["keyword_inject_h2"] = site.InjectConfig.KeywordInjectH2
			updates["keyword_inject_alt"] = site.InjectConfig.KeywordInjectAlt
			
			// 处理字符串字段 - 只有非空才更新
			if site.InjectConfig.TitleTemplate != "" {
				updates["title_template"] = site.InjectConfig.TitleTemplate
			}
			if site.InjectConfig.MetaTemplate != "" {
				updates["meta_template"] = site.InjectConfig.MetaTemplate
			}
			if site.InjectConfig.HomeTitle != "" {
				updates["home_title"] = site.InjectConfig.HomeTitle
			}
			if site.InjectConfig.HomeDescription != "" {
				updates["home_description"] = site.InjectConfig.HomeDescription
			}
			if site.InjectConfig.HomeKeywords != "" {
				updates["home_keywords"] = site.InjectConfig.HomeKeywords
			}
			if site.InjectConfig.UnicodeScope != "" {
				updates["unicode_scope"] = site.InjectConfig.UnicodeScope
			}
			if site.InjectConfig.RandomStringPrefix != "" {
				updates["random_string_prefix"] = site.InjectConfig.RandomStringPrefix
			}
			if site.InjectConfig.HiddenHTMLPosition != "" {
				updates["hidden_html_position"] = site.InjectConfig.HiddenHTMLPosition
			}
			if site.InjectConfig.H1TagPosition != "" {
				updates["h1_tag_position"] = site.InjectConfig.H1TagPosition
			}
			if site.InjectConfig.KeywordTitleTemplate != "" {
				updates["keyword_title_template"] = site.InjectConfig.KeywordTitleTemplate
			}
			if site.InjectConfig.KeywordMetaTemplate != "" {
				updates["keyword_meta_template"] = site.InjectConfig.KeywordMetaTemplate
			}
			if site.InjectConfig.KeywordDescTemplate != "" {
				updates["keyword_desc_template"] = site.InjectConfig.KeywordDescTemplate
			}
			
			// 处理数值字段
			if site.InjectConfig.RandomStringLength > 0 {
				updates["random_string_length"] = site.InjectConfig.RandomStringLength
			}
			if site.InjectConfig.HiddenHTMLLength > 0 {
				updates["hidden_html_length"] = site.InjectConfig.HiddenHTMLLength
			}
			if site.InjectConfig.KeywordMaxPerPage > 0 {
				updates["keyword_max_per_page"] = site.InjectConfig.KeywordMaxPerPage
			}
			if site.InjectConfig.KeywordMinWordCount > 0 {
				updates["keyword_min_word_count"] = site.InjectConfig.KeywordMinWordCount
			}
			if site.InjectConfig.StructureMinPerPage > 0 {
				updates["structure_min_per_page"] = site.InjectConfig.StructureMinPerPage
			}
			if site.InjectConfig.StructureMaxPerPage > 0 {
				updates["structure_max_per_page"] = site.InjectConfig.StructureMaxPerPage
			}
			if site.InjectConfig.KeywordInjectRatio > 0 {
				updates["keyword_inject_ratio"] = site.InjectConfig.KeywordInjectRatio
			}
			if site.InjectConfig.KeywordDensity > 0 {
				updates["keyword_density"] = site.InjectConfig.KeywordDensity
			}
			
			// 处理JSON字段
			if site.InjectConfig.Keywords != nil {
				updates["keywords"] = site.InjectConfig.Keywords
			}
			if site.InjectConfig.PseudoLibraryIDs != nil {
				updates["pseudo_library_ids"] = site.InjectConfig.PseudoLibraryIDs
			}
			if site.InjectConfig.KeywordLibraryIDs != nil {
				updates["keyword_library_ids"] = site.InjectConfig.KeywordLibraryIDs
			}
			if site.InjectConfig.TitleKeywordLibraryIDs != nil {
				updates["title_keyword_library_ids"] = site.InjectConfig.TitleKeywordLibraryIDs
			}
			if site.InjectConfig.MetaKeywordLibraryIDs != nil {
				updates["meta_keyword_library_ids"] = site.InjectConfig.MetaKeywordLibraryIDs
			}
			if site.InjectConfig.DescKeywordLibraryIDs != nil {
				updates["desc_keyword_library_ids"] = site.InjectConfig.DescKeywordLibraryIDs
			}
			if site.InjectConfig.StructureLibraryIDs != nil {
				updates["structure_library_ids"] = site.InjectConfig.StructureLibraryIDs
			}
			
			// 首页关键词注入配置
			updates["enable_home_keyword_inject"] = site.InjectConfig.EnableHomeKeywordInject
			if site.InjectConfig.HomeKeywordLibraryIDs != nil {
				updates["home_keyword_library_ids"] = site.InjectConfig.HomeKeywordLibraryIDs
			}
			if site.InjectConfig.HomeKeywordInjectCount > 0 {
				updates["home_keyword_inject_count"] = site.InjectConfig.HomeKeywordInjectCount
			}
			updates["enable_home_keyword_unicode"] = site.InjectConfig.EnableHomeKeywordUnicode
			
			// 执行更新
			if err := tx.Model(&existingConfig).Updates(updates).Error; err != nil {
				return err
			}
		} else if err == gorm.ErrRecordNotFound {
			// 如果不存在，创建新的
			if err := tx.Create(site.InjectConfig).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}
	
	return nil
	})
}

func (r *SiteRepository) Delete(id uint) error {
	// 先获取站点信息
	var site model.Site
	if err := r.db.First(&site, id).Error; err != nil {
		return err
	}
	
	// 不使用事务，分别删除各个表的数据，避免因为某个表不存在导致整个事务失败
	
	// 删除子域名记录
	r.db.Where("site_id = ?", id).Delete(&model.SiteAlias{})
	
	// 删除注入配置
	r.db.Where("site_id = ?", id).Delete(&model.InjectConfig{})
	
	// 删除路由规则
	r.db.Where("site_id = ?", id).Delete(&model.RouteRule{})
	
	// 删除爬虫统计记录 (按域名删除)
	r.db.Where("domain = ?", site.Domain).Delete(&model.SpiderStats{})
	
	// 删除权重记录 - 如果表存在的话
	// 使用原生SQL，避免GORM的表名映射问题
	r.db.Exec("DELETE FROM weight_histories WHERE domain = ?", site.Domain)
	
	// 删除sitemap记录 - 如果表存在的话
	r.db.Exec("DELETE FROM sitemap_entries WHERE site_id = ? OR domain = ?", id, site.Domain)
	
	// 删除爬取任务日志
	r.db.Where("site_id = ?", id).Delete(&model.CrawlJob{})
	r.db.Where("site_id = ?", id).Delete(&model.TaskLog{})
	
	// 最后删除站点本身
	return r.db.Delete(&model.Site{}, id).Error
}

func (r *SiteRepository) List(offset, limit int, conditions map[string]interface{}) ([]*model.Site, error) {
	var sites []*model.Site
	query := r.db.Preload("InjectConfig").Preload("RouteRules").Preload("Category").Preload("Aliases")
	
	// 应用条件
	if status, ok := conditions["status"]; ok && status != "" {
		query = query.Where("status = ?", status)
	}
	
	// 分类筛选
	if categoryID, ok := conditions["category_id"]; ok {
		// 尝试多种类型断言
		switch v := categoryID.(type) {
		case uint:
			if v > 0 {
				query = query.Where("category_id = ?", v)
			}
		case int:
			if v > 0 {
				query = query.Where("category_id = ?", v)
			}
		case int64:
			if v > 0 {
				query = query.Where("category_id = ?", v)
			}
		case float64:
			if v > 0 {
				query = query.Where("category_id = ?", uint(v))
			}
		default:
			// 尝试转换为字符串再转换为数字
			if str, ok := categoryID.(string); ok {
				if id, err := strconv.ParseUint(str, 10, 32); err == nil && id > 0 {
					query = query.Where("category_id = ?", id)
				}
			}
		}
	}
	
	// 关键词搜索（支持批量搜索，逗号分隔）
	if keyword, ok := conditions["keyword"]; ok && keyword != "" {
		keywordStr := keyword.(string)
		// 检查是否包含逗号（批量搜索）
		if strings.Contains(keywordStr, ",") {
			// 批量搜索：分割关键词并构建OR条件
			keywords := strings.Split(keywordStr, ",")
			orConditions := make([]string, 0)
			args := make([]interface{}, 0)
			
			for _, kw := range keywords {
				kw = strings.TrimSpace(kw)
				if kw != "" {
					orConditions = append(orConditions, "(domain LIKE ? OR target_url LIKE ?)")
					args = append(args, "%"+kw+"%", "%"+kw+"%")
				}
			}
			
			if len(orConditions) > 0 {
				query = query.Where(strings.Join(orConditions, " OR "), args...)
			}
		} else {
			// 单个关键词搜索
			query = query.Where("domain LIKE ? OR target_url LIKE ?", "%"+keywordStr+"%", "%"+keywordStr+"%")
		}
	}
	
	// 构建排序语句
	orderBy := "id DESC" // 默认排序
	if sortBy, ok := conditions["sort_by"]; ok && sortBy != "" {
		sortField := sortBy.(string)
		sortOrder := "DESC"
		if order, ok := conditions["sort_order"]; ok && order != "" {
			sortOrder = strings.ToUpper(order.(string))
			if sortOrder != "ASC" && sortOrder != "DESC" {
				sortOrder = "DESC"
			}
		}
		
		// 处理特殊字段映射
		switch sortField {
		case "cache_status":
			// 对于cache_status，使用CASE语句进行自定义排序
			// success=3, updating=2, pending=1, failed=0
			if sortOrder == "DESC" {
				orderBy = `CASE cache_status 
					WHEN 'success' THEN 3 
					WHEN 'updating' THEN 2 
					WHEN 'pending' THEN 1 
					WHEN 'failed' THEN 0 
					ELSE -1 END DESC, id DESC`
			} else {
				orderBy = `CASE cache_status 
					WHEN 'failed' THEN 0 
					WHEN 'pending' THEN 1 
					WHEN 'updating' THEN 2 
					WHEN 'success' THEN 3 
					ELSE 4 END ASC, id ASC`
			}
		case "id", "domain", "status", "created_at", "updated_at", "cache_update_at":
			orderBy = fmt.Sprintf("%s %s", sortField, sortOrder)
		case "count_404", "count404":
			// count404字段在数据库中的列名是count404
			orderBy = fmt.Sprintf("count404 %s", sortOrder)
		case "cache_size":
			// cache_size 不是数据库字段，无法直接排序
			// 保持默认排序
			orderBy = "id DESC"
		case "sitemap_count":
			// sitemap_count 不是数据库字段，无法直接排序
			// 保持默认排序
			orderBy = "id DESC"
		default:
			orderBy = "id DESC"
		}
	}
	
	// 分页
	query = query.Order(orderBy)
	if offset > 0 {
		query = query.Offset(offset)
	}
	if limit > 0 {
		query = query.Limit(limit)
	}
	err := query.Find(&sites).Error
	return sites, err
}

func (r *SiteRepository) Count(conditions map[string]interface{}) (int64, error) {
	var count int64
	query := r.db.Model(&model.Site{})
	
	// 应用条件
	if status, ok := conditions["status"]; ok {
		query = query.Where("status = ?", status)
	}
	
	// 分类筛选
	if categoryID, ok := conditions["category_id"]; ok {
		query = query.Where("category_id = ?", categoryID)
	}
	
	// 关键词搜索（支持批量搜索，逗号分隔）
	if keyword, ok := conditions["keyword"]; ok && keyword != "" {
		keywordStr := keyword.(string)
		// 检查是否包含逗号（批量搜索）
		if strings.Contains(keywordStr, ",") {
			// 批量搜索：分割关键词并构建OR条件
			keywords := strings.Split(keywordStr, ",")
			orConditions := make([]string, 0)
			args := make([]interface{}, 0)
			
			for _, kw := range keywords {
				kw = strings.TrimSpace(kw)
				if kw != "" {
					orConditions = append(orConditions, "(domain LIKE ? OR target_url LIKE ?)")
					args = append(args, "%"+kw+"%", "%"+kw+"%")
				}
			}
			
			if len(orConditions) > 0 {
				query = query.Where(strings.Join(orConditions, " OR "), args...)
			}
		} else {
			// 单个关键词搜索
			query = query.Where("domain LIKE ? OR target_url LIKE ?", "%"+keywordStr+"%", "%"+keywordStr+"%")
		}
	}
	
	err := query.Count(&count).Error
	return count, err
}

func (r *SiteRepository) CreateCrawlJob(job *model.CrawlJob) error {
	return r.db.Create(job).Error
}

func (r *SiteRepository) GetCrawlJobStats(siteID uint) (map[string]int64, error) {
	stats := make(map[string]int64)
	
	// 获取各状态的统计
	var results []struct {
		Status string
		Count  int64
	}
	
	err := r.db.Model(&model.CrawlJob{}).
		Select("status, count(*) as count").
		Where("site_id = ?", siteID).
		Group("status").
		Find(&results).Error
		
	if err != nil {
		return nil, err
	}
	
	total := int64(0)
	for _, result := range results {
		stats[result.Status] = result.Count
		total += result.Count
	}
	stats["total"] = total
	
	return stats, nil
}

func (r *SiteRepository) GetCacheStats(domain string) (map[string]interface{}, error) {
	// 这个方法返回缓存统计，实际实现可能需要从文件系统获取
	return map[string]interface{}{
		"size":        0,
		"files_count": 0,
	}, nil
}

// tableExists 检查表是否存在
func (r *SiteRepository) tableExists(tx *gorm.DB, tableName string) bool {
	var exists bool
	query := `SELECT EXISTS (
		SELECT FROM information_schema.tables 
		WHERE table_schema = 'public' 
		AND table_name = ?
	)`
	tx.Raw(query, tableName).Scan(&exists)
	return exists
}

// columnExists 检查列是否存在
func (r *SiteRepository) columnExists(tx *gorm.DB, tableName, columnName string) bool {
	var exists bool
	query := `SELECT EXISTS (
		SELECT FROM information_schema.columns 
		WHERE table_schema = 'public' 
		AND table_name = ? 
		AND column_name = ?
	)`
	tx.Raw(query, tableName, columnName).Scan(&exists)
	return exists
}

// GetDB 获取数据库实例
func (r *SiteRepository) GetDB() interface{} {
	return r.db
}

// DetectOrphanedData 检测孤立数据
func (r *SiteRepository) DetectOrphanedData() ([]model.OrphanedData, error) {
	var orphanedData []model.OrphanedData
	
	// 查找孤立的 inject_configs（没有对应的 sites 记录）
	var orphanedConfigs []struct {
		ID        uint      `json:"id"`
		SiteID    uint      `json:"site_id"`
		CreatedAt time.Time `json:"created_at"`
	}
	
	err := r.db.Raw(`
		SELECT ic.id, ic.site_id, ic.created_at 
		FROM inject_configs ic 
		LEFT JOIN sites s ON ic.site_id = s.id 
		WHERE s.id IS NULL
	`).Scan(&orphanedConfigs).Error
	
	if err != nil {
		return nil, err
	}
	
	// 转换为 OrphanedData 格式
	for _, config := range orphanedConfigs {
		orphanedData = append(orphanedData, model.OrphanedData{
			Type:        "inject_config",
			ID:          config.ID,
			SiteID:      config.SiteID,
			Domain:      "",
			Description: fmt.Sprintf("孤立的注入配置，站点ID: %d", config.SiteID),
			CreatedAt:   config.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	
	// 查找孤立的 route_rules（没有对应的 sites 记录）
	var orphanedRules []struct {
		ID        uint      `json:"id"`
		SiteID    uint      `json:"site_id"`
		CreatedAt time.Time `json:"created_at"`
	}
	
	err = r.db.Raw(`
		SELECT rr.id, rr.site_id, rr.created_at 
		FROM route_rules rr 
		LEFT JOIN sites s ON rr.site_id = s.id 
		WHERE s.id IS NULL
	`).Scan(&orphanedRules).Error
	
	if err == nil { // 如果表不存在也不报错
		for _, rule := range orphanedRules {
			orphanedData = append(orphanedData, model.OrphanedData{
				Type:        "route_rule",
				ID:          rule.ID,
				SiteID:      rule.SiteID,
				Domain:      "",
				Description: fmt.Sprintf("孤立的路由规则，站点ID: %d", rule.SiteID),
				CreatedAt:   rule.CreatedAt.Format("2006-01-02 15:04:05"),
			})
		}
	}
	
	// 确保返回空数组而不是nil
	if orphanedData == nil {
		orphanedData = []model.OrphanedData{}
	}
	return orphanedData, nil
}

// CleanupAllOrphanedData 批量清理所有孤立数据
func (r *SiteRepository) CleanupAllOrphanedData() (int, error) {
	totalCleaned := 0
	
	// 清理孤立的 inject_configs
	result := r.db.Exec(`
		DELETE FROM inject_configs 
		WHERE site_id NOT IN (SELECT id FROM sites)
	`)
	if result.Error != nil {
		return 0, result.Error
	}
	totalCleaned += int(result.RowsAffected)
	
	// 清理孤立的 route_rules（如果表存在）
	result = r.db.Exec(`
		DELETE FROM route_rules 
		WHERE site_id NOT IN (SELECT id FROM sites)
	`)
	if result.Error == nil { // 如果表不存在，忽略错误
		totalCleaned += int(result.RowsAffected)
	}
	
	return totalCleaned, nil
}

// GetByAliasDomain 通过子域名获取站点
func (r *SiteRepository) GetByAliasDomain(domain string) (*model.Site, error) {
	var alias model.SiteAlias
	err := r.db.Where("alias_domain = ? AND is_active = ?", domain, true).First(&alias).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	
	// 根据站点ID获取站点信息
	return r.GetByID(uint(alias.SiteID))
}

// GetSiteAliases 获取站点的所有子域名
func (r *SiteRepository) GetSiteAliases(siteID uint) ([]model.SiteAlias, error) {
	var aliases []model.SiteAlias
	err := r.db.Where("site_id = ?", siteID).Order("id ASC").Find(&aliases).Error
	return aliases, err
}

// CreateSiteAlias 创建子域名映射
func (r *SiteRepository) CreateSiteAlias(alias *model.SiteAlias) error {
	return r.db.Create(alias).Error
}

// DeleteSiteAliases 删除站点的所有子域名
func (r *SiteRepository) DeleteSiteAliases(siteID uint) error {
	return r.db.Where("site_id = ?", siteID).Delete(&model.SiteAlias{}).Error
}