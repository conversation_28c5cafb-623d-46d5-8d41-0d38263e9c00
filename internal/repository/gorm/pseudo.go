package gorm

import (
	"site-cluster/internal/model"
	"gorm.io/gorm"
)

type PseudoRepository struct {
	db *gorm.DB
}

func NewPseudoRepository(db *gorm.DB) *PseudoRepository {
	return &PseudoRepository{db: db}
}

// GetLibraries 获取所有词库
func (r *PseudoRepository) GetLibraries() ([]model.PseudoLibrary, error) {
	var libraries []model.PseudoLibrary
	err := r.db.Find(&libraries).Error
	return libraries, err
}

// GetLibrary 获取词库详情
func (r *PseudoRepository) GetLibrary(id uint) (*model.PseudoLibrary, error) {
	var library model.PseudoLibrary
	err := r.db.Preload("Words").First(&library, id).Error
	return &library, err
}

// CreateLibrary 创建词库
func (r *PseudoRepository) CreateLibrary(library *model.PseudoLibrary) error {
	return r.db.Create(library).Error
}

// UpdateLibrary 更新词库
func (r *PseudoRepository) UpdateLibrary(library *model.PseudoLibrary) error {
	return r.db.Save(library).Error
}

// DeleteLibrary 删除词库
func (r *PseudoRepository) DeleteLibrary(id uint) error {
	return r.db.Delete(&model.PseudoLibrary{}, id).Error
}

// GetWords 获取词库的词条
func (r *PseudoRepository) GetWords(libraryID uint) ([]model.PseudoWord, error) {
	var words []model.PseudoWord
	err := r.db.Where("library_id = ?", libraryID).Find(&words).Error
	return words, err
}

// GetWord 获取词条
func (r *PseudoRepository) GetWord(id uint) (*model.PseudoWord, error) {
	var word model.PseudoWord
	err := r.db.First(&word, id).Error
	return &word, err
}

// CreateWord 创建词条
func (r *PseudoRepository) CreateWord(word *model.PseudoWord) error {
	return r.db.Create(word).Error
}

// UpdateWord 更新词条
func (r *PseudoRepository) UpdateWord(word *model.PseudoWord) error {
	return r.db.Save(word).Error
}

// DeleteWord 删除词条
func (r *PseudoRepository) DeleteWord(id uint) error {
	return r.db.Delete(&model.PseudoWord{}, id).Error
}

// GetLibrariesByIDs 根据ID列表获取词库
func (r *PseudoRepository) GetLibrariesByIDs(ids []uint) ([]model.PseudoLibrary, error) {
	var libraries []model.PseudoLibrary
	if len(ids) == 0 {
		return libraries, nil
	}
	err := r.db.Preload("Words").Where("id IN ?", ids).Find(&libraries).Error
	return libraries, err
}