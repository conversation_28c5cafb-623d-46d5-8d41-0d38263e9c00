package gorm

import (
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"

	"gorm.io/gorm"
)

type adminRepository struct {
	db *gorm.DB
}

// NewAdminRepository 创建管理员仓库
func NewAdminRepository(db *gorm.DB) repository.AdminRepository {
	return &adminRepository{db: db}
}

// CreateAdmin 创建管理员
func (r *adminRepository) CreateAdmin(admin *model.Admin) error {
	return r.db.Create(admin).Error
}

// UpdateAdmin 更新管理员
func (r *adminRepository) UpdateAdmin(admin *model.Admin) error {
	return r.db.Save(admin).Error
}

// DeleteAdmin 删除管理员
func (r *adminRepository) DeleteAdmin(id uint) error {
	return r.db.Delete(&model.Admin{}, id).Error
}

// GetAdminByID 根据ID获取管理员
func (r *adminRepository) GetAdminByID(id uint) (*model.Admin, error) {
	var admin model.Admin
	err := r.db.First(&admin, id).Error
	if err != nil {
		return nil, err
	}
	return &admin, nil
}

// GetAdminByUsername 根据用户名获取管理员
func (r *adminRepository) GetAdminByUsername(username string) (*model.Admin, error) {
	var admin model.Admin
	err := r.db.Where("username = ?", username).First(&admin).Error
	if err != nil {
		return nil, err
	}
	return &admin, nil
}

// GetAdmins 获取管理员列表
func (r *adminRepository) GetAdmins(page, pageSize int) ([]*model.Admin, int64, error) {
	var admins []*model.Admin
	var total int64
	
	// 计算总数
	if err := r.db.Model(&model.Admin{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	err := r.db.Offset(offset).Limit(pageSize).Find(&admins).Error
	
	return admins, total, err
}

// CountAdmins 统计管理员数量
func (r *adminRepository) CountAdmins() (int64, error) {
	var count int64
	err := r.db.Model(&model.Admin{}).Count(&count).Error
	return count, err
}

// CreateSession 创建会话
func (r *adminRepository) CreateSession(session *model.Session) error {
	return r.db.Create(session).Error
}

// DeleteSession 删除会话
func (r *adminRepository) DeleteSession(token string) error {
	return r.db.Where("token = ?", token).Delete(&model.Session{}).Error
}

// DeleteExpiredSessions 删除过期会话
func (r *adminRepository) DeleteExpiredSessions() error {
	return r.db.Where("expired_at < ?", time.Now()).Delete(&model.Session{}).Error
}

// GetSessionByToken 根据令牌获取会话
func (r *adminRepository) GetSessionByToken(token string) (*model.Session, error) {
	var session model.Session
	err := r.db.Where("token = ?", token).First(&session).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

// GetSessionsByAdminID 获取管理员的所有会话
func (r *adminRepository) GetSessionsByAdminID(adminID uint) ([]*model.Session, error) {
	var sessions []*model.Session
	err := r.db.Where("admin_id = ?", adminID).Find(&sessions).Error
	return sessions, err
}