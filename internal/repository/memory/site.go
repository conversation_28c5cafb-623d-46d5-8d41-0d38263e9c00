package memory

import (
	"errors"
	"sync"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"
)

// SiteRepository 内存实现的站点仓储
type SiteRepository struct {
	mu    sync.RWMutex
	sites map[uint]*model.Site
	jobs  map[uint]*model.CrawlJob
	idSeq uint
}

func NewSiteRepository() repository.SiteRepository {
	return &SiteRepository{
		sites: make(map[uint]*model.Site),
		jobs:  make(map[uint]*model.CrawlJob),
		idSeq: 1,
	}
}

func (r *SiteRepository) Create(site *model.Site) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// 检查域名是否已存在
	for _, s := range r.sites {
		if s.Domain == site.Domain {
			return errors.New("域名已存在")
		}
	}
	
	site.ID = r.idSeq
	r.idSeq++
	site.CreatedAt = time.Now()
	site.UpdatedAt = time.Now()
	
	r.sites[site.ID] = site
	return nil
}

func (r *SiteRepository) GetByID(id uint) (*model.Site, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	site, exists := r.sites[id]
	if !exists {
		return nil, nil
	}
	
	return site, nil
}

func (r *SiteRepository) GetByDomain(domain string) (*model.Site, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, site := range r.sites {
		if site.Domain == domain {
			return site, nil
		}
	}
	
	return nil, nil
}

// UpdateCacheStatus 更新缓存状态
func (r *SiteRepository) UpdateCacheStatus(siteID uint, status string, errMsg string, updateTime time.Time) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if s, exists := r.sites[siteID]; exists {
		s.CacheStatus = status
		s.CacheError = errMsg
		s.CacheUpdateAt = updateTime
		return nil
	}
	return errors.New("站点不存在")
}

func (r *SiteRepository) Update(site *model.Site) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.sites[site.ID]; !exists {
		return errors.New("站点不存在")
	}
	
	site.UpdatedAt = time.Now()
	r.sites[site.ID] = site
	return nil
}

func (r *SiteRepository) Delete(id uint) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.sites[id]; !exists {
		return errors.New("站点不存在")
	}
	
	delete(r.sites, id)
	return nil
}

func (r *SiteRepository) List(offset, limit int, conditions map[string]interface{}) ([]*model.Site, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var result []*model.Site
	
	// 过滤
	for _, site := range r.sites {
		match := true
		
		// 检查条件
		if status, ok := conditions["status"]; ok && site.Status != status {
			match = false
		}
		
		if match {
			result = append(result, site)
		}
	}
	
	// 分页
	total := len(result)
	start := offset
	end := offset + limit
	
	if start >= total {
		return []*model.Site{}, nil
	}
	
	if end > total {
		end = total
	}
	
	return result[start:end], nil
}

func (r *SiteRepository) Count(conditions map[string]interface{}) (int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	count := 0
	
	for _, site := range r.sites {
		match := true
		
		if status, ok := conditions["status"]; ok && site.Status != status {
			match = false
		}
		
		if match {
			count++
		}
	}
	
	return int64(count), nil
}

func (r *SiteRepository) CreateCrawlJob(job *model.CrawlJob) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	job.ID = r.idSeq
	r.idSeq++
	job.CreatedAt = time.Now()
	
	r.jobs[job.ID] = job
	return nil
}

func (r *SiteRepository) GetCrawlJobStats(siteID uint) (map[string]int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	stats := make(map[string]int64)
	
	for _, job := range r.jobs {
		if job.SiteID == siteID {
			stats[job.Status]++
			stats["total"]++
		}
	}
	
	return stats, nil
}

func (r *SiteRepository) GetCacheStats(domain string) (map[string]interface{}, error) {
	// 内存实现暂时返回空统计
	return map[string]interface{}{
		"size":        0,
		"files_count": 0,
	}, nil
}

func (r *SiteRepository) GetDB() interface{} {
	// 内存实现没有数据库实例
	return nil
}

func (r *SiteRepository) DetectOrphanedData() ([]model.OrphanedData, error) {
	// 内存实现暂时返回空数据
	return []model.OrphanedData{}, nil
}

func (r *SiteRepository) CleanupAllOrphanedData() (int, error) {
	// 内存实现暂时返回0
	return 0, nil
}

func (r *SiteRepository) GetByAliasDomain(domain string) (*model.Site, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	// 内存实现暂不支持别名域名
	return nil, nil
}

func (r *SiteRepository) GetSiteAliases(siteID uint) ([]model.SiteAlias, error) {
	// 内存实现暂时返回空列表
	return []model.SiteAlias{}, nil
}

func (r *SiteRepository) CreateSiteAlias(alias *model.SiteAlias) error {
	// 内存实现暂不支持别名
	return nil
}

func (r *SiteRepository) DeleteSiteAliases(siteID uint) error {
	// 内存实现暂不支持别名
	return nil
}