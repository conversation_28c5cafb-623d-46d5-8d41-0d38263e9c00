package memory

import (
	"errors"
	"strings"
	"sync"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"
)

// KeywordRepository 内存实现的关键词仓储
type KeywordRepository struct {
	mu        sync.RWMutex
	libraries map[uint]*model.KeywordLibrary
	keywords  map[uint]*model.Keyword
	idSeq     uint
}

func NewKeywordRepository() repository.KeywordRepository {
	return &KeywordRepository{
		libraries: make(map[uint]*model.KeywordLibrary),
		keywords:  make(map[uint]*model.Keyword),
		idSeq:     1,
	}
}

func (r *KeywordRepository) CreateLibrary(library *model.KeywordLibrary) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// 检查名称是否已存在
	for _, lib := range r.libraries {
		if lib.Name == library.Name {
			return errors.New("词库名称已存在")
		}
	}
	
	library.ID = r.idSeq
	r.idSeq++
	library.CreatedAt = time.Now()
	library.UpdatedAt = time.Now()
	
	r.libraries[library.ID] = library
	return nil
}

func (r *KeywordRepository) GetLibraryByID(id uint) (*model.KeywordLibrary, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	library, exists := r.libraries[id]
	if !exists {
		return nil, nil
	}
	
	return library, nil
}

func (r *KeywordRepository) GetLibraryByName(name string) (*model.KeywordLibrary, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, library := range r.libraries {
		if library.Name == name {
			return library, nil
		}
	}
	
	return nil, nil
}

func (r *KeywordRepository) UpdateLibrary(library *model.KeywordLibrary) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.libraries[library.ID]; !exists {
		return errors.New("词库不存在")
	}
	
	library.UpdatedAt = time.Now()
	r.libraries[library.ID] = library
	return nil
}

func (r *KeywordRepository) DeleteLibrary(id uint) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.libraries[id]; !exists {
		return errors.New("词库不存在")
	}
	
	// 删除词库下的所有关键词
	var toDelete []uint
	for kwID, keyword := range r.keywords {
		if keyword.LibraryID == id {
			toDelete = append(toDelete, kwID)
		}
	}
	
	for _, kwID := range toDelete {
		delete(r.keywords, kwID)
	}
	
	delete(r.libraries, id)
	return nil
}

func (r *KeywordRepository) ListLibraries(offset, limit int) ([]*model.KeywordLibrary, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var result []*model.KeywordLibrary
	
	for _, library := range r.libraries {
		result = append(result, library)
	}
	
	// 按创建时间倒序排序
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].CreatedAt.Before(result[j].CreatedAt) {
				result[i], result[j] = result[j], result[i]
			}
		}
	}
	
	// 分页
	total := len(result)
	start := offset
	end := offset + limit
	
	if start >= total {
		return []*model.KeywordLibrary{}, nil
	}
	
	if end > total {
		end = total
	}
	
	return result[start:end], nil
}

func (r *KeywordRepository) CountLibraries() (int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	return int64(len(r.libraries)), nil
}

func (r *KeywordRepository) CountLibrariesByType() (map[string]int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	counts := make(map[string]int64)
	
	for _, library := range r.libraries {
		counts[library.Type]++
	}
	
	return counts, nil
}

func (r *KeywordRepository) CreateKeyword(keyword *model.Keyword) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// 检查是否已存在
	for _, kw := range r.keywords {
		if kw.LibraryID == keyword.LibraryID && kw.Keyword == keyword.Keyword {
			return errors.New("关键词已存在")
		}
	}
	
	keyword.ID = r.idSeq
	r.idSeq++
	keyword.CreatedAt = time.Now()
	keyword.UpdatedAt = time.Now()
	
	r.keywords[keyword.ID] = keyword
	return nil
}

func (r *KeywordRepository) GetKeywordByID(id uint) (*model.Keyword, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	keyword, exists := r.keywords[id]
	if !exists {
		return nil, nil
	}
	
	return keyword, nil
}

func (r *KeywordRepository) GetKeywordByText(libraryID uint, text string) (*model.Keyword, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	for _, keyword := range r.keywords {
		if keyword.LibraryID == libraryID && keyword.Keyword == text {
			return keyword, nil
		}
	}
	
	return nil, nil
}

func (r *KeywordRepository) UpdateKeyword(keyword *model.Keyword) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.keywords[keyword.ID]; !exists {
		return errors.New("关键词不存在")
	}
	
	keyword.UpdatedAt = time.Now()
	r.keywords[keyword.ID] = keyword
	return nil
}

func (r *KeywordRepository) DeleteKeyword(id uint) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.keywords[id]; !exists {
		return errors.New("关键词不存在")
	}
	
	delete(r.keywords, id)
	return nil
}

func (r *KeywordRepository) DeleteKeywordsByLibrary(libraryID uint) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	for id, keyword := range r.keywords {
		if keyword.LibraryID == libraryID {
			delete(r.keywords, id)
		}
	}
	
	return nil
}

func (r *KeywordRepository) ListKeywords(libraryID uint, offset, limit int, search string) ([]*model.Keyword, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var result []*model.Keyword
	
	for _, keyword := range r.keywords {
		if keyword.LibraryID == libraryID {
			// 搜索过滤
			if search != "" && !strings.Contains(keyword.Keyword, search) {
				continue
			}
			result = append(result, keyword)
		}
	}
	
	// 按创建时间倒序排序
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].CreatedAt.Before(result[j].CreatedAt) {
				result[i], result[j] = result[j], result[i]
			}
		}
	}
	
	// 分页
	total := len(result)
	start := offset
	end := offset + limit
	
	if start >= total {
		return []*model.Keyword{}, nil
	}
	
	if end > total {
		end = total
	}
	
	return result[start:end], nil
}

func (r *KeywordRepository) CountKeywords(libraryID uint, search string) (int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	count := 0
	
	for _, keyword := range r.keywords {
		if keyword.LibraryID == libraryID {
			if search != "" && !strings.Contains(keyword.Keyword, search) {
				continue
			}
			count++
		}
	}
	
	return int64(count), nil
}

func (r *KeywordRepository) CountKeywordsByLibrary(libraryID uint) (int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	count := 0
	
	for _, keyword := range r.keywords {
		if keyword.LibraryID == libraryID {
			count++
		}
	}
	
	return int64(count), nil
}

func (r *KeywordRepository) GetAllKeywordsByLibrary(libraryID uint) ([]*model.Keyword, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var result []*model.Keyword
	
	for _, keyword := range r.keywords {
		if keyword.LibraryID == libraryID {
			result = append(result, keyword)
		}
	}
	
	return result, nil
}

func (r *KeywordRepository) CountAllKeywords() (int64, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	return int64(len(r.keywords)), nil
}

func (r *KeywordRepository) GetRandomKeywordsByLibrary(libraryID uint, limit int) ([]*model.Keyword, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	var keywords []*model.Keyword
	
	// 获取该词库的所有关键词
	for _, keyword := range r.keywords {
		if keyword.LibraryID == libraryID {
			keywords = append(keywords, keyword)
		}
	}
	
	// 如果关键词数量小于等于limit，直接返回所有关键词
	if len(keywords) <= limit {
		return keywords, nil
	}
	
	// 简单的随机选择（使用当前时间作为种子）
	// 注意：这是一个简单的实现，可能不够随机
	result := make([]*model.Keyword, 0, limit)
	used := make(map[int]bool)
	
	for len(result) < limit {
		// 简单的伪随机选择
		idx := int(time.Now().UnixNano()) % len(keywords)
		if !used[idx] {
			result = append(result, keywords[idx])
			used[idx] = true
		}
	}
	
	return result, nil
}