package repository

import (
	"fmt"
	"time"

	"site-cluster/internal/model"

	"gorm.io/gorm"
)

// WeightRepository 权重仓储接口
type WeightRepository interface {
	// 配置相关
	GetWeightConfig() (*model.WeightMonitorConfig, error)
	CreateWeightConfig(config *model.WeightMonitorConfig) error
	UpdateWeightConfig(config *model.WeightMonitorConfig) error
	
	// 历史记录相关
	SaveWeightHistory(history *model.WeightHistory) error
	GetWeightHistory(domain string, startTime, endTime time.Time) ([]*model.WeightHistory, error)
	GetWeightHistoryByDateRange(startDate, endDate string, domains []string) ([]*model.WeightHistory, error)
	GetLatestWeights(limit int) ([]*model.WeightHistory, error)
	GetWeightTrend(domain string, days int) ([]*model.WeightTrend, error)
	GetAllDomainsLatestWeight() ([]*model.WeightStats, error)
	GetDomainWeightStats(domain string) (*model.WeightStats, error)
	GetWeightedDomains() ([]string, error)
	GetLatestWeightByDomain(domain string) (*model.WeightHistory, error)
	GetWeightStats() (totalDomains int, weightedCount int, err error)
	
	// 站点相关
	GetActiveSites() ([]*model.Site, error)
	GetSiteByDomain(domain string) (*model.Site, error)
	
	// 数据清理
	ClearAllWeightData() error
	DeleteDomainWeightData(domain string) error
}

// weightRepository 权重仓储实现
type weightRepository struct {
	db *gorm.DB
}

// NewWeightRepository 创建权重仓储
func NewWeightRepository(db *gorm.DB) WeightRepository {
	return &weightRepository{db: db}
}

// GetWeightConfig 获取权重监测配置
func (r *weightRepository) GetWeightConfig() (*model.WeightMonitorConfig, error) {
	var config model.WeightMonitorConfig
	err := r.db.First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

// CreateWeightConfig 创建权重监测配置
func (r *weightRepository) CreateWeightConfig(config *model.WeightMonitorConfig) error {
	return r.db.Create(config).Error
}

// UpdateWeightConfig 更新权重监测配置
func (r *weightRepository) UpdateWeightConfig(config *model.WeightMonitorConfig) error {
	// 先查找是否存在配置
	var existingConfig model.WeightMonitorConfig
	err := r.db.First(&existingConfig).Error
	
	if err == gorm.ErrRecordNotFound {
		// 不存在则创建
		return r.db.Create(config).Error
	} else if err != nil {
		return err
	}
	
	// 存在则更新，保持原有ID
	config.ID = existingConfig.ID
	config.CreatedAt = existingConfig.CreatedAt
	return r.db.Save(config).Error
}

// SaveWeightHistory 保存权重历史记录
func (r *weightRepository) SaveWeightHistory(history *model.WeightHistory) error {
	return r.db.Create(history).Error
}

// GetWeightHistory 获取权重历史记录
func (r *weightRepository) GetWeightHistory(domain string, startTime, endTime time.Time) ([]*model.WeightHistory, error) {
	var histories []*model.WeightHistory
	query := r.db.Where("domain = ?", domain)
	
	if !startTime.IsZero() {
		query = query.Where("check_time >= ?", startTime)
	}
	if !endTime.IsZero() {
		query = query.Where("check_time <= ?", endTime)
	}
	
	err := query.Order("check_time DESC").Find(&histories).Error
	return histories, err
}

// GetLatestWeights 获取最新的权重记录
func (r *weightRepository) GetLatestWeights(limit int) ([]*model.WeightHistory, error) {
	var histories []*model.WeightHistory
	
	// 使用子查询获取每个域名的最新记录
	subQuery := r.db.Model(&model.WeightHistory{}).
		Select("domain, MAX(check_time) as max_time").
		Group("domain")
	
	err := r.db.Table("weight_history AS w1").
		Joins("JOIN (?) AS w2 ON w1.domain = w2.domain AND w1.check_time = w2.max_time", subQuery).
		Order("w1.check_time DESC").
		Limit(limit).
		Find(&histories).Error
		
	return histories, err
}

// GetWeightTrend 获取权重趋势数据
func (r *weightRepository) GetWeightTrend(domain string, days int) ([]*model.WeightTrend, error) {
	var trends []*model.WeightTrend
	
	startTime := time.Now().AddDate(0, 0, -days)
	
	query := `
		SELECT 
			DATE(check_time) as date,
			domain,
			AVG(pc_br) as pc_br,
			AVG(m_br) as m_br,
			MAX(ip) as total_ip
		FROM weight_history
		WHERE domain = ? AND check_time >= ?
		GROUP BY DATE(check_time), domain
		ORDER BY date ASC
	`
	
	err := r.db.Raw(query, domain, startTime).Scan(&trends).Error
	return trends, err
}

// GetAllDomainsLatestWeight 获取所有域名的最新权重统计
func (r *weightRepository) GetAllDomainsLatestWeight() ([]*model.WeightStats, error) {
	var stats []*model.WeightStats
	
	// 获取每个域名的最新和前一次记录
	query := `
		WITH ranked_weights AS (
			SELECT 
				domain,
				pc_br,
				m_br,
				ip,
				pc_ip,
				m_ip,
				check_time,
				ROW_NUMBER() OVER (PARTITION BY domain ORDER BY check_time DESC) as rn
			FROM weight_history
		)
		SELECT 
			w1.domain,
			w1.pc_br as current_pc_br,
			w1.m_br as current_mobile_br,
			w1.ip as current_ip,
			w1.pc_ip as current_pc_ip,
			w1.m_ip as current_m_ip,
			COALESCE(w2.pc_br, 0) as prev_pc_br,
			COALESCE(w2.m_br, 0) as prev_mobile_br,
			COALESCE(w2.ip, '') as prev_ip,
			COALESCE(w2.pc_ip, '') as prev_pc_ip,
			COALESCE(w2.m_ip, '') as prev_m_ip,
			w1.pc_br - COALESCE(w2.pc_br, w1.pc_br) as pc_br_change,
			w1.m_br - COALESCE(w2.m_br, w1.m_br) as mobile_br_change,
			w1.check_time as last_check_time
		FROM ranked_weights w1
		LEFT JOIN ranked_weights w2 ON w1.domain = w2.domain AND w2.rn = 2
		WHERE w1.rn = 1
		ORDER BY w1.domain
	`
	
	err := r.db.Raw(query).Scan(&stats).Error
	return stats, err
}

// GetActiveSites 获取所有活跃站点
func (r *weightRepository) GetActiveSites() ([]*model.Site, error) {
	var sites []*model.Site
	err := r.db.Where("status = ?", "active").Find(&sites).Error
	return sites, err
}

// GetSiteByDomain 根据域名获取站点
func (r *weightRepository) GetSiteByDomain(domain string) (*model.Site, error) {
	var site model.Site
	err := r.db.Where("domain = ?", domain).First(&site).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &site, nil
}

// GetWeightHistoryByDateRange 获取指定日期范围的权重历史
func (r *weightRepository) GetWeightHistoryByDateRange(startDate, endDate string, domains []string) ([]*model.WeightHistory, error) {
	query := r.db.Model(&model.WeightHistory{})
	
	if startDate != "" && endDate != "" {
		start, _ := time.Parse("2006-01-02", startDate)
		end, _ := time.Parse("2006-01-02", endDate)
		end = end.Add(24 * time.Hour) // 包含结束日期的全天
		query = query.Where("check_time BETWEEN ? AND ?", start, end)
	}
	
	if len(domains) > 0 {
		query = query.Where("domain IN ?", domains)
	}
	
	var histories []*model.WeightHistory
	err := query.Order("check_time DESC").Find(&histories).Error
	return histories, err
}

// GetDomainWeightStats 获取域名权重统计
func (r *weightRepository) GetDomainWeightStats(domain string) (*model.WeightStats, error) {
	var stat model.WeightStats
	
	// 获取最新记录
	var latest model.WeightHistory
	if err := r.db.Where("domain = ?", domain).Order("check_time DESC").First(&latest).Error; err != nil {
		return nil, fmt.Errorf("获取最新权重失败: %v", err)
	}
	
	// 获取前一条记录
	var prev model.WeightHistory
	r.db.Where("domain = ? AND check_time < ?", domain, latest.CheckTime).
		Order("check_time DESC").First(&prev)
	
	stat.Domain = domain
	stat.CurrentPCBR = latest.PCBR
	stat.CurrentMobileBR = latest.MobileBR
	stat.CurrentIP = latest.IP
	stat.CurrentPCIP = latest.PCIP
	stat.CurrentMIP = latest.MobileIP
	stat.LastCheckTime = latest.CheckTime
	
	if prev.ID > 0 {
		stat.PrevPCBR = prev.PCBR
		stat.PrevMobileBR = prev.MobileBR
		stat.PrevIP = prev.IP
		stat.PrevPCIP = prev.PCIP
		stat.PrevMIP = prev.MobileIP
		stat.PCBRChange = latest.PCBR - prev.PCBR
		stat.MobileBRChange = latest.MobileBR - prev.MobileBR
	}
	
	return &stat, nil
}

// GetWeightedDomains 获取所有有权重记录的域名列表（去重）
func (r *weightRepository) GetWeightedDomains() ([]string, error) {
	var domains []string
	err := r.db.Model(&model.WeightHistory{}).
		Distinct("domain").
		Pluck("domain", &domains).Error
	return domains, err
}

// GetLatestWeightByDomain 获取域名的最新权重记录
func (r *weightRepository) GetLatestWeightByDomain(domain string) (*model.WeightHistory, error) {
	var history model.WeightHistory
	err := r.db.Where("domain = ?", domain).
		Order("created_at DESC").
		First(&history).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &history, nil
}

// GetWeightStats 获取权重统计（优化版 V4 - 单查询）
func (r *weightRepository) GetWeightStats() (totalDomains int, weightedCount int, err error) {
	// 使用单个优化的SQL查询获取所有统计信息
	// 这个查询利用了子查询和聚合函数，应该很快
	
	type Stats struct {
		TotalDomains  int
		WeightedCount int
	}
	
	var stats Stats
	
	// 使用单个查询获取所有统计信息
	sql := `
		WITH latest_per_domain AS (
			SELECT DISTINCT ON (domain) 
				domain,
				pc_br,
				mobile_br
			FROM weight_history
			ORDER BY domain, id DESC
		)
		SELECT 
			COUNT(*) as total_domains,
			COUNT(CASE WHEN pc_br > 0 OR mobile_br > 0 THEN 1 END) as weighted_count
		FROM latest_per_domain
	`
	
	err = r.db.Raw(sql).Scan(&stats).Error
	if err != nil {
		return 0, 0, err
	}
	
	return stats.TotalDomains, stats.WeightedCount, nil
}

// ClearAllWeightData 清空所有权重数据
func (r *weightRepository) ClearAllWeightData() error {
	// 使用GORM的安全方法清空数据
	err := r.db.Session(&gorm.Session{AllowGlobalUpdate: true}).Delete(&model.WeightHistory{}).Error
	if err != nil {
		return err
	}
	
	// 重置自增序列（可选，可能不是所有数据库都支持）
	_ = r.db.Exec("ALTER SEQUENCE weight_history_id_seq RESTART WITH 1").Error
	
	return nil
}

// DeleteDomainWeightData 删除指定域名的权重数据
func (r *weightRepository) DeleteDomainWeightData(domain string) error {
	err := r.db.Where("domain = ?", domain).Delete(&model.WeightHistory{}).Error
	if err != nil {
		return err
	}
	
	return nil
}