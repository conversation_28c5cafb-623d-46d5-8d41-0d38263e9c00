package repository

import (
	"site-cluster/internal/model"
	"time"

	"gorm.io/gorm"
)

// LoginLogRepository 登录日志仓储接口
type LoginLogRepository interface {
	Create(log *model.LoginLog) error
	GetList(username string, page, pageSize int) ([]*model.LoginLog, int64, error)
	GetByDateRange(username string, startDate, endDate time.Time, page, pageSize int) ([]*model.LoginLog, int64, error)
	GetRecentFailedAttempts(ip string, duration time.Duration) (int64, error)
	CleanOldLogs(days int) (int64, error)
	CleanAllLogs() (int64, error)
}

// loginLogRepository 登录日志仓储实现
type loginLogRepository struct {
	db *gorm.DB
}

// NewLoginLogRepository 创建登录日志仓储
func NewLoginLogRepository(db *gorm.DB) LoginLogRepository {
	return &loginLogRepository{db: db}
}

// Create 创建登录日志
func (r *loginLogRepository) Create(log *model.LoginLog) error {
	return r.db.Create(log).Error
}

// GetList 获取登录日志列表
func (r *loginLogRepository) GetList(username string, page, pageSize int) ([]*model.LoginLog, int64, error) {
	var logs []*model.LoginLog
	var total int64
	
	query := r.db.Model(&model.LoginLog{})
	
	if username != "" {
		query = query.Where("username = ?", username)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("login_time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&logs).Error; err != nil {
		return nil, 0, err
	}
	
	return logs, total, nil
}

// GetByDateRange 根据日期范围获取登录日志
func (r *loginLogRepository) GetByDateRange(username string, startDate, endDate time.Time, page, pageSize int) ([]*model.LoginLog, int64, error) {
	var logs []*model.LoginLog
	var total int64
	
	query := r.db.Model(&model.LoginLog{}).
		Where("login_time BETWEEN ? AND ?", startDate, endDate)
	
	if username != "" {
		query = query.Where("username = ?", username)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("login_time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&logs).Error; err != nil {
		return nil, 0, err
	}
	
	return logs, total, nil
}

// GetRecentFailedAttempts 获取最近失败的登录尝试次数
func (r *loginLogRepository) GetRecentFailedAttempts(ip string, duration time.Duration) (int64, error) {
	var count int64
	
	since := time.Now().Add(-duration)
	err := r.db.Model(&model.LoginLog{}).
		Where("ip = ? AND status = ? AND login_time >= ?", ip, "failed", since).
		Count(&count).Error
	
	return count, err
}

// CleanOldLogs 清理旧日志
func (r *loginLogRepository) CleanOldLogs(days int) (int64, error) {
	before := time.Now().AddDate(0, 0, -days)
	result := r.db.Where("login_time < ?", before).Delete(&model.LoginLog{})
	return result.RowsAffected, result.Error
}

// CleanAllLogs 清空所有日志
func (r *loginLogRepository) CleanAllLogs() (int64, error) {
	result := r.db.Delete(&model.LoginLog{}, "1 = 1")
	return result.RowsAffected, result.Error
}