package repository

import (
	"site-cluster/internal/model"
	"time"
)

// SiteRepository 站点仓储接口
type SiteRepository interface {
	// 基本CRUD
	Create(site *model.Site) error
	GetByID(id uint) (*model.Site, error)
	GetByDomain(domain string) (*model.Site, error)
	Update(site *model.Site) error
	UpdateCacheStatus(siteID uint, status string, errMsg string, updateTime time.Time) error
	Delete(id uint) error
	
	// 列表查询
	List(offset, limit int, conditions map[string]interface{}) ([]*model.Site, error)
	Count(conditions map[string]interface{}) (int64, error)
	
	// 爬取任务相关
	CreateCrawlJob(job *model.CrawlJob) error
	GetCrawlJobStats(siteID uint) (map[string]int64, error)
	
	// 缓存统计
	GetCacheStats(domain string) (map[string]interface{}, error)
	
	// 获取数据库实例
	GetDB() interface{}
	
	// 脏数据管理
	DetectOrphanedData() ([]model.OrphanedData, error)
	CleanupAllOrphanedData() (int, error)
	
	// 子域名管理
	GetByAliasDomain(domain string) (*model.Site, error)
	GetSiteAliases(siteID uint) ([]model.SiteAlias, error)
	CreateSiteAlias(alias *model.SiteAlias) error
	DeleteSiteAliases(siteID uint) error
}


// KeywordRepository 关键词仓储接口
type KeywordRepository interface {
	// 词库相关
	CreateLibrary(library *model.KeywordLibrary) error
	GetLibraryByID(id uint) (*model.KeywordLibrary, error)
	GetLibraryByName(name string) (*model.KeywordLibrary, error)
	UpdateLibrary(library *model.KeywordLibrary) error
	DeleteLibrary(id uint) error
	ListLibraries(offset, limit int) ([]*model.KeywordLibrary, error)
	CountLibraries() (int64, error)
	CountLibrariesByType() (map[string]int64, error)
	
	// 关键词相关
	CreateKeyword(keyword *model.Keyword) error
	GetKeywordByID(id uint) (*model.Keyword, error)
	GetKeywordByText(libraryID uint, text string) (*model.Keyword, error)
	UpdateKeyword(keyword *model.Keyword) error
	DeleteKeyword(id uint) error
	DeleteKeywordsByLibrary(libraryID uint) error
	ListKeywords(libraryID uint, offset, limit int, search string) ([]*model.Keyword, error)
	CountKeywords(libraryID uint, search string) (int64, error)
	CountKeywordsByLibrary(libraryID uint) (int64, error)
	GetAllKeywordsByLibrary(libraryID uint) ([]*model.Keyword, error)
	GetRandomKeywordsByLibrary(libraryID uint, limit int) ([]*model.Keyword, error)
	CountAllKeywords() (int64, error)
}

// RoutingRuleRepository 路由规则仓储接口
type RoutingRuleRepository interface {
	Create(rule *model.RoutingRule) error
	GetByID(id uint) (*model.RoutingRule, error)
	GetBySiteID(siteID uint) ([]*model.RoutingRule, error)
	Update(rule *model.RoutingRule) error
	Delete(id uint) error
}