package interfaces

import (
	"time"
	
	"site-cluster/internal/model"
)

// CacheService 缓存服务接口
type CacheService interface {
	SaveContent(domain string, content *model.CachedContent) error
	GetContent(domain, url string, maxAge time.Duration) (*model.CachedContent, bool)
	SaveResource(domain, url string, data []byte, contentType string) error
	GetResource(domain, url string) ([]byte, string, bool)
}

// SiteService 站点服务接口
type SiteService interface {
	GetSite(id uint) (*model.Site, error)
	UpdateSite(site *model.Site) error
	GetKeywords(injectConfigID uint) ([]*model.Keyword, error)
}

// Crawler 爬虫接口
type Crawler interface {
	CrawlSite(site *model.Site, job *model.CrawlJob) error
	CrawlPage(pageURL, domain string, depth int) error
}

// TaskQueue 任务队列接口
type TaskQueue interface {
	EnqueueCrawlJob(job *model.CrawlJob) error
	GetQueueSize() int
}

// Scheduler 调度器接口
type Scheduler interface {
	RegisterSiteCrawlJob(site *model.Site)
	RemoveSiteCrawlJob(siteID uint)
}