package injector

import (
	"fmt"
	"math/rand"
	"strings"
	"time"

	"site-cluster/internal/model"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"
)

type StructureInjector struct {
	logger            *zap.Logger
	random            *rand.Rand
	keywordsByLibrary map[uint][]string // 按库ID分组的关键词
}

func NewStructureInjector(logger *zap.Logger) *StructureInjector {
	return &StructureInjector{
		logger: logger,
		random: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// InjectStructures 注入结构
func (si *StructureInjector) InjectStructures(html string, config *model.InjectConfig) (string, error) {
	if !config.EnableStructure {
		return html, nil
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return html, err
	}

	// 设置按库分组的关键词
	si.keywordsByLibrary = config.KeywordsByLibrary

	// 获取结构注入使用的关键词
	keywords := si.getStructureKeywords(config)
	if len(keywords) == 0 {
		// 如果没有关键词，不进行注入
		return html, nil
	}

	// 确定本次注入的数量（在最小值和最大值之间随机）
	minCount := config.StructureMinPerPage
	maxCount := config.StructureMaxPerPage
	
	// 设置默认值
	if minCount <= 0 {
		minCount = 10
	}
	if maxCount <= 0 || maxCount < minCount {
		maxCount = minCount + 10
	}
	
	// 随机选择注入数量
	injectCount := minCount
	if maxCount > minCount {
		injectCount = minCount + si.random.Intn(maxCount-minCount+1)
	}
	
	si.logger.Info("结构注入",
		zap.Int("min", minCount),
		zap.Int("max", maxCount),
		zap.Int("count", injectCount),
		zap.Int("keywords", len(keywords)))

	// 应用结构注入规则
	for _, rule := range config.InjectRules {
		if rule.Type == "structure" {
			si.applyStructureRule(doc, rule)
		}
	}

	// 根据注入数量，添加各种隐藏结构
	injected := 0
	
	// 添加隐藏结构
	if injected < injectCount {
		count := si.injectHiddenStructuresWithCount(doc, keywords, injectCount-injected)
		injected += count
	}

	// 添加透明层
	if injected < injectCount && si.random.Float32() < 0.3 {
		si.injectTransparentLayer(doc, keywords)
		injected++
	}

	// 添加离屏内容
	if injected < injectCount && si.random.Float32() < 0.4 {
		si.injectOffscreenContent(doc, keywords)
		injected++
	}

	result, err := doc.Html()
	if err != nil {
		return html, err
	}

	return result, nil
}

// applyStructureRule 应用结构规则
func (si *StructureInjector) applyStructureRule(doc *goquery.Document, rule model.InjectRule) {
	if si.random.Float32() > rule.Probability {
		return
	}

	structure := si.generateStructure(rule.Content)
	
	switch rule.Target {
	case "body":
		doc.Find("body").AppendHtml(structure)
	case "head":
		doc.Find("head").AppendHtml(structure)
	default:
		selection := doc.Find(rule.Target)
		if selection.Length() > 0 {
			switch rule.Position {
			case "before":
				selection.BeforeHtml(structure)
			case "after":
				selection.AfterHtml(structure)
			case "append":
				selection.AppendHtml(structure)
			}
		}
	}
}

// injectHiddenStructures 注入隐藏结构
func (si *StructureInjector) injectHiddenStructures(doc *goquery.Document, keywords []string) {
	// 1. 使用 display:none 的div
	if si.random.Float32() < 0.5 {
		hiddenDiv := fmt.Sprintf(`
			<div style="display:none" class="seo-content">
				<h2>%s</h2>
				<p>%s</p>
			</div>`,
			si.generateTitle(keywords),
			si.generateParagraph(keywords),
		)
		doc.Find("body").AppendHtml(hiddenDiv)
	}

	// 2. 使用 position:absolute 的div
	if si.random.Float32() < 0.4 {
		absoluteDiv := fmt.Sprintf(`
			<div style="position:absolute;left:-10000px;top:-10000px">
				<ul>%s</ul>
			</div>`,
			si.generateList(keywords),
		)
		doc.Find("body").AppendHtml(absoluteDiv)
	}

	// 3. 使用 text-indent 的段落
	if si.random.Float32() < 0.3 {
		indentP := fmt.Sprintf(`
			<p style="text-indent:-9999px;height:0;overflow:hidden">
				%s
			</p>`,
			strings.Join(keywords, " "),
		)
		doc.Find("body").PrependHtml(indentP)
	}

	// 4. 使用 font-size:0 的span
	spans := si.generateHiddenSpans(keywords)
	doc.Find("p").Each(func(i int, s *goquery.Selection) {
		if i < len(spans) && si.random.Float32() < 0.2 {
			s.AppendHtml(spans[i])
		}
	})
}

// injectTransparentLayer 注入透明层
func (si *StructureInjector) injectTransparentLayer(doc *goquery.Document, keywords []string) {
	layer := fmt.Sprintf(`
		<div style="position:fixed;top:0;left:0;width:100%%;height:100%%;opacity:0.01;z-index:-1;pointer-events:none">
			<div style="padding:20px">
				%s
			</div>
		</div>`,
		si.generateContent(keywords),
	)
	doc.Find("body").AppendHtml(layer)
}

// injectOffscreenContent 注入离屏内容
func (si *StructureInjector) injectOffscreenContent(doc *goquery.Document, keywords []string) {
	positions := []string{
		"position:fixed;top:-2000px",
		"position:absolute;left:-2000px",
		"margin-left:-9999px",
		"margin-top:-9999px",
	}

	position := positions[si.random.Intn(len(positions))]
	
	content := fmt.Sprintf(`
		<div style="%s" class="offscreen-content">
			<article>
				<h1>%s</h1>
				%s
			</article>
		</div>`,
		position,
		si.generateTitle(keywords),
		si.generateArticle(keywords),
	)
	
	doc.Find("body").AppendHtml(content)
}

// generateStructure 生成结构
func (si *StructureInjector) generateStructure(template string) string {
	// 可以根据模板生成不同的结构
	return template
}

// generateTitle 生成标题
func (si *StructureInjector) generateTitle(keywords []string) string {
	if len(keywords) == 0 {
		return ""
	}
	
	templates := []string{
		"%s - 专业服务",
		"关于%s的详细信息",
		"%s | 最新资讯",
		"高质量的%s服务",
	}
	
	template := templates[si.random.Intn(len(templates))]
	keyword := keywords[si.random.Intn(len(keywords))]
	
	return fmt.Sprintf(template, keyword)
}

// generateParagraph 生成段落
func (si *StructureInjector) generateParagraph(keywords []string) string {
	if len(keywords) == 0 {
		return ""
	}
	
	sentences := []string{
		"我们提供专业的%s服务，满足您的各种需求。",
		"作为行业领先的%s供应商，我们致力于提供最优质的解决方案。",
		"选择我们的%s，您将获得最专业的技术支持。",
		"多年来，我们在%s领域积累了丰富的经验。",
	}
	
	paragraph := ""
	for i := 0; i < 3; i++ {
		sentence := sentences[si.random.Intn(len(sentences))]
		keyword := keywords[si.random.Intn(len(keywords))]
		paragraph += fmt.Sprintf(sentence, keyword) + " "
	}
	
	return paragraph
}

// generateList 生成列表
func (si *StructureInjector) generateList(keywords []string) string {
	items := ""
	for _, keyword := range keywords {
		items += fmt.Sprintf("<li>%s</li>", keyword)
	}
	return items
}

// generateHiddenSpans 生成隐藏的span
func (si *StructureInjector) generateHiddenSpans(keywords []string) []string {
	styles := []string{
		"font-size:0",
		"color:transparent",
		"visibility:hidden",
		"display:inline-block;width:0;height:0;overflow:hidden",
	}
	
	spans := make([]string, len(keywords))
	for i, keyword := range keywords {
		style := styles[si.random.Intn(len(styles))]
		spans[i] = fmt.Sprintf(`<span style="%s">%s</span>`, style, keyword)
	}
	
	return spans
}

// generateContent 生成内容
func (si *StructureInjector) generateContent(keywords []string) string {
	content := "<div>"
	content += "<h3>" + si.generateTitle(keywords) + "</h3>"
	content += "<p>" + si.generateParagraph(keywords) + "</p>"
	content += "<ul>" + si.generateList(keywords) + "</ul>"
	content += "</div>"
	return content
}

// generateArticle 生成文章
func (si *StructureInjector) generateArticle(keywords []string) string {
	article := ""
	for i := 0; i < 3; i++ {
		article += "<p>" + si.generateParagraph(keywords) + "</p>"
	}
	return article
}

// getStructureKeywords 获取结构注入使用的关键词
func (si *StructureInjector) getStructureKeywords(config *model.InjectConfig) []string {
	// 如果配置了结构注入专用关键词库，从中获取关键词
	if len(config.StructureLibraryIDs) > 0 {
		return si.getKeywordsFromLibraries(config.StructureLibraryIDs)
	}
	// 否则使用通用关键词
	return config.Keywords
}

// getKeywordsFromLibraries 从指定的关键词库IDs中获取关键词
func (si *StructureInjector) getKeywordsFromLibraries(libraryIDs []uint) []string {
	// 从按库ID分组的关键词中获取
	if si.keywordsByLibrary == nil {
		return []string{}
	}
	
	var result []string
	seen := make(map[string]bool)
	
	for _, libID := range libraryIDs {
		if keywords, ok := si.keywordsByLibrary[libID]; ok {
			for _, keyword := range keywords {
				if !seen[keyword] {
					result = append(result, keyword)
					seen[keyword] = true
				}
			}
		}
	}
	
	return result
}

// injectHiddenStructuresWithCount 注入指定数量的隐藏结构
func (si *StructureInjector) injectHiddenStructuresWithCount(doc *goquery.Document, keywords []string, count int) int {
	injected := 0
	
	// 1. 使用 display:none 的div
	if injected < count && si.random.Float32() < 0.5 {
		for i := 0; i < count/3 && injected < count; i++ {
			hiddenDiv := fmt.Sprintf(`
				<div style="display:none" class="seo-content-%d">
					<h2>%s</h2>
					<p>%s</p>
				</div>`,
				si.random.Intn(10000),
				si.generateTitle(keywords),
				si.generateParagraph(keywords),
			)
			doc.Find("body").AppendHtml(hiddenDiv)
			injected++
		}
	}

	// 2. 使用 position:absolute 的div
	if injected < count && si.random.Float32() < 0.4 {
		for i := 0; i < count/3 && injected < count; i++ {
			absoluteDiv := fmt.Sprintf(`
				<div style="position:absolute;left:-%dpx;top:-%dpx">
					<ul>%s</ul>
				</div>`,
				10000+si.random.Intn(5000),
				10000+si.random.Intn(5000),
				si.generateList(si.getRandomKeywords(keywords, 5)),
			)
			doc.Find("body").AppendHtml(absoluteDiv)
			injected++
		}
	}

	// 3. 使用 text-indent 的段落
	if injected < count && si.random.Float32() < 0.3 {
		for i := 0; i < count/3 && injected < count; i++ {
			indentP := fmt.Sprintf(`
				<p style="text-indent:-%dpx;height:0;overflow:hidden">
					%s
				</p>`,
				9999+si.random.Intn(1000),
				strings.Join(si.getRandomKeywords(keywords, 3), " "),
			)
			doc.Find("body").PrependHtml(indentP)
			injected++
		}
	}

	// 4. 使用 font-size:0 的span
	remaining := count - injected
	if remaining > 0 {
		spans := si.generateHiddenSpansWithCount(keywords, remaining)
		doc.Find("p").Each(func(i int, s *goquery.Selection) {
			if i < len(spans) && injected < count {
				s.AppendHtml(spans[i])
				injected++
			}
		})
	}
	
	return injected
}

// getRandomKeywords 从关键词列表中随机选择指定数量的关键词
func (si *StructureInjector) getRandomKeywords(keywords []string, count int) []string {
	if len(keywords) == 0 {
		return []string{}
	}
	
	result := make([]string, 0, count)
	for i := 0; i < count && i < len(keywords); i++ {
		result = append(result, keywords[si.random.Intn(len(keywords))])
	}
	return result
}

// generateHiddenSpansWithCount 生成指定数量的隐藏span
func (si *StructureInjector) generateHiddenSpansWithCount(keywords []string, count int) []string {
	styles := []string{
		"font-size:0",
		"color:transparent",
		"visibility:hidden",
		"display:inline-block;width:0;height:0;overflow:hidden",
	}
	
	spans := make([]string, 0, count)
	for i := 0; i < count; i++ {
		style := styles[si.random.Intn(len(styles))]
		keyword := keywords[si.random.Intn(len(keywords))]
		spans = append(spans, fmt.Sprintf(`<span style="%s">%s</span>`, style, keyword))
	}
	
	return spans
}