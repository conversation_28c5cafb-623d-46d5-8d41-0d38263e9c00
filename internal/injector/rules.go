package injector

import (
	"regexp"
	"strings"

	"site-cluster/internal/model"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"
)

type RuleEngine struct {
	logger     *zap.Logger
	rules      []model.InjectRule
	conditions map[string]func(*goquery.Document) bool
}

func NewRuleEngine(logger *zap.Logger) *RuleEngine {
	re := &RuleEngine{
		logger:     logger,
		conditions: make(map[string]func(*goquery.Document) bool),
	}
	
	// 注册默认条件
	re.registerDefaultConditions()
	
	return re
}

// registerDefaultConditions 注册默认条件
func (re *RuleEngine) registerDefaultConditions() {
	// 页面包含特定元素
	re.conditions["has_element"] = func(doc *goquery.Document) bool {
		return doc.Find("article").Length() > 0
	}
	
	// 页面标题包含关键词
	re.conditions["title_contains"] = func(doc *goquery.Document) bool {
		title := doc.Find("title").Text()
		return strings.Contains(title, "产品") || strings.Contains(title, "服务")
	}
	
	// 页面是列表页
	re.conditions["is_list_page"] = func(doc *goquery.Document) bool {
		return doc.Find("ul li").Length() > 10 || doc.Find(".list-item").Length() > 5
	}
	
	// 页面是详情页
	re.conditions["is_detail_page"] = func(doc *goquery.Document) bool {
		return doc.Find("article").Length() > 0 || doc.Find(".content").Length() > 0
	}
}

// ApplyRules 应用注入规则
func (re *RuleEngine) ApplyRules(html string, config *model.InjectConfig) (string, error) {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return html, err
	}

	// 应用所有规则
	for _, rule := range config.InjectRules {
		if re.shouldApplyRule(doc, rule) {
			re.applyRule(doc, rule)
		}
	}

	result, err := doc.Html()
	if err != nil {
		return html, err
	}

	return result, nil
}

// shouldApplyRule 判断是否应用规则
func (re *RuleEngine) shouldApplyRule(doc *goquery.Document, rule model.InjectRule) bool {
	// 检查条件
	if condition, exists := re.conditions[rule.Target]; exists {
		return condition(doc)
	}
	
	// 默认选择器匹配
	return doc.Find(rule.Target).Length() > 0
}

// applyRule 应用单个规则
func (re *RuleEngine) applyRule(doc *goquery.Document, rule model.InjectRule) {
	switch rule.Type {
	case "insert":
		re.applyInsertRule(doc, rule)
	case "modify":
		re.applyModifyRule(doc, rule)
	case "wrap":
		re.applyWrapRule(doc, rule)
	case "custom":
		re.applyCustomRule(doc, rule)
	}
}

// applyInsertRule 应用插入规则
func (re *RuleEngine) applyInsertRule(doc *goquery.Document, rule model.InjectRule) {
	selection := doc.Find(rule.Target)
	
	switch rule.Position {
	case "before":
		selection.BeforeHtml(rule.Content)
	case "after":
		selection.AfterHtml(rule.Content)
	case "prepend":
		selection.PrependHtml(rule.Content)
	case "append":
		selection.AppendHtml(rule.Content)
	}
}

// applyModifyRule 应用修改规则
func (re *RuleEngine) applyModifyRule(doc *goquery.Document, rule model.InjectRule) {
	selection := doc.Find(rule.Target)
	
	selection.Each(func(i int, s *goquery.Selection) {
		switch rule.Position {
		case "text":
			originalText := s.Text()
			newText := re.processContent(originalText, rule.Content)
			s.SetText(newText)
		case "html":
			s.SetHtml(rule.Content)
		case "attr":
			// 修改属性，Content格式: "attr_name:attr_value"
			parts := strings.Split(rule.Content, ":")
			if len(parts) == 2 {
				s.SetAttr(parts[0], parts[1])
			}
		}
	})
}

// applyWrapRule 应用包装规则
func (re *RuleEngine) applyWrapRule(doc *goquery.Document, rule model.InjectRule) {
	selection := doc.Find(rule.Target)
	selection.WrapHtml(rule.Content)
}

// applyCustomRule 应用自定义规则
func (re *RuleEngine) applyCustomRule(doc *goquery.Document, rule model.InjectRule) {
	// 自定义规则可以包含更复杂的逻辑
	switch rule.Content {
	case "add_schema":
		re.addSchemaMarkup(doc)
	case "add_breadcrumb":
		re.addBreadcrumb(doc)
	case "optimize_images":
		re.optimizeImages(doc)
	}
}

// processContent 处理内容
func (re *RuleEngine) processContent(original, template string) string {
	// 支持变量替换
	result := template
	result = strings.ReplaceAll(result, "{original}", original)
	result = strings.ReplaceAll(result, "{upper}", strings.ToUpper(original))
	result = strings.ReplaceAll(result, "{lower}", strings.ToLower(original))
	
	// 支持正则替换
	if strings.HasPrefix(template, "regex:") {
		parts := strings.SplitN(template[6:], ":", 2)
		if len(parts) == 2 {
			if regex, err := regexp.Compile(parts[0]); err == nil {
				result = regex.ReplaceAllString(original, parts[1])
			}
		}
	}
	
	return result
}

// addSchemaMarkup 添加结构化数据
func (re *RuleEngine) addSchemaMarkup(doc *goquery.Document) {
	schema := `
	<script type="application/ld+json">
	{
		"@context": "https://schema.org",
		"@type": "WebPage",
		"name": "` + doc.Find("title").Text() + `",
		"description": "` + doc.Find("meta[name='description']").AttrOr("content", "") + `"
	}
	</script>`
	
	doc.Find("head").AppendHtml(schema)
}

// addBreadcrumb 添加面包屑
func (re *RuleEngine) addBreadcrumb(doc *goquery.Document) {
	breadcrumb := `
	<nav class="breadcrumb">
		<a href="/">首页</a> &gt;
		<span>当前页面</span>
	</nav>`
	
	doc.Find("body").PrependHtml(breadcrumb)
}

// optimizeImages 优化图片
func (re *RuleEngine) optimizeImages(doc *goquery.Document) {
	doc.Find("img").Each(func(i int, s *goquery.Selection) {
		// 添加懒加载
		src := s.AttrOr("src", "")
		s.SetAttr("data-src", src)
		s.SetAttr("src", "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==")
		s.AddClass("lazyload")
		
		// 添加alt属性
		if alt := s.AttrOr("alt", ""); alt == "" {
			s.SetAttr("alt", "图片")
		}
	})
}