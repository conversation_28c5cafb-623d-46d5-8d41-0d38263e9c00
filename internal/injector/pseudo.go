package injector

import (
	"math/rand"
	"regexp"
	"strings"
	"sync"
	"time"
	"unicode"

	"site-cluster/internal/model"
	"github.com/PuerkitoBio/goquery"

	"go.uber.org/zap"
)

type PseudoOriginalProcessor struct {
	logger       *zap.Logger
	random       *rand.Rand
	synonymMap   map[string][]string
	sentencePool []string
	mu           sync.RWMutex  // 保护synonymMap的并发访问
}

func NewPseudoOriginalProcessor(logger *zap.Logger) *PseudoOriginalProcessor {
	return &PseudoOriginalProcessor{
		logger: logger,
		random: rand.New(rand.NewSource(time.Now().UnixNano())),
		synonymMap: make(map[string][]string),
		sentencePool: []string{},
	}
}

// ProcessContent 处理内容伪原创 - 只处理body内的中文文本
func (pop *PseudoOriginalProcessor) ProcessContent(content string, config *model.InjectConfig, keywords []model.Keyword) (string, error) {
	if !config.EnablePseudo {
		return content, nil
	}

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(content))
	if err != nil {
		pop.logger.Error("解析HTML失败", zap.Error(err))
		return content, nil
	}

	// 构建同义词映射
	pop.buildSynonymMap(keywords)

	// 只处理body内的文本节点
	doc.Find("body").Each(func(i int, s *goquery.Selection) {
		pop.processTextNodes(s)
	})

	// 如果没有body标签，处理整个文档的文本节点
	if doc.Find("body").Length() == 0 {
		pop.processTextNodes(doc.Selection)
	}

	// 返回处理后的HTML
	html, _ := doc.Html()
	return html, nil
}

// processTextNodes 递归处理文本节点
func (pop *PseudoOriginalProcessor) processTextNodes(s *goquery.Selection) {
	// 跳过script、style、textarea等标签
	if s.Is("script, style, textarea, code, pre") {
		return
	}

	// 处理当前节点的直接文本内容
	s.Contents().Each(func(i int, child *goquery.Selection) {
		// 判断是否为文本节点
		if child.Nodes != nil && len(child.Nodes) > 0 && child.Nodes[0].Type == 1 { // TextNode
			originalText := child.Text()
			
			// 只处理包含中文的文本
			if pop.containsChinese(originalText) && len(strings.TrimSpace(originalText)) > 0 {
				// 应用伪原创处理
				processedText := pop.processChineseText(originalText)
				
				// 替换文本节点内容
				if processedText != originalText {
					child.ReplaceWithHtml(processedText)
				}
			}
		} else {
			// 递归处理子节点
			pop.processTextNodes(child)
		}
	})
}

// containsChinese 检查文本是否包含中文
func (pop *PseudoOriginalProcessor) containsChinese(text string) bool {
	for _, r := range text {
		if unicode.Is(unicode.Han, r) {
			return true
		}
	}
	return false
}

// processChineseText 处理中文文本
func (pop *PseudoOriginalProcessor) processChineseText(text string) string {
	// 保留原始的空白字符结构
	if strings.TrimSpace(text) == "" {
		return text
	}

	result := text

	// 1. 同义词替换（只替换中文词汇）
	result = pop.replaceChineseSynonyms(result)

	// 2. 对于较长的文本，可以进行句子调整
	if len([]rune(result)) > 50 { // 超过50个字符才进行句子调整
		// 插入过渡词
		result = pop.insertChineseTransitions(result)
	}

	return result
}

// buildSynonymMap 构建同义词映射
func (pop *PseudoOriginalProcessor) buildSynonymMap(keywords []model.Keyword) {
	pop.mu.Lock()
	defer pop.mu.Unlock()
	
	// 重新初始化map
	pop.synonymMap = make(map[string][]string)
	
	for _, kw := range keywords {
		if len(kw.Synonyms) > 0 {
			pop.synonymMap[kw.Keyword] = kw.Synonyms
			// 反向映射
			for _, syn := range kw.Synonyms {
				if _, exists := pop.synonymMap[syn]; !exists {
					pop.synonymMap[syn] = []string{kw.Keyword}
				} else {
					pop.synonymMap[syn] = append(pop.synonymMap[syn], kw.Keyword)
				}
			}
		}
	}
}

// replaceChineseSynonyms 替换中文同义词
func (pop *PseudoOriginalProcessor) replaceChineseSynonyms(content string) string {
	result := content
	
	// 使用读锁保护map访问
	pop.mu.RLock()
	// 复制map到本地变量，避免长时间持有锁
	localMap := make(map[string][]string, len(pop.synonymMap))
	for k, v := range pop.synonymMap {
		localMap[k] = v
	}
	pop.mu.RUnlock()
	
	// 遍历同义词映射，替换中文词汇
	for word, synonyms := range localMap {
		if len(synonyms) > 0 {
			// 30%概率替换
			if pop.random.Float32() < 0.3 {
				replacement := synonyms[pop.random.Intn(len(synonyms))]
				// 直接替换中文词汇（中文没有词边界）
				result = strings.ReplaceAll(result, word, replacement)
			}
		}
	}
	
	return result
}

// shuffleSentences 打乱句子顺序
func (pop *PseudoOriginalProcessor) shuffleSentences(content string) string {
	// 分割段落
	paragraphs := strings.Split(content, "\n\n")
	
	for i, para := range paragraphs {
		if strings.TrimSpace(para) == "" {
			continue
		}
		
		// 分割句子
		sentences := pop.splitSentences(para)
		if len(sentences) > 2 && pop.random.Float32() < 0.3 { // 30%概率打乱
			// 保留首尾句，中间句子打乱
			if len(sentences) > 3 {
				middle := sentences[1 : len(sentences)-1]
				pop.shuffleSlice(middle)
				sentences = append([]string{sentences[0]}, middle...)
				sentences = append(sentences, sentences[len(sentences)-1])
			}
			paragraphs[i] = strings.Join(sentences, " ")
		}
	}
	
	return strings.Join(paragraphs, "\n\n")
}

// insertChineseTransitions 插入中文过渡词
func (pop *PseudoOriginalProcessor) insertChineseTransitions(content string) string {
	transitions := []string{
		"此外，",
		"另外，",
		"值得注意的是，",
		"需要说明的是，",
		"总的来说，",
		"具体而言，",
		"实际上，",
		"换句话说，",
		"更重要的是，",
		"相比之下，",
	}
	
	// 查找中文句号、叹号、问号的位置
	chinesePunctuations := regexp.MustCompile(`[。！？]`)
	matches := chinesePunctuations.FindAllStringIndex(content, -1)
	
	// 随机在某些句子后添加过渡词（最多添加1-2个）
	addedCount := 0
	maxAdd := 2
	
	for i := len(matches) - 1; i >= 0 && addedCount < maxAdd; i-- {
		if pop.random.Float32() < 0.2 { // 20%概率添加
			pos := matches[i][1]
			if pos < len(content) {
				transition := transitions[pop.random.Intn(len(transitions))]
				content = content[:pos] + transition + content[pos:]
				addedCount++
			}
		}
	}
	
	return content
}

// shuffleParagraphs 打乱段落顺序
func (pop *PseudoOriginalProcessor) shuffleParagraphs(content string) string {
	paragraphs := strings.Split(content, "\n\n")
	
	if len(paragraphs) > 3 && pop.random.Float32() < 0.2 { // 20%概率打乱
		// 保留首尾段，中间段落打乱
		middle := paragraphs[1 : len(paragraphs)-1]
		pop.shuffleSlice(middle)
		result := []string{paragraphs[0]}
		result = append(result, middle...)
		result = append(result, paragraphs[len(paragraphs)-1])
		return strings.Join(result, "\n\n")
	}
	
	return content
}

// addModifiers 添加修饰语
func (pop *PseudoOriginalProcessor) addModifiers(content string) string {
	modifiers := []string{
		"专业的",
		"优质的",
		"高效的",
		"可靠的",
		"先进的",
		"创新的",
		"全面的",
		"独特的",
		"卓越的",
		"领先的",
	}
	
	// 匹配名词短语的正则
	nounPattern := regexp.MustCompile(`(\b)(服务|产品|方案|技术|系统|平台|功能|性能|质量|效果)`)
	
	return nounPattern.ReplaceAllStringFunc(content, func(match string) string {
		if pop.random.Float32() < 0.2 { // 20%概率添加修饰语
			modifier := modifiers[pop.random.Intn(len(modifiers))]
			return modifier + match
		}
		return match
	})
}

// splitSentences 分割句子
func (pop *PseudoOriginalProcessor) splitSentences(text string) []string {
	// 简单的句子分割
	sentences := regexp.MustCompile(`[.!?。！？]+\s*`).Split(text, -1)
	
	// 过滤空句子
	result := []string{}
	for _, s := range sentences {
		s = strings.TrimSpace(s)
		if s != "" {
			result = append(result, s)
		}
	}
	
	return result
}

// shuffleSlice 打乱切片
func (pop *PseudoOriginalProcessor) shuffleSlice(slice []string) {
	for i := range slice {
		j := pop.random.Intn(i + 1)
		slice[i], slice[j] = slice[j], slice[i]
	}
}