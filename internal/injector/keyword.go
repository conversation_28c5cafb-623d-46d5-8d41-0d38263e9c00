package injector

import (
	"fmt"
	"math/rand"
	"strings"
	"time"

	"site-cluster/internal/model"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"
)

type KeywordInjector struct {
	logger            *zap.Logger
	keywords          []string
	keywordsByLibrary map[uint][]string
	random            *rand.Rand
}

func NewKeywordInjector(logger *zap.Logger) *KeywordInjector {
	return &KeywordInjector{
		logger: logger,
		random: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// InjectKeywords 注入关键词
func (ki *KeywordInjector) InjectKeywords(html string, config *model.InjectConfig) (string, error) {
	if !config.EnableKeyword || len(config.Keywords) == 0 {
		return html, nil
	}

	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return html, err
	}

	ki.keywords = config.Keywords
	ki.keywordsByLibrary = config.KeywordsByLibrary
	
	// 设置默认值
	if config.KeywordMaxPerPage == 0 {
		config.KeywordMaxPerPage = 10 // 默认每页最多10个关键词
	}
	if config.KeywordInjectRatio == 0 {
		config.KeywordInjectRatio = 0.3 // 默认30%概率
	}
	if config.KeywordMinWordCount == 0 {
		config.KeywordMinWordCount = 20 // 默认最少20字
	}

	injectedCount := 0
	maxKeywords := config.KeywordMaxPerPage

	// 处理标题
	if config.KeywordInjectTitle && injectedCount < maxKeywords {
		template := config.KeywordTitleTemplate
		if template == "" {
			// 如果没有配置模板，跳过注入
		} else {
			ki.injectTitle(doc, template, config)
			injectedCount++
		}
	}

	// 处理Meta标签
	if config.KeywordInjectMeta && injectedCount < maxKeywords {
		template := config.KeywordMetaTemplate
		if template == "" {
			// 如果没有配置模板，跳过注入
		} else {
			ki.injectMeta(doc, template, config)
			injectedCount++
		}
	}
	
	// 处理描述
	if config.KeywordInjectDesc && injectedCount < maxKeywords {
		template := config.KeywordDescTemplate
		if template == "" {
			// 如果没有配置模板，跳过注入
		} else {
			ki.injectDescriptionWithTemplate(doc, template, config)
			injectedCount++
		}
	}
	
	// 处理H1标签
	if config.KeywordInjectH1 && injectedCount < maxKeywords {
		injectedCount += ki.injectInHeadings(doc, "h1", config, maxKeywords-injectedCount)
	}
	
	// 处理H2标签
	if config.KeywordInjectH2 && injectedCount < maxKeywords {
		injectedCount += ki.injectInHeadings(doc, "h2", config, maxKeywords-injectedCount)
	}
	
	// 处理图片Alt属性
	if config.KeywordInjectAlt && injectedCount < maxKeywords {
		injectedCount += ki.injectInImageAlt(doc, config, maxKeywords-injectedCount)
	}

	// 根据规则注入内容
	for _, rule := range config.InjectRules {
		if rule.Type == "keyword" && injectedCount < maxKeywords {
			ki.applyRule(doc, rule)
			injectedCount++
		}
	}

	// 在正文中插入关键词
	if config.KeywordInjectBody && injectedCount < maxKeywords {
		injectedCount += ki.injectInBodyWithControl(doc, config, maxKeywords-injectedCount)
	}
	
	// 添加隐藏关键词
	if config.KeywordInjectHidden && injectedCount < maxKeywords {
		ki.injectHiddenKeywords(doc, config)
	}

	result, err := doc.Html()
	if err != nil {
		return html, err
	}

	return result, nil
}

// injectTitle 注入标题
func (ki *KeywordInjector) injectTitle(doc *goquery.Document, template string, config *model.InjectConfig) {
	title := doc.Find("title").First()
	originalTitle := title.Text()
	
	// 使用标题专用的关键词
	titleKeywords := ki.getTitleKeywords(config)
	if len(titleKeywords) == 0 {
		titleKeywords = ki.keywords // 如果没有单独设置，使用通用关键词
	}
	
	vars := map[string]string{
		"original": originalTitle,
	}
	
	// 支持多个关键词变量，每个都随机选择
	for i := 1; i <= 5; i++ {
		if len(titleKeywords) > 0 {
			// 随机选择一个关键词
			randomIndex := ki.random.Intn(len(titleKeywords))
			vars[fmt.Sprintf("keyword%d", i)] = titleKeywords[randomIndex]
		}
	}
	
	newTitle := ki.processTemplate(template, vars)
	title.SetText(newTitle)
}

// injectMeta 注入Meta标签
func (ki *KeywordInjector) injectMeta(doc *goquery.Document, template string, config *model.InjectConfig) {
	// 处理keywords meta，不区分大小写
	keywordsMeta := doc.Find("meta[name='keywords'], meta[name='Keywords'], meta[name='KEYWORDS']").First()
	if keywordsMeta.Length() == 0 {
		doc.Find("head").AppendHtml(`<meta name="keywords" content="">`)
		keywordsMeta = doc.Find("meta[name='keywords']").First()
	}

	// 使用Meta专用的关键词
	metaKeywords := ki.getMetaKeywords(config)
	if len(metaKeywords) == 0 {
		metaKeywords = ki.keywords // 如果没有单独设置，使用通用关键词
	}
	
	vars := map[string]string{
		"original_keywords": keywordsMeta.AttrOr("content", ""),
	}
	
	// 支持多个关键词变量，确保不重复
	usedIndices := make(map[int]bool)
	for i := 1; i <= 10; i++ {
		if len(metaKeywords) > 0 {
			// 如果所有关键词都用过了，重新开始
			if len(usedIndices) >= len(metaKeywords) {
				usedIndices = make(map[int]bool)
			}
			
			// 随机选择一个未使用的关键词
			var randomIndex int
			for {
				randomIndex = ki.random.Intn(len(metaKeywords))
				if !usedIndices[randomIndex] {
					break
				}
				// 如果只有一个关键词，直接使用
				if len(metaKeywords) == 1 {
					break
				}
			}
			usedIndices[randomIndex] = true
			vars[fmt.Sprintf("keyword%d", i)] = metaKeywords[randomIndex]
		}
	}
	
	content := ki.processTemplate(template, vars)
	keywordsMeta.SetAttr("content", content)
}

// injectDescriptionWithTemplate 使用模板注入描述
func (ki *KeywordInjector) injectDescriptionWithTemplate(doc *goquery.Document, template string, config *model.InjectConfig) {
	// 查找description meta标签，不区分大小写
	descMeta := doc.Find("meta[name='description'], meta[name='Description'], meta[name='DESCRIPTION']").First()
	if descMeta.Length() == 0 {
		doc.Find("head").AppendHtml(`<meta name="description" content="">`)
		descMeta = doc.Find("meta[name='description']").First()
	}
	
	// 使用描述专用的关键词
	descKeywords := ki.getDescKeywords(config)
	if len(descKeywords) == 0 {
		descKeywords = ki.keywords // 如果没有单独设置，使用通用关键词
	}
	
	vars := map[string]string{
		"original_desc": descMeta.AttrOr("content", ""),
	}
	
	// 支持多个关键词变量，确保不重复
	usedIndices := make(map[int]bool)
	for i := 1; i <= 10; i++ {
		if len(descKeywords) > 0 {
			// 如果所有关键词都用过了，重新开始
			if len(usedIndices) >= len(descKeywords) {
				usedIndices = make(map[int]bool)
			}
			
			// 随机选择一个未使用的关键词
			var randomIndex int
			for {
				randomIndex = ki.random.Intn(len(descKeywords))
				if !usedIndices[randomIndex] {
					break
				}
				// 如果只有一个关键词，直接使用
				if len(descKeywords) == 1 {
					break
				}
			}
			usedIndices[randomIndex] = true
			vars[fmt.Sprintf("keyword%d", i)] = descKeywords[randomIndex]
		}
	}
	
	newDesc := ki.processTemplate(template, vars)
	descMeta.SetAttr("content", newDesc)
}

// applyRule 应用注入规则
func (ki *KeywordInjector) applyRule(doc *goquery.Document, rule model.InjectRule) {
	// 根据概率决定是否注入
	if ki.random.Float32() > rule.Probability {
		return
	}

	selection := doc.Find(rule.Target)
	if selection.Length() == 0 {
		return
	}

	content := ki.processTemplate(rule.Content, map[string]string{
		"keyword": ki.getRandomKeyword(),
		"keywords": strings.Join(ki.keywords, " "),
	})

	switch rule.Position {
	case "before":
		selection.BeforeHtml(content)
	case "after":
		selection.AfterHtml(content)
	case "prepend":
		selection.PrependHtml(content)
	case "append":
		selection.AppendHtml(content)
	case "replace":
		selection.ReplaceWithHtml(content)
	}
}

// injectInBody 在正文中注入关键词
func (ki *KeywordInjector) injectInBody(doc *goquery.Document) {
	// 在段落中随机插入关键词
	doc.Find("p").Each(func(i int, s *goquery.Selection) {
		if ki.random.Float32() < 0.3 { // 30%概率
			text := s.Text()
			newText := ki.insertKeywordsInText(text, 1)
			s.SetText(newText)
		}
	})

	// 在适当位置添加隐藏关键词
	if ki.random.Float32() < 0.5 {
		hiddenDiv := ki.createHiddenKeywordDiv()
		doc.Find("body").AppendHtml(hiddenDiv)
	}
}

// insertKeywordsInText 在文本中插入关键词
func (ki *KeywordInjector) insertKeywordsInText(text string, count int) string {
	words := strings.Fields(text)
	if len(words) < 10 {
		return text
	}

	for i := 0; i < count && i < len(ki.keywords); i++ {
		// 随机位置插入
		pos := ki.random.Intn(len(words))
		keyword := ki.keywords[ki.random.Intn(len(ki.keywords))]
		words = append(words[:pos], append([]string{keyword}, words[pos:]...)...)
	}

	return strings.Join(words, " ")
}

// createHiddenKeywordDiv 创建隐藏的关键词div
func (ki *KeywordInjector) createHiddenKeywordDiv() string {
	styles := []string{
		"display:none",
		"position:absolute;left:-9999px",
		"visibility:hidden",
		"font-size:0",
		"color:transparent",
		"opacity:0",
	}

	style := styles[ki.random.Intn(len(styles))]
	keywords := ki.getRandomKeywords(5)

	return fmt.Sprintf(`<div style="%s">%s</div>`, style, strings.Join(keywords, " "))
}

// getRandomKeyword 获取随机关键词
func (ki *KeywordInjector) getRandomKeyword() string {
	if len(ki.keywords) == 0 {
		return ""
	}
	return ki.keywords[ki.random.Intn(len(ki.keywords))]
}

// getRandomKeywords 获取多个随机关键词
func (ki *KeywordInjector) getRandomKeywords(count int) []string {
	if len(ki.keywords) == 0 {
		return []string{}
	}

	result := make([]string, 0, count)
	for i := 0; i < count && i < len(ki.keywords); i++ {
		result = append(result, ki.keywords[ki.random.Intn(len(ki.keywords))])
	}
	return result
}

// processTemplate 处理模板
func (ki *KeywordInjector) processTemplate(template string, vars map[string]string) string {
	result := template
	for k, v := range vars {
		result = strings.ReplaceAll(result, "{"+k+"}", v)
	}
	return result
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// injectDescription 注入描述
func (ki *KeywordInjector) injectDescription(doc *goquery.Document, config *model.InjectConfig) {
	// 查找description meta标签，不区分大小写
	descMeta := doc.Find("meta[name='description'], meta[name='Description'], meta[name='DESCRIPTION']").First()
	if descMeta.Length() == 0 {
		doc.Find("head").AppendHtml(`<meta name="description" content="">`)
		descMeta = doc.Find("meta[name='description']").First()
	}
	
	originalDesc := descMeta.AttrOr("content", "")
	// 根据配置的比例插入关键词
	newDesc := ki.insertKeywordsWithRatio(originalDesc, config.KeywordInjectRatio)
	descMeta.SetAttr("content", newDesc)
}

// injectInHeadings 在标题标签中注入关键词
func (ki *KeywordInjector) injectInHeadings(doc *goquery.Document, tag string, config *model.InjectConfig, maxCount int) int {
	injected := 0
	doc.Find(tag).Each(func(i int, s *goquery.Selection) {
		if injected >= maxCount {
			return
		}
		if ki.random.Float32() < config.KeywordInjectRatio {
			text := s.Text()
			// 在标题后添加关键词
			keyword := ki.getRandomKeyword()
			newText := text + " - " + keyword
			s.SetText(newText)
			injected++
		}
	})
	return injected
}

// injectInImageAlt 在图片Alt属性中注入关键词
func (ki *KeywordInjector) injectInImageAlt(doc *goquery.Document, config *model.InjectConfig, maxCount int) int {
	injected := 0
	doc.Find("img").Each(func(i int, s *goquery.Selection) {
		if injected >= maxCount {
			return
		}
		alt := s.AttrOr("alt", "")
		if alt == "" || ki.random.Float32() < config.KeywordInjectRatio {
			keyword := ki.getRandomKeyword()
			if alt != "" {
				s.SetAttr("alt", alt + " " + keyword)
			} else {
				s.SetAttr("alt", keyword)
			}
			injected++
		}
	})
	return injected
}

// injectInBodyWithControl 在正文中有控制地注入关键词
func (ki *KeywordInjector) injectInBodyWithControl(doc *goquery.Document, config *model.InjectConfig, maxCount int) int {
	injected := 0
	doc.Find("p").Each(func(i int, s *goquery.Selection) {
		if injected >= maxCount {
			return
		}
		
		text := s.Text()
		wordCount := len(strings.Fields(text))
		
		// 检查最小字数要求
		if wordCount < config.KeywordMinWordCount {
			return
		}
		
		// 根据配置的概率决定是否注入
		if ki.random.Float32() < config.KeywordInjectRatio {
			// 计算应该注入的关键词数量（基于密度控制）
			keywordsToInject := 1
			if config.KeywordDensity > 0 {
				keywordsToInject = int(float32(wordCount) * config.KeywordDensity / 100)
				if keywordsToInject < 1 {
					keywordsToInject = 1
				}
			}
			
			newText := ki.insertKeywordsInTextWithCount(text, keywordsToInject)
			s.SetText(newText)
			injected += keywordsToInject
		}
	})
	return injected
}

// insertKeywordsWithRatio 根据比例插入关键词
func (ki *KeywordInjector) insertKeywordsWithRatio(text string, ratio float32) string {
	words := strings.Fields(text)
	if len(words) < 5 {
		return text
	}
	
	// 计算要插入的关键词数量
	insertCount := int(float32(len(words)) * ratio)
	if insertCount < 1 {
		insertCount = 1
	}
	
	for i := 0; i < insertCount && i < len(ki.keywords); i++ {
		pos := ki.random.Intn(len(words))
		keyword := ki.keywords[ki.random.Intn(len(ki.keywords))]
		words = append(words[:pos], append([]string{keyword}, words[pos:]...)...)
	}
	
	return strings.Join(words, " ")
}

// insertKeywordsInTextWithCount 在文本中插入指定数量的关键词
func (ki *KeywordInjector) insertKeywordsInTextWithCount(text string, count int) string {
	words := strings.Fields(text)
	if len(words) < 10 {
		return text
	}

	for i := 0; i < count && i < len(ki.keywords); i++ {
		pos := ki.random.Intn(len(words))
		keyword := ki.keywords[ki.random.Intn(len(ki.keywords))]
		words = append(words[:pos], append([]string{keyword}, words[pos:]...)...)
	}

	return strings.Join(words, " ")
}

// injectHiddenKeywords 注入隐藏关键词
func (ki *KeywordInjector) injectHiddenKeywords(doc *goquery.Document, config *model.InjectConfig) {
	// 总是注入隐藏关键词（如果启用）
	hiddenDiv := ki.createHiddenKeywordDiv()
	doc.Find("body").AppendHtml(hiddenDiv)
}

// getTitleKeywords 获取标题专用的关键词
func (ki *KeywordInjector) getTitleKeywords(config *model.InjectConfig) []string {
	// 如果配置了标题专用关键词库，从中获取关键词
	if len(config.TitleKeywordLibraryIDs) > 0 {
		return ki.getKeywordsFromLibraries(config.TitleKeywordLibraryIDs)
	}
	// 否则使用通用关键词
	return ki.keywords
}

// getMetaKeywords 获取Meta专用的关键词
func (ki *KeywordInjector) getMetaKeywords(config *model.InjectConfig) []string {
	// 如果配置了Meta专用关键词库，从中获取关键词
	if len(config.MetaKeywordLibraryIDs) > 0 {
		return ki.getKeywordsFromLibraries(config.MetaKeywordLibraryIDs)
	}
	// 否则使用通用关键词
	return ki.keywords
}

// getDescKeywords 获取描述专用的关键词
func (ki *KeywordInjector) getDescKeywords(config *model.InjectConfig) []string {
	// 如果配置了描述专用关键词库，从中获取关键词
	if len(config.DescKeywordLibraryIDs) > 0 {
		return ki.getKeywordsFromLibraries(config.DescKeywordLibraryIDs)
	}
	// 否则使用通用关键词
	return ki.keywords
}

// getKeywordsFromLibraries 从指定的关键词库IDs中获取关键词
func (ki *KeywordInjector) getKeywordsFromLibraries(libraryIDs []uint) []string {
	// 从按库ID分组的关键词中获取
	if ki.keywordsByLibrary == nil {
		return ki.keywords
	}
	
	var result []string
	seen := make(map[string]bool)
	
	for _, libID := range libraryIDs {
		if keywords, ok := ki.keywordsByLibrary[libID]; ok {
			for _, keyword := range keywords {
				if !seen[keyword] {
					result = append(result, keyword)
					seen[keyword] = true
				}
			}
		}
	}
	
	// 如果没有找到任何关键词，返回通用关键词
	if len(result) == 0 {
		return ki.keywords
	}
	
	return result
}