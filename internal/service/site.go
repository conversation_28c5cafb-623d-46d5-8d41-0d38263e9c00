package service

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"
	"site-cluster/internal/interfaces"

	"go.uber.org/zap"
)

type SiteService struct {
	logger                *zap.Logger
	repo                  repository.SiteRepository
	scheduler             interfaces.Scheduler
	queue                 interfaces.TaskQueue
	systemSettingsService *SystemSettingsService
	keywordService        *KeywordService
	cache404Service       *Cache404Service
	companyLibraryService *CompanyLibraryService
	redisCacheService     *RedisCacheService
	sitemapService        *SitemapService
	fileCacheService      *FileCacheService
}

func NewSiteService(
	logger *zap.Logger,
	repo repository.SiteRepository,
	scheduler interfaces.Scheduler,
	queue interfaces.TaskQueue,
	systemSettingsService *SystemSettingsService,
	keywordService *KeywordService,
) *SiteService {
	return &SiteService{
		logger:                logger,
		repo:                  repo,
		scheduler:             scheduler,
		queue:                 queue,
		systemSettingsService: systemSettingsService,
		keywordService:        keywordService,
	}
}

// SetKeywordService 设置关键词服务
func (s *SiteService) SetKeywordService(keywordService *KeywordService) {
	s.keywordService = keywordService
}

// SetCache404Service 设置404缓存服务
func (s *SiteService) SetCache404Service(cache404Service *Cache404Service) {
	s.cache404Service = cache404Service
}

// SetCompanyLibraryService 设置企业名称库服务
func (s *SiteService) SetCompanyLibraryService(companyLibraryService *CompanyLibraryService) {
	s.companyLibraryService = companyLibraryService
}

// SetRedisCacheService 设置Redis缓存服务
func (s *SiteService) SetRedisCacheService(redisCacheService *RedisCacheService) {
	s.redisCacheService = redisCacheService
}

// SetSitemapService 设置站点地图服务
func (s *SiteService) SetSitemapService(sitemapService *SitemapService) {
	s.sitemapService = sitemapService
}

// SetFileCacheService 设置文件缓存服务
func (s *SiteService) SetFileCacheService(fileCacheService *FileCacheService) {
	s.fileCacheService = fileCacheService
}

// GetSites 获取站点列表
func (s *SiteService) GetSites(page, pageSize int, status string, categoryID uint, keyword string, sortBy, sortOrder string) ([]*model.Site, int64, error) {
	offset := (page - 1) * pageSize
	
	// 构建查询条件
	conditions := make(map[string]interface{})
	if status != "" {
		conditions["status"] = status
	}
	if categoryID > 0 {
		conditions["category_id"] = categoryID
	}
	if keyword != "" {
		conditions["keyword"] = keyword
	}
	// 添加排序参数
	conditions["sort_by"] = sortBy
	conditions["sort_order"] = sortOrder
	
	sites, err := s.repo.List(offset, pageSize, conditions)
	if err != nil {
		s.logger.Error("获取站点列表失败", zap.Error(err))
		return nil, 0, err
	}
	
	total, err := s.repo.Count(conditions)
	if err != nil {
		s.logger.Error("获取站点总数失败", zap.Error(err))
		return nil, 0, err
	}
	
	return sites, total, nil
}

// CountByCategoryID 统计分类下的站点数量
func (s *SiteService) CountByCategoryID(categoryID uint) (int, error) {
	conditions := map[string]interface{}{
		"category_id": categoryID,
	}
	count, err := s.repo.Count(conditions)
	return int(count), err
}

// GetSitesByCategoryID 获取分类下的所有站点
func (s *SiteService) GetSitesByCategoryID(categoryID uint) ([]*model.Site, error) {
	conditions := map[string]interface{}{
		"category_id": categoryID,
	}
	// 获取所有站点，不限制数量
	sites, err := s.repo.List(0, 0, conditions)
	if err != nil {
		return nil, err
	}
	return sites, nil
}

// GetSite 获取单个站点
func (s *SiteService) GetSite(id uint) (*model.Site, error) {
	site, err := s.repo.GetByID(id)
	if err != nil {
		s.logger.Error("获取站点失败", zap.Uint("id", id), zap.Error(err))
		return nil, err
	}
	
	if site == nil {
		return nil, errors.New("站点不存在")
	}
	
	// 加载关键词（用于前端展示）
	s.loadSiteKeywords(site)
	
	return site, nil
}

// CreateSite 创建站点
func (s *SiteService) CreateSite(site *model.Site) error {
	// 检查域名是否已存在
	existing, err := s.repo.GetByDomain(site.Domain)
	if err != nil {
		s.logger.Error("检查域名失败", zap.Error(err))
		return err
	}
	
	if existing != nil {
		return errors.New("域名已存在")
	}
	
	// 检查子域名是否已被其他站点使用
	if len(site.Aliases) > 0 {
		for _, alias := range site.Aliases {
			if err := s.checkAliasDomainAvailable(alias.AliasDomain); err != nil {
				return err
			}
		}
	}
	
	// 双重检查：清理可能的脏数据（防止之前失败的创建留下残留数据）
	s.cleanupOrphanedData(site.Domain)
	
	// 如果有关键词库ID，只保存ID，不加载关键词
	if site.InjectConfig != nil && s.keywordService != nil {
		// 兼容旧版：如果只设置了通用关键词库，复制到各个位置
		if len(site.InjectConfig.KeywordLibraryIDs) > 0 {
			if len(site.InjectConfig.TitleKeywordLibraryIDs) == 0 {
				site.InjectConfig.TitleKeywordLibraryIDs = site.InjectConfig.KeywordLibraryIDs
			}
			if len(site.InjectConfig.MetaKeywordLibraryIDs) == 0 {
				site.InjectConfig.MetaKeywordLibraryIDs = site.InjectConfig.KeywordLibraryIDs
			}
			if len(site.InjectConfig.DescKeywordLibraryIDs) == 0 {
				site.InjectConfig.DescKeywordLibraryIDs = site.InjectConfig.KeywordLibraryIDs
			}
		}
		
		// 不再在这里加载关键词，只保存关键词库ID
		// 关键词将在实际使用时动态加载（GetSiteByDomain等方法中）
		site.InjectConfig.Keywords = nil // 清空关键词，只保留ID
		site.InjectConfig.KeywordsByLibrary = nil
	}
	
	// 如果启用了企业名称且没有指定名称，随机选择一个
	if site.EnableCompanyName && site.CompanyName == "" && site.CompanyLibraryID > 0 {
		if s.companyLibraryService != nil {
			companyName, err := s.companyLibraryService.GetRandomCompanyName(site.CompanyLibraryID)
			if err != nil {
				s.logger.Warn("获取随机企业名称失败", zap.Error(err))
			} else {
				site.CompanyName = companyName
			}
		}
	}
	
	// 创建站点
	if err := s.repo.Create(site); err != nil {
		s.logger.Error("创建站点失败", zap.Error(err))
		return err
	}
	
	// 注册定时任务
	if site.Status == "active" {
		s.scheduler.RegisterSiteCrawlJob(site)
	}
	
	s.logger.Info("站点创建成功", 
		zap.Uint("id", site.ID),
		zap.String("domain", site.Domain))
	
	return nil
}

// cleanupOrphanedData 清理可能的脏数据
func (s *SiteService) cleanupOrphanedData(domain string) {
	// 注意：这个方法应该谨慎使用，只在确认没有正常站点记录时清理关联数据
	// 查询是否存在孤立的InjectConfig记录（没有对应的Site记录）
	// 由于我们已经通过GetByDomain确认站点不存在，所以可以安全清理
}

// DetectOrphanedData 检测孤立数据
func (s *SiteService) DetectOrphanedData() ([]model.OrphanedData, error) {
	return s.repo.DetectOrphanedData()
}

// CleanupAllOrphanedData 批量清理所有孤立数据
func (s *SiteService) CleanupAllOrphanedData() (int, error) {
	return s.repo.CleanupAllOrphanedData()
}

// UpdateCacheStatus 更新站点缓存状态
func (s *SiteService) UpdateCacheStatus(siteID uint, status string, errMsg string) error {
	updateTime := time.Now()
	return s.repo.UpdateCacheStatus(siteID, status, errMsg, updateTime)
}

// UpdateSite 更新站点
func (s *SiteService) UpdateSite(site *model.Site) error {
	oldSite, err := s.repo.GetByID(site.ID)
	if err != nil {
		return err
	}
	
	if oldSite == nil {
		return errors.New("站点不存在")
	}
	
	// 如果有关键词库ID，只保存ID，不加载关键词
	if site.InjectConfig != nil && s.keywordService != nil {
		// 兼容旧版：如果只设置了通用关键词库，复制到各个位置
		if len(site.InjectConfig.KeywordLibraryIDs) > 0 {
			if len(site.InjectConfig.TitleKeywordLibraryIDs) == 0 {
				site.InjectConfig.TitleKeywordLibraryIDs = site.InjectConfig.KeywordLibraryIDs
			}
			if len(site.InjectConfig.MetaKeywordLibraryIDs) == 0 {
				site.InjectConfig.MetaKeywordLibraryIDs = site.InjectConfig.KeywordLibraryIDs
			}
			if len(site.InjectConfig.DescKeywordLibraryIDs) == 0 {
				site.InjectConfig.DescKeywordLibraryIDs = site.InjectConfig.KeywordLibraryIDs
			}
		}
		
		// 不再在这里加载关键词，只保存关键词库ID
		// 关键词将在实际使用时动态加载（GetSiteByDomain等方法中）
		site.InjectConfig.Keywords = nil // 清空关键词，只保留ID
		site.InjectConfig.KeywordsByLibrary = nil
	}
	
	// 如果启用了企业名称，且之前没有名称，且没有手动设置名称，则随机选择一个
	if site.EnableCompanyName && oldSite.CompanyName == "" && site.CompanyName == "" && site.CompanyLibraryID > 0 {
		if s.companyLibraryService != nil {
			companyName, err := s.companyLibraryService.GetRandomCompanyName(site.CompanyLibraryID)
			if err != nil {
				s.logger.Warn("获取随机企业名称失败", zap.Error(err))
			} else {
				site.CompanyName = companyName
			}
		}
	}
	
	// 检查目标站地址是否变化
	targetUrlChanged := oldSite.TargetURL != site.TargetURL
	
	// 更新站点
	if err := s.repo.Update(site); err != nil {
		s.logger.Error("更新站点失败", zap.Error(err))
		return err
	}
	
	// 如果目标站地址变化，清除站点地图数据
	if targetUrlChanged {
		s.logger.Info("目标站地址变化，清除站点地图", 
			zap.String("domain", site.Domain),
			zap.String("old_target", oldSite.TargetURL),
			zap.String("new_target", site.TargetURL))
		
		// 清除站点地图数据
		if s.sitemapService != nil {
			if _, err := s.sitemapService.ClearSitemapBySiteID(site.ID); err != nil {
				s.logger.Error("清除站点地图失败", zap.Error(err))
			}
		}
		
		// 清除所有缓存（Redis缓存和文件缓存）
		s.clearSiteRedisCache(site.ID, site.Domain)
		if s.fileCacheService != nil {
			if _, err := s.fileCacheService.ClearSiteCache(site.Domain); err != nil {
				s.logger.Error("清除文件缓存失败", zap.Error(err))
			}
		}
	}
	
	// 清除Redis缓存（如果配置发生变化）
	if oldSite.InjectConfig != nil && site.InjectConfig != nil {
		// 检查拼音配置是否发生变化
		oldPinyinConfig := oldSite.InjectConfig.EnablePinyin || 
			oldSite.InjectConfig.EnablePinyinSpecialChars || 
			oldSite.InjectConfig.PinyinSpecialCharsRatio != site.InjectConfig.PinyinSpecialCharsRatio ||
			oldSite.InjectConfig.PinyinSpecialChars != site.InjectConfig.PinyinSpecialChars
		
		newPinyinConfig := site.InjectConfig.EnablePinyin || 
			site.InjectConfig.EnablePinyinSpecialChars
		
		if oldPinyinConfig || newPinyinConfig {
			// 拼音配置有变化，清除Redis中的站点配置缓存
			s.clearSiteRedisCache(site.ID, site.Domain)
		}
	}
	
	// 更新定时任务
	if oldSite.Status != site.Status {
		if site.Status == "active" {
			s.scheduler.RegisterSiteCrawlJob(site)
		} else {
			s.scheduler.RemoveSiteCrawlJob(site.ID)
		}
	}
	
	s.logger.Info("站点更新成功", 
		zap.Uint("id", site.ID),
		zap.String("domain", site.Domain))
	
	return nil
}

// GetEffectiveSiteSettings 获取站点的有效设置（考虑全局默认值）
func (s *SiteService) GetEffectiveSiteSettings(site *model.Site) *model.Site {
	if s.systemSettingsService == nil {
		return site
	}
	
	systemSettings, err := s.systemSettingsService.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return site
	}
	
	// 复制站点信息
	effectiveSite := *site
	
	// 使用全局缓存设置
	if site.UseGlobalCache {
		effectiveSite.CacheHomeTTL = systemSettings.DefaultCacheHomeTTL
		effectiveSite.CacheOtherTTL = systemSettings.DefaultCacheOtherTTL
		effectiveSite.EnableRedisCache = systemSettings.DefaultEnableRedisCache
	}
	
	return &effectiveSite
}

// DeleteSite 删除站点
func (s *SiteService) DeleteSite(id uint) error {
	site, err := s.repo.GetByID(id)
	if err != nil {
		return err
	}
	
	if site == nil {
		return errors.New("站点不存在")
	}
	
	// 删除定时任务
	s.scheduler.RemoveSiteCrawlJob(id)
	
	// 清理404缓存和统计（如果存在）
	if s.cache404Service != nil {
		s.cache404Service.Clear404Cache(site.Domain)
		s.cache404Service.Reset404Stats(site.Domain)
	}
	
	// 删除站点（包含所有关联数据）
	if err := s.repo.Delete(id); err != nil {
		s.logger.Error("删除站点失败", zap.Error(err))
		return err
	}
	
	s.logger.Info("站点删除成功", 
		zap.Uint("id", id),
		zap.String("domain", site.Domain))
	
	return nil
}

// BatchDeleteSites 批量删除站点
func (s *SiteService) BatchDeleteSites(ids []uint) (int, error) {
	deletedCount := 0
	
	for _, id := range ids {
		// 获取站点信息
		site, err := s.repo.GetByID(id)
		if err != nil || site == nil {
			continue // 跳过不存在的站点
		}
		
		// 删除定时任务
		s.scheduler.RemoveSiteCrawlJob(id)
		
		// 清理404缓存和统计（如果存在）
		if s.cache404Service != nil {
			s.cache404Service.Clear404Cache(site.Domain)
			s.cache404Service.Reset404Stats(site.Domain)
		}
		
		// 删除站点（包含所有关联数据）
		if err := s.repo.Delete(id); err != nil {
			s.logger.Error("删除站点失败", 
				zap.Uint("id", id),
				zap.String("domain", site.Domain),
				zap.Error(err))
			continue // 继续删除其他站点
		}
		
		deletedCount++
		s.logger.Info("站点删除成功", 
			zap.Uint("id", id),
			zap.String("domain", site.Domain))
	}
	
	return deletedCount, nil
}

// TriggerCrawl 触发爬取
func (s *SiteService) TriggerCrawl(siteID uint) (*model.CrawlJob, error) {
	site, err := s.repo.GetByID(siteID)
	if err != nil {
		return nil, err
	}
	
	if site == nil {
		return nil, errors.New("站点不存在")
	}
	
	if site.Status != "active" {
		return nil, errors.New("站点未激活")
	}
	
	// 创建爬取任务
	job := &model.CrawlJob{
		SiteID:    site.ID,
		Type:      "manual",
		Status:    "pending",
		CreatedAt: time.Now(),
	}
	
	// 保存任务
	if err := s.repo.CreateCrawlJob(job); err != nil {
		s.logger.Error("创建爬取任务失败", zap.Error(err))
		return nil, err
	}
	
	// 加入任务队列
	if err := s.queue.EnqueueCrawlJob(job); err != nil {
		s.logger.Error("任务入队失败", zap.Error(err))
		return nil, err
	}
	
	s.logger.Info("爬取任务已创建", 
		zap.Uint("job_id", job.ID),
		zap.String("domain", site.Domain))
	
	return job, nil
}

// GetSiteStats 获取站点统计
func (s *SiteService) GetSiteStats(siteID uint) (map[string]interface{}, error) {
	site, err := s.repo.GetByID(siteID)
	if err != nil {
		return nil, err
	}
	
	if site == nil {
		return nil, errors.New("站点不存在")
	}
	
	// 获取统计信息
	stats := make(map[string]interface{})
	
	// 获取爬取任务统计
	jobStats, err := s.repo.GetCrawlJobStats(siteID)
	if err != nil {
		s.logger.Error("获取任务统计失败", zap.Error(err))
		jobStats = make(map[string]int64)
	}
	
	// 获取缓存统计
	cacheStats, err := s.repo.GetCacheStats(site.Domain)
	if err != nil {
		s.logger.Error("获取缓存统计失败", zap.Error(err))
		cacheStats = make(map[string]interface{})
	}
	
	stats["site"] = site
	stats["jobs"] = jobStats
	stats["cache"] = cacheStats
	
	return stats, nil
}

// GetKeywords 获取站点关键词
func (s *SiteService) GetKeywords(injectConfigID uint) ([]*model.Keyword, error) {
	// 这里应该从关键词服务获取
	// 暂时返回空数组
	return []*model.Keyword{}, nil
}

// GetKeywordsByLibraryID 通过库ID获取关键词
func (s *SiteService) GetKeywordsByLibraryID(libraryID uint) ([]*model.Keyword, error) {
	if s.keywordService != nil {
		return s.keywordService.GetKeywordsByLibraryID(libraryID)
	}
	return []*model.Keyword{}, nil
}

// GetActiveSites 获取活跃站点
func (s *SiteService) GetActiveSites() ([]*model.Site, error) {
	conditions := map[string]interface{}{
		"status": "active",
	}
	return s.repo.List(0, 1000, conditions)
}

// GetAllSites 获取所有站点
func (s *SiteService) GetAllSites() ([]*model.Site, error) {
	return s.repo.List(0, 10000, nil)
}

// GetFailedTasks 获取失败的任务
func (s *SiteService) GetFailedTasks(limit int) ([]*model.CrawlJob, error) {
	// 这里应该从任务仓储获取
	// 暂时返回空数组
	return []*model.CrawlJob{}, nil
}

// GetSiteByDomain 根据域名获取站点
func (s *SiteService) GetSiteByDomain(domain string) (*model.Site, error) {
	// 先尝试直接匹配主域名
	site, err := s.repo.GetByDomain(domain)
	if err != nil {
		return nil, err
	}
	
	// 如果没找到，尝试从子域名表查找
	if site == nil {
		site, err = s.repo.GetByAliasDomain(domain)
		if err != nil {
			return nil, err
		}
	}
	
	// 如果还没找到，检查是否是根域名访问
	if site == nil && !strings.HasPrefix(domain, "www.") {
		// 尝试查找 www 子域名的配置
		wwwDomain := "www." + domain
		site, err = s.repo.GetByDomain(wwwDomain)
		if err != nil {
			return nil, err
		}
		
		// 如果还没找到，尝试从子域名表查找
		if site == nil {
			site, err = s.repo.GetByAliasDomain(wwwDomain)
			if err != nil {
				return nil, err
			}
		}
		
		// 只有当 www 站点开启了 @ → www 跳转时，才认为根域名有效
		if site != nil {
			// 检查是否开启了跳转
			shouldRedirect := false
			if site.RedirectWWW != nil {
				shouldRedirect = *site.RedirectWWW
			} else if s.systemSettingsService != nil {
				// 使用全局设置
				settings, _ := s.systemSettingsService.GetSystemSettings()
				if settings != nil {
					shouldRedirect = settings.GlobalRedirectWWW
				}
			}
			
			// 如果没有开启跳转，根域名不应该被识别
			if !shouldRedirect {
				return nil, nil
			}
		}
	}
	
	if site != nil {
		// 生产环境优化：只在必要时加载
		// 注意：子域名列表暂不自动加载，需要时再单独调用
		s.loadSiteKeywords(site) // 加载关键词配置，这对关键词注入功能是必要的
		// s.loadSiteAliases(site)
		
		// 但要确保通过子域名访问时，站点的Domain字段保持为主域名
		// GetByAliasDomain已经返回了主站点信息，Domain字段应该是正确的
	}
	
	return site, nil
}

// loadSiteKeywords 加载站点的关键词配置
func (s *SiteService) loadSiteKeywords(site *model.Site) {
	if site.InjectConfig == nil || s.keywordService == nil {
		return
	}
	
	// 收集所有需要的关键词库ID
	allLibraryIDs := make(map[uint]bool)
	for _, id := range site.InjectConfig.TitleKeywordLibraryIDs {
		allLibraryIDs[id] = true
	}
	for _, id := range site.InjectConfig.MetaKeywordLibraryIDs {
		allLibraryIDs[id] = true
	}
	for _, id := range site.InjectConfig.DescKeywordLibraryIDs {
		allLibraryIDs[id] = true
	}
	// 兼容旧版
	for _, id := range site.InjectConfig.KeywordLibraryIDs {
		allLibraryIDs[id] = true
	}
	
	// 加载所有关键词
	if len(allLibraryIDs) > 0 {
		var libraryIDs []uint
		for id := range allLibraryIDs {
			libraryIDs = append(libraryIDs, id)
		}
		
		// 加载通用关键词列表
		keywords, err := s.keywordService.GetKeywordsByLibraryIDs(libraryIDs)
		if err != nil {
			s.logger.Error("加载关键词库失败", zap.Error(err))
		} else {
			site.InjectConfig.Keywords = keywords
		}
		
		// 按库ID分组加载关键词
		site.InjectConfig.KeywordsByLibrary = make(map[uint][]string)
		for libID := range allLibraryIDs {
			libKeywords, err := s.keywordService.GetKeywordsByLibraryIDs([]uint{libID})
			if err == nil && len(libKeywords) > 0 {
				site.InjectConfig.KeywordsByLibrary[libID] = libKeywords
			}
		}
	}
}

// GetTotalSites 获取站点总数
func (s *SiteService) GetTotalSites() (int, error) {
	count, err := s.repo.Count(nil)
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

// ResetSite404Count 重置站点的404统计
func (s *SiteService) ResetSite404Count(domain string) error {
	site, err := s.repo.GetByDomain(domain)
	if err != nil {
		return err
	}
	if site == nil {
		return fmt.Errorf("站点不存在: %s", domain)
	}
	
	// 重置404统计
	site.Count404 = 0
	
	// 更新数据库
	return s.repo.Update(site)
}

// GetDB 获取数据库实例
func (s *SiteService) GetDB() interface{} {
	return s.repo.GetDB()
}

// clearSiteRedisCache 清除站点的Redis缓存
func (s *SiteService) clearSiteRedisCache(siteID uint, domain string) {
	// 当站点配置更新时，需要清除Redis中的缓存以确保新配置立即生效
	if s.redisCacheService != nil {
		// 清除基于站点ID的缓存（新方式）
		siteIDKey := fmt.Sprintf("site:id:%d", siteID)
		if err := s.redisCacheService.Del(siteIDKey); err != nil {
			s.logger.Error("清除站点ID Redis缓存失败", 
				zap.Uint("site_id", siteID),
				zap.String("domain", domain),
				zap.Error(err))
		} else {
			s.logger.Info("成功清除站点ID Redis缓存", 
				zap.Uint("site_id", siteID),
				zap.String("domain", domain))
		}
		
		// 兼容性：清除旧的基于域名的缓存
		// 清除主域名缓存
		siteKey := fmt.Sprintf("site:%s", domain)
		if err := s.redisCacheService.Del(siteKey); err != nil {
			// 忽略清除缓存的错误，不影响主流程
		}
		
		// 清除www子域名缓存（如果存在）
		if !strings.HasPrefix(domain, "www.") {
			wwwKey := fmt.Sprintf("site:www.%s", domain)
			if err := s.redisCacheService.Del(wwwKey); err != nil {
				// 忽略清除缓存的错误，不影响主流程
			}
		}
		
		// 清除拼音配置缓存
		pinyinKey := fmt.Sprintf("site:%s:pinyin_config", domain)
		if err := s.redisCacheService.Del(pinyinKey); err != nil {
			// 忽略清除缓存的错误，不影响主流程
		}
	}
}

// BatchUpdateSites 批量更新站点
func (s *SiteService) BatchUpdateSites(siteIDs []uint, settings map[string]interface{}) (int, error) {
	if len(siteIDs) == 0 {
		return 0, errors.New("至少需要选择一个站点")
	}

	if len(settings) == 0 {
		return 0, errors.New("至少需要设置一个配置项")
	}

	// 获取所有要更新的站点
	sites := make([]*model.Site, 0, len(siteIDs))
	for _, siteID := range siteIDs {
		site, err := s.repo.GetByID(siteID)
		if err != nil {
			// s.logger.Warn("批量更新时获取站点失败", 
			//	zap.Uint("site_id", siteID),
			//	zap.Error(err))
			continue
		}
		if site == nil {
			// s.logger.Warn("站点不存在", 
			//	zap.Uint("site_id", siteID))
			continue
		}
		sites = append(sites, site)
	}

	if len(sites) == 0 {
		return 0, errors.New("未找到有效的站点")
	}

	updatedCount := 0
	
	// 逐个站点进行更新
	for _, site := range sites {
		// 应用批量设置到站点
		updated := s.applyBatchSettingsToSite(site, settings)
		
		if updated {
			// 保存到数据库
			if err := s.repo.Update(site); err != nil {
				s.logger.Error("批量更新站点失败", 
					zap.Uint("site_id", site.ID),
					zap.String("domain", site.Domain),
					zap.Error(err))
				continue
			}
			
			// 清除Redis缓存
			s.clearSiteRedisCache(site.ID, site.Domain)
			
			updatedCount++
			s.logger.Debug("批量更新站点成功", 
				zap.Uint("site_id", site.ID),
				zap.String("domain", site.Domain))
		}
	}

	return updatedCount, nil
}

// applyBatchSettingsToSite 将批量设置应用到单个站点
func (s *SiteService) applyBatchSettingsToSite(site *model.Site, settings map[string]interface{}) bool {
	updated := false

	// 基础功能设置
	if value, exists := settings["enable_preload"]; exists {
		if boolValue, ok := value.(bool); ok && site.EnablePreload != boolValue {
			site.EnablePreload = boolValue
			updated = true
		}
	}

	if value, exists := settings["download_external_resources"]; exists {
		if boolValue, ok := value.(bool); ok && site.DownloadExternalResources != boolValue {
			site.DownloadExternalResources = boolValue
			updated = true
		}
	}

	if value, exists := settings["enable_https_check"]; exists {
		if boolValue, ok := value.(bool); ok && site.EnableHTTPSCheck != boolValue {
			site.EnableHTTPSCheck = boolValue
			updated = true
		}
	}

	if value, exists := settings["enable_traditional_convert"]; exists {
		if boolValue, ok := value.(bool); ok && site.EnableTraditionalConvert != boolValue {
			site.EnableTraditionalConvert = boolValue
			updated = true
		}
	}

	// 访问控制设置
	if value, exists := settings["use_global_ua_check"]; exists {
		var boolPtr *bool
		if value == "" {
			boolPtr = nil
		} else if boolValue, ok := value.(bool); ok {
			boolPtr = &boolValue
		}
		if (site.UseGlobalUACheck == nil && boolPtr != nil) ||
			(site.UseGlobalUACheck != nil && boolPtr == nil) ||
			(site.UseGlobalUACheck != nil && boolPtr != nil && *site.UseGlobalUACheck != *boolPtr) {
			site.UseGlobalUACheck = boolPtr
			updated = true
		}
	}

	if value, exists := settings["use_global_referer_check"]; exists {
		var boolPtr *bool
		if value == "" {
			boolPtr = nil
		} else if boolValue, ok := value.(bool); ok {
			boolPtr = &boolValue
		}
		if (site.UseGlobalRefererCheck == nil && boolPtr != nil) ||
			(site.UseGlobalRefererCheck != nil && boolPtr == nil) ||
			(site.UseGlobalRefererCheck != nil && boolPtr != nil && *site.UseGlobalRefererCheck != *boolPtr) {
			site.UseGlobalRefererCheck = boolPtr
			updated = true
		}
	}

	if value, exists := settings["enable_spider_block"]; exists {
		if boolValue, ok := value.(bool); ok && site.EnableSpiderBlock != boolValue {
			site.EnableSpiderBlock = boolValue
			updated = true
		}
	}

	if value, exists := settings["redirect_www"]; exists {
		var boolPtr *bool
		if value == "" {
			boolPtr = nil
		} else if boolValue, ok := value.(bool); ok {
			boolPtr = &boolValue
		}
		if (site.RedirectWWW == nil && boolPtr != nil) ||
			(site.RedirectWWW != nil && boolPtr == nil) ||
			(site.RedirectWWW != nil && boolPtr != nil && *site.RedirectWWW != *boolPtr) {
			site.RedirectWWW = boolPtr
			updated = true
		}
	}

	// 内容处理设置 (注入配置)
	if site.InjectConfig == nil {
		site.InjectConfig = &model.InjectConfig{SiteID: site.ID}
	}

	if value, exists := settings["enable_keyword"]; exists {
		if boolValue, ok := value.(bool); ok && site.InjectConfig.EnableKeyword != boolValue {
			site.InjectConfig.EnableKeyword = boolValue
			updated = true
		}
	}

	if value, exists := settings["enable_pseudo"]; exists {
		if boolValue, ok := value.(bool); ok && site.InjectConfig.EnablePseudo != boolValue {
			site.InjectConfig.EnablePseudo = boolValue
			updated = true
		}
	}

	if value, exists := settings["enable_structure"]; exists {
		if boolValue, ok := value.(bool); ok && site.InjectConfig.EnableStructure != boolValue {
			site.InjectConfig.EnableStructure = boolValue
			updated = true
		}
	}

	if value, exists := settings["filter_external_links"]; exists {
		if boolValue, ok := value.(bool); ok && site.InjectConfig.FilterExternalLinks != boolValue {
			site.InjectConfig.FilterExternalLinks = boolValue
			updated = true
		}
	}

	if value, exists := settings["enable_unicode"]; exists {
		if boolValue, ok := value.(bool); ok && site.InjectConfig.EnableUnicode != boolValue {
			site.InjectConfig.EnableUnicode = boolValue
			updated = true
		}
	}

	// Sitemap和缓存配置
	if value, exists := settings["enable_sitemap"]; exists {
		if boolValue, ok := value.(bool); ok && site.EnableSitemap != boolValue {
			site.EnableSitemap = boolValue
			updated = true
		}
	}

	if value, exists := settings["enable_cache"]; exists {
		if boolValue, ok := value.(bool); ok && site.EnableCache != boolValue {
			site.EnableCache = boolValue
			updated = true
		}
	}

	if value, exists := settings["use_global_cache"]; exists {
		if boolValue, ok := value.(bool); ok && site.UseGlobalCache != boolValue {
			site.UseGlobalCache = boolValue
			updated = true
		}
	}

	if value, exists := settings["enable_redis_cache"]; exists {
		if boolValue, ok := value.(bool); ok && site.EnableRedisCache != boolValue {
			site.EnableRedisCache = boolValue
			updated = true
		}
	}

	// 站点状态
	if value, exists := settings["status"]; exists {
		if stringValue, ok := value.(string); ok && site.Status != stringValue {
			site.Status = stringValue
			updated = true
		}
	}

	// 统计设置
	if value, exists := settings["use_global_analytics"]; exists {
		var boolPtr *bool
		if value == "" {
			boolPtr = nil
		} else if stringValue, ok := value.(string); ok {
			if stringValue == "false" {
				falseValue := false
				boolPtr = &falseValue
			}
		}
		if (site.UseGlobalAnalytics == nil && boolPtr != nil) ||
			(site.UseGlobalAnalytics != nil && boolPtr == nil) ||
			(site.UseGlobalAnalytics != nil && boolPtr != nil && *site.UseGlobalAnalytics != *boolPtr) {
			site.UseGlobalAnalytics = boolPtr
			updated = true
		}
	}

	return updated
}

// loadSiteAliases 加载站点的子域名列表
func (s *SiteService) loadSiteAliases(site *model.Site) {
	if site == nil {
		return
	}
	
	aliases, err := s.repo.GetSiteAliases(site.ID)
	if err != nil {
		s.logger.Error("加载站点子域名失败", 
			zap.Uint("site_id", site.ID),
			zap.Error(err))
		return
	}
	
	site.Aliases = aliases
}

// checkAliasDomainAvailable 检查子域名是否可用
func (s *SiteService) checkAliasDomainAvailable(aliasDomain string) error {
	// 检查是否已被主域名使用
	existing, err := s.repo.GetByDomain(aliasDomain)
	if err != nil {
		return fmt.Errorf("检查域名失败: %v", err)
	}
	if existing != nil {
		return fmt.Errorf("域名 %s 已被使用", aliasDomain)
	}
	
	// 检查是否已被其他站点作为子域名使用
	existingAlias, err := s.repo.GetByAliasDomain(aliasDomain)
	if err != nil {
		return fmt.Errorf("检查子域名失败: %v", err)
	}
	if existingAlias != nil {
		return fmt.Errorf("子域名 %s 已被其他站点使用", aliasDomain)
	}
	
	return nil
}

// CreateSiteWithAliases 创建站点并设置子域名
func (s *SiteService) CreateSiteWithAliases(site *model.Site, aliasPrefixes []string) error {
	// 创建站点
	if err := s.CreateSite(site); err != nil {
		return err
	}
	
	// 创建子域名映射
	for _, prefix := range aliasPrefixes {
		var aliasDomain string
		if prefix == "@" {
			aliasDomain = site.Domain // 裸域名就是主域名本身
		} else {
			aliasDomain = prefix + "." + site.Domain
		}
		
		alias := &model.SiteAlias{
			SiteID:      int(site.ID),
			AliasDomain: aliasDomain,
			IsActive:    true,
		}
		
		if err := s.repo.CreateSiteAlias(alias); err != nil {
			s.logger.Error("创建子域名失败", 
				zap.String("alias", aliasDomain),
				zap.Error(err))
			// 继续处理其他子域名
		}
	}
	
	return nil
}

// UpdateSiteAliases 更新站点的子域名列表
func (s *SiteService) UpdateSiteAliases(siteID uint, aliasPrefixes []string) error {
	site, err := s.repo.GetByID(siteID)
	if err != nil {
		return err
	}
	if site == nil {
		return errors.New("站点不存在")
	}
	
	// 删除旧的子域名
	if err := s.repo.DeleteSiteAliases(siteID); err != nil {
		return fmt.Errorf("删除旧子域名失败: %v", err)
	}
	
	// 创建新的子域名
	for _, prefix := range aliasPrefixes {
		var aliasDomain string
		if prefix == "@" {
			aliasDomain = site.Domain
		} else {
			aliasDomain = prefix + "." + site.Domain
		}
		
		// 检查是否可用
		if err := s.checkAliasDomainAvailable(aliasDomain); err != nil {
			// s.logger.Warn("子域名不可用", 
			//	zap.String("alias", aliasDomain),
			//	zap.Error(err))
			continue
		}
		
		alias := &model.SiteAlias{
			SiteID:      int(siteID),
			AliasDomain: aliasDomain,
			IsActive:    true,
		}
		
		if err := s.repo.CreateSiteAlias(alias); err != nil {
			s.logger.Error("创建子域名失败", 
				zap.String("alias", aliasDomain),
				zap.Error(err))
		}
	}
	
	// 清除Redis缓存
	s.clearSiteRedisCache(site.ID, site.Domain)
	
	return nil
}