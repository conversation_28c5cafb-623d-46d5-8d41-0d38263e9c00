package captcha

import (
	"bytes"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"math/big"
	"strings"
	"sync"
	"time"

	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	"golang.org/x/image/math/fixed"
)

// CaptchaService 验证码服务
type CaptchaService struct {
	store         map[string]*captchaData
	mu            sync.RWMutex
	captchaLength int // 验证码长度
}

// captchaData 验证码数据
type captchaData struct {
	code      string
	expiresAt time.Time
}

// NewCaptchaService 创建验证码服务
func NewCaptchaService() *CaptchaService {
	service := &CaptchaService{
		store:         make(map[string]*captchaData),
		captchaLength: 4, // 默认长度
	}
	// 启动定时清理过期验证码
	go service.cleanExpired()
	return service
}

// SetCaptchaLength 设置验证码长度
func (s *CaptchaService) SetCaptchaLength(length int) {
	if length < 4 {
		length = 4
	}
	if length > 8 {
		length = 8
	}
	s.captchaLength = length
}

// Generate 生成验证码
func (s *CaptchaService) Generate() (id, code, imageBase64 string, err error) {
	// 生成验证码ID
	id = generateID()
	
	// 生成验证码文本
	code = generateCode(s.captchaLength)
	
	// 生成验证码图片
	img := s.createImage(code)
	
	// 转换为base64
	var buf bytes.Buffer
	if err = png.Encode(&buf, img); err != nil {
		return "", "", "", err
	}
	imageBase64 = "data:image/png;base64," + base64.StdEncoding.EncodeToString(buf.Bytes())
	
	// 存储验证码
	s.mu.Lock()
	s.store[id] = &captchaData{
		code:      code,
		expiresAt: time.Now().Add(5 * time.Minute), // 5分钟过期
	}
	s.mu.Unlock()
	
	return id, code, imageBase64, nil
}

// Verify 验证验证码
func (s *CaptchaService) Verify(id, code string) bool {
	s.mu.RLock()
	data, exists := s.store[id]
	s.mu.RUnlock()
	
	if !exists {
		return false
	}
	
	// 检查是否过期
	if time.Now().After(data.expiresAt) {
		s.mu.Lock()
		delete(s.store, id)
		s.mu.Unlock()
		return false
	}
	
	// 验证码不区分大小写
	valid := strings.EqualFold(data.code, code)
	
	// 验证后删除（一次性使用）
	if valid {
		s.mu.Lock()
		delete(s.store, id)
		s.mu.Unlock()
	}
	
	return valid
}

// createImage 创建验证码图片
func (s *CaptchaService) createImage(code string) image.Image {
	// 根据验证码长度动态计算宽度
	// 每个字符需要约20像素，加上左右边距
	width := 20 + len(code)*20 + 10  // 左边距20 + 字符宽度 + 右边距10
	height := 40
	
	// 创建图片
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	
	// 设置背景色
	bgColor := color.RGBA{240, 240, 240, 255}
	draw.Draw(img, img.Bounds(), &image.Uniform{bgColor}, image.Point{}, draw.Src)
	
	// 添加干扰线
	for i := 0; i < 5; i++ {
		drawLine(img, randomColor())
	}
	
	// 绘制文字
	drawString(img, code)
	
	// 添加噪点
	for i := 0; i < 100; i++ {
		x := randomInt(width)
		y := randomInt(height)
		img.Set(x, y, randomColor())
	}
	
	return img
}

// drawString 绘制文字
func drawString(img *image.RGBA, s string) {
	col := color.RGBA{50, 50, 50, 255}
	point := fixed.Point26_6{
		X: fixed.Int26_6(20 * 64),
		Y: fixed.Int26_6(28 * 64),
	}
	
	d := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(col),
		Face: basicfont.Face7x13,
		Dot:  point,
	}
	
	// 绘制每个字符，添加随机偏移
	charSpacing := 20 // 字符间距
	for i, ch := range s {
		// 添加随机偏移
		offset := randomInt(5) - 2
		d.Dot.X = fixed.Int26_6((20 + i*charSpacing) * 64)
		d.Dot.Y = fixed.Int26_6((28 + offset) * 64)
		d.DrawString(string(ch))
	}
}

// drawLine 绘制干扰线
func drawLine(img *image.RGBA, col color.Color) {
	width := img.Bounds().Max.X
	height := img.Bounds().Max.Y
	
	x1 := randomInt(width)
	y1 := randomInt(height)
	x2 := randomInt(width)
	y2 := randomInt(height)
	
	// 简单的线条绘制
	dx := abs(x2 - x1)
	dy := abs(y2 - y1)
	sx := 1
	sy := 1
	if x1 > x2 {
		sx = -1
	}
	if y1 > y2 {
		sy = -1
	}
	err := dx - dy
	
	for {
		img.Set(x1, y1, col)
		if x1 == x2 && y1 == y2 {
			break
		}
		e2 := 2 * err
		if e2 > -dy {
			err -= dy
			x1 += sx
		}
		if e2 < dx {
			err += dx
			y1 += sy
		}
	}
}

// cleanExpired 清理过期验证码
func (s *CaptchaService) cleanExpired() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		s.mu.Lock()
		now := time.Now()
		for id, data := range s.store {
			if now.After(data.expiresAt) {
				delete(s.store, id)
			}
		}
		s.mu.Unlock()
	}
}

// 辅助函数

func generateID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x", b)
}

func generateCode(length int) string {
	chars := "0123456789"
	code := make([]byte, length)
	for i := range code {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(chars))))
		code[i] = chars[n.Int64()]
	}
	return string(code)
}

func randomInt(max int) int {
	n, _ := rand.Int(rand.Reader, big.NewInt(int64(max)))
	return int(n.Int64())
}

func randomColor() color.Color {
	return color.RGBA{
		uint8(randomInt(256)),
		uint8(randomInt(256)),
		uint8(randomInt(256)),
		255,
	}
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}