package service

import (
	"fmt"
	"site-cluster/internal/model"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type SpiderBlockStatsService struct {
	db           *gorm.DB
	logger       *zap.Logger
	mu           sync.Mutex
	pendingStats map[string]*model.SpiderBlockHourlyStats // 缓存待写入的统计
	stopChan     chan struct{}                            // 停止通道
	wg           sync.WaitGroup                           // 等待组
}

func NewSpiderBlockStatsService(db *gorm.DB, logger *zap.Logger) *SpiderBlockStatsService {
	service := &SpiderBlockStatsService{
		db:           db,
		logger:       logger,
		pendingStats: make(map[string]*model.SpiderBlockHourlyStats),
		stopChan:     make(chan struct{}),
	}
	
	// 启动定时刷新任务
	service.wg.Add(1)
	go service.startFlushWorker()
	
	return service
}

// RecordBlock 记录屏蔽事件
func (s *SpiderBlockStatsService) RecordBlock(spiderName, domain string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// 生成当前小时的key
	now := time.Now()
	hour := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
	key := fmt.Sprintf("%s_%s_%s", hour.Format("2006010215"), spiderName, domain)
	
	// 更新缓存中的统计
	if stat, exists := s.pendingStats[key]; exists {
		stat.BlockCount++
	} else {
		s.pendingStats[key] = &model.SpiderBlockHourlyStats{
			Hour:       hour,
			SpiderName: spiderName,
			Domain:     domain,
			BlockCount: 1,
		}
	}
}

// startFlushWorker 启动定时刷新工作器
func (s *SpiderBlockStatsService) startFlushWorker() {
	defer s.wg.Done()
	ticker := time.NewTicker(30 * time.Second) // 每30秒刷新一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			s.flushStats()
		case <-s.stopChan:
			// 最后刷新一次未保存的数据
			s.flushStats()
			s.logger.Info("蜘蛛屏蔽统计服务工作器已停止")
			return
		}
	}
}

// Stop 停止服务
func (s *SpiderBlockStatsService) Stop() {
	close(s.stopChan)
	s.wg.Wait()
}

// ClearCache 清空缓存的统计数据
func (s *SpiderBlockStatsService) ClearCache() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.pendingStats = make(map[string]*model.SpiderBlockHourlyStats)
}

// flushStats 将缓存的统计数据写入数据库
func (s *SpiderBlockStatsService) flushStats() {
	s.mu.Lock()
	if len(s.pendingStats) == 0 {
		s.mu.Unlock()
		return
	}
	
	// 复制并清空缓存
	toFlush := s.pendingStats
	s.pendingStats = make(map[string]*model.SpiderBlockHourlyStats)
	s.mu.Unlock()
	
	// 批量更新或插入
	for _, stat := range toFlush {
		if err := s.db.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "hour"}, {Name: "spider_name"}},
			DoUpdates: clause.Assignments(map[string]interface{}{
				"block_count": gorm.Expr("spider_block_hourly_stats.block_count + ?", stat.BlockCount),
				"updated_at":  time.Now(),
			}),
		}).Create(stat).Error; err != nil {
			s.logger.Error("写入蜘蛛屏蔽统计失败", 
				zap.Error(err),
				zap.String("spider", stat.SpiderName),
				zap.Time("hour", stat.Hour))
		}
	}
}

// GetHourlyStats 获取小时统计数据
func (s *SpiderBlockStatsService) GetHourlyStats(startTime, endTime time.Time, spiderName, domain string) ([]model.SpiderBlockHourlyStats, error) {
	// 先刷新缓存
	s.flushStats()
	
	query := s.db.Model(&model.SpiderBlockHourlyStats{}).
		Where("hour >= ? AND hour <= ?", startTime, endTime)
	
	if spiderName != "" {
		query = query.Where("spider_name = ?", spiderName)
	}
	
	if domain != "" {
		query = query.Where("domain = ?", domain)
	}
	
	var stats []model.SpiderBlockHourlyStats
	err := query.Order("hour ASC").Find(&stats).Error
	return stats, err
}

// GetTopBlockedSpiders 获取被屏蔽最多的蜘蛛
func (s *SpiderBlockStatsService) GetTopBlockedSpiders(startTime, endTime time.Time, limit int) ([]map[string]interface{}, error) {
	// 先刷新缓存
	s.flushStats()
	
	var results []map[string]interface{}
	
	err := s.db.Model(&model.SpiderBlockHourlyStats{}).
		Select("spider_name, SUM(block_count) as total_count").
		Where("hour >= ? AND hour <= ?", startTime, endTime).
		Group("spider_name").
		Order("total_count DESC").
		Limit(limit).
		Scan(&results).Error
	
	return results, err
}

// GetChartData 获取图表数据
func (s *SpiderBlockStatsService) GetChartData(period string, domain string) (map[string]interface{}, error) {
	// 先刷新缓存
	s.flushStats()
	
	now := time.Now()
	var startTime time.Time
	var interval string
	
	switch period {
	case "today":
		startTime = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		interval = "hour"
	case "yesterday":
		startTime = time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location())
		now = startTime.Add(24 * time.Hour)
		interval = "hour"
	case "7days":
		startTime = now.AddDate(0, 0, -7)
		interval = "day"
	case "15days":
		startTime = now.AddDate(0, 0, -15)
		interval = "day"
	default: // 默认7天
		startTime = now.AddDate(0, 0, -7)
		interval = "day"
	}
	
	// 获取时间范围内的统计
	query := s.db.Model(&model.SpiderBlockHourlyStats{}).
		Where("hour >= ? AND hour < ?", startTime, now)
	
	if domain != "" {
		query = query.Where("domain = ?", domain)
	}
	
	// 根据时间间隔分组
	var results []struct {
		TimeLabel  string `json:"time_label"`
		SpiderName string `json:"spider_name"`
		Count      int64  `json:"count"`
	}
	
	if interval == "hour" {
		// 按小时分组 (PostgreSQL语法)
		err := query.
			Select("TO_CHAR(hour, 'YYYY-MM-DD HH24:00') as time_label, spider_name, SUM(block_count) as count").
			Group("time_label, spider_name").
			Order("time_label ASC").
			Scan(&results).Error
		if err != nil {
			return nil, err
		}
	} else {
		// 按天分组 (PostgreSQL语法)
		err := query.
			Select("TO_CHAR(hour, 'YYYY-MM-DD') as time_label, spider_name, SUM(block_count) as count").
			Group("time_label, spider_name").
			Order("time_label ASC").
			Scan(&results).Error
		if err != nil {
			return nil, err
		}
	}
	
	// 整理数据为图表格式
	chartData := s.formatChartData(results, startTime, now, interval)
	
	// 获取统计摘要
	summary := s.getSummary(startTime, now, domain)
	
	return map[string]interface{}{
		"chart":   chartData,
		"summary": summary,
		"period":  period,
	}, nil
}

// formatChartData 格式化图表数据
func (s *SpiderBlockStatsService) formatChartData(results []struct {
	TimeLabel  string `json:"time_label"`
	SpiderName string `json:"spider_name"`
	Count      int64  `json:"count"`
}, startTime, endTime time.Time, interval string) map[string]interface{} {
	// 生成时间标签
	var timeLabels []string
	spiderData := make(map[string][]int64)
	
	if interval == "hour" {
		// 生成小时标签
		for t := startTime; t.Before(endTime); t = t.Add(time.Hour) {
			label := t.Format("15:00")
			timeLabels = append(timeLabels, label)
		}
	} else {
		// 生成日期标签
		for t := startTime; t.Before(endTime); t = t.AddDate(0, 0, 1) {
			label := t.Format("01-02")
			timeLabels = append(timeLabels, label)
		}
	}
	
	// 初始化蜘蛛数据
	spiderNames := make(map[string]bool)
	for _, r := range results {
		spiderNames[r.SpiderName] = true
	}
	
	for spider := range spiderNames {
		spiderData[spider] = make([]int64, len(timeLabels))
	}
	
	// 填充数据
	for _, r := range results {
		// 找到时间索引
		var timeIndex int
		if interval == "hour" {
			t, _ := time.Parse("2006-01-02 15:00", r.TimeLabel)
			timeIndex = int(t.Sub(startTime).Hours())
		} else {
			t, _ := time.Parse("2006-01-02", r.TimeLabel)
			timeIndex = int(t.Sub(startTime).Hours() / 24)
		}
		
		if timeIndex >= 0 && timeIndex < len(timeLabels) {
			spiderData[r.SpiderName][timeIndex] = r.Count
		}
	}
	
	// 构建系列数据
	var series []map[string]interface{}
	for spider, data := range spiderData {
		series = append(series, map[string]interface{}{
			"name": spider,
			"data": data,
			"type": "line",
			"smooth": true,
		})
	}
	
	return map[string]interface{}{
		"labels": timeLabels,
		"series": series,
	}
}

// getSummary 获取统计摘要
func (s *SpiderBlockStatsService) getSummary(startTime, endTime time.Time, domain string) map[string]interface{} {
	query := s.db.Model(&model.SpiderBlockHourlyStats{}).
		Where("hour >= ? AND hour < ?", startTime, endTime)
	
	if domain != "" {
		query = query.Where("domain = ?", domain)
	}
	
	// 总屏蔽次数
	var totalBlocks int64
	query.Select("SUM(block_count)").Scan(&totalBlocks)
	
	// 独立蜘蛛数量
	var spiderCount int64
	query.Select("COUNT(DISTINCT spider_name)").Scan(&spiderCount)
	
	// Top 5 蜘蛛
	var topSpiders []struct {
		SpiderName string `json:"spider_name"`
		Count      int64  `json:"count"`
	}
	
	s.db.Model(&model.SpiderBlockHourlyStats{}).
		Select("spider_name, SUM(block_count) as count").
		Where("hour >= ? AND hour < ?", startTime, endTime).
		Group("spider_name").
		Order("count DESC").
		Limit(5).
		Scan(&topSpiders)
	
	return map[string]interface{}{
		"total_blocks":  totalBlocks,
		"spider_count":  spiderCount,
		"top_spiders":   topSpiders,
		"period_start":  startTime.Format("2006-01-02 15:04"),
		"period_end":    endTime.Format("2006-01-02 15:04"),
	}
}

// ClearStats 清空所有统计数据
func (s *SpiderBlockStatsService) ClearStats() error {
	// 先清空缓存
	s.ClearCache()
	
	// 使用GORM的安全方法清空数据
	// 清空小时统计表
	if err := s.db.Session(&gorm.Session{AllowGlobalUpdate: true}).Delete(&model.SpiderBlockHourlyStats{}).Error; err != nil {
		s.logger.Error("清空小时统计表失败", zap.Error(err))
		return err
	}
	
	// 清空日统计表
	if err := s.db.Session(&gorm.Session{AllowGlobalUpdate: true}).Delete(&model.SpiderBlockDailyStats{}).Error; err != nil {
		s.logger.Error("清空日统计表失败", zap.Error(err))
		return err
	}
	
	// 重置自增序列（使用参数化查询）
	if err := s.db.Exec("ALTER SEQUENCE spider_block_hourly_stats_id_seq RESTART WITH 1").Error; err != nil {
		// 不是所有数据库都支持，忽略错误
		s.logger.Warn("重置序列失败，可能不支持", zap.Error(err))
	}
	
	s.logger.Info("已清空所有蜘蛛屏蔽统计数据")
	return nil
}

// AggregateToDaily 聚合小时数据到天（定时任务调用）
func (s *SpiderBlockStatsService) AggregateToDaily() error {
	// 聚合昨天的数据
	yesterday := time.Now().AddDate(0, 0, -1)
	startOfDay := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)
	
	// 查询昨天的小时数据
	var hourlyStats []model.SpiderBlockHourlyStats
	err := s.db.Where("hour >= ? AND hour < ?", startOfDay, endOfDay).Find(&hourlyStats).Error
	if err != nil {
		return err
	}
	
	// 按蜘蛛名称和域名分组聚合
	dailyMap := make(map[string]*model.SpiderBlockDailyStats)
	for _, stat := range hourlyStats {
		key := fmt.Sprintf("%s_%s", stat.SpiderName, stat.Domain)
		if daily, exists := dailyMap[key]; exists {
			daily.BlockCount += stat.BlockCount
		} else {
			dailyMap[key] = &model.SpiderBlockDailyStats{
				Date:       startOfDay,
				SpiderName: stat.SpiderName,
				Domain:     stat.Domain,
				BlockCount: stat.BlockCount,
			}
		}
	}
	
	// 批量插入日统计
	for _, daily := range dailyMap {
		if err := s.db.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "date"}, {Name: "spider_name"}},
			DoUpdates: clause.Assignments(map[string]interface{}{
				"block_count": daily.BlockCount,
				"updated_at":  time.Now(),
			}),
		}).Create(daily).Error; err != nil {
			s.logger.Error("写入日统计失败", zap.Error(err))
		}
	}
	
	return nil
}

// CleanOldStats 清理旧的统计数据
func (s *SpiderBlockStatsService) CleanOldStats(keepDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -keepDays)
	
	// 删除旧的小时统计
	if err := s.db.Where("hour < ?", cutoffTime).Delete(&model.SpiderBlockHourlyStats{}).Error; err != nil {
		return err
	}
	
	// 保留更长时间的日统计（比如90天）
	dailyCutoff := time.Now().AddDate(0, 0, -90)
	if err := s.db.Where("date < ?", dailyCutoff).Delete(&model.SpiderBlockDailyStats{}).Error; err != nil {
		return err
	}
	
	s.logger.Info("清理旧统计数据完成", 
		zap.Time("hourly_cutoff", cutoffTime),
		zap.Time("daily_cutoff", dailyCutoff))
	
	return nil
}