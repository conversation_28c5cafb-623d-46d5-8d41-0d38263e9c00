package service

import (
	"crypto/md5"
	"fmt"
	"runtime"
	"strings"
	"math/rand"
	"sync"
	"time"
	"site-cluster/internal/model"
	"site-cluster/internal/utils"
	"gorm.io/gorm"
	"go.uber.org/zap"
)

type SystemSettingsService struct {
	db              *gorm.DB
	logger          *zap.Logger
	resourceLimiter *ResourceLimiter // 资源限流器引用
	
	// 内存缓存
	cacheMu       sync.RWMutex
	cachedSettings *model.SystemSettings
	cacheTime     time.Time
	cacheTTL      time.Duration
}

// UpdateSystemSettingsParams 更新系统设置参数
type UpdateSystemSettingsParams struct {
	EnableGlobalUACheck            bool   `json:"enable_global_ua_check"`
	DefaultNonSpiderHTML           string `json:"default_non_spider_html"`
	DefaultAllowedUA               string `json:"default_allowed_ua"`
	DefaultSpiderBlockUA           string `json:"default_spider_block_ua"`
	UserAgentList                  string `json:"user_agent_list"`
	// CrawlerConcurrency 已废弃，使用 MaxHTTPRequests 代替
	CrawlerTimeout                 int    `json:"crawler_timeout"`
	DefaultCacheHomeTTL            int    `json:"default_cache_home_ttl"`
	DefaultCacheOtherTTL           int    `json:"default_cache_other_ttl"`
	DefaultEnableRedisCache        bool   `json:"default_enable_redis_cache"`
	EnableGlobalSpiderBlock        bool   `json:"enable_global_spider_block"`
	SpiderBlock403Template         string `json:"spider_block_403_template"`
	SiteNotFoundHTML               string `json:"site_not_found_html"`
	Default404HTML                 string `json:"default_404_html"`
	Enable404Cache                 bool   `json:"enable_404_cache"`
	Cache404TTL                    int    `json:"cache_404_ttl"`
	GlobalRedirectWWW              bool   `json:"global_redirect_www"`  // 全局 @ 跳转到 www
	EnableUAStats                  bool   `json:"enable_ua_stats"`       // UA统计开关
	
	// 拼音特殊字符配置
	EnableGlobalPinyin      bool    `json:"enable_global_pinyin"`       // 全局启用拼音
	PinyinSpecialChars      string  `json:"pinyin_special_chars"`       // 特殊字符列表
	PinyinSpecialCharsRatio float32 `json:"pinyin_special_chars_ratio"` // 插入比例
	
	// 缓存超时配置
	RedisConnectTimeout     int `json:"redis_connect_timeout"`
	RedisReadTimeout        int `json:"redis_read_timeout"`
	RedisWriteTimeout       int `json:"redis_write_timeout"`
	RedisPoolTimeout        int `json:"redis_pool_timeout"`
	RedisMaxPoolSize        int `json:"redis_max_pool_size"`
	
	FileReadTimeout         int `json:"file_read_timeout"`
	FileWriteTimeout        int `json:"file_write_timeout"`
	AsyncQueueSize          int `json:"async_queue_size"`
	AsyncWorkerCount        int `json:"async_worker_count"`
	
	ProxyRequestTimeout     int `json:"proxy_request_timeout"`
	
	// 性能配置字段
	DBMaxOpenConns          int `json:"db_max_open_conns"`
	DBMaxIdleConns          int `json:"db_max_idle_conns"`
	DBConnMaxLifetime       int `json:"db_conn_max_lifetime"`
	DBSlaveMaxOpenConns     int `json:"db_slave_max_open_conns"`
	DBSlaveMaxIdleConns     int `json:"db_slave_max_idle_conns"`
	
	HTTPMaxIdleConns        int `json:"http_max_idle_conns"`
	HTTPMaxIdleConnsPerHost int `json:"http_max_idle_conns_per_host"`
	HTTPMaxConnsPerHost     int `json:"http_max_conns_per_host"`
	HTTPIdleConnTimeout     int `json:"http_idle_conn_timeout"`
	
	SchedulerMaxWorkers     int     `json:"scheduler_max_workers"`
	// SchedulerQueueSize 已废弃，使用信号量机制代替
	CrawlerRateLimit        float64 `json:"crawler_rate_limit"`
	
	// 工作池配置
	WorkerPoolMode          string  `json:"worker_pool_mode"`       // 工作池模式 (auto, fixed, dynamic)
	WorkerPoolSize          int     `json:"worker_pool_size"`       // 固定工作池大小
	WorkerPoolMinSize       int     `json:"worker_pool_min_size"`   // 动态工作池最小大小
	WorkerPoolMaxSize       int     `json:"worker_pool_max_size"`   // 动态工作池最大大小
	WorkerPoolScaleRatio    float64 `json:"worker_pool_scale_ratio"` // 工作池缩放比例
	
	CacheLockTimeout        int `json:"cache_lock_timeout"`
	CacheLockRetryInterval  int `json:"cache_lock_retry_interval"`
	
	// 验证码配置
	EnableCaptcha           bool   `json:"enable_captcha"`
	CaptchaLength           int    `json:"captcha_length"`
	CaptchaExpiry           int    `json:"captcha_expiry"`
	
	// CSRF配置
	EnableCSRF              bool   `json:"enable_csrf"`
	
	// 日志配置
	LogEnabled              bool   `json:"log_enabled"`
	LogLevel                string `json:"log_level"`
	LogStorage              string `json:"log_storage"`
	LogRetentionDays        int    `json:"log_retention_days"`
	LogMaxSize              int    `json:"log_max_size"`
	LogMaxBackups           int    `json:"log_max_backups"`
	LogAccessEnabled        bool   `json:"log_access_enabled"`
	LogErrorEnabled         bool   `json:"log_error_enabled"`
	
	// 站点地图配置
	SitemapRefreshInterval   int  `json:"sitemap_refresh_interval"`
	
	// 资源限流配置
	MaxDatabaseConn         int `json:"max_database_conn"`
	MaxRedisConn            int `json:"max_redis_conn"`
	MaxHTTPRequests         int `json:"max_http_requests"`
	MaxFileOps              int `json:"max_file_ops"`
	MaxCrawlerTasks         int `json:"max_crawler_tasks"`
	
	// 统一超时配置
	RouteTimeout            int `json:"route_timeout"`
	DatabaseQueryTimeout    int `json:"database_query_timeout"`
	RedisOpTimeout          int `json:"redis_op_timeout"`
	HTTPRequestTimeout      int `json:"http_request_timeout"`
	FileOpTimeout           int `json:"file_op_timeout"`
	CrawlerTaskTimeout      int `json:"crawler_task_timeout"`
	
	// 来源判断配置
	EnableGlobalRefererCheck bool   `json:"enable_global_referer_check"`
	GlobalAllowedReferers    string `json:"global_allowed_referers"`
	GlobalRefererBlockCode   int    `json:"global_referer_block_code"`
	GlobalRefererBlockHTML   string `json:"global_referer_block_html"`
}

func NewSystemSettingsService(db *gorm.DB, logger *zap.Logger) *SystemSettingsService {
	// 初始化随机数种子
	rand.Seed(time.Now().UnixNano())
	
	return &SystemSettingsService{
		db:       db,
		logger:   logger,
		cacheTTL: 30 * time.Minute, // 缓存30分钟
	}
}

// SetResourceLimiter 设置资源限流器
func (s *SystemSettingsService) SetResourceLimiter(resourceLimiter *ResourceLimiter) {
	s.resourceLimiter = resourceLimiter
}

// GetSystemSettings 获取系统设置（带缓存）
func (s *SystemSettingsService) GetSystemSettings() (*model.SystemSettings, error) {
	// 先检查缓存
	s.cacheMu.RLock()
	if s.cachedSettings != nil && time.Since(s.cacheTime) < s.cacheTTL {
		settings := s.cachedSettings
		s.cacheMu.RUnlock()
		return settings, nil
	}
	s.cacheMu.RUnlock()
	
	// 缓存过期或不存在，需要从数据库加载
	s.cacheMu.Lock()
	defer s.cacheMu.Unlock()
	
	// 双重检查，避免并发时重复查询
	if s.cachedSettings != nil && time.Since(s.cacheTime) < s.cacheTTL {
		return s.cachedSettings, nil
	}
	
	// 从数据库查询
	var settings model.SystemSettings
	err := s.db.First(&settings).Error
	if err == gorm.ErrRecordNotFound {
		// 如果不存在，创建默认设置
		settings = model.SystemSettings{
			DefaultNonSpiderHTML:           s.getDefaultNonSpiderHTML(),
			DefaultAllowedUA:               "", // 空则不进行UA判断
			DefaultSpiderBlockUA:           s.getDefaultSpiderBlockUA(),
			UserAgentList:                  s.getDefaultUserAgentList(),
			// CrawlerConcurrency 和 CrawlerTimeout 已废弃
			DefaultCacheHomeTTL:            30,
			DefaultCacheOtherTTL:           1440,
			DefaultEnableRedisCache:        true,
			Default404HTML:                 s.getDefault404HTML(),
			Enable404Cache:                 true,
			Cache404TTL:                    86400, // 24小时
			
			// Redis连接池配置
			RedisMaxPoolSize:        30,
			
			FileReadTimeout:         5000,  // 5秒
			FileWriteTimeout:        10000, // 10秒
			AsyncQueueSize:          10000,
			AsyncWorkerCount:        10,
			
			ProxyRequestTimeout:     60000, // 60秒
			
			// 日志配置默认值
			LogEnabled:              true,
			LogLevel:                "info",
			LogStorage:              "file",
			LogRetentionDays:        7,
			LogMaxSize:              100,
			LogMaxBackups:           10,
			LogAccessEnabled:        true,
			LogErrorEnabled:         true,
		}
		if err := s.db.Create(&settings).Error; err != nil {
			return nil, err
		}
	} else if err != nil {
		return nil, err
	}
	
	// 更新缓存
	s.cachedSettings = &settings
	s.cacheTime = time.Now()
	s.logger.Debug("系统设置已缓存", zap.Time("cache_time", s.cacheTime))
	
	return &settings, nil
}

// clearCache 清除缓存
func (s *SystemSettingsService) clearCache() {
	s.cacheMu.Lock()
	s.cachedSettings = nil
	s.cacheTime = time.Time{}
	s.cacheMu.Unlock()
	s.logger.Info("系统设置缓存已清除")
}

// UpdateSystemSettings 更新系统设置
func (s *SystemSettingsService) UpdateSystemSettings(params UpdateSystemSettingsParams) error {
	// 先清除缓存，确保获取最新数据
	s.clearCache()
	
	settings, err := s.GetSystemSettings()
	if err != nil {
		return err
	}

	// 使用Select确保布尔字段也被更新
	settings.EnableGlobalUACheck = params.EnableGlobalUACheck
	settings.DefaultNonSpiderHTML = params.DefaultNonSpiderHTML
	settings.DefaultAllowedUA = params.DefaultAllowedUA
	settings.DefaultSpiderBlockUA = params.DefaultSpiderBlockUA
	settings.UserAgentList = params.UserAgentList
	// CrawlerConcurrency 已废弃，忽略该参数
	// CrawlerTimeout 已废弃，映射到 CrawlerTaskTimeout
	if params.CrawlerTimeout > 0 {
		settings.CrawlerTaskTimeout = params.CrawlerTimeout * 1000
	}
	settings.DefaultCacheHomeTTL = params.DefaultCacheHomeTTL
	settings.DefaultCacheOtherTTL = params.DefaultCacheOtherTTL
	settings.DefaultEnableRedisCache = params.DefaultEnableRedisCache
	settings.EnableGlobalSpiderBlock = params.EnableGlobalSpiderBlock
	settings.SpiderBlock403Template = params.SpiderBlock403Template
	settings.SiteNotFoundHTML = params.SiteNotFoundHTML
	settings.Default404HTML = params.Default404HTML
	settings.Enable404Cache = params.Enable404Cache
	if params.Cache404TTL > 0 {
		settings.Cache404TTL = params.Cache404TTL
	}
	settings.GlobalRedirectWWW = params.GlobalRedirectWWW
	settings.EnableUAStats = params.EnableUAStats
	
	// 拼音特殊字符配置
	settings.EnableGlobalPinyin = params.EnableGlobalPinyin
	// 总是更新这个字段，即使是空字符串（允许清空）
	settings.PinyinSpecialChars = params.PinyinSpecialChars
	if params.PinyinSpecialCharsRatio > 0 {
		settings.PinyinSpecialCharsRatio = params.PinyinSpecialCharsRatio
	}
	
	// Redis超时相关字段已废弃，由 RedisOpTimeout 统一管理
	if params.RedisMaxPoolSize > 0 {
		settings.RedisMaxPoolSize = params.RedisMaxPoolSize
	}
	
	if params.FileReadTimeout > 0 {
		settings.FileReadTimeout = params.FileReadTimeout
	}
	if params.FileWriteTimeout > 0 {
		settings.FileWriteTimeout = params.FileWriteTimeout
	}
	if params.AsyncQueueSize > 0 {
		settings.AsyncQueueSize = params.AsyncQueueSize
	}
	if params.AsyncWorkerCount > 0 {
		settings.AsyncWorkerCount = params.AsyncWorkerCount
	}
	
	if params.ProxyRequestTimeout > 0 {
		settings.ProxyRequestTimeout = params.ProxyRequestTimeout
	}
	
	// 性能配置
	if params.DBMaxOpenConns > 0 {
		settings.DBMaxOpenConns = params.DBMaxOpenConns
	}
	if params.DBMaxIdleConns > 0 {
		settings.DBMaxIdleConns = params.DBMaxIdleConns
	}
	if params.DBConnMaxLifetime > 0 {
		settings.DBConnMaxLifetime = params.DBConnMaxLifetime
	}
	if params.DBSlaveMaxOpenConns > 0 {
		settings.DBSlaveMaxOpenConns = params.DBSlaveMaxOpenConns
	}
	if params.DBSlaveMaxIdleConns > 0 {
		settings.DBSlaveMaxIdleConns = params.DBSlaveMaxIdleConns
	}
	
	if params.HTTPMaxIdleConns > 0 {
		settings.HTTPMaxIdleConns = params.HTTPMaxIdleConns
	}
	if params.HTTPMaxIdleConnsPerHost > 0 {
		settings.HTTPMaxIdleConnsPerHost = params.HTTPMaxIdleConnsPerHost
	}
	if params.HTTPMaxConnsPerHost > 0 {
		settings.HTTPMaxConnsPerHost = params.HTTPMaxConnsPerHost
	}
	if params.HTTPIdleConnTimeout > 0 {
		settings.HTTPIdleConnTimeout = params.HTTPIdleConnTimeout
	}
	
	// SchedulerMaxWorkers 和 SchedulerQueueSize 已废弃，由信号量机制管理
	// 如果传入这些参数，忽略它们
	
	if params.CrawlerRateLimit > 0 {
		settings.CrawlerRateLimit = params.CrawlerRateLimit
	}
	
	// 工作池配置
	if params.WorkerPoolMode != "" {
		settings.WorkerPoolMode = params.WorkerPoolMode
	}
	if params.WorkerPoolSize >= 0 { // 允许设置为0表示自动
		settings.WorkerPoolSize = params.WorkerPoolSize
	}
	if params.WorkerPoolMinSize > 0 {
		settings.WorkerPoolMinSize = params.WorkerPoolMinSize
	}
	if params.WorkerPoolMaxSize > 0 {
		settings.WorkerPoolMaxSize = params.WorkerPoolMaxSize
	}
	if params.WorkerPoolScaleRatio > 0 {
		settings.WorkerPoolScaleRatio = params.WorkerPoolScaleRatio
	}
	
	if params.CacheLockTimeout > 0 {
		settings.CacheLockTimeout = params.CacheLockTimeout
	}
	if params.CacheLockRetryInterval > 0 {
		settings.CacheLockRetryInterval = params.CacheLockRetryInterval
	}
	
	// 验证码配置
	settings.EnableCaptcha = params.EnableCaptcha
	if params.CaptchaLength > 0 {
		settings.CaptchaLength = params.CaptchaLength
	}
	if params.CaptchaExpiry > 0 {
		settings.CaptchaExpiry = params.CaptchaExpiry
	}
	
	// CSRF配置
	settings.EnableCSRF = params.EnableCSRF
	
	// 日志配置
	settings.LogEnabled = params.LogEnabled
	if params.LogLevel != "" {
		settings.LogLevel = params.LogLevel
	}
	if params.LogStorage != "" {
		settings.LogStorage = params.LogStorage
	}
	if params.LogRetentionDays > 0 {
		settings.LogRetentionDays = params.LogRetentionDays
	}
	if params.LogMaxSize > 0 {
		settings.LogMaxSize = params.LogMaxSize
	}
	if params.LogMaxBackups > 0 {
		settings.LogMaxBackups = params.LogMaxBackups
	}
	settings.LogAccessEnabled = params.LogAccessEnabled
	settings.LogErrorEnabled = params.LogErrorEnabled
	
	// 站点地图配置
	if params.SitemapRefreshInterval > 0 {
		settings.SitemapRefreshInterval = params.SitemapRefreshInterval
	}
	
	// 资源限流配置
	if params.MaxDatabaseConn > 0 {
		settings.MaxDatabaseConn = params.MaxDatabaseConn
	}
	if params.MaxRedisConn > 0 {
		settings.MaxRedisConn = params.MaxRedisConn
	}
	if params.MaxHTTPRequests > 0 {
		settings.MaxHTTPRequests = params.MaxHTTPRequests
	}
	if params.MaxFileOps > 0 {
		settings.MaxFileOps = params.MaxFileOps
	}
	if params.MaxCrawlerTasks > 0 {
		settings.MaxCrawlerTasks = params.MaxCrawlerTasks
	}
	
	// 统一超时配置
	if params.RouteTimeout > 0 {
		settings.RouteTimeout = params.RouteTimeout
	}
	if params.DatabaseQueryTimeout > 0 {
		settings.DatabaseQueryTimeout = params.DatabaseQueryTimeout
	}
	if params.RedisOpTimeout > 0 {
		settings.RedisOpTimeout = params.RedisOpTimeout
	}
	if params.HTTPRequestTimeout > 0 {
		settings.HTTPRequestTimeout = params.HTTPRequestTimeout
	}
	if params.FileOpTimeout > 0 {
		settings.FileOpTimeout = params.FileOpTimeout
	}
	if params.CrawlerTaskTimeout > 0 {
		settings.CrawlerTaskTimeout = params.CrawlerTaskTimeout
	}
	
	// 来源判断配置
	settings.EnableGlobalRefererCheck = params.EnableGlobalRefererCheck
	// 总是更新这些字段，即使是空字符串（允许清空）
	settings.GlobalAllowedReferers = params.GlobalAllowedReferers
	if params.GlobalRefererBlockCode > 0 {
		settings.GlobalRefererBlockCode = params.GlobalRefererBlockCode
	}
	// 总是更新这个字段，即使是空字符串（允许清空）
	settings.GlobalRefererBlockHTML = params.GlobalRefererBlockHTML

	// 使用Select确保所有字段都被更新（包括布尔值false）
	err = s.db.Select("*").Save(settings).Error
	if err != nil {
		return err
	}
	
	// 更新成功后清除缓存，确保下次获取最新数据
	s.clearCache()
	
	// 应用日志配置
	s.ApplyLogSettings(settings)
	
	// 更新资源限流器配置
	if s.resourceLimiter != nil {
		newConfig := &ResourceLimitConfig{
			MaxDatabaseConn:  int64(settings.MaxDatabaseConn),
			MaxRedisConn:     int64(settings.MaxRedisConn),
			MaxHTTPRequests:  int64(settings.MaxHTTPRequests),
			MaxFileOps:       int64(settings.MaxFileOps),
			MaxCrawlerTasks:  int64(settings.MaxCrawlerTasks),
			RouteTimeout:     settings.RouteTimeout,
			DatabaseTimeout:  settings.DatabaseQueryTimeout,
			RedisTimeout:     settings.RedisOpTimeout,
			HTTPTimeout:      settings.HTTPRequestTimeout,
			FileTimeout:      settings.FileOpTimeout,
			CrawlerTimeout:   settings.CrawlerTaskTimeout,
		}
		s.resourceLimiter.UpdateConfig(newConfig)
		s.logger.Info("资源限流器配置已更新")
	}
	
	return nil
}

// IsUACheckEnabled 检查是否启用了UA检查
func (s *SystemSettingsService) IsUACheckEnabled() bool {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return false
	}
	// 检查全局开关是否启用，且UA列表不为空
	return settings.EnableGlobalUACheck && settings.DefaultAllowedUA != ""
}

// IsGlobalSpiderBlockEnabled 检查全局蜘蛛屏蔽是否启用
func (s *SystemSettingsService) IsGlobalSpiderBlockEnabled() bool {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return false
	}
	return settings.EnableGlobalSpiderBlock
}

// GetSpiderBlock403Template 获取蜘蛛屏蔽403模板
func (s *SystemSettingsService) GetSpiderBlock403Template() string {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return "<h1>403 Forbidden</h1><p>访问被拒绝</p>"
	}
	if settings.SpiderBlock403Template != "" {
		return settings.SpiderBlock403Template
	}
	return "<h1>403 Forbidden</h1><p>访问被拒绝</p>"
}

// GetDefaultNonSpiderHTML 获取默认的非爬虫HTML
func (s *SystemSettingsService) GetDefaultNonSpiderHTML() string {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return s.getDefaultNonSpiderHTML()
	}
	if settings.DefaultNonSpiderHTML == "" {
		return s.getDefaultNonSpiderHTML()
	}
	return settings.DefaultNonSpiderHTML
}

// ReplaceNonSpiderHTMLTags 替换非爬虫HTML中的标签
// 支持的标签：
// {title} - 首页标题（支持Unicode转码和简繁转换）
// {description} - 首页描述（支持Unicode转码和简繁转换）
// {keywords} - 首页关键词（支持Unicode转码和简繁转换）
// {analytics} - 统计代码
// {company} - 当前企业名称（不进行Unicode转码）
// {domain} - 当前域名
// {year} - 当前年份
// {date} - 当前日期
// {time} - 当前时间
func (s *SystemSettingsService) ReplaceNonSpiderHTMLTags(html string, site *model.Site) string {
	if html == "" {
		return html
	}
	
	// 准备SEO相关的文本
	titleText := "访问限制"
	descText := ""
	keywordsText := ""
	
	if site.InjectConfig != nil {
		titleText = site.InjectConfig.HomeTitle
		descText = site.InjectConfig.HomeDescription
		keywordsText = site.InjectConfig.HomeKeywords
		
		// 应用简繁转换（如果启用）
		if site.EnableTraditionalConvert {
			titleText = s.convertToTraditional(titleText)
			descText = s.convertToTraditional(descText)
			keywordsText = s.convertToTraditional(keywordsText)
		}
		
		// 应用Unicode转码（如果启用）
		if site.InjectConfig.EnableUnicode {
			// 根据Unicode配置范围决定是否转码
			if site.InjectConfig.UnicodeScope == "all" || site.InjectConfig.EnableUnicodeTitle {
				titleText = s.toUnicodeEntities(titleText)
			}
			if site.InjectConfig.UnicodeScope == "all" || site.InjectConfig.EnableUnicodeDesc {
				descText = s.toUnicodeEntities(descText)
			}
			if site.InjectConfig.UnicodeScope == "all" || site.InjectConfig.EnableUnicodeKeywords {
				keywordsText = s.toUnicodeEntities(keywordsText)
			}
		}
	}
	
	// 替换SEO相关标签
	html = strings.ReplaceAll(html, "{title}", titleText)
	html = strings.ReplaceAll(html, "{description}", descText)
	html = strings.ReplaceAll(html, "{keywords}", keywordsText)
	
	// 替换基础标签（不进行转码）
	html = strings.ReplaceAll(html, "{domain}", site.Domain)
	html = strings.ReplaceAll(html, "{year}", fmt.Sprintf("%d", time.Now().Year()))
	html = strings.ReplaceAll(html, "{date}", time.Now().Format("2006-01-02"))
	html = strings.ReplaceAll(html, "{time}", time.Now().Format("15:04:05"))
	
	// 生成统计JS引用
	analyticsScript := s.getAnalyticsScript(site.Domain)
	html = strings.ReplaceAll(html, "{analytics}", analyticsScript)
	
	// 获取企业名称（不进行Unicode转码）
	companyName := s.getRandomCompanyName(site)
	html = strings.ReplaceAll(html, "{company}", companyName)
	
	return html
}


// getAnalyticsScript 获取统计JS脚本引用
func (s *SystemSettingsService) getAnalyticsScript(domain string) string {
	// 获取域名的MD5值（前12位）
	hash := md5.Sum([]byte(domain))
	md5Str := fmt.Sprintf("%x", hash)[:12]
	
	// 返回统计JS脚本标签
	return fmt.Sprintf(`<script src="/%s.js" async></script>`, md5Str)
}

// getRandomCompanyName 获取随机企业名称
func (s *SystemSettingsService) getRandomCompanyName(site *model.Site) string {
	// 如果站点配置了企业名称库，从中随机选择一个
	if site.CompanyLibraryID > 0 {
		var companyNames []model.CompanyName
		err := s.db.Where("library_id = ?", site.CompanyLibraryID).Find(&companyNames).Error
		if err == nil && len(companyNames) > 0 {
			rand.Seed(time.Now().UnixNano())
			return companyNames[rand.Intn(len(companyNames))].Name
		}
	}
	// 如果配置了固定企业名称，使用它
	if site.CompanyName != "" {
		return site.CompanyName
	}
	// 返回默认企业名称
	return "示例公司"
}

// GetDefaultAllowedUA 获取默认允许的UA列表
func (s *SystemSettingsService) GetDefaultAllowedUA() string {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return ""
	}
	return settings.DefaultAllowedUA
}

// CheckUserAgent 检查UserAgent是否在允许列表中
func (s *SystemSettingsService) CheckUserAgent(userAgent string, customAllowedUA string) bool {
	// 如果没有自定义规则，使用系统默认规则
	allowedUA := customAllowedUA
	if allowedUA == "" {
		allowedUA = s.GetDefaultAllowedUA()
	}
	
	// 如果仍然为空，则允许所有（不进行UA判断）
	if allowedUA == "" {
		return true
	}
	
	// 将UA转换为小写进行比较
	userAgentLower := strings.ToLower(userAgent)
	
	// 按 | 分割规则
	rules := strings.Split(allowedUA, "|")
	for _, rule := range rules {
		rule = strings.TrimSpace(strings.ToLower(rule))
		if rule != "" && strings.Contains(userAgentLower, rule) {
			return true
		}
	}
	
	return false
}

// CalculateWorkerPoolSize 根据配置计算工作池大小
func (s *SystemSettingsService) CalculateWorkerPoolSize(settings *model.SystemSettings, resourceLimiter *ResourceLimiter) int {
	// 如果是固定模式且设置了大小
	if settings.WorkerPoolMode == "fixed" && settings.WorkerPoolSize > 0 {
		return settings.WorkerPoolSize
	}
	
	// 自动模式或未设置
	if settings.WorkerPoolMode == "auto" || settings.WorkerPoolMode == "" || settings.WorkerPoolSize == 0 {
		// 获取资源限流配置
		var totalConcurrency int64
		if resourceLimiter != nil && resourceLimiter.config != nil {
			totalConcurrency = resourceLimiter.config.MaxDatabaseConn +
				resourceLimiter.config.MaxRedisConn +
				resourceLimiter.config.MaxHTTPRequests +
				resourceLimiter.config.MaxFileOps +
				resourceLimiter.config.MaxCrawlerTasks
		} else {
			// 如果没有资源限流器，基于系统设置计算
			totalConcurrency = int64(settings.MaxDatabaseConn +
				settings.MaxRedisConn +
				settings.MaxHTTPRequests +
				settings.MaxFileOps +
				settings.MaxCrawlerTasks)
		}
		
		// 如果总并发为0，使用默认值
		if totalConcurrency == 0 {
			cpuCores := runtime.NumCPU()
			totalConcurrency = int64(cpuCores * 40) // 默认每核40个并发
		}
		
		// 使用缩放比例
		scaleRatio := settings.WorkerPoolScaleRatio
		if scaleRatio <= 0 {
			scaleRatio = 1.2 // 默认1.2倍
		}
		
		poolSize := int(float64(totalConcurrency) * scaleRatio)
		
		// 限制范围
		if poolSize < 100 {
			poolSize = 100
		}
		if poolSize > 5000 {
			poolSize = 5000
		}
		
		return poolSize
	}
	
	// 动态模式返回最大值（动态池会自己管理）
	if settings.WorkerPoolMode == "dynamic" {
		if settings.WorkerPoolMaxSize > 0 {
			return settings.WorkerPoolMaxSize
		}
		return 2000 // 默认最大值
	}
	
	// 默认值
	return 350
}

// getDefaultNonSpiderHTML 获取内置的默认非爬虫HTML
func (s *SystemSettingsService) getDefaultNonSpiderHTML() string {
	return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>访问限制</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; }
        p { color: #666; line-height: 1.6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>访问受限</h1>
        <p>抱歉，您使用的浏览器或访问方式不被支持。</p>
        <p>请使用标准浏览器访问本站。</p>
    </div>
</body>
</html>`
}

// GetRandomUserAgent 获取随机的User-Agent
func (s *SystemSettingsService) GetRandomUserAgent() string {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}
	
	// 如果没有设置UA列表，返回默认的
	if settings.UserAgentList == "" {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}
	
	// 按行分割UA列表
	lines := strings.Split(settings.UserAgentList, "\n")
	var userAgents []string
	
	// 过滤空行
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed != "" {
			userAgents = append(userAgents, trimmed)
		}
	}
	
	// 如果没有有效的UA，返回默认的
	if len(userAgents) == 0 {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}
	
	// 随机选择一个UA
	return userAgents[rand.Intn(len(userAgents))]
}

// getDefaultUserAgentList 获取默认的User-Agent列表
func (s *SystemSettingsService) getDefaultUserAgentList() string {
	return `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:121.0) Gecko/20100101 Firefox/121.0
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/106.0.0.0
Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36`
}

// IsCaptchaEnabled 检查验证码是否启用
func (s *SystemSettingsService) IsCaptchaEnabled() bool {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return false
	}
	return settings.EnableCaptcha
}

// GetCaptchaConfig 获取验证码配置
func (s *SystemSettingsService) GetCaptchaConfig() (enabled bool, length int, expiry int) {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return false, 4, 300 // 默认值
	}
	
	length = settings.CaptchaLength
	if length <= 0 {
		length = 4
	}
	
	expiry = settings.CaptchaExpiry
	if expiry <= 0 {
		expiry = 300
	}
	
	return settings.EnableCaptcha, length, expiry
}

// GetCrawlerConcurrency 获取请求并发数（为了兼容旧代码，返回 MaxHTTPRequests）
func (s *SystemSettingsService) GetCrawlerConcurrency() int {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return 40 // 默认值已更新为40
	}
	if settings.MaxHTTPRequests <= 0 {
		return 40 // 使用新的默认值
	}
	return settings.MaxHTTPRequests
}

// GetCrawlerTimeout 获取HTTP请求超时时间（秒）
// 保留此函数以兼容旧代码，实际返回HTTP客户端超时时间
func (s *SystemSettingsService) GetCrawlerTimeout() int {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return 30
	}
	// 返回爬虫任务超时时间（毫秒转秒）
	if settings.CrawlerTaskTimeout <= 0 {
		return 30
	}
	return settings.CrawlerTaskTimeout / 1000
}

// GetProxyRequestTimeout 获取代理请求超时时间（秒）
// 用于镜像站点的HTTP请求
func (s *SystemSettingsService) GetProxyRequestTimeout() int {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return 60
	}
	// 返回代理请求超时时间（毫秒转秒）
	if settings.ProxyRequestTimeout <= 0 {
		return 60
	}
	return settings.ProxyRequestTimeout / 1000
}

// GetSystemSuggestions 获取基于系统配置的建议值
func (s *SystemSettingsService) GetSystemSuggestions() map[string]interface{} {
	// 获取系统CPU核心数
	numCPU := runtime.NumCPU()
	
	// 基于CPU核心数的并发建议
	recommendedConcurrency := numCPU * 2
	if recommendedConcurrency < 4 {
		recommendedConcurrency = 4
	} else if recommendedConcurrency > 20 {
		recommendedConcurrency = 20
	}
	
	// 获取系统内存
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	totalMemoryGB := float64(m.Sys) / 1024 / 1024 / 1024
	
	// 基于内存的并发建议调整
	if totalMemoryGB < 2 {
		recommendedConcurrency = min(recommendedConcurrency, 5)
	} else if totalMemoryGB < 4 {
		recommendedConcurrency = min(recommendedConcurrency, 10)
	}
	
	return map[string]interface{}{
		"cpu_cores":                numCPU,
		"recommended_concurrency":   recommendedConcurrency,
		"memory_gb":                totalMemoryGB,
		"concurrency_description":  fmt.Sprintf("基于您的系统配置（%d核CPU），建议并发数为 %d", numCPU, recommendedConcurrency),
	}
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// toUnicodeEntities 将中文字符转换为HTML实体编码（十六进制格式）
func (s *SystemSettingsService) toUnicodeEntities(text string) string {
	var result strings.Builder
	for _, r := range text {
		// 检查是否为非ASCII字符
		if r > 127 {
			// 转换为 &#x十六进制; 格式（HTML实体编码标准格式）
			result.WriteString(fmt.Sprintf("&#x%04X;", r))
		} else {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// convertToTraditional 将简体中文转换为繁体中文
func (s *SystemSettingsService) convertToTraditional(text string) string {
	return utils.SimplifiedToTraditional(text)
}

// getDefaultSpiderBlockUA 获取默认的蜘蛛屏蔽UA列表
func (s *SystemSettingsService) getDefaultSpiderBlockUA() string {
	return `AhrefsBot
SemrushBot
MJ12bot
DotBot
SEOkicks
PetalBot
Bytespider
BlexBot
Qwantify
serpstatbot
turnitinbot
BLEXBot
GrapeshotCrawler
MegaIndex
SeznamBot
ltx71
Neevabot
SirdataBot
MaCoCu
VelenPublicWebCrawler`
}

// GetDefaultSpiderBlockUA 获取默认的蜘蛛屏蔽UA
func (s *SystemSettingsService) GetDefaultSpiderBlockUA() string {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return s.getDefaultSpiderBlockUA()
	}
	if settings.DefaultSpiderBlockUA == "" {
		return s.getDefaultSpiderBlockUA()
	}
	return settings.DefaultSpiderBlockUA
}

// getDefault404HTML 获取内置的默认404页面HTML
func (s *SystemSettingsService) getDefault404HTML() string {
	return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>404 - 页面未找到</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { 
            color: #333;
            font-size: 72px;
            margin: 0;
        }
        h2 {
            color: #666;
            font-size: 24px;
            margin: 20px 0;
        }
        p { 
            color: #999; 
            line-height: 1.6;
        }
        .error-code {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="error-code">404</h1>
        <h2>页面未找到</h2>
        <p>抱歉，您访问的页面不存在或已被移除。</p>
        <p>请检查URL是否正确，或返回首页继续浏览。</p>
    </div>
</body>
</html>`
}

// GetDefault404HTML 获取默认的404页面HTML
func (s *SystemSettingsService) GetDefault404HTML() string {
	settings, err := s.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return s.getDefault404HTML()
	}
	if settings.Default404HTML == "" {
		return s.getDefault404HTML()
	}
	return settings.Default404HTML
}

// ApplyLogSettings 应用日志配置
func (s *SystemSettingsService) ApplyLogSettings(settings *model.SystemSettings) {
	// 日志配置应用逻辑
	// 注意：这里只是记录配置，实际的日志级别控制在 main.go 中初始化时设置
	// 这里可以通过全局变量或其他机制通知日志系统配置已更改
	
	if !settings.LogEnabled {
		s.logger.Info("日志记录已禁用")
		// 可以设置一个全局标志来禁用日志
		return
	}
	
	s.logger.Info("日志配置已更新",
		zap.String("level", settings.LogLevel),
		zap.String("storage", settings.LogStorage),
		zap.Int("retention_days", settings.LogRetentionDays),
		zap.Int("max_size", settings.LogMaxSize),
		zap.Int("max_backups", settings.LogMaxBackups),
		zap.Bool("access_enabled", settings.LogAccessEnabled),
		zap.Bool("error_enabled", settings.LogErrorEnabled))
	
	// TODO: 实际的日志轮转和清理逻辑可以在定时任务中实现
}