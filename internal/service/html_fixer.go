package service

import (
	"bytes"
	"strings"
	
	"golang.org/x/net/html"
	"go.uber.org/zap"
)

// HTMLFixer HTML修复服务，用于修复不闭合的标签等问题
type HTMLFixer struct {
	logger *zap.Logger
}

// NewHTMLFixer 创建HTML修复服务
func NewHTMLFixer(logger *zap.Logger) *HTMLFixer {
	return &HTMLFixer{
		logger: logger,
	}
}

// Fix 修复HTML，自动闭合标签并规范化
func (hf *HTMLFixer) Fix(htmlContent string) (string, error) {
	// 记录输入信息
	hf.logger.Info("HTMLFixer.Fix 被调用", 
		zap.Int("输入长度", len(htmlContent)),
		zap.Bool("包含</body>", strings.Contains(htmlContent, "</body>")),
		zap.Bool("包含</html>", strings.Contains(htmlContent, "</html>")))
	
	// 解析HTML
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		hf.logger.Warn("HTML解析失败，返回原内容", zap.Error(err))
		return htmlContent, err
	}
	
	// 渲染回字符串
	var buf bytes.Buffer
	if err := html.Render(&buf, doc); err != nil {
		hf.logger.Warn("HTML渲染失败，返回原内容", zap.Error(err))
		return htmlContent, err
	}
	
	result := buf.String()
	hf.logger.Info("HTMLFixer.Fix 完成", 
		zap.Int("输出长度", len(result)),
		zap.Bool("输出包含</body>", strings.Contains(result, "</body>")),
		zap.Bool("输出包含</html>", strings.Contains(result, "</html>")))
	
	return result, nil
}

// FixAndInject 修复HTML并注入内容到body标签前
func (hf *HTMLFixer) FixAndInject(htmlContent string, injection string) (string, error) {
	// 解析HTML
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		hf.logger.Warn("HTML解析失败", zap.Error(err))
		return htmlContent, err
	}
	
	// 查找body结束标签的位置并注入内容
	var inject func(*html.Node)
	inject = func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "body" {
			// 创建要注入的节点
			injectionDoc, err := html.ParseFragment(strings.NewReader(injection), &html.Node{
				Type: html.ElementNode,
				Data: "body",
			})
			if err == nil && len(injectionDoc) > 0 {
				// 将注入内容添加到body的最后
				for _, node := range injectionDoc {
					n.AppendChild(node)
				}
			}
		}
		
		// 递归处理子节点
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			inject(c)
		}
	}
	
	inject(doc)
	
	// 渲染回字符串
	var buf bytes.Buffer
	if err := html.Render(&buf, doc); err != nil {
		hf.logger.Warn("HTML渲染失败", zap.Error(err))
		return htmlContent, err
	}
	
	return buf.String(), nil
}

// InjectBeforeBodyEnd 在</body>标签前注入内容
func (hf *HTMLFixer) InjectBeforeBodyEnd(htmlContent string, injection string) (string, error) {
	// 如果HTML内容很短或者明显不是完整HTML，直接返回
	if len(htmlContent) < 100 || !strings.Contains(strings.ToLower(htmlContent), "<html") {
		return htmlContent, nil
	}
	
	// 解析HTML
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		// 如果解析失败，尝试简单的字符串替换
		hf.logger.Debug("HTML解析失败，使用字符串替换", zap.Error(err))
		return hf.simpleInject(htmlContent, injection), nil
	}
	
	// 查找body节点并注入
	injected := false
	var findAndInject func(*html.Node)
	findAndInject = func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "body" && !injected {
			// 解析要注入的HTML片段
			injectionNodes, err := html.ParseFragment(strings.NewReader(injection), &html.Node{
				Type: html.ElementNode,
				Data: "body",
			})
			if err == nil {
				// 将注入内容添加到body的最后
				for _, node := range injectionNodes {
					n.AppendChild(node)
				}
				injected = true
			}
		}
		
		// 递归处理子节点
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			findAndInject(c)
		}
	}
	
	findAndInject(doc)
	
	// 渲染回字符串
	var buf bytes.Buffer
	if err := html.Render(&buf, doc); err != nil {
		hf.logger.Warn("HTML渲染失败", zap.Error(err))
		return htmlContent, err
	}
	
	result := buf.String()
	
	// 清理golang.org/x/net/html自动添加的额外标签
	result = hf.cleanupAutoAddedTags(result)
	
	return result, nil
}

// simpleInject 简单的字符串注入（后备方案）
func (hf *HTMLFixer) simpleInject(htmlContent string, injection string) string {
	// 查找</body>标签
	bodyEndIndex := strings.LastIndex(strings.ToLower(htmlContent), "</body>")
	if bodyEndIndex > 0 {
		// 在</body>前插入内容
		return htmlContent[:bodyEndIndex] + injection + "\n" + htmlContent[bodyEndIndex:]
	}
	
	// 如果没有找到</body>，尝试在</html>前插入
	htmlEndIndex := strings.LastIndex(strings.ToLower(htmlContent), "</html>")
	if htmlEndIndex > 0 {
		return htmlContent[:htmlEndIndex] + injection + "\n</body>\n" + htmlContent[htmlEndIndex:]
	}
	
	// 如果都没找到，直接在末尾添加
	return htmlContent + "\n" + injection + "\n</body>\n</html>"
}

// cleanupAutoAddedTags 清理golang.org/x/net/html自动添加的额外标签
func (hf *HTMLFixer) cleanupAutoAddedTags(html string) string {
	// golang.org/x/net/html 有时会添加额外的 <html><head></head><body> 标签
	// 如果原始内容没有这些标签，我们需要移除它们
	
	// 简单处理：如果内容不是以<!DOCTYPE或<html开头，可能是片段
	trimmed := strings.TrimSpace(html)
	if !strings.HasPrefix(strings.ToLower(trimmed), "<!doctype") && 
	   !strings.HasPrefix(strings.ToLower(trimmed), "<html") {
		// 这可能是一个片段，移除自动添加的包装
		// 这里需要更智能的处理，暂时返回原内容
		return html
	}
	
	return html
}

// EnsureClosingTags 确保HTML有正确的闭合标签
func (hf *HTMLFixer) EnsureClosingTags(htmlContent string) string {
	lower := strings.ToLower(htmlContent)
	
	// 检查并添加缺失的闭合标签
	if !strings.Contains(lower, "</body>") {
		if strings.Contains(lower, "<body") {
			// 有开始标签但没有结束标签
			if !strings.Contains(lower, "</html>") {
				htmlContent += "\n</body>\n</html>"
			} else {
				// 在</html>前插入</body>
				htmlContent = strings.Replace(htmlContent, "</html>", "</body>\n</html>", 1)
			}
		}
	}
	
	if !strings.Contains(lower, "</html>") && strings.Contains(lower, "<html") {
		htmlContent += "\n</html>"
	}
	
	return htmlContent
}