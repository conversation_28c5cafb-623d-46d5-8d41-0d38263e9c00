package service

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io/ioutil"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/utils"

	"github.com/patrickmn/go-cache"
	"github.com/tdewolff/minify/v2"
	"github.com/tdewolff/minify/v2/css"
	"github.com/tdewolff/minify/v2/html"
	"github.com/tdewolff/minify/v2/js"
	"go.uber.org/zap"
)

type FileCacheService struct {
	logger   *zap.Logger
	basePath string
	config   *CacheConfig
	minifier *minify.M
	sizeCache *cache.Cache  // 内存缓存，用于缓存目录大小
}

type CacheConfig struct {
	MaxSize         int64         // 最大缓存大小（字节）
	DefaultTTL      time.Duration // 默认过期时间
	CleanupInterval time.Duration // 清理间隔
}

func NewFileCacheService(logger *zap.Logger, basePath string) *FileCacheService {
	// 初始化minifier
	m := minify.New()
	// 配置HTML压缩器，保留闭合标签
	htmlMinifier := &html.Minifier{
		KeepEndTags: true,  // 保留闭合标签
		KeepDocumentTags: true, // 保留文档标签（html, head, body）
	}
	m.AddFuncRegexp(regexp.MustCompile("^(text/html).*"), htmlMinifier.Minify)
	m.AddFunc("text/css", css.Minify)
	m.AddFunc("application/javascript", js.Minify)
	m.AddFunc("text/javascript", js.Minify)
	
	service := &FileCacheService{
		logger:   logger,
		basePath: basePath,
		minifier: m,
		config: &CacheConfig{
			MaxSize:         10 * 1024 * 1024 * 1024, // 10GB
			DefaultTTL:      24 * time.Hour,
			CleanupInterval: 60 * time.Minute,
		},
		sizeCache: cache.New(30*time.Second, 60*time.Second), // 缓存30秒，每60秒清理过期项
	}
	
	// 确保缓存目录存在
	if err := os.MkdirAll(basePath, 0755); err != nil {
		logger.Error("创建缓存目录失败", zap.Error(err))
	}
	
	// 禁用定期清理，让缓存永久有效
	// go service.startCleanup()
	
	return service
}

// SaveContent 保存内容到缓存
func (s *FileCacheService) SaveContent(domain string, content *model.CachedContent) error {
	// 构建文件路径
	filePath := s.getFilePath(domain, content.URL)
	
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}
	
	// 检查文件路径是否已经存在且是目录（路径冲突）
	if info, err := os.Stat(filePath); err == nil {
		if info.IsDir() {
			// 路径冲突：文件路径已经是一个目录
			// 记录冲突信息到.conflict文件
			conflictFile := filePath + ".conflict"
			conflictInfo := fmt.Sprintf("Path conflict detected at %s\nURL: %s\nTime: %s\nReason: Path exists as directory\n", 
				filePath, content.URL, time.Now().Format(time.RFC3339))
			ioutil.WriteFile(conflictFile, []byte(conflictInfo), 0644)
			
			// s.logger.Warn("缓存路径冲突，跳过缓存", 
			//	zap.String("domain", domain),
			//	zap.String("url", content.URL),
			//	zap.String("file_path", filePath),
			//	zap.String("conflict_file", conflictFile))
			return nil // 返回nil表示不是错误，只是跳过
		}
		// 如果是普通文件，检查是否需要更新
		if time.Since(info.ModTime()) < 24*time.Hour {
			// 文件较新，跳过缓存
			s.logger.Debug("缓存文件已存在且较新，跳过", 
				zap.String("file_path", filePath),
				zap.Time("mod_time", info.ModTime()))
			return nil
		}
	}
	
	originalSize := len(content.Data)
	processedData := content.Data
	
	// 对HTML/CSS/JS内容进行压缩处理
	if s.shouldMinify(content.ContentType) {
		// 使用minify库压缩内容
		minified, err := s.minifyContent(content.Data, content.ContentType)
		if err != nil {
			// s.logger.Warn("内容压缩失败，使用原始数据", zap.Error(err))
		} else {
			processedData = minified
		}
	}
	
	// 保存处理后的内容
	if err := ioutil.WriteFile(filePath, processedData, 0644); err != nil {
		return err
	}
	
	processedSize := len(processedData)
	if originalSize > 0 {
		_ = float64(processedSize) / float64(originalSize) * 100
		// ratio := float64(processedSize) / float64(originalSize) * 100
		// s.logger.Info("内容已缓存并压缩", 
		//	zap.String("domain", domain),
		//	zap.String("url", content.URL),
		//	zap.Int("original_size", originalSize),
		//	zap.Int("processed_size", processedSize),
		//	zap.Float64("minify_ratio", ratio))
	}
	
	return nil
}

// SaveResource 保存资源到缓存
func (s *FileCacheService) SaveResource(domain, url string, data []byte, contentType string) error {
	content := &model.CachedContent{
		URL:         url,
		ContentType: contentType,
		Data:        data,
		Headers:     make(map[string]string),
		CachedAt:    time.Now(),
		ExpiresAt:   time.Now().Add(7 * 24 * time.Hour), // 资源缓存7天
	}
	return s.SaveContent(domain, content)
}

// GetResource 从缓存获取资源
func (s *FileCacheService) GetResource(domain, url string) ([]byte, string, bool) {
	content, found := s.GetContent(domain, url, 7*24*time.Hour)
	if !found {
		return nil, "", false
	}
	return content.Data, content.ContentType, true
}

// GetContent 从缓存获取内容
func (s *FileCacheService) GetContent(domain, url string, maxAge time.Duration) (*model.CachedContent, bool) {
	filePath := s.getFilePath(domain, url)
	s.logger.Debug("检查缓存文件",
		zap.String("domain", domain),
		zap.String("url", url),
		zap.String("file_path", filePath))
	
	// 检查缓存文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		s.logger.Debug("缓存文件不存在", zap.String("file_path", filePath))
		return nil, false
	}
	
	// 如果是目录而不是文件，检查是否有冲突记录
	if fileInfo.IsDir() {
		// 检查冲突文件
		conflictFile := filePath + ".conflict"
		if _, err := os.Stat(conflictFile); err == nil {
			s.logger.Debug("路径冲突，跳过缓存读取", 
				zap.String("file_path", filePath),
				zap.String("conflict_file", conflictFile))
		} else {
			// s.logger.Warn("缓存路径是目录而非文件", 
			//	zap.String("file_path", filePath))
		}
		return nil, false
	}
	
	// 使用文件修改时间作为缓存时间
	cachedAt := fileInfo.ModTime()
	
	// 注释掉过期检查，让缓存永久有效
	// if time.Since(cachedAt) > maxAge {
	//	return nil, false
	// }
	
	// 读取内容
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, false
	}
	
	// 根据文件扩展名推断内容类型
	contentType := "text/html"
	ext := filepath.Ext(filePath)
	switch ext {
	case ".css":
		contentType = "text/css"
	case ".js":
		contentType = "application/javascript"
	case ".json":
		contentType = "application/json"
	case ".png":
		contentType = "image/png"
	case ".jpg", ".jpeg":
		contentType = "image/jpeg"
	case ".gif":
		contentType = "image/gif"
	case ".svg":
		contentType = "image/svg+xml"
	case ".ico":
		contentType = "image/x-icon"
	}
	
	content := &model.CachedContent{
		URL:         url,
		ContentType: contentType,
		Data:        data,
		CachedAt:    cachedAt,
		ExpiresAt:   cachedAt.Add(maxAge),
		Headers:     make(map[string]string),
	}
	
	// s.logger.Info("成功从缓存读取内容",
	//	zap.String("domain", domain),
	//	zap.String("url", url),
	//	zap.String("file_path", filePath),
	//	zap.Int("size", len(data)))
	
	return content, true
}

// GetStats 获取缓存统计
func (s *FileCacheService) GetStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	var totalSize int64
	var totalFiles int
	var expiredFiles int
	sitesMap := make(map[string]*SiteCacheInfo)
	
	err := filepath.Walk(s.basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		
		if info.IsDir() {
			return nil
		}
		
		// 不再需要跳过.meta文件，因为不再生成
		
		totalSize += info.Size()
		totalFiles++
		
		// 获取域名
		relPath, _ := filepath.Rel(s.basePath, path)
		parts := strings.Split(relPath, string(os.PathSeparator))
		if len(parts) > 0 {
			domain := parts[0]
			if _, exists := sitesMap[domain]; !exists {
				sitesMap[domain] = &SiteCacheInfo{
					Domain: domain,
				}
			}
			sitesMap[domain].Size += info.Size()
			sitesMap[domain].FilesCount++
			
			if info.ModTime().After(sitesMap[domain].LastUpdated) {
				sitesMap[domain].LastUpdated = info.ModTime()
			}
		}
		
		// 不再检查过期，缓存永久有效
		// if time.Since(info.ModTime()) > 7*24*time.Hour {
		// 	expiredFiles++
		// }
		
		return nil
	})
	
	if err != nil {
		s.logger.Error("获取缓存统计失败", zap.Error(err))
		return nil, err
	}
	
	// 转换站点信息为数组
	var sites []interface{}
	for _, siteInfo := range sitesMap {
		sites = append(sites, siteInfo)
	}
	
	stats["total_size"] = totalSize
	stats["files_count"] = totalFiles
	stats["expired_count"] = expiredFiles
	stats["sites_count"] = len(sitesMap)
	stats["sites"] = sites
	
	return stats, nil
}

// ClearSiteCache 清除站点缓存
func (s *FileCacheService) ClearSiteCache(domain string) (int, error) {
	sitePath := filepath.Join(s.basePath, domain)
	
	var deletedCount int
	err := filepath.Walk(sitePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		
		if !info.IsDir() {
			if err := os.Remove(path); err == nil {
				deletedCount++
			}
		}
		
		return nil
	})
	
	if err != nil {
		s.logger.Error("清除站点缓存失败", zap.Error(err))
		return 0, err
	}
	
	// 删除空目录
	os.RemoveAll(sitePath)
	
	// s.logger.Info("站点缓存已清除", 
	//	zap.String("domain", domain),
	//	zap.Int("deleted_count", deletedCount))
	
	return deletedCount, nil
}

// ClearExpiredCache 清除过期缓存
func (s *FileCacheService) ClearExpiredCache() (int, error) {
	var deletedCount int
	
	err := filepath.Walk(s.basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		
		if info.IsDir() {
			return nil
		}
		
		// 不再自动删除过期文件，缓存永久有效
		// if time.Since(info.ModTime()) > 7*24*time.Hour {
		// 	os.Remove(path)
		// 	deletedCount++
		// }
		
		return nil
	})
	
	if err != nil {
		s.logger.Error("清除过期缓存失败", zap.Error(err))
		return 0, err
	}
	
	// s.logger.Info("过期缓存已清除", zap.Int("deleted_count", deletedCount))
	return deletedCount, nil
}

// GetCacheSize 获取缓存大小
func (s *FileCacheService) GetCacheSize(domain string) (int64, error) {
	var size int64
	
	startPath := s.basePath
	if domain != "" {
		startPath = filepath.Join(s.basePath, domain)
	}
	
	err := filepath.Walk(startPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		
		if !info.IsDir() {
			size += info.Size()
		}
		
		return nil
	})
	
	if err != nil {
		return 0, err
	}
	
	return size, nil
}

// GetCacheSizeWithCache 获取缓存大小（带内存缓存）
func (s *FileCacheService) GetCacheSizeWithCache(domain string) (int64, error) {
	// 使用内存缓存，30秒过期
	cacheKey := "cache_size:" + domain
	
	// 检查内存缓存
	if s.sizeCache != nil {
		if size, found := s.sizeCache.Get(cacheKey); found {
			if sizeInt64, ok := size.(int64); ok {
				return sizeInt64, nil
			}
		}
	}
	
	// 计算实际大小
	size, err := s.GetCacheSize(domain)
	if err != nil {
		return 0, err
	}
	
	// 存入缓存
	if s.sizeCache != nil {
		s.sizeCache.Set(cacheKey, size, cache.DefaultExpiration)
	}
	
	return size, nil
}

// GetConfig 获取缓存配置
func (s *FileCacheService) GetConfig() *CacheConfig {
	return s.config
}

// UpdateConfig 更新缓存配置
func (s *FileCacheService) UpdateConfig(config *CacheConfig) error {
	s.config = config
	// s.logger.Info("缓存配置已更新", 
	//	zap.Int64("max_size", config.MaxSize),
	//	zap.Duration("default_ttl", config.DefaultTTL),
	//	zap.Duration("cleanup_interval", config.CleanupInterval))
	return nil
}

// TriggerRefresh 触发刷新缓存
func (s *FileCacheService) TriggerRefresh(domain, url string) (*model.CrawlJob, error) {
	// 这里应该调用爬虫服务重新爬取
	// 暂时返回模拟数据
	job := &model.CrawlJob{
		ID:        uint(time.Now().Unix()),
		SiteID:    1,
		URL:       url,
		Type:      "refresh",
		Status:    "pending",
		CreatedAt: time.Now(),
	}
	
	return job, nil
}

// GetCachedURLs 获取缓存的URL列表
func (s *FileCacheService) GetCachedURLs(domain string, page, pageSize int) ([]URLInfo, int, error) {
	var urls []URLInfo
	sitePath := filepath.Join(s.basePath, domain)
	
	// 收集所有URL
	var allURLs []URLInfo
	err := filepath.Walk(sitePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		
		if info.IsDir() {
			return nil
		}
		
		// 从文件路径构建URL
		relPath, _ := filepath.Rel(sitePath, path)
		urlPath := "/" + strings.ReplaceAll(relPath, string(os.PathSeparator), "/")
		
		// 根据文件扩展名推断内容类型
		contentType := "text/html"
		ext := filepath.Ext(path)
		switch ext {
		case ".css":
			contentType = "text/css"
		case ".js":
			contentType = "application/javascript"
		case ".json":
			contentType = "application/json"
		case ".png":
			contentType = "image/png"
		case ".jpg", ".jpeg":
			contentType = "image/jpeg"
		case ".gif":
			contentType = "image/gif"
		case ".svg":
			contentType = "image/svg+xml"
		}
		
		urlInfo := URLInfo{
			URL:         urlPath,
			ContentType: contentType,
			Size:        info.Size(),
			CachedAt:    info.ModTime(),
		}
		
		allURLs = append(allURLs, urlInfo)
		
		return nil
	})
	
	if err != nil {
		return nil, 0, err
	}
	
	// 分页
	total := len(allURLs)
	start := (page - 1) * pageSize
	end := start + pageSize
	
	if start < total {
		if end > total {
			end = total
		}
		urls = allURLs[start:end]
	}
	
	return urls, total, nil
}

// 辅助函数
func (s *FileCacheService) getFilePath(domain, urlStr string) string {
	// 解析URL
	u, err := url.Parse(urlStr)
	if err != nil {
		// 如果解析失败，使用MD5作为文件名
		return filepath.Join(s.basePath, domain, s.generateMD5(urlStr))
	}
	
	// 获取路径
	path := u.Path
	if path == "" || path == "/" {
		path = "/index.html"
	}
	
	// 如果路径以/结尾（目录），添加index.html
	if strings.HasSuffix(path, "/") && path != "/" {
		path = path + "index.html"
	}
	
	// 如果是动态URL（包含查询参数或动态文件），使用特殊处理
	if u.RawQuery != "" || utils.IsDynamicURL(path) {
		// 使用动态URL规范化
		normalizedPath := utils.NormalizeDynamicURL(urlStr)
		// 确保路径安全
		normalizedPath = filepath.Clean(normalizedPath)
		normalizedPath = strings.TrimPrefix(normalizedPath, "/")
		return filepath.Join(s.basePath, domain, normalizedPath)
	}
	
	// 静态URL的处理保持不变
	// 确保路径安全
	path = filepath.Clean(path)
	path = strings.TrimPrefix(path, "/")
	
	return filepath.Join(s.basePath, domain, path)
}

// generateMD5 生成MD5哈希
func (s *FileCacheService) generateMD5(text string) string {
	hash := md5.New()
	hash.Write([]byte(text))
	return hex.EncodeToString(hash.Sum(nil))
}

// startCleanup 启动定期清理（已禁用，缓存永久有效）
func (s *FileCacheService) startCleanup() {
	// 注释掉自动清理功能，让缓存永久有效
	// ticker := time.NewTicker(s.config.CleanupInterval)
	// defer ticker.Stop()
	// 
	// for range ticker.C {
	// 	if _, err := s.ClearExpiredCache(); err != nil {
	// 		s.logger.Error("定期清理失败", zap.Error(err))
	// 	}
	// }
}

// SiteCacheInfo 站点缓存信息
type SiteCacheInfo struct {
	Domain      string    `json:"domain"`
	Size        int64     `json:"size"`
	FilesCount  int       `json:"files_count"`
	LastUpdated time.Time `json:"last_updated"`
}

// URLInfo URL信息
type URLInfo struct {
	URL         string    `json:"url"`
	ContentType string    `json:"content_type"`
	Size        int64     `json:"size"`
	CachedAt    time.Time `json:"cached_at"`
}

// GetCacheDetails 获取缓存详情
func (s *FileCacheService) GetCacheDetails(domain, url string) (map[string]interface{}, error) {
	content, found := s.GetContent(domain, url, 365*24*time.Hour)
	if !found {
		return nil, fmt.Errorf("缓存不存在")
	}
	
	details := map[string]interface{}{
		"url":          content.URL,
		"content_type": content.ContentType,
		"size":         len(content.Data),
		"cached_at":    content.CachedAt,
		"expires_at":   content.ExpiresAt,
		"headers":      content.Headers,
	}
	
	return details, nil
}

// PreloadCache 预加载缓存
func (s *FileCacheService) PreloadCache(domain string, urls []string) (*model.CrawlJob, error) {
	// 这里应该调用爬虫服务批量爬取
	// 暂时返回模拟数据
	job := &model.CrawlJob{
		ID:        uint(time.Now().Unix()),
		SiteID:    1,
		Type:      "preload",
		Status:    "pending",
		CreatedAt: time.Now(),
	}
	
	return job, nil
}

// shouldMinify 判断是否应该压缩内容
func (s *FileCacheService) shouldMinify(contentType string) bool {
	contentType = strings.ToLower(contentType)
	return strings.Contains(contentType, "text/html") ||
		   strings.Contains(contentType, "text/css") ||
		   strings.Contains(contentType, "application/javascript") ||
		   strings.Contains(contentType, "text/javascript")
}

// minifyContent 使用minify库压缩内容
func (s *FileCacheService) minifyContent(data []byte, contentType string) ([]byte, error) {
	var buf bytes.Buffer
	err := s.minifier.Minify(contentType, &buf, bytes.NewReader(data))
	if err != nil {
		return data, err
	}
	return buf.Bytes(), nil
}

