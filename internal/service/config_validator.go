package service

import (
	"fmt"
	"runtime"
)

// ConfigValidator 配置验证器
type ConfigValidator struct {
	cpuCores int
	memoryGB float64
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator() *ConfigValidator {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	return &ConfigValidator{
		cpuCores: runtime.NumCPU(),
		memoryGB: float64(m.Sys) / 1024 / 1024 / 1024,
	}
}

// ValidateSystemSettings 验证系统设置参数
func (v *ConfigValidator) ValidateSystemSettings(params UpdateSystemSettingsParams) error {
	// 验证超时配置
	if err := v.validateTimeouts(params); err != nil {
		return err
	}
	
	// 验证并发配置
	if err := v.validateConcurrency(params); err != nil {
		return err
	}
	
	// 验证连接池配置
	if err := v.validateConnectionPools(params); err != nil {
		return err
	}
	
	// 验证缓存配置
	if err := v.validateCache(params); err != nil {
		return err
	}
	
	// 验证日志配置
	if err := v.validateLogging(params); err != nil {
		return err
	}
	
	return nil
}

// validateTimeouts 验证超时配置
func (v *ConfigValidator) validateTimeouts(params UpdateSystemSettingsParams) error {
	// ProxyRequestTimeout: 5-300秒（代理请求）
	if params.ProxyRequestTimeout > 0 {
		if params.ProxyRequestTimeout < 5000 || params.ProxyRequestTimeout > 300000 {
			return fmt.Errorf("代理请求超时必须在 5-300 秒之间")
		}
	}
	
	// HTTPRequestTimeout: 100ms-30秒（普通HTTP请求）
	if params.HTTPRequestTimeout > 0 {
		if params.HTTPRequestTimeout < 100 || params.HTTPRequestTimeout > 30000 {
			return fmt.Errorf("HTTP请求超时必须在 100ms-30秒 之间")
		}
	}
	
	// DatabaseQueryTimeout: 100ms-10秒
	if params.DatabaseQueryTimeout > 0 {
		if params.DatabaseQueryTimeout < 100 || params.DatabaseQueryTimeout > 10000 {
			return fmt.Errorf("数据库查询超时必须在 100ms-10秒 之间")
		}
	}
	
	// RedisOpTimeout: 10ms-5秒
	if params.RedisOpTimeout > 0 {
		if params.RedisOpTimeout < 10 || params.RedisOpTimeout > 5000 {
			return fmt.Errorf("Redis操作超时必须在 10ms-5秒 之间")
		}
	}
	
	// FileOpTimeout: 100ms-30秒
	if params.FileOpTimeout > 0 {
		if params.FileOpTimeout < 100 || params.FileOpTimeout > 30000 {
			return fmt.Errorf("文件操作超时必须在 100ms-30秒 之间")
		}
	}
	
	// CrawlerTaskTimeout: 5-600秒
	if params.CrawlerTaskTimeout > 0 {
		if params.CrawlerTaskTimeout < 5000 || params.CrawlerTaskTimeout > 600000 {
			return fmt.Errorf("爬虫任务超时必须在 5-600秒 之间")
		}
	}
	
	return nil
}

// validateConcurrency 验证并发配置
func (v *ConfigValidator) validateConcurrency(params UpdateSystemSettingsParams) error {
	maxRecommended := v.cpuCores * 20 // 每核最多20个并发
	
	// MaxDatabaseConn: 1-500，推荐不超过CPU核心数*10
	if params.MaxDatabaseConn > 0 {
		if params.MaxDatabaseConn < 1 || params.MaxDatabaseConn > 500 {
			return fmt.Errorf("数据库最大连接数必须在 1-500 之间")
		}
		if params.MaxDatabaseConn > v.cpuCores*10 {
			// 警告级别，不阻止
		}
	}
	
	// MaxRedisConn: 1-1000
	if params.MaxRedisConn > 0 {
		if params.MaxRedisConn < 1 || params.MaxRedisConn > 1000 {
			return fmt.Errorf("Redis最大连接数必须在 1-1000 之间")
		}
	}
	
	// MaxHTTPRequests: 1-200
	if params.MaxHTTPRequests > 0 {
		if params.MaxHTTPRequests < 1 || params.MaxHTTPRequests > 200 {
			return fmt.Errorf("HTTP最大并发请求数必须在 1-200 之间")
		}
		if params.MaxHTTPRequests > maxRecommended {
			// 警告：可能超过系统处理能力
		}
	}
	
	// MaxFileOps: 1-100
	if params.MaxFileOps > 0 {
		if params.MaxFileOps < 1 || params.MaxFileOps > 100 {
			return fmt.Errorf("文件操作最大并发数必须在 1-100 之间")
		}
	}
	
	// MaxCrawlerTasks: 1-100
	if params.MaxCrawlerTasks > 0 {
		if params.MaxCrawlerTasks < 1 || params.MaxCrawlerTasks > 100 {
			return fmt.Errorf("爬虫任务最大并发数必须在 1-100 之间")
		}
		// 推荐值：CPU核心数*2
		if params.MaxCrawlerTasks > v.cpuCores*4 {
			// 警告：可能影响性能
		}
	}
	
	return nil
}

// validateConnectionPools 验证连接池配置
func (v *ConfigValidator) validateConnectionPools(params UpdateSystemSettingsParams) error {
	// DBMaxOpenConns: 1-500
	if params.DBMaxOpenConns > 0 {
		if params.DBMaxOpenConns < 1 || params.DBMaxOpenConns > 500 {
			return fmt.Errorf("数据库最大打开连接数必须在 1-500 之间")
		}
		// 验证连接池大小 >= 并发限制
		if params.MaxDatabaseConn > 0 && params.DBMaxOpenConns < params.MaxDatabaseConn {
			return fmt.Errorf("数据库连接池大小(%d)不能小于并发限制(%d)，建议设为并发限制的2倍", 
				params.DBMaxOpenConns, params.MaxDatabaseConn)
		}
	}
	
	// DBMaxIdleConns: 1-DBMaxOpenConns
	if params.DBMaxIdleConns > 0 {
		if params.DBMaxIdleConns < 1 {
			return fmt.Errorf("数据库最大空闲连接数必须大于 0")
		}
		if params.DBMaxOpenConns > 0 && params.DBMaxIdleConns > params.DBMaxOpenConns {
			return fmt.Errorf("数据库最大空闲连接数不能超过最大打开连接数")
		}
	}
	
	// DBConnMaxLifetime: 0 或 60-3600秒
	if params.DBConnMaxLifetime > 0 {
		if params.DBConnMaxLifetime < 60 || params.DBConnMaxLifetime > 3600 {
			return fmt.Errorf("数据库连接最大生命周期必须在 60-3600秒 之间")
		}
	}
	
	// HTTPMaxIdleConns: 1-1000
	if params.HTTPMaxIdleConns > 0 {
		if params.HTTPMaxIdleConns < 1 || params.HTTPMaxIdleConns > 1000 {
			return fmt.Errorf("HTTP最大空闲连接数必须在 1-1000 之间")
		}
	}
	
	// HTTPMaxIdleConnsPerHost: 1-100
	if params.HTTPMaxIdleConnsPerHost > 0 {
		if params.HTTPMaxIdleConnsPerHost < 1 || params.HTTPMaxIdleConnsPerHost > 100 {
			return fmt.Errorf("HTTP每主机最大空闲连接数必须在 1-100 之间")
		}
	}
	
	// HTTPMaxConnsPerHost: 1-200
	if params.HTTPMaxConnsPerHost > 0 {
		if params.HTTPMaxConnsPerHost < 1 || params.HTTPMaxConnsPerHost > 200 {
			return fmt.Errorf("HTTP每主机最大连接数必须在 1-200 之间")
		}
		// 验证HTTP连接池大小 >= HTTP并发限制
		if params.MaxHTTPRequests > 0 && params.HTTPMaxConnsPerHost < params.MaxHTTPRequests {
			return fmt.Errorf("HTTP连接池大小(%d)不能小于HTTP并发限制(%d)，建议设为并发限制的2倍", 
				params.HTTPMaxConnsPerHost, params.MaxHTTPRequests)
		}
	}
	
	// HTTPIdleConnTimeout: 10-300秒
	if params.HTTPIdleConnTimeout > 0 {
		if params.HTTPIdleConnTimeout < 10 || params.HTTPIdleConnTimeout > 300 {
			return fmt.Errorf("HTTP空闲连接超时必须在 10-300秒 之间")
		}
	}
	
	// RedisMaxPoolSize: 10-500
	if params.RedisMaxPoolSize > 0 {
		if params.RedisMaxPoolSize < 10 || params.RedisMaxPoolSize > 500 {
			return fmt.Errorf("Redis连接池大小必须在 10-500 之间")
		}
	}
	
	return nil
}

// validateCache 验证缓存配置
func (v *ConfigValidator) validateCache(params UpdateSystemSettingsParams) error {
	// DefaultCacheHomeTTL: 30-10080分钟（30分钟到7天）
	if params.DefaultCacheHomeTTL > 0 {
		if params.DefaultCacheHomeTTL < 30 || params.DefaultCacheHomeTTL > 10080 {
			return fmt.Errorf("首页缓存TTL必须在 30分钟-7天 之间")
		}
	}
	
	// DefaultCacheOtherTTL: 30-10080分钟
	if params.DefaultCacheOtherTTL > 0 {
		if params.DefaultCacheOtherTTL < 30 || params.DefaultCacheOtherTTL > 10080 {
			return fmt.Errorf("其他页面缓存TTL必须在 30分钟-7天 之间")
		}
	}
	
	// Cache404TTL: 5-1440分钟（5分钟到1天）
	if params.Cache404TTL > 0 {
		if params.Cache404TTL < 5 || params.Cache404TTL > 1440 {
			return fmt.Errorf("404缓存TTL必须在 5分钟-1天 之间")
		}
	}
	
	// CacheLockTimeout: 100-10000毫秒
	if params.CacheLockTimeout > 0 {
		if params.CacheLockTimeout < 100 || params.CacheLockTimeout > 10000 {
			return fmt.Errorf("缓存锁超时必须在 100ms-10秒 之间")
		}
	}
	
	// CacheLockRetryInterval: 10-1000毫秒
	if params.CacheLockRetryInterval > 0 {
		if params.CacheLockRetryInterval < 10 || params.CacheLockRetryInterval > 1000 {
			return fmt.Errorf("缓存锁重试间隔必须在 10ms-1秒 之间")
		}
	}
	
	// AsyncQueueSize: 100-100000
	if params.AsyncQueueSize > 0 {
		if params.AsyncQueueSize < 100 || params.AsyncQueueSize > 100000 {
			return fmt.Errorf("异步队列大小必须在 100-100000 之间")
		}
	}
	
	// AsyncWorkerCount: 1-100
	if params.AsyncWorkerCount > 0 {
		if params.AsyncWorkerCount < 1 || params.AsyncWorkerCount > 100 {
			return fmt.Errorf("异步工作线程数必须在 1-100 之间")
		}
	}
	
	return nil
}

// validateLogging 验证日志配置
func (v *ConfigValidator) validateLogging(params UpdateSystemSettingsParams) error {
	// LogLevel 必须是有效值
	if params.LogLevel != "" {
		validLevels := []string{"debug", "info", "warn", "error", "fatal"}
		valid := false
		for _, level := range validLevels {
			if params.LogLevel == level {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("日志级别必须是: debug, info, warn, error, fatal 之一")
		}
	}
	
	// LogStorage 必须是有效值
	if params.LogStorage != "" {
		validStorages := []string{"file", "console", "both"}
		valid := false
		for _, storage := range validStorages {
			if params.LogStorage == storage {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("日志存储方式必须是: file, console, both 之一")
		}
	}
	
	// LogRetentionDays: 1-365天
	if params.LogRetentionDays > 0 {
		if params.LogRetentionDays < 1 || params.LogRetentionDays > 365 {
			return fmt.Errorf("日志保留天数必须在 1-365 之间")
		}
	}
	
	// LogMaxSize: 1-1000 MB
	if params.LogMaxSize > 0 {
		if params.LogMaxSize < 1 || params.LogMaxSize > 1000 {
			return fmt.Errorf("日志文件最大大小必须在 1-1000MB 之间")
		}
	}
	
	// LogMaxBackups: 1-100
	if params.LogMaxBackups > 0 {
		if params.LogMaxBackups < 1 || params.LogMaxBackups > 100 {
			return fmt.Errorf("日志备份数量必须在 1-100 之间")
		}
	}
	
	// CrawlerRateLimit: 0.1-100 请求/秒
	if params.CrawlerRateLimit > 0 {
		if params.CrawlerRateLimit < 0.1 || params.CrawlerRateLimit > 100 {
			return fmt.Errorf("爬虫速率限制必须在 0.1-100 请求/秒 之间")
		}
	}
	
	return nil
}

// GetRecommendedSettings 获取推荐配置
func (v *ConfigValidator) GetRecommendedSettings(siteCount int) map[string]interface{} {
	recommendations := make(map[string]interface{})
	
	// 基于站点数量的推荐
	if siteCount <= 100 {
		// 小规模：100个站点以内
		recommendations["db_max_open_conns"] = 50
		recommendations["db_max_idle_conns"] = 10
		recommendations["http_max_idle_conns"] = 100
		recommendations["max_crawler_tasks"] = v.cpuCores * 2
		recommendations["max_http_requests"] = 20
		recommendations["redis_max_pool_size"] = 50
	} else if siteCount <= 500 {
		// 中等规模：100-500个站点
		recommendations["db_max_open_conns"] = 200
		recommendations["db_max_idle_conns"] = 50
		recommendations["http_max_idle_conns"] = 300
		recommendations["max_crawler_tasks"] = v.cpuCores * 3
		recommendations["max_http_requests"] = 40
		recommendations["redis_max_pool_size"] = 150
	} else {
		// 大规模：500个站点以上
		recommendations["db_max_open_conns"] = 400
		recommendations["db_max_idle_conns"] = 100
		recommendations["http_max_idle_conns"] = 500
		recommendations["max_crawler_tasks"] = v.cpuCores * 4
		recommendations["max_http_requests"] = 80
		recommendations["redis_max_pool_size"] = 300
	}
	
	// 基于CPU核心数的推荐
	recommendations["async_worker_count"] = v.cpuCores * 2
	recommendations["max_file_ops"] = v.cpuCores * 3
	recommendations["max_database_conn"] = v.cpuCores * 10
	
	// 基于内存的推荐
	if v.memoryGB >= 16 {
		recommendations["async_queue_size"] = 50000
		recommendations["cache_lock_timeout"] = 3000
	} else if v.memoryGB >= 8 {
		recommendations["async_queue_size"] = 20000
		recommendations["cache_lock_timeout"] = 2000
	} else {
		recommendations["async_queue_size"] = 10000
		recommendations["cache_lock_timeout"] = 1000
	}
	
	return recommendations
}