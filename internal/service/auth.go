package service

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"

	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

type AuthService struct {
	logger     *zap.Logger
	adminRepo  repository.AdminRepository
}

func NewAuthService(logger *zap.Logger, adminRepo repository.AdminRepository) *AuthService {
	return &AuthService{
		logger:    logger,
		adminRepo: adminRepo,
	}
}

// CreateDefaultAdmin 创建默认管理员账号
func (s *AuthService) CreateDefaultAdmin() error {
	// 检查是否已存在管理员
	count, err := s.adminRepo.CountAdmins()
	if err != nil {
		return err
	}
	
	if count > 0 {
		return nil // 已存在管理员，不需要创建
	}
	
	// 创建默认管理员
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	
	admin := &model.Admin{
		Username: "admin",
		Password: string(hashedPassword),
		Nickname: "管理员",
		Status:   "active",
	}
	
	return s.adminRepo.CreateAdmin(admin)
}

// Login 管理员登录
func (s *AuthService) Login(username, password string) (*model.Admin, *model.Session, error) {
	// 查找管理员
	admin, err := s.adminRepo.GetAdminByUsername(username)
	if err != nil {
		return nil, nil, errors.New("用户名或密码错误")
	}
	
	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(password))
	if err != nil {
		return nil, nil, errors.New("用户名或密码错误")
	}
	
	// 检查账号状态
	if admin.Status != "active" {
		return nil, nil, errors.New("账号已被禁用")
	}
	
	// 更新最后登录时间
	admin.LastLogin = time.Now()
	if err := s.adminRepo.UpdateAdmin(admin); err != nil {
		s.logger.Error("更新登录时间失败", zap.Error(err))
	}
	
	// 创建会话
	session := &model.Session{
		AdminID:   admin.ID,
		Token:     s.generateToken(),
		ExpiredAt: time.Now().Add(24 * time.Hour), // 24小时有效期
	}
	
	if err := s.adminRepo.CreateSession(session); err != nil {
		return nil, nil, errors.New("创建会话失败")
	}
	
	return admin, session, nil
}

// Logout 登出
func (s *AuthService) Logout(token string) error {
	return s.adminRepo.DeleteSession(token)
}

// VerifySession 验证会话
func (s *AuthService) VerifySession(token string) (*model.Admin, error) {
	session, err := s.adminRepo.GetSessionByToken(token)
	if err != nil {
		return nil, errors.New("会话无效")
	}
	
	// 检查是否过期
	if time.Now().After(session.ExpiredAt) {
		// 删除过期会话
		s.adminRepo.DeleteSession(token)
		return nil, errors.New("会话已过期")
	}
	
	// 获取管理员信息
	admin, err := s.adminRepo.GetAdminByID(session.AdminID)
	if err != nil {
		return nil, errors.New("管理员不存在")
	}
	
	// 检查账号状态
	if admin.Status != "active" {
		return nil, errors.New("账号已被禁用")
	}
	
	return admin, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(adminID uint, oldPassword, newPassword string) error {
	admin, err := s.adminRepo.GetAdminByID(adminID)
	if err != nil {
		return errors.New("管理员不存在")
	}
	
	// 验证旧密码
	err = bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(oldPassword))
	if err != nil {
		return errors.New("原密码错误")
	}
	
	// 生成新密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	
	admin.Password = string(hashedPassword)
	return s.adminRepo.UpdateAdmin(admin)
}

// CleanExpiredSessions 清理过期会话
func (s *AuthService) CleanExpiredSessions() error {
	return s.adminRepo.DeleteExpiredSessions()
}

// generateToken 生成随机令牌
func (s *AuthService) generateToken() string {
	b := make([]byte, 32)
	rand.Read(b)
	return hex.EncodeToString(b)
}

// 管理员管理方法

// GetAdmins 获取管理员列表
func (s *AuthService) GetAdmins(page, pageSize int) ([]*model.Admin, int64, error) {
	return s.adminRepo.GetAdmins(page, pageSize)
}

// GetAdminByID 根据ID获取管理员
func (s *AuthService) GetAdminByID(id uint) (*model.Admin, error) {
	return s.adminRepo.GetAdminByID(id)
}

// CreateAdmin 创建管理员
func (s *AuthService) CreateAdmin(admin *model.Admin) error {
	// 检查用户名是否已存在
	if _, err := s.adminRepo.GetAdminByUsername(admin.Username); err == nil {
		return errors.New("用户名已存在")
	}
	
	return s.adminRepo.CreateAdmin(admin)
}

// UpdateAdmin 更新管理员
func (s *AuthService) UpdateAdmin(admin *model.Admin) error {
	return s.adminRepo.UpdateAdmin(admin)
}

// UpdateAdminPassword 更新管理员密码
func (s *AuthService) UpdateAdminPassword(adminID uint, hashedPassword string) error {
	admin, err := s.adminRepo.GetAdminByID(adminID)
	if err != nil {
		return errors.New("管理员不存在")
	}
	
	admin.Password = hashedPassword
	return s.adminRepo.UpdateAdmin(admin)
}

// DeleteAdmin 删除管理员
func (s *AuthService) DeleteAdmin(id uint) error {
	// 检查是否为系统管理员
	admin, err := s.adminRepo.GetAdminByID(id)
	if err != nil {
		return errors.New("管理员不存在")
	}
	
	if admin.Username == "admin" {
		return errors.New("不能删除系统管理员")
	}
	
	// 删除该管理员的所有会话
	sessions, _ := s.adminRepo.GetSessionsByAdminID(id)
	for _, session := range sessions {
		s.adminRepo.DeleteSession(session.Token)
	}
	
	return s.adminRepo.DeleteAdmin(id)
}