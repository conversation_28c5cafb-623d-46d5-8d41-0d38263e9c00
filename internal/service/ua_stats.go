package service

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"regexp"
	"site-cluster/internal/model"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UAStatsService UA统计服务
type UAStatsService struct {
	db                  *gorm.DB
	logger              *zap.Logger
	systemSettingsService *SystemSettingsService
	spiderBlockService  *SpiderBlockService
	
	// 内存缓冲区
	buffer     map[string]*model.UAStatsBuffer
	bufferMu   sync.RWMutex
	lastFlush  time.Time
	flushInterval time.Duration
	
	// 会话管理
	sessions   map[string]*model.VisitorSession // key: sessionID (IP+UA hash)
	sessionMu  sync.RWMutex
	sessionTTL time.Duration // 会话超时时间
	
	// 控制协程
	stopCh     chan struct{}
	wg         sync.WaitGroup
}

// NewUAStatsService 创建UA统计服务
func NewUAStatsService(db *gorm.DB, logger *zap.Logger, systemSettings *SystemSettingsService, spiderBlock *SpiderBlockService) *UAStatsService {
	uas := &UAStatsService{
		db:                    db,
		logger:                logger,
		systemSettingsService: systemSettings,
		spiderBlockService:    spiderBlock,
		buffer:                make(map[string]*model.UAStatsBuffer),
		sessions:              make(map[string]*model.VisitorSession),
		lastFlush:             time.Now(),
		flushInterval:         5 * time.Minute, // 5分钟批量写入一次
		sessionTTL:            15 * time.Minute, // 会话15分钟超时
		stopCh:                make(chan struct{}),
	}
	
	// 启动批量写入协程
	uas.wg.Add(1)
	go uas.flushWorker()
	
	return uas
}

// RecordUA 记录UA访问（带IP和资源类型）
func (uas *UAStatsService) RecordUA(userAgent string, ip string, resourceType string) {
	// 检查是否启用UA统计
	settings, err := uas.systemSettingsService.GetSystemSettings()
	if err != nil || !settings.EnableUAStats {
		return
	}
	
	// 空UA或IP不记录
	if userAgent == "" || ip == "" {
		return
	}
	
	// 确定资源类型
	if resourceType == "" {
		resourceType = "unknown"
	}
	
	// 计算UA哈希
	hash := uas.calculateHash(userAgent)
	
	// 解析UA信息
	info := uas.parseUA(userAgent)
	
	// 更新会话信息
	sessionID := uas.calculateHash(ip + userAgent)
	uas.updateSession(sessionID, ip, userAgent, resourceType)
	
	// 添加到缓冲区
	uas.bufferMu.Lock()
	defer uas.bufferMu.Unlock()
	
	if item, exists := uas.buffer[hash]; exists {
		item.Count++
		item.LastSeen = time.Now()
		// 更新资源统计
		if resourceType == "html" {
			item.PageViews++
		} else {
			item.ResourceHits++
		}
	} else {
		uas.buffer[hash] = &model.UAStatsBuffer{
			UAHash:     hash,
			UserAgent:  userAgent,
			Browser:    info.Browser,
			BrowserVer: info.BrowserVer,
			OS:         info.OS,
			OSVer:      info.OSVer,
			Device:     info.Device,
			DeviceName: info.DeviceName,
			SpiderName: info.SpiderName,
			SpiderKey:  info.SpiderKey,
			IsSpider:   info.IsSpider,
			Count:      1,
			LastSeen:   time.Now(),
			Sessions:   make(map[string]*model.VisitorSession),
			PageViews:  0,
			ResourceHits: 0,
		}
		// 初始化资源统计
		if resourceType == "html" {
			uas.buffer[hash].PageViews = 1
		} else {
			uas.buffer[hash].ResourceHits = 1
		}
	}
	
	// 关联会话到UA
	if uas.buffer[hash].Sessions == nil {
		uas.buffer[hash].Sessions = make(map[string]*model.VisitorSession)
	}
	uas.sessionMu.RLock()
	if session, exists := uas.sessions[sessionID]; exists {
		uas.buffer[hash].Sessions[sessionID] = session
	}
	uas.sessionMu.RUnlock()
}

// UAInfo UA解析结果
type UAInfo struct {
	Browser    string
	BrowserVer string
	OS         string
	OSVer      string
	Device     string
	DeviceName string
	SpiderName string
	SpiderKey  string  // 爬虫标识（用于拦截）
	IsSpider   bool
}

// parseUA 解析User-Agent
func (uas *UAStatsService) parseUA(userAgent string) UAInfo {
	info := UAInfo{
		Device: "Desktop", // 默认为桌面设备
	}
	
	ua := strings.ToLower(userAgent)
	
	// 检测爬虫
	spiders := map[string]string{
		"googlebot":        "Googlebot",
		"bingbot":          "Bingbot",
		"baiduspider":      "Baiduspider",
		"yandexbot":        "YandexBot",
		"facebookexternalhit": "Facebook",
		"twitterbot":       "TwitterBot",
		"linkedinbot":      "LinkedInBot",
		"whatsapp":         "WhatsApp",
		"slackbot":         "SlackBot",
		"discord":          "DiscordBot",
		"telegrambot":      "TelegramBot",
		"semrushbot":       "SemrushBot",
		"ahrefs":           "AhrefsBot",
		"mj12bot":          "MJ12bot",
		"dotbot":           "DotBot",
		"petalbot":         "PetalBot",
		"yisou":            "YisouSpider",
		"360spider":        "360Spider",
		"sogou":            "Sogou Spider",
		"bytespider":       "Bytespider",
	}
	
	for key, name := range spiders {
		if strings.Contains(ua, key) {
			info.SpiderName = name
			info.SpiderKey = key  // 保存爬虫标识（小写）
			info.IsSpider = true
			info.Device = "Bot"
			return info
		}
	}
	
	// 检测设备类型
	if strings.Contains(ua, "mobile") || strings.Contains(ua, "android") || strings.Contains(ua, "iphone") {
		info.Device = "Mobile"
	} else if strings.Contains(ua, "tablet") || strings.Contains(ua, "ipad") {
		info.Device = "Tablet"
	}
	
	// 解析浏览器
	browsers := []struct {
		pattern string
		name    string
		version *regexp.Regexp
	}{
		{"edg/", "Edge", regexp.MustCompile(`edg/([\d.]+)`)},
		{"edge/", "Edge", regexp.MustCompile(`edge/([\d.]+)`)},
		{"opr/", "Opera", regexp.MustCompile(`opr/([\d.]+)`)},
		{"chrome/", "Chrome", regexp.MustCompile(`chrome/([\d.]+)`)},
		{"safari/", "Safari", regexp.MustCompile(`version/([\d.]+).*safari`)},
		{"firefox/", "Firefox", regexp.MustCompile(`firefox/([\d.]+)`)},
		{"msie", "Internet Explorer", regexp.MustCompile(`msie ([\d.]+)`)},
		{"trident", "Internet Explorer", regexp.MustCompile(`rv:([\d.]+)`)},
	}
	
	for _, b := range browsers {
		if strings.Contains(ua, b.pattern) {
			info.Browser = b.name
			if b.version != nil {
				if matches := b.version.FindStringSubmatch(ua); len(matches) > 1 {
					info.BrowserVer = matches[1]
				}
			}
			break
		}
	}
	
	// 解析操作系统
	osPatterns := []struct {
		pattern string
		name    string
		version *regexp.Regexp
	}{
		{"windows nt 10.0", "Windows", nil},
		{"windows nt 6.3", "Windows", nil},
		{"windows nt 6.2", "Windows", nil},
		{"windows nt 6.1", "Windows", nil},
		{"windows", "Windows", nil},
		{"mac os x", "macOS", regexp.MustCompile(`mac os x ([\d_]+)`)},
		{"android", "Android", regexp.MustCompile(`android ([\d.]+)`)},
		{"iphone os", "iOS", regexp.MustCompile(`iphone os ([\d_]+)`)},
		{"ipad", "iPadOS", regexp.MustCompile(`os ([\d_]+)`)},
		{"linux", "Linux", nil},
		{"ubuntu", "Ubuntu", nil},
		{"debian", "Debian", nil},
		{"fedora", "Fedora", nil},
		{"centos", "CentOS", nil},
	}
	
	for _, os := range osPatterns {
		if strings.Contains(ua, os.pattern) {
			info.OS = os.name
			if os.version != nil {
				if matches := os.version.FindStringSubmatch(ua); len(matches) > 1 {
					info.OSVer = strings.Replace(matches[1], "_", ".", -1)
				}
			}
			break
		}
	}
	
	// 解析设备名称
	devicePatterns := []struct {
		pattern string
		name    string
	}{
		{"iphone", "iPhone"},
		{"ipad", "iPad"},
		{"macintosh", "Mac"},
		{"windows phone", "Windows Phone"},
		{"kindle", "Kindle"},
		{"xbox", "Xbox"},
		{"playstation", "PlayStation"},
		{"nintendo", "Nintendo"},
		{"samsung", "Samsung"},
		{"huawei", "Huawei"},
		{"xiaomi", "Xiaomi"},
		{"oppo", "OPPO"},
		{"vivo", "Vivo"},
		{"oneplus", "OnePlus"},
		{"pixel", "Google Pixel"},
	}
	
	for _, device := range devicePatterns {
		if strings.Contains(ua, device.pattern) {
			info.DeviceName = device.name
			break
		}
	}
	
	return info
}

// calculateHash 计算UA的MD5哈希
func (uas *UAStatsService) calculateHash(userAgent string) string {
	h := md5.New()
	h.Write([]byte(userAgent))
	return hex.EncodeToString(h.Sum(nil))
}

// updateSession 更新会话信息
func (uas *UAStatsService) updateSession(sessionID, ip, userAgent, resourceType string) {
	uas.sessionMu.Lock()
	defer uas.sessionMu.Unlock()
	
	now := time.Now()
	
	// 清理过期会话
	for id, session := range uas.sessions {
		if now.Sub(session.LastSeenAt) > uas.sessionTTL {
			delete(uas.sessions, id)
		}
	}
	
	// 更新或创建会话
	if session, exists := uas.sessions[sessionID]; exists {
		// 更新现有会话
		session.LastSeenAt = now
		session.TotalRequests++
		
		// 更新资源统计
		if session.ResourceStats == nil {
			session.ResourceStats = make(map[string]int)
		}
		session.ResourceStats[resourceType]++
		
		if resourceType == "html" {
			session.PageViews++
		}
		
		// 分析访问者类型
		uas.analyzeVisitorType(session)
	} else {
		// 创建新会话
		session := &model.VisitorSession{
			SessionID:     sessionID,
			IP:            ip,
			UserAgent:     userAgent,
			FirstSeenAt:   now,
			LastSeenAt:    now,
			ResourceStats: make(map[string]int),
			TotalRequests: 1,
			PageViews:     0,
			VisitorType:   "unknown",
			Confidence:    0,
		}
		
		session.ResourceStats[resourceType] = 1
		if resourceType == "html" {
			session.PageViews = 1
		}
		
		uas.sessions[sessionID] = session
	}
}

// analyzeVisitorType 分析访问者类型
func (uas *UAStatsService) analyzeVisitorType(session *model.VisitorSession) {
	// 基于资源加载模式判断
	htmlCount := session.ResourceStats["html"]
	cssCount := session.ResourceStats["css"]
	jsCount := session.ResourceStats["js"]
	imageCount := session.ResourceStats["image"]
	totalResources := session.TotalRequests
	
	// 计算资源比例
	var avgResourcesPerPage float32
	if htmlCount > 0 {
		avgResourcesPerPage = float32(totalResources) / float32(htmlCount)
	}
	
	// 判断逻辑
	if totalResources == 1 && htmlCount == 1 {
		// 只访问了HTML，可能是爬虫
		session.VisitorType = "crawler"
		session.Confidence = 0.8
	} else if avgResourcesPerPage > 5 && cssCount > 0 && jsCount > 0 {
		// 加载了完整的页面资源，可能是真实用户
		session.VisitorType = "real_user"
		session.Confidence = 0.9
	} else if avgResourcesPerPage > 2 && avgResourcesPerPage <= 5 {
		// 部分资源加载，可能是机器人或轻量级爬虫
		session.VisitorType = "bot"
		session.Confidence = 0.7
	} else if htmlCount > 3 && totalResources == htmlCount {
		// 多个HTML页面但没有其他资源，典型爬虫行为
		session.VisitorType = "crawler"
		session.Confidence = 0.95
	} else {
		// 无法确定
		session.VisitorType = "unknown"
		session.Confidence = 0.5
	}
	
	// 如果有图片加载，增加真实用户的可能性
	if imageCount > 0 && session.VisitorType != "crawler" {
		session.Confidence = minFloat32(session.Confidence+0.1, 1.0)
		if session.VisitorType == "unknown" {
			session.VisitorType = "real_user"
		}
	}
}

// minFloat32 返回较小值
func minFloat32(a, b float32) float32 {
	if a < b {
		return a
	}
	return b
}

// flushWorker 批量写入工作协程
func (uas *UAStatsService) flushWorker() {
	defer uas.wg.Done()
	
	ticker := time.NewTicker(uas.flushInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			uas.flush()
		case <-uas.stopCh:
			// 停止前最后一次刷新
			uas.flush()
			return
		}
	}
}

// FlushCache 立即将缓冲区数据写入数据库（公开方法）
func (uas *UAStatsService) FlushCache() {
	uas.flush()
	uas.logger.Info("UA统计缓存已手动刷新到数据库")
}

// flush 将缓冲区数据写入数据库
func (uas *UAStatsService) flush() {
	uas.bufferMu.Lock()
	if len(uas.buffer) == 0 {
		uas.bufferMu.Unlock()
		return
	}
	
	// 复制缓冲区并清空
	bufferCopy := make(map[string]*model.UAStatsBuffer)
	for k, v := range uas.buffer {
		bufferCopy[k] = v
	}
	uas.buffer = make(map[string]*model.UAStatsBuffer)
	uas.bufferMu.Unlock()
	
	// 批量更新数据库
	for _, item := range bufferCopy {
		// 计算会话统计
		sessionCount := len(item.Sessions)
		var totalConfidence float32
		var visitorType string
		var lastIP string
		var avgResources float32
		
		if sessionCount > 0 {
			// 统计访问者类型
			typeCount := make(map[string]int)
			for _, session := range item.Sessions {
				typeCount[session.VisitorType]++
				totalConfidence += session.Confidence
				lastIP = session.IP // 取最后一个IP
				if session.PageViews > 0 {
					avgResources += float32(session.TotalRequests) / float32(session.PageViews)
				}
			}
			
			// 选择最多的访问者类型
			maxCount := 0
			for vType, count := range typeCount {
				if count > maxCount {
					maxCount = count
					visitorType = vType
				}
			}
			
			// 计算平均置信度和资源数
			if sessionCount > 0 {
				totalConfidence /= float32(sessionCount)
				avgResources /= float32(sessionCount)
			}
		}
		
		var existing model.UAStatsSummary
		err := uas.db.Where("ua_hash = ?", item.UAHash).First(&existing).Error
		
		if err == gorm.ErrRecordNotFound {
			// 新记录
			summary := model.UAStatsSummary{
				UAHash:       item.UAHash,
				UserAgent:    item.UserAgent,
				Browser:      item.Browser,
				BrowserVer:   item.BrowserVer,
				OS:           item.OS,
				OSVer:        item.OSVer,
				Device:       item.Device,
				DeviceName:   item.DeviceName,
				SpiderName:   item.SpiderName,
				SpiderKey:    item.SpiderKey,
				IsSpider:     item.IsSpider,
				HitCount:     item.Count,
				SessionCount: sessionCount,
				PageViews:    item.PageViews,
				ResourceHits: item.ResourceHits,
				VisitorType:  visitorType,
				Confidence:   totalConfidence,
				AvgResources: avgResources,
				LastIP:       lastIP,
				LastSeenAt:   item.LastSeen,
				FirstSeenAt:  item.LastSeen,
			}
			if err := uas.db.Create(&summary).Error; err != nil {
				uas.logger.Error("创建UA统计记录失败", zap.Error(err))
			}
		} else if err == nil {
			// 更新现有记录
			updates := map[string]interface{}{
				"hit_count":     gorm.Expr("hit_count + ?", item.Count),
				"session_count": gorm.Expr("session_count + ?", sessionCount),
				"page_views":    gorm.Expr("page_views + ?", item.PageViews),
				"resource_hits": gorm.Expr("resource_hits + ?", item.ResourceHits),
				"visitor_type":  visitorType,
				"confidence":    totalConfidence,
				"avg_resources": avgResources,
				"last_ip":       lastIP,
				"last_seen_at":  item.LastSeen,
			}
			// 如果有spider_key就更新
			if item.SpiderKey != "" {
				updates["spider_key"] = item.SpiderKey
			}
			if err := uas.db.Model(&existing).Updates(updates).Error; err != nil {
				uas.logger.Error("更新UA统计记录失败", zap.Error(err))
			}
		} else {
			uas.logger.Error("查询UA统计记录失败", zap.Error(err))
		}
	}
	
	uas.logger.Info("UA统计批量写入完成", zap.Int("count", len(bufferCopy)))
}

// GetStats 获取UA统计数据
func (uas *UAStatsService) GetStats(limit int, sortBy string) ([]model.UAStatsSummary, error) {
	var stats []model.UAStatsSummary
	
	query := uas.db.Model(&model.UAStatsSummary{})
	
	// 根据排序字段
	switch sortBy {
	case "hit_count":
		query = query.Order("hit_count DESC")
	case "last_seen":
		query = query.Order("last_seen_at DESC")
	case "first_seen":
		query = query.Order("first_seen_at DESC")
	case "session_count":
		query = query.Order("session_count DESC")
	case "page_views":
		query = query.Order("page_views DESC")
	default:
		query = query.Order("hit_count DESC")
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&stats).Error
	return stats, err
}

// GetStatsPaginated 获取分页的UA统计数据
func (uas *UAStatsService) GetStatsPaginated(page, pageSize int, sortBy string) ([]model.UAStatsSummary, int64, error) {
	return uas.GetStatsWithFilter(page, pageSize, sortBy, "", "", "")
}

// GetStatsWithFilter 获取带筛选的分页UA统计数据
func (uas *UAStatsService) GetStatsWithFilter(page, pageSize int, sortBy, filterType, deviceType, searchQuery string) ([]model.UAStatsSummary, int64, error) {
	var stats []model.UAStatsSummary
	var total int64
	
	offset := (page - 1) * pageSize
	
	// 构建基础查询
	query := uas.db.Model(&model.UAStatsSummary{})
	
	// 应用访问者类型筛选
	if filterType != "" {
		switch filterType {
		case "real_user":
			query = query.Where("visitor_type = ?", "real_user")
		case "crawler":
			query = query.Where("visitor_type = ? OR is_spider = ?", "crawler", true)
		case "bot":
			query = query.Where("visitor_type = ?", "bot")
		case "unknown":
			query = query.Where("visitor_type = ? OR visitor_type IS NULL OR visitor_type = ''", "unknown")
		}
	}
	
	// 应用设备类型筛选
	if deviceType != "" {
		query = query.Where("device = ?", deviceType)
	}
	
	// 应用搜索条件
	if searchQuery != "" {
		searchPattern := "%" + searchQuery + "%"
		query = query.Where(
			"user_agent ILIKE ? OR browser ILIKE ? OR os ILIKE ? OR device ILIKE ? OR spider_name ILIKE ?",
			searchPattern, searchPattern, searchPattern, searchPattern, searchPattern,
		)
	}
	
	// 先获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 根据排序字段
	switch sortBy {
	case "hit_count":
		query = query.Order("hit_count DESC")
	case "last_seen":
		query = query.Order("last_seen_at DESC")
	case "first_seen":
		query = query.Order("first_seen_at DESC")
	case "session_count":
		query = query.Order("session_count DESC")
	case "page_views":
		query = query.Order("page_views DESC")
	case "confidence":
		query = query.Order("confidence DESC")
	default:
		query = query.Order("hit_count DESC")
	}
	
	// 分页查询
	err := query.Offset(offset).Limit(pageSize).Find(&stats).Error
	if err != nil {
		return stats, total, err
	}
	
	// 检查每个UA是否已被拦截
	var blockedUAs []model.SpiderBlock
	if err := uas.db.Where("enabled = ?", true).Find(&blockedUAs).Error; err == nil {
		// 只有当有拦截规则时才进行检查
		if len(blockedUAs) > 0 {
			uas.logger.Info("检查拦截状态", 
				zap.Int("规则数量", len(blockedUAs)),
				zap.Any("第一条规则", blockedUAs[0].UserAgent))
			
			for i := range stats {
				stats[i].IsBlocked = false
				stats[i].BlockRuleID = 0 // 初始化为0
				
				// 如果是爬虫但没有spider_key（旧数据），尝试提取
				if stats[i].IsSpider && stats[i].SpiderKey == "" {
					info := uas.parseUA(stats[i].UserAgent)
					if info.SpiderKey != "" {
						stats[i].SpiderKey = info.SpiderKey
						// 更新数据库中的spider_key
						uas.db.Model(&stats[i]).Update("spider_key", info.SpiderKey)
					}
				}
				
				// 只有存在拦截规则时才检查
				for _, block := range blockedUAs {
					blockUA := strings.ToLower(strings.TrimSpace(block.UserAgent))
					if blockUA == "" {
						continue // 跳过空规则
					}
					
					matched := false
					
					// 优先使用spider_key精确匹配
					if stats[i].SpiderKey != "" {
						// 精确匹配spider_key
						if strings.EqualFold(stats[i].SpiderKey, blockUA) {
							matched = true
							uas.logger.Info("通过spider_key匹配到拦截规则",
								zap.String("spider_key", stats[i].SpiderKey),
								zap.String("规则", blockUA))
						}
					}
					
					// 如果没有匹配到，且是爬虫，尝试UA字符串匹配
					if !matched && (stats[i].IsSpider || stats[i].VisitorType == "crawler" || stats[i].VisitorType == "bot") {
						// 精确包含匹配，避免误判
						uaLower := strings.ToLower(stats[i].UserAgent)
						if strings.Contains(uaLower, blockUA) {
							matched = true
							uas.logger.Info("通过UA字符串匹配到拦截规则",
								zap.String("UA", stats[i].UserAgent),
								zap.String("规则", blockUA))
						}
					}
					
					if matched {
						stats[i].IsBlocked = true
						stats[i].BlockRuleID = block.ID // 记录规则ID便于删除
						break
					}
				}
			}
		} else {
			// 没有任何拦截规则，所有UA都不应该被标记为已拦截
			for i := range stats {
				stats[i].IsBlocked = false
				stats[i].BlockRuleID = 0
				
				// 仍然更新spider_key（如果需要）
				if stats[i].IsSpider && stats[i].SpiderKey == "" {
					info := uas.parseUA(stats[i].UserAgent)
					if info.SpiderKey != "" {
						stats[i].SpiderKey = info.SpiderKey
						uas.db.Model(&stats[i]).Update("spider_key", info.SpiderKey)
					}
				}
			}
		}
	} else {
		// 查询失败，所有UA都不应该被标记为已拦截
		for i := range stats {
			stats[i].IsBlocked = false
			stats[i].BlockRuleID = 0
		}
	}
	
	return stats, total, err
}

// GetStatsByType 按类型获取统计
func (uas *UAStatsService) GetStatsByType(statType string) (map[string]int64, error) {
	result := make(map[string]int64)
	
	var field string
	switch statType {
	case "browser":
		field = "browser"
	case "os":
		field = "os"
	case "device":
		field = "device"
	case "spider":
		field = "spider_name"
	default:
		return nil, fmt.Errorf("unknown stat type: %s", statType)
	}
	
	// 对于spider类型，只统计爬虫
	query := uas.db.Model(&model.UAStatsSummary{})
	if statType == "spider" {
		query = query.Where("is_spider = ?", true)
	}
	
	rows, err := query.
		Select(field + " as name, SUM(hit_count) as count").
		Where(field + " != ''").
		Group(field).
		Rows()
	
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	for rows.Next() {
		var name string
		var count int64
		if err := rows.Scan(&name, &count); err != nil {
			continue
		}
		result[name] = count
	}
	
	return result, nil
}

// GetTotalStats 获取总体统计
func (uas *UAStatsService) GetTotalStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// 总UA数
	var totalUA int64
	uas.db.Model(&model.UAStatsSummary{}).Count(&totalUA)
	stats["total_ua"] = totalUA
	
	// 总访问次数
	var totalHits int64
	uas.db.Model(&model.UAStatsSummary{}).Select("SUM(hit_count)").Scan(&totalHits)
	stats["total_hits"] = totalHits
	
	// 爬虫UA数
	var spiderUA int64
	uas.db.Model(&model.UAStatsSummary{}).Where("is_spider = ?", true).Count(&spiderUA)
	stats["spider_ua"] = spiderUA
	
	// 普通UA数
	stats["normal_ua"] = totalUA - spiderUA
	
	// 获取最活跃的UA
	var topUA model.UAStatsSummary
	if err := uas.db.Order("hit_count DESC").First(&topUA).Error; err == nil {
		stats["top_ua"] = map[string]interface{}{
			"user_agent": topUA.UserAgent,
			"hit_count":  topUA.HitCount,
			"browser":    topUA.Browser,
			"os":         topUA.OS,
		}
	}
	
	return stats, nil
}

// ClearStats 清空统计数据
func (uas *UAStatsService) ClearStats() error {
	// 清空缓冲区
	uas.bufferMu.Lock()
	uas.buffer = make(map[string]*model.UAStatsBuffer)
	uas.bufferMu.Unlock()
	
	// 清空数据库
	return uas.db.Where("1 = 1").Delete(&model.UAStatsSummary{}).Error
}

// GetUADetail 获取特定UA的详细信息
func (uas *UAStatsService) GetUADetail(hash string) (*model.UAStatsSummary, error) {
	var ua model.UAStatsSummary
	err := uas.db.Where("ua_hash = ?", hash).First(&ua).Error
	return &ua, err
}

// Stop 停止服务
func (uas *UAStatsService) Stop() {
	close(uas.stopCh)
	uas.wg.Wait()
}