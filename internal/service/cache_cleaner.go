package service

import (
	"os"
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
)

// CacheCleaner 缓存清理服务
type CacheCleaner struct {
	logger          *zap.Logger
	cacheService    *FileCacheService
	redisService    *RedisCacheService
	settingsService *SystemSettingsService
	ticker          *time.Ticker
	stopChan        chan struct{}
	mu              sync.Mutex
	isRunning       bool
}

// NewCacheCleaner 创建缓存清理服务
func NewCacheCleaner(
	logger *zap.Logger,
	cacheService *FileCacheService,
	redisService *RedisCacheService,
	settingsService *SystemSettingsService,
) *CacheCleaner {
	return &CacheCleaner{
		logger:          logger,
		cacheService:    cacheService,
		redisService:    redisService,
		settingsService: settingsService,
		stopChan:        make(chan struct{}),
	}
}

// Start 启动自动清理
func (cc *CacheCleaner) Start() {
	cc.mu.Lock()
	defer cc.mu.Unlock()
	
	if cc.isRunning {
		return
	}
	
	// 获取清理间隔（小时）
	interval := cc.getCleanupInterval()
	if interval <= 0 {
		interval = 24 // 默认24小时
	}
	
	cc.ticker = time.NewTicker(time.Duration(interval) * time.Hour)
	cc.isRunning = true
	
	go cc.cleanupLoop()
	
	cc.logger.Info("缓存自动清理服务已启动", 
		zap.Int("interval_hours", interval))
}

// Stop 停止自动清理
func (cc *CacheCleaner) Stop() {
	cc.mu.Lock()
	defer cc.mu.Unlock()
	
	if !cc.isRunning {
		return
	}
	
	if cc.ticker != nil {
		cc.ticker.Stop()
	}
	
	close(cc.stopChan)
	cc.isRunning = false
	
	cc.logger.Info("缓存自动清理服务已停止")
}

// cleanupLoop 清理循环
func (cc *CacheCleaner) cleanupLoop() {
	// 启动时先执行一次清理
	cc.performCleanup()
	
	for {
		select {
		case <-cc.ticker.C:
			cc.performCleanup()
		case <-cc.stopChan:
			return
		}
	}
}

// performCleanup 执行清理
func (cc *CacheCleaner) performCleanup() {
	startTime := time.Now()
	cc.logger.Info("开始清理缓存")
	
	// 1. 清理过期的文件缓存
	filesDeleted := cc.cleanFileCache()
	
	// 2. 清理Redis过期键（Redis自动处理，这里只是触发检查）
	cc.cleanRedisCache()
	
	// 3. 清理临时文件
	tempDeleted := cc.cleanTempFiles()
	
	// 4. 清理超大缓存文件
	largeDeleted := cc.cleanLargeFiles()
	
	duration := time.Since(startTime)
	cc.logger.Info("缓存清理完成",
		zap.Int("files_deleted", filesDeleted),
		zap.Int("temp_deleted", tempDeleted),
		zap.Int("large_deleted", largeDeleted),
		zap.Duration("duration", duration))
}

// cleanFileCache 清理文件缓存
func (cc *CacheCleaner) cleanFileCache() int {
	if cc.cacheService == nil {
		return 0
	}
	
	count := 0
	cacheDir := "./cache" // 使用默认缓存目录
	
	// 获取过期时间（天）
	expireDays := cc.getExpireDays()
	if expireDays <= 0 {
		expireDays = 7 // 默认7天
	}
	
	expireTime := time.Now().Add(-time.Duration(expireDays) * 24 * time.Hour)
	
	err := filepath.Walk(cacheDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		
		// 跳过目录
		if info.IsDir() {
			return nil
		}
		
		// 检查文件修改时间
		if info.ModTime().Before(expireTime) {
			if err := os.Remove(path); err == nil {
				count++
				cc.logger.Debug("删除过期缓存文件", 
					zap.String("file", path),
					zap.Time("mod_time", info.ModTime()))
			}
		}
		
		return nil
	})
	
	if err != nil {
		cc.logger.Error("清理文件缓存失败", zap.Error(err))
	}
	
	return count
}

// cleanRedisCache 清理Redis缓存
func (cc *CacheCleaner) cleanRedisCache() {
	if cc.redisService == nil {
		return
	}
	
	// Redis会自动处理过期键，这里可以执行一些额外的清理
	// 比如清理特定前缀的键
	
	// 触发Redis的过期键清理
	client := cc.redisService.GetClient()
	if client != nil {
		// 随机删除一些过期键
		client.RandomKey(cc.redisService.ctx)
	}
}

// cleanTempFiles 清理临时文件
func (cc *CacheCleaner) cleanTempFiles() int {
	count := 0
	tempDirs := []string{
		"/tmp/site-cluster",
		"./temp",
		"./tmp",
	}
	
	for _, dir := range tempDirs {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			continue
		}
		
		files, err := os.ReadDir(dir)
		if err != nil {
			continue
		}
		
		for _, file := range files {
			if file.IsDir() {
				continue
			}
			
			info, err := file.Info()
			if err != nil {
				continue
			}
			
			// 删除超过24小时的临时文件
			if time.Since(info.ModTime()) > 24*time.Hour {
				path := filepath.Join(dir, file.Name())
				if err := os.Remove(path); err == nil {
					count++
				}
			}
		}
	}
	
	return count
}

// cleanLargeFiles 清理超大缓存文件
func (cc *CacheCleaner) cleanLargeFiles() int {
	if cc.cacheService == nil {
		return 0
	}
	
	count := 0
	cacheDir := "./cache" // 使用默认缓存目录
	maxSize := int64(10 * 1024 * 1024) // 10MB
	
	err := filepath.Walk(cacheDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		
		// 跳过目录
		if info.IsDir() {
			return nil
		}
		
		// 检查文件大小
		if info.Size() > maxSize {
			// 只删除超过3天的大文件
			if time.Since(info.ModTime()) > 3*24*time.Hour {
				if err := os.Remove(path); err == nil {
					count++
					cc.logger.Info("删除超大缓存文件",
						zap.String("file", path),
						zap.Int64("size", info.Size()))
				}
			}
		}
		
		return nil
	})
	
	if err != nil {
		cc.logger.Error("清理大文件失败", zap.Error(err))
	}
	
	return count
}

// getCleanupInterval 获取清理间隔（小时）
func (cc *CacheCleaner) getCleanupInterval() int {
	if cc.settingsService == nil {
		return 24
	}
	
	settings, err := cc.settingsService.GetSystemSettings()
	if err != nil || settings == nil {
		return 24
	}
	
	// 如果没有这个字段，添加默认值
	// TODO: 需要在SystemSettings模型中添加CacheCleanupInterval字段
	return 24 // 暂时返回默认值
}

// getExpireDays 获取过期天数
func (cc *CacheCleaner) getExpireDays() int {
	if cc.settingsService == nil {
		return 7
	}
	
	settings, err := cc.settingsService.GetSystemSettings()
	if err != nil || settings == nil {
		return 7
	}
	
	// 暂时使用默认值
	// TODO: 在SystemSettings中添加CacheExpireDays字段
	
	return 7
}

// ManualCleanup 手动触发清理
func (cc *CacheCleaner) ManualCleanup() {
	go cc.performCleanup()
}