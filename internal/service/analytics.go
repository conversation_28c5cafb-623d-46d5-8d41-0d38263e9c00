package service

import (
	"crypto/md5"
	"fmt"
	"os"
	"path/filepath"
	"time"
	"site-cluster/internal/model"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AnalyticsService struct {
	db     *gorm.DB
	logger *zap.Logger
}

func NewAnalyticsService(db *gorm.DB, logger *zap.Logger) *AnalyticsService {
	return &AnalyticsService{
		db:     db,
		logger: logger,
	}
}

// GetAnalytics 获取统计设置
func (as *AnalyticsService) GetAnalytics() (*model.Analytics, error) {
	var analytics model.Analytics
	
	// 明确指定查询所有字段，包括auto_refresh
	err := as.db.Select("id, code, enabled, refresh_interval, auto_refresh, last_refresh, created_at, updated_at").First(&analytics).Error
	if err == gorm.ErrRecordNotFound {
		// 如果不存在，创建一个默认的
		analytics = model.Analytics{
			Code:            "",
			Enabled:         false,
			RefreshInterval: 60,
			AutoRefresh:     false,
		}
		if err := as.db.Create(&analytics).Error; err != nil {
			return nil, err
		}
	} else if err != nil {
		return nil, err
	}
	
	// 确保返回的结构包含所有字段
	// as.logger.Info("获取Analytics设置完成", 
	//	zap.Uint("id", analytics.ID),
	//	zap.Bool("enabled", analytics.Enabled),
	//	zap.Bool("auto_refresh", analytics.AutoRefresh),
	//	zap.Int("refresh_interval", analytics.RefreshInterval))
	
	return &analytics, nil
}

// UpdateAnalytics 更新统计设置
func (as *AnalyticsService) UpdateAnalytics(code string, enabled bool, refreshInterval int, autoRefresh bool) error {
	analytics, err := as.GetAnalytics()
	if err != nil {
		return err
	}

	// 添加调试日志
	// as.logger.Info("UpdateAnalytics开始", 
	//	zap.Bool("current_auto_refresh", analytics.AutoRefresh),
	//	zap.Bool("new_auto_refresh", autoRefresh))

	analytics.Code = code
	analytics.Enabled = enabled
	analytics.AutoRefresh = autoRefresh
	if refreshInterval > 0 {
		analytics.RefreshInterval = refreshInterval
	}

	// 再次确认更新前的值
	// as.logger.Info("保存前的analytics值", 
	//	zap.Bool("auto_refresh", analytics.AutoRefresh))

	if err := as.db.Save(analytics).Error; err != nil {
		return err
	}

	// 保存后验证
	// var saved model.Analytics
	// if err := as.db.First(&saved).Error; err == nil {
	//	as.logger.Info("保存后验证", 
	//		zap.Bool("saved_auto_refresh", saved.AutoRefresh))
	// }

	// 更新所有域名的统计JS文件
	if enabled {
		return as.updateAllDomainAnalyticsFiles()
	}
	return nil
}

// GetDomainMD5 获取域名的MD5标识（前12位）
func (as *AnalyticsService) GetDomainMD5(domain string) string {
	hash := md5.Sum([]byte(domain))
	return fmt.Sprintf("%x", hash)[:12]
}

// GenerateAnalyticsJS 为指定域名生成统计JS文件
func (as *AnalyticsService) GenerateAnalyticsJS(domain string) error {
	analytics, err := as.GetAnalytics()
	if err != nil {
		return err
	}

	// 获取域名MD5标识
	domainMD5 := as.GetDomainMD5(domain)
	
	// 创建域名对应的缓存目录（如果不存在）
	domainCacheDir := filepath.Join("./cache", domain)
	if err := os.MkdirAll(domainCacheDir, 0755); err != nil {
		return err
	}

	// 生成JS文件路径（放在域名对应的目录下）
	jsPath := filepath.Join(domainCacheDir, fmt.Sprintf("%s.js", domainMD5))

	// 写入统计代码到JS文件（即使代码为空也要创建文件）
	var content string
	if analytics.Enabled && analytics.Code != "" {
		content = fmt.Sprintf("/* Analytics for %s */\n%s", domain, analytics.Code)
	} else {
		content = fmt.Sprintf("/* Analytics for %s - No tracking code configured */", domain)
	}
	
	if err := os.WriteFile(jsPath, []byte(content), 0644); err != nil {
		return err
	}

	// as.logger.Info("生成统计JS文件", 
	//	zap.String("domain", domain),
	//	zap.String("md5", domainMD5),
	//	zap.String("path", jsPath))

	return nil
}

// updateAllDomainAnalyticsFiles 更新所有域名的统计文件
func (as *AnalyticsService) updateAllDomainAnalyticsFiles() error {
	var sites []model.Site
	if err := as.db.Find(&sites).Error; err != nil {
		return err
	}

	for _, site := range sites {
		if err := as.GenerateAnalyticsJS(site.Domain); err != nil {
			as.logger.Error("生成统计JS文件失败", 
				zap.String("domain", site.Domain),
				zap.Error(err))
		}
	}

	return nil
}

// GenerateCategoryAnalyticsJS 为指定域名生成分类独立统计JS文件
func (as *AnalyticsService) GenerateCategoryAnalyticsJS(domain string, analyticsCode string) error {
	// 获取域名MD5标识（使用相同的标识，覆盖全局统计）
	domainMD5 := as.GetDomainMD5(domain)
	
	// 创建域名对应的缓存目录（如果不存在）
	domainCacheDir := filepath.Join("./cache", domain)
	if err := os.MkdirAll(domainCacheDir, 0755); err != nil {
		return err
	}

	// 生成JS文件路径（使用相同的文件名，会覆盖全局统计）
	jsPath := filepath.Join(domainCacheDir, fmt.Sprintf("%s.js", domainMD5))

	// 写入统计代码到JS文件
	var content string
	if analyticsCode != "" {
		content = fmt.Sprintf("/* Category Analytics for %s */\n%s", domain, analyticsCode)
	} else {
		content = fmt.Sprintf("/* Category Analytics for %s - No tracking code configured */", domain)
	}
	
	if err := os.WriteFile(jsPath, []byte(content), 0644); err != nil {
		return err
	}

	return nil
}

// GetAnalyticsScript 获取要注入的统计脚本标签
func (as *AnalyticsService) GetAnalyticsScript(domain string) string {
	// 总是返回JS引用，即使统计代码为空
	domainMD5 := as.GetDomainMD5(domain)
	
	// 获取JS文件的修改时间作为版本号
	jsPath := filepath.Join("./cache", domain, fmt.Sprintf("%s.js", domainMD5))
	var timestamp int64
	if fileInfo, err := os.Stat(jsPath); err == nil {
		timestamp = fileInfo.ModTime().Unix()
	} else {
		// 如果文件不存在，使用当前时间
		timestamp = time.Now().Unix()
	}
	
	// 添加时间戳参数以防止CDN缓存
	return fmt.Sprintf(`<script src="/%s.js?t=%d" type="text/javascript"></script>`, domainMD5, timestamp)
}

// GetDB 获取数据库实例
func (as *AnalyticsService) GetDB() *gorm.DB {
	return as.db
}

// ShouldRefresh 检查是否需要刷新统计代码
func (as *AnalyticsService) ShouldRefresh() (bool, error) {
	analytics, err := as.GetAnalytics()
	if err != nil {
		return false, err
	}
	
	// 检查统计功能和自动刷新是否都启用
	if !analytics.Enabled || !analytics.AutoRefresh {
		return false, nil
	}
	
	// 如果从未刷新过，需要刷新
	if analytics.LastRefresh.IsZero() {
		return true, nil
	}
	
	// 检查刷新间隔
	interval := time.Duration(analytics.RefreshInterval) * time.Minute
	return time.Since(analytics.LastRefresh) > interval, nil
}

// UpdateLastRefresh 更新最后刷新时间
func (as *AnalyticsService) UpdateLastRefresh() error {
	analytics, err := as.GetAnalytics()
	if err != nil {
		return err
	}
	
	analytics.LastRefresh = time.Now()
	return as.db.Save(analytics).Error
}