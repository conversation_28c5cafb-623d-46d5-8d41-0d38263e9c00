package weight

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// AizhanAPI 爱站API客户端
type AizhanAPI struct {
	apiKey  string
	baseURL string
	client  *http.Client
}

// AizhanResponse 爱站API响应结构
type AizhanResponse struct {
	Code   int    `json:"code"`
	Status string `json:"status"`
	Data   struct {
		Success []DomainWeight `json:"success"`
		Failed  []string       `json:"failed"`
		Count   int            `json:"count"`
	} `json:"data"`
	Msg string `json:"msg"`
}

// DomainWeight 域名权重数据
type DomainWeight struct {
	Domain   string `json:"domain"`
	PCBR     int    `json:"pc_br"`     // PC端百度权重
	MobileBR int    `json:"m_br"`      // 移动端百度权重  
	IP       string `json:"ip"`        // 预估总流量
	PCIP     string `json:"pc_ip"`     // PC端预估流量
	MobileIP string `json:"m_ip"`      // 移动端预估流量
}

// NewAizhanAPI 创建爱站API客户端
func NewAizhanAPI(apiKey string) *AizhanAPI {
	return &AizhanAPI{
		apiKey:  apiKey,
		baseURL: "https://apistore.aizhan.com/baidurank/siteinfos",
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetBatchWeights 批量获取域名权重
func (a *AizhanAPI) GetBatchWeights(domains []string) (*AizhanResponse, error) {
	if len(domains) == 0 {
		return nil, fmt.Errorf("域名列表不能为空")
	}

	// 构建请求URL
	domainsStr := strings.Join(domains, "|")
	url := fmt.Sprintf("%s/%s?domains=%s", a.baseURL, a.apiKey, domainsStr)

	// 发送请求
	resp, err := a.client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求爱站API失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析JSON
	var result AizhanResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查API返回状态
	if result.Code != 200000 {
		return nil, fmt.Errorf("API返回错误: %s (code: %d)", result.Msg, result.Code)
	}

	return &result, nil
}

// GetSingleWeight 获取单个域名权重
func (a *AizhanAPI) GetSingleWeight(domain string) (*DomainWeight, error) {
	result, err := a.GetBatchWeights([]string{domain})
	if err != nil {
		return nil, err
	}

	if len(result.Data.Success) == 0 {
		return nil, fmt.Errorf("未获取到域名 %s 的权重数据", domain)
	}

	return &result.Data.Success[0], nil
}

// ValidateAPIKey 验证API密钥是否有效
func (a *AizhanAPI) ValidateAPIKey() error {
	// 使用一个测试域名验证API
	_, err := a.GetSingleWeight("www.baidu.com")
	if err != nil {
		if strings.Contains(err.Error(), "API返回错误") {
			return fmt.Errorf("API密钥无效或已过期")
		}
		return err
	}
	return nil
}