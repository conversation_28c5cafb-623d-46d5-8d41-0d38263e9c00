package weight

import (
	"fmt"
	"log"
	"sync"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// MonitorService 权重监测服务
type MonitorService struct {
	repo      repository.WeightRepository
	logger    *zap.Logger
	api       *AizhanAPI
	cron      *cron.Cron
	isRunning bool
	mu        sync.RWMutex
	config    *model.WeightMonitorConfig
}

// NewMonitorService 创建权重监测服务
func NewMonitorService(repo repository.WeightRepository, logger *zap.Logger) *MonitorService {
	return &MonitorService{
		repo:   repo,
		logger: logger,
		cron:   cron.New(),
	}
}

// Start 启动监测服务
func (s *MonitorService) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("监测服务已在运行")
	}

	// 加载配置
	config, err := s.LoadConfig()
	if err != nil {
		return fmt.Errorf("加载配置失败: %v", err)
	}

	if !config.Enabled {
		// s.logger.Info("权重监测服务未启用")
		return nil
	}
	
	if config.APIKey == "" {
		return fmt.Errorf("API密钥未配置")
	}

	s.config = config
	s.api = NewAizhanAPI(config.APIKey)

	// 验证API密钥
	if err := s.api.ValidateAPIKey(); err != nil {
		return fmt.Errorf("API密钥验证失败: %v", err)
	}

	// 设置定时任务
	spec := fmt.Sprintf("*/%d * * * *", config.CheckInterval)
	_, err = s.cron.AddFunc(spec, s.checkWeights)
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %v", err)
	}

	s.cron.Start()
	s.isRunning = true
	// s.logger.Info("权重监测服务已启动", zap.Int("interval", config.CheckInterval))
	
	// 立即执行一次
	go s.checkWeights()

	return nil
}

// Stop 停止监测服务
func (s *MonitorService) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return
	}

	s.cron.Stop()
	s.isRunning = false
	// s.logger.Info("权重监测服务已停止")
}

// IsRunning 检查服务是否运行中
func (s *MonitorService) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// checkWeights 检查所有站点权重
func (s *MonitorService) checkWeights() {
	// s.logger.Info("开始执行权重检查任务")
	startTime := time.Now()

	// 获取所有活跃站点的域名
	sites, err := s.repo.GetActiveSites()
	if err != nil {
		s.logger.Error("获取站点列表失败", zap.Error(err))
		return
	}

	if len(sites) == 0 {
		// s.logger.Info("没有需要检查的站点")
		return
	}
	
	// 提取域名列表（使用站点域名，不是目标URL）
	var domains []string
	domainToSiteID := make(map[string]uint)
	for _, site := range sites {
		domains = append(domains, site.Domain)
		domainToSiteID[site.Domain] = site.ID
	}

	// s.logger.Info("准备检查域名权重", zap.Int("count", len(domains)))
	
	// 分批处理
	batchSize := s.config.BatchSize
	if batchSize <= 0 {
		batchSize = 5 // 默认每批5个
	}
	
	successCount := 0
	failCount := 0
	
	for i := 0; i < len(domains); i += batchSize {
		end := i + batchSize
		if end > len(domains) {
			end = len(domains)
		}

		batch := domains[i:end]
		// s.logger.Info("处理批次", zap.Int("batch", i/batchSize+1), zap.Int("size", len(batch)))
		
		// 调用API获取权重
		result, err := s.api.GetBatchWeights(batch)
		if err != nil {
			s.logger.Error("获取权重失败", zap.Error(err), zap.Strings("domains", batch))
			failCount += len(batch)
			continue
		}

		// 保存结果
		for _, weight := range result.Data.Success {
			siteID := domainToSiteID[weight.Domain]
			history := &model.WeightHistory{
				Domain:    weight.Domain,
				SiteID:    siteID,
				PCBR:      weight.PCBR,
				MobileBR:  weight.MobileBR,
				IP:        weight.IP,
				PCIP:      weight.PCIP,
				MobileIP:  weight.MobileIP,
				CheckTime: time.Now(),
			}

			if err := s.repo.SaveWeightHistory(history); err != nil {
				s.logger.Error("保存权重历史失败", zap.Error(err), zap.String("domain", weight.Domain))
				failCount++
			} else {
				successCount++
			}
		}

		// 记录失败的域名
		for _, failedDomain := range result.Data.Failed {
			s.logger.Warn("域名权重获取失败", zap.String("domain", failedDomain))
			failCount++
		}

		// 批次间延迟
		if i+batchSize < len(domains) && s.config.BatchDelay > 0 {
			time.Sleep(time.Duration(s.config.BatchDelay) * time.Second)
		}
	}

	// 更新最后检查时间
	s.config.LastCheckTime = time.Now()
	if err := s.repo.UpdateWeightConfig(s.config); err != nil {
		s.logger.Error("更新配置失败", zap.Error(err))
	}

	_ = time.Since(startTime)
	// duration := time.Since(startTime)
	// s.logger.Info("权重检查任务完成",
	//	zap.Duration("duration", duration),
	//	zap.Int("success", successCount),
	//	zap.Int("fail", failCount),
	// )

	// 如果配置了循环等待时间，停止定时任务并等待
	if s.config.CycleWait > 0 {
		s.logger.Info("进入循环等待", zap.Int("hours", s.config.CycleWait))
		time.AfterFunc(time.Duration(s.config.CycleWait)*time.Hour, func() {
			s.logger.Info("循环等待结束，恢复定时检查")
		})
	}
}

// LoadConfig 加载配置
func (s *MonitorService) LoadConfig() (*model.WeightMonitorConfig, error) {
	config, err := s.repo.GetWeightConfig()
	if err != nil {
		// 如果配置不存在，创建默认配置
		config = &model.WeightMonitorConfig{
			Enabled:       false,
			CheckInterval: 60,    // 默认60分钟
			BatchSize:     5,     // 默认每批5个
			BatchDelay:    5,     // 默认延迟5秒
			CycleWait:     24,    // 默认等待24小时
		}
		if err := s.repo.CreateWeightConfig(config); err != nil {
			return nil, err
		}
	}
	return config, nil
}

// UpdateConfig 更新配置
func (s *MonitorService) UpdateConfig(config *model.WeightMonitorConfig) error {
	// 先保存配置到数据库
	if err := s.repo.UpdateWeightConfig(config); err != nil {
		return err
	}

	// 获取当前状态
	s.mu.Lock()
	wasRunning := s.isRunning
	oldConfig := s.config
	s.config = config
	s.mu.Unlock()

	// 根据配置变化决定是否重启服务（在锁外执行避免死锁）
	if wasRunning && !config.Enabled {
		// 如果原来在运行但新配置禁用了，则停止服务
		s.Stop()
	} else if !wasRunning && config.Enabled {
		// 如果原来没运行但新配置启用了，则启动服务
		return s.Start()
	} else if wasRunning && config.Enabled {
		// 如果都是启用状态，检查配置是否变化需要重启
		if oldConfig == nil || oldConfig.CheckInterval != config.CheckInterval {
			// 配置有变化，重启服务
			s.Stop()
			return s.Start()
		}
	}

	return nil
}

// ManualCheck 手动触发检查
func (s *MonitorService) ManualCheck(domains []string) error {
	if s.config == nil || s.config.APIKey == "" {
		return fmt.Errorf("服务未配置或API密钥为空")
	}

	if s.api == nil {
		s.api = NewAizhanAPI(s.config.APIKey)
	}

	// 如果没有指定域名，检查所有域名
	if len(domains) == 0 {
		go s.checkWeights()
		return nil
	}

	// 检查指定域名
	result, err := s.api.GetBatchWeights(domains)
	if err != nil {
		return err
	}

	// 保存结果
	for _, weight := range result.Data.Success {
		// 查找对应的站点ID
		site, _ := s.repo.GetSiteByDomain(weight.Domain)
		var siteID uint
		if site != nil {
			siteID = site.ID
		}

		history := &model.WeightHistory{
			Domain:    weight.Domain,
			SiteID:    siteID,
			PCBR:      weight.PCBR,
			MobileBR:  weight.MobileBR,
			IP:        weight.IP,
			PCIP:      weight.PCIP,
			MobileIP:  weight.MobileIP,
			CheckTime: time.Now(),
		}

		if err := s.repo.SaveWeightHistory(history); err != nil {
			log.Printf("保存权重历史失败: %v", err)
		}
	}

	return nil
}