package service

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
	"golang.org/x/sync/semaphore"
)

// ResourceLimiter 资源限流器
type ResourceLimiter struct {
	logger *zap.Logger
	mu     sync.RWMutex

	// 信号量限流器
	databaseSem  *semaphore.Weighted // 数据库操作限流
	redisSem     *semaphore.Weighted // Redis操作限流
	httpSem      *semaphore.Weighted // HTTP请求限流
	fileSem      *semaphore.Weighted // 文件操作限流
	crawlerSem   *semaphore.Weighted // 爬虫任务限流

	// 当前配置
	config *ResourceLimitConfig

	// 统计信息
	stats *LimiterStats
}

// ResourceLimitConfig 资源限流配置
type ResourceLimitConfig struct {
	// 并发限制配置
	MaxDatabaseConn  int64 `json:"max_database_conn"`  // 最大数据库并发连接数
	MaxRedisConn     int64 `json:"max_redis_conn"`     // 最大Redis并发连接数  
	MaxHTTPRequests  int64 `json:"max_http_requests"`  // 最大HTTP并发请求数
	MaxFileOps       int64 `json:"max_file_ops"`       // 最大文件并发操作数
	MaxCrawlerTasks  int64 `json:"max_crawler_tasks"`  // 最大爬虫并发任务数

	// 超时配置（毫秒）
	RouteTimeout     int `json:"route_timeout"`      // 路由处理总超时时间
	DatabaseTimeout  int `json:"database_timeout"`   // 数据库查询超时时间
	RedisTimeout     int `json:"redis_timeout"`      // Redis操作超时时间
	HTTPTimeout      int `json:"http_timeout"`       // HTTP请求超时时间
	FileTimeout      int `json:"file_timeout"`       // 文件操作超时时间
	CrawlerTimeout   int `json:"crawler_timeout"`    // 爬虫任务超时时间
}

// LimiterStats 限流器统计信息
type LimiterStats struct {
	mu sync.RWMutex

	// 当前使用情况
	DatabaseUsed  int64 `json:"database_used"`
	RedisUsed     int64 `json:"redis_used"`
	HTTPUsed      int64 `json:"http_used"`
	FileUsed      int64 `json:"file_used"`
	CrawlerUsed   int64 `json:"crawler_used"`

	// 拒绝计数
	DatabaseRejected  int64 `json:"database_rejected"`
	RedisRejected     int64 `json:"redis_rejected"`
	HTTPRejected      int64 `json:"http_rejected"`
	FileRejected      int64 `json:"file_rejected"`
	CrawlerRejected   int64 `json:"crawler_rejected"`

	// 超时计数
	RouteTimeouts     int64 `json:"route_timeouts"`
	DatabaseTimeouts  int64 `json:"database_timeouts"`
	RedisTimeouts     int64 `json:"redis_timeouts"`
	HTTPTimeouts      int64 `json:"http_timeouts"`
	FileTimeouts      int64 `json:"file_timeouts"`
}

// NewResourceLimiter 创建资源限流器
func NewResourceLimiter(logger *zap.Logger, config *ResourceLimitConfig) *ResourceLimiter {
	if config == nil {
		config = GetDefaultResourceConfig()
	}

	rl := &ResourceLimiter{
		logger: logger,
		config: config,
		stats:  &LimiterStats{},
	}

	rl.applyConfig(config)
	return rl
}

// applyConfig 应用配置
func (rl *ResourceLimiter) applyConfig(config *ResourceLimitConfig) {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	rl.config = config
	rl.databaseSem = semaphore.NewWeighted(config.MaxDatabaseConn)
	rl.redisSem = semaphore.NewWeighted(config.MaxRedisConn)
	rl.httpSem = semaphore.NewWeighted(config.MaxHTTPRequests)
	rl.fileSem = semaphore.NewWeighted(config.MaxFileOps)
	rl.crawlerSem = semaphore.NewWeighted(config.MaxCrawlerTasks)

	// rl.logger.Info("资源限流器配置已更新",
	// 	zap.Int64("数据库并发", config.MaxDatabaseConn),
	// 	zap.Int64("Redis并发", config.MaxRedisConn),
	// 	zap.Int64("HTTP并发", config.MaxHTTPRequests),
	// 	zap.Int64("文件并发", config.MaxFileOps),
	// 	zap.Int64("爬虫并发", config.MaxCrawlerTasks))
}

// UpdateConfig 更新配置
func (rl *ResourceLimiter) UpdateConfig(config *ResourceLimitConfig) {
	rl.applyConfig(config)
}

// GetDefaultResourceConfig 获取默认配置（基于系统资源自动计算）
func GetDefaultResourceConfig() *ResourceLimitConfig {
	// 获取CPU核心数
	cpuCores := runtime.NumCPU()
	
	// 基于CPU核心数计算默认值
	return &ResourceLimitConfig{
		// 并发限制 - 基于CPU核心数
		MaxDatabaseConn:  int64(cpuCores * 10),  // 每核10个数据库连接
		MaxRedisConn:     int64(cpuCores * 20),  // 每核20个Redis连接
		MaxHTTPRequests:  int64(cpuCores * 5),   // 每核5个HTTP请求
		MaxFileOps:       int64(cpuCores * 3),   // 每核3个文件操作
		MaxCrawlerTasks:  int64(cpuCores * 2),   // 每核2个爬虫任务

		// 超时配置（毫秒）
		RouteTimeout:    1200,  // 1.2秒路由总超时
		DatabaseTimeout: 800,   // 800ms数据库超时
		RedisTimeout:    200,   // 200ms Redis超时
		HTTPTimeout:     500,   // 500ms HTTP请求超时
		FileTimeout:     1000,  // 1秒文件操作超时
		CrawlerTimeout:  10000, // 10秒爬虫任务超时
	}
}

// GetOptimizedConfig 获取优化配置（基于服务器配置）
func GetOptimizedConfig(memoryGB int, cpuCores int, isSSD bool) *ResourceLimitConfig {
	config := &ResourceLimitConfig{}

	// 基于内存计算并发数
	if memoryGB >= 16 {
		// 高配服务器
		config.MaxDatabaseConn = int64(cpuCores * 15)
		config.MaxRedisConn = int64(cpuCores * 30)
		config.MaxHTTPRequests = int64(cpuCores * 8)
		config.MaxFileOps = int64(cpuCores * 5)
		config.MaxCrawlerTasks = int64(cpuCores * 4)
	} else if memoryGB >= 8 {
		// 中配服务器
		config.MaxDatabaseConn = int64(cpuCores * 10)
		config.MaxRedisConn = int64(cpuCores * 20)
		config.MaxHTTPRequests = int64(cpuCores * 5)
		config.MaxFileOps = int64(cpuCores * 3)
		config.MaxCrawlerTasks = int64(cpuCores * 2)
	} else {
		// 低配服务器
		config.MaxDatabaseConn = int64(cpuCores * 5)
		config.MaxRedisConn = int64(cpuCores * 10)
		config.MaxHTTPRequests = int64(cpuCores * 3)
		config.MaxFileOps = int64(cpuCores * 2)
		config.MaxCrawlerTasks = int64(cpuCores * 1)
	}

	// 基于存储类型调整超时
	if isSSD {
		// SSD存储，文件操作更快
		config.RouteTimeout = 1000     // 1秒
		config.DatabaseTimeout = 600    // 600ms
		config.RedisTimeout = 150       // 150ms
		config.HTTPTimeout = 400        // 400ms
		config.FileTimeout = 500        // 500ms
		config.CrawlerTimeout = 8000    // 8秒
	} else {
		// HDD存储，需要更长超时
		config.RouteTimeout = 1500     // 1.5秒
		config.DatabaseTimeout = 1000   // 1秒
		config.RedisTimeout = 300       // 300ms
		config.HTTPTimeout = 800        // 800ms
		config.FileTimeout = 2000       // 2秒
		config.CrawlerTimeout = 15000   // 15秒
	}

	return config
}

// AcquireDatabase 获取数据库操作许可
func (rl *ResourceLimiter) AcquireDatabase(ctx context.Context) error {
	timeout := time.Duration(rl.config.DatabaseTimeout) * time.Millisecond
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	if err := rl.databaseSem.Acquire(ctx, 1); err != nil {
		rl.stats.mu.Lock()
		rl.stats.DatabaseRejected++
		rl.stats.mu.Unlock()
		return fmt.Errorf("数据库操作限流: %w", err)
	}

	rl.stats.mu.Lock()
	rl.stats.DatabaseUsed++
	rl.stats.mu.Unlock()
	return nil
}

// ReleaseDatabase 释放数据库操作许可
func (rl *ResourceLimiter) ReleaseDatabase() {
	rl.databaseSem.Release(1)
	rl.stats.mu.Lock()
	rl.stats.DatabaseUsed--
	rl.stats.mu.Unlock()
}

// AcquireRedis 获取Redis操作许可
func (rl *ResourceLimiter) AcquireRedis(ctx context.Context) error {
	timeout := time.Duration(rl.config.RedisTimeout) * time.Millisecond
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	if err := rl.redisSem.Acquire(ctx, 1); err != nil {
		rl.stats.mu.Lock()
		rl.stats.RedisRejected++
		rl.stats.mu.Unlock()
		return fmt.Errorf("Redis操作限流: %w", err)
	}

	rl.stats.mu.Lock()
	rl.stats.RedisUsed++
	rl.stats.mu.Unlock()
	return nil
}

// ReleaseRedis 释放Redis操作许可
func (rl *ResourceLimiter) ReleaseRedis() {
	rl.redisSem.Release(1)
	rl.stats.mu.Lock()
	rl.stats.RedisUsed--
	rl.stats.mu.Unlock()
}

// AcquireHTTP 获取HTTP请求许可
func (rl *ResourceLimiter) AcquireHTTP(ctx context.Context) error {
	timeout := time.Duration(rl.config.HTTPTimeout) * time.Millisecond
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	if err := rl.httpSem.Acquire(ctx, 1); err != nil {
		rl.stats.mu.Lock()
		rl.stats.HTTPRejected++
		rl.stats.mu.Unlock()
		return fmt.Errorf("HTTP请求限流: %w", err)
	}

	rl.stats.mu.Lock()
	rl.stats.HTTPUsed++
	rl.stats.mu.Unlock()
	return nil
}

// ReleaseHTTP 释放HTTP请求许可
func (rl *ResourceLimiter) ReleaseHTTP() {
	rl.httpSem.Release(1)
	rl.stats.mu.Lock()
	rl.stats.HTTPUsed--
	rl.stats.mu.Unlock()
}

// AcquireFile 获取文件操作许可
func (rl *ResourceLimiter) AcquireFile(ctx context.Context) error {
	timeout := time.Duration(rl.config.FileTimeout) * time.Millisecond
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	if err := rl.fileSem.Acquire(ctx, 1); err != nil {
		rl.stats.mu.Lock()
		rl.stats.FileRejected++
		rl.stats.mu.Unlock()
		return fmt.Errorf("文件操作限流: %w", err)
	}

	rl.stats.mu.Lock()
	rl.stats.FileUsed++
	rl.stats.mu.Unlock()
	return nil
}

// ReleaseFile 释放文件操作许可
func (rl *ResourceLimiter) ReleaseFile() {
	rl.fileSem.Release(1)
	rl.stats.mu.Lock()
	rl.stats.FileUsed--
	rl.stats.mu.Unlock()
}

// AcquireCrawler 获取爬虫任务许可
func (rl *ResourceLimiter) AcquireCrawler(ctx context.Context) error {
	timeout := time.Duration(rl.config.CrawlerTimeout) * time.Millisecond
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	if err := rl.crawlerSem.Acquire(ctx, 1); err != nil {
		rl.stats.mu.Lock()
		rl.stats.CrawlerRejected++
		rl.stats.mu.Unlock()
		return fmt.Errorf("爬虫任务限流: %w", err)
	}

	rl.stats.mu.Lock()
	rl.stats.CrawlerUsed++
	rl.stats.mu.Unlock()
	return nil
}

// ReleaseCrawler 释放爬虫任务许可
func (rl *ResourceLimiter) ReleaseCrawler() {
	rl.crawlerSem.Release(1)
	rl.stats.mu.Lock()
	rl.stats.CrawlerUsed--
	rl.stats.mu.Unlock()
}

// GetStats 获取统计信息
func (rl *ResourceLimiter) GetStats() *LimiterStats {
	rl.stats.mu.RLock()
	defer rl.stats.mu.RUnlock()

	// 返回副本
	return &LimiterStats{
		DatabaseUsed:     rl.stats.DatabaseUsed,
		RedisUsed:        rl.stats.RedisUsed,
		HTTPUsed:         rl.stats.HTTPUsed,
		FileUsed:         rl.stats.FileUsed,
		CrawlerUsed:      rl.stats.CrawlerUsed,
		DatabaseRejected: rl.stats.DatabaseRejected,
		RedisRejected:    rl.stats.RedisRejected,
		HTTPRejected:     rl.stats.HTTPRejected,
		FileRejected:     rl.stats.FileRejected,
		CrawlerRejected:  rl.stats.CrawlerRejected,
		RouteTimeouts:    rl.stats.RouteTimeouts,
		DatabaseTimeouts: rl.stats.DatabaseTimeouts,
		RedisTimeouts:    rl.stats.RedisTimeouts,
		HTTPTimeouts:     rl.stats.HTTPTimeouts,
		FileTimeouts:     rl.stats.FileTimeouts,
	}
}

// GetConfig 获取当前配置
func (rl *ResourceLimiter) GetConfig() *ResourceLimitConfig {
	rl.mu.RLock()
	defer rl.mu.RUnlock()
	return rl.config
}

// ResetStats 重置统计信息
func (rl *ResourceLimiter) ResetStats() {
	rl.stats.mu.Lock()
	defer rl.stats.mu.Unlock()

	rl.stats = &LimiterStats{}
}

// WithRouteTimeout 为路由设置总超时
func (rl *ResourceLimiter) WithRouteTimeout(ctx context.Context) (context.Context, context.CancelFunc) {
	timeout := time.Duration(rl.config.RouteTimeout) * time.Millisecond
	return context.WithTimeout(ctx, timeout)
}

// RecordTimeout 记录超时
func (rl *ResourceLimiter) RecordTimeout(timeoutType string) {
	rl.stats.mu.Lock()
	defer rl.stats.mu.Unlock()

	switch timeoutType {
	case "route":
		rl.stats.RouteTimeouts++
	case "database":
		rl.stats.DatabaseTimeouts++
	case "redis":
		rl.stats.RedisTimeouts++
	case "http":
		rl.stats.HTTPTimeouts++
	case "file":
		rl.stats.FileTimeouts++
	}
}