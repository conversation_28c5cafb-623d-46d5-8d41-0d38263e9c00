package service

import (
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Cache404Service 404状态缓存服务
type Cache404Service struct {
	logger       *zap.Logger
	redis        *RedisCacheService
	settings     *SystemSettingsService
	db           *gorm.DB             // 数据库连接
	memoryCache  map[string]time.Time // 内存缓存404状态
	mu           sync.RWMutex
	statsCache   map[string]int       // 404统计缓存 domain -> count
	statsMu      sync.RWMutex
}

// NewCache404Service 创建404缓存服务
func NewCache404Service(logger *zap.Logger, redis *RedisCacheService, settings *SystemSettingsService) *Cache404Service {
	service := &Cache404Service{
		logger:      logger,
		redis:       redis,
		settings:    settings,
		memoryCache: make(map[string]time.Time),
		statsCache:  make(map[string]int),
	}
	// 初始化时从Redis加载统计数据
	service.loadStatsFromRedis()
	return service
}

// SetDB 设置数据库连接
func (s *Cache404Service) SetDB(db *gorm.DB) {
	s.db = db
	// 从数据库加载现有的404统计
	s.loadStatsFromDB()
}

// loadStatsFromDB 从数据库加载404统计
func (s *Cache404Service) loadStatsFromDB() {
	if s.db == nil {
		return
	}
	
	type siteStats struct {
		Domain   string
		Count404 int `gorm:"column:count404"`
	}
	
	var stats []siteStats
	if err := s.db.Table("sites").Select("domain, count404").Find(&stats).Error; err != nil {
		s.logger.Error("从数据库加载404统计失败", zap.Error(err))
		return
	}
	
	s.statsMu.Lock()
	defer s.statsMu.Unlock()
	
	for _, stat := range stats {
		if stat.Count404 > 0 {
			s.statsCache[stat.Domain] = stat.Count404
		}
	}
	
	// // s.logger.Info("从数据库加载404统计", zap.Int("count", len(s.statsCache)))
}

// Is404Cached 检查URL是否被缓存为404
func (s *Cache404Service) Is404Cached(domain, path string) bool {
	settings, err := s.settings.GetSystemSettings()
	if err != nil || !settings.Enable404Cache {
		return false
	}

	key := fmt.Sprintf("404:%s:%s", domain, path)
	
	// 先检查内存缓存
	s.mu.RLock()
	if expireTime, exists := s.memoryCache[key]; exists {
		s.mu.RUnlock()
		if time.Now().Before(expireTime) {
			s.logger.Debug("命中404内存缓存", 
				zap.String("domain", domain),
				zap.String("path", path))
			return true
		}
		// 过期了，删除
		s.mu.Lock()
		delete(s.memoryCache, key)
		s.mu.Unlock()
	} else {
		s.mu.RUnlock()
	}

	// 检查Redis缓存
	if s.redis != nil {
		if _, exists := s.redis.GetHTML(key); exists {
			// 同步到内存缓存
			s.mu.Lock()
			s.memoryCache[key] = time.Now().Add(time.Duration(settings.Cache404TTL) * time.Second)
			s.mu.Unlock()
			
			s.logger.Debug("命中404 Redis缓存",
				zap.String("domain", domain),
				zap.String("path", path))
			return true
		}
	}

	return false
}

// Cache404Status 缓存404状态
func (s *Cache404Service) Cache404Status(domain, path string) error {
	// s.logger.Info("开始缓存404状态",
	//	zap.String("domain", domain),
	//	zap.String("path", path))
	
	settings, err := s.settings.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return err
	}
	
	if !settings.Enable404Cache {
		// s.logger.Info("404缓存功能未启用")
  // return nil
 // }
 //  // key := fmt.Sprintf("404:%s:%s", domain, path)
	ttl := time.Duration(settings.Cache404TTL) * time.Second

	// 存储到内存缓存
	s.mu.Lock()
	s.memoryCache[key] = time.Now().Add(ttl)
	s.mu.Unlock()

	// 更新统计
	s.increment404Stats(domain)

	// 存储到Redis缓存
	if s.redis != nil {
		if err := s.redis.SetHTML(key, "404", ttl); err != nil {
			// s.logger.Warn("缓存404状态到Redis失败",
    // zap.String("domain", domain),
				zap.String("path", path),
				zap.Error(err))
		} else {
			// s.logger.Info("成功缓存404状态到Redis",
    // zap.String("key", key),
				zap.Duration("ttl", ttl))
		}
	}

	// s.logger.Info("成功缓存404状态",
  // zap.String("domain", domain),
		zap.String("path", path),
		zap.String("key", key),
		zap.Duration("ttl", ttl))

	return nil
}

// Clear404Cache 清除指定域名的404缓存
func (s *Cache404Service) Clear404Cache(domain string) {
	// 清除内存缓存
	s.mu.Lock()
	for key := range s.memoryCache {
		if len(key) > 4 && key[:4] == "404:" {
			if len(key) > len(domain)+5 && key[4:4+len(domain)] == domain {
				delete(s.memoryCache, key)
			}
		}
	}
	s.mu.Unlock()

	// 清除Redis缓存（需要遍历，这里简化处理）
	// s.logger.Info("清除404缓存", zap.String("domain", domain))
// }
 // // ClearAll404Cache 清除所有404缓存
// func (s *Cache404Service) ClearAll404Cache() {
	s.mu.Lock()
	s.memoryCache = make(map[string]time.Time)
	s.mu.Unlock()

	// s.logger.Info("清除所有404缓存")
// }
 // // CleanupExpired 清理过期的内存缓存
// func (s *Cache404Service) CleanupExpired() {
	s.mu.Lock()
	defer s.mu.Unlock()

	now := time.Now()
	for key, expireTime := range s.memoryCache {
		if now.After(expireTime) {
			delete(s.memoryCache, key)
		}
	}
}

// StartCleanupTask 启动定期清理任务
func (s *Cache404Service) StartCleanupTask() {
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			s.CleanupExpired()
		}
	}()
}

// Get404Stats 获取所有域名的404统计
func (s *Cache404Service) Get404Stats() map[string]int {
	s.statsMu.RLock()
	defer s.statsMu.RUnlock()
	
	result := make(map[string]int)
	for domain, count := range s.statsCache {
		result[domain] = count
	}
	return result
}

// Get404CountByDomain 获取指定域名的404数量
func (s *Cache404Service) Get404CountByDomain(domain string) int {
	s.statsMu.RLock()
	defer s.statsMu.RUnlock()
	
	return s.statsCache[domain]
}

// increment404Stats 增加404统计计数
func (s *Cache404Service) increment404Stats(domain string) {
	s.statsMu.Lock()
	defer s.statsMu.Unlock()
	
	s.statsCache[domain]++
	
	// 直接更新数据库中的count_404字段
	if s.db != nil {
		// s.logger.Info("准备更新数据库404统计", 
   // zap.String("domain", domain),
			zap.Int("count", s.statsCache[domain]))
		// 使用原生SQL更新，避免GORM查询整个对象
		sql := "UPDATE sites SET count404 = count404 + 1 WHERE domain = ?"
		result := s.db.Exec(sql, domain)
		if result.Error != nil {
			s.logger.Error("更新404统计失败", 
				zap.String("domain", domain),
				zap.Error(result.Error))
		} else {
			// s.logger.Info("成功更新404统计", 
    // zap.String("domain", domain),
				zap.Int("count", s.statsCache[domain]),
				zap.Int64("rows_affected", result.RowsAffected))
		}
	} else {
		// s.logger.Warn("数据库连接为空，无法更新404统计",
   // zap.String("domain", domain))
	}
	
	// 同步到Redis（作为缓存）
	if s.redis != nil {
		statsKey := fmt.Sprintf("404_stats:%s", domain)
		// 使用Redis的INCR命令原子增加
		if s.redis.GetClient() != nil {
			s.redis.GetClient().Incr(s.redis.GetClient().Context(), statsKey)
		}
	}
}

// loadStatsFromRedis 从Redis加载统计数据
func (s *Cache404Service) loadStatsFromRedis() {
	if s.redis == nil || s.redis.GetClient() == nil {
		return
	}
	
	// 这里简化处理，实际应该扫描所有404_stats:*键
	// 暂时不实现完整的加载逻辑
	s.logger.Debug("从Redis加载404统计数据")
}

// Reset404Stats 重置指定域名的404统计
func (s *Cache404Service) Reset404Stats(domain string) {
	s.statsMu.Lock()
	defer s.statsMu.Unlock()
	
	// 清除内存缓存
	delete(s.statsCache, domain)
	
	// 更新数据库中的count404字段为0
	if s.db != nil {
		// s.logger.Info("重置数据库404统计", zap.String("domain", domain))
  // sql := "UPDATE sites SET count404 = 0 WHERE domain = ?"
  // result := s.db.Exec(sql, domain)
		if result.Error != nil {
			s.logger.Error("重置404统计失败", 
				zap.String("domain", domain),
				zap.Error(result.Error))
		} else {
			// s.logger.Info("成功重置404统计", 
    // zap.String("domain", domain),
				zap.Int64("rows_affected", result.RowsAffected))
		}
	}
	
	// 从Redis删除
	if s.redis != nil && s.redis.GetClient() != nil {
		statsKey := fmt.Sprintf("404_stats:%s", domain)
		s.redis.GetClient().Del(s.redis.GetClient().Context(), statsKey)
	}
}

// ResetAll404Stats 重置所有404统计
func (s *Cache404Service) ResetAll404Stats() {
	s.statsMu.Lock()
	defer s.statsMu.Unlock()
	
	s.statsCache = make(map[string]int)
	
	// 清除Redis中的所有统计
	if s.redis != nil && s.redis.GetClient() != nil {
		// 扫描并删除所有404_stats:*键
		ctx := s.redis.GetClient().Context()
		iter := s.redis.GetClient().Scan(ctx, 0, "404_stats:*", 0).Iterator()
		for iter.Next(ctx) {
			s.redis.GetClient().Del(ctx, iter.Val())
		}
	}
}