package service

import (
	"site-cluster/internal/model"
	"site-cluster/internal/repository"
)

// SiteCategoryService 站点分类服务
type SiteCategoryService struct {
	repo repository.SiteCategoryRepository
}

// NewSiteCategoryService 创建站点分类服务
func NewSiteCategoryService(repo repository.SiteCategoryRepository) *SiteCategoryService {
	return &SiteCategoryService{
		repo: repo,
	}
}

// GetAll 获取所有分类
func (s *SiteCategoryService) GetAll() ([]*model.SiteCategory, error) {
	return s.repo.GetAll()
}

// GetByID 根据ID获取分类
func (s *SiteCategoryService) GetByID(id uint) (*model.SiteCategory, error) {
	return s.repo.GetByID(id)
}

// Create 创建分类
func (s *SiteCategoryService) Create(category *model.SiteCategory) error {
	return s.repo.Create(category)
}

// Update 更新分类
func (s *SiteCategoryService) Update(category *model.SiteCategory) error {
	return s.repo.Update(category)
}

// Delete 删除分类
func (s *SiteCategoryService) Delete(id uint) error {
	return s.repo.Delete(id)
}