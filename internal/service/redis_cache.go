package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

type RedisCacheService struct {
	logger       *zap.Logger
	client       *redis.Client
	ctx          context.Context
	readTimeout  time.Duration
	writeTimeout time.Duration
	settings     *SystemSettingsService
}

// GetClient 获取Redis客户端
func (r *RedisCacheService) GetClient() *redis.Client {
	return r.client
}

// Ping 检查Redis连接
func (r *RedisCacheService) Ping() error {
	if r.client == nil {
		return fmt.Errorf("redis client is nil")
	}
	return r.client.Ping(r.ctx).Err()
}

// NewRedisCacheService 创建Redis缓存服务
func NewRedisCacheService(logger *zap.Logger, addr string, password string, db int) *RedisCacheService {
	// 默认超时配置
	defaultSettings := struct {
		RedisConnectTimeout int
		RedisReadTimeout    int
		RedisWriteTimeout   int
		RedisPoolTimeout    int
		RedisMaxPoolSize    int
	}{
		RedisConnectTimeout: 5000,
		RedisReadTimeout:    2000,
		RedisWriteTimeout:   3000,
		RedisPoolTimeout:    4000,
		RedisMaxPoolSize:    30,
	}
	
	client := redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     password,
		DB:           db,
		PoolSize:     defaultSettings.RedisMaxPoolSize,
		MinIdleConns: 5,
		MaxRetries:   3,
		DialTimeout:  time.Duration(defaultSettings.RedisConnectTimeout) * time.Millisecond,
		ReadTimeout:  time.Duration(defaultSettings.RedisReadTimeout) * time.Millisecond,
		WriteTimeout: time.Duration(defaultSettings.RedisWriteTimeout) * time.Millisecond,
		PoolTimeout:  time.Duration(defaultSettings.RedisPoolTimeout) * time.Millisecond,
	})

	ctx := context.Background()
	
	// 测试连接
	if err := client.Ping(ctx).Err(); err != nil {
		logger.Error("Redis连接失败", zap.Error(err))
	}

	return &RedisCacheService{
		logger:       logger,
		client:       client,
		ctx:          ctx,
		readTimeout:  time.Duration(defaultSettings.RedisReadTimeout) * time.Millisecond,
		writeTimeout: time.Duration(defaultSettings.RedisWriteTimeout) * time.Millisecond,
	}
}

// UpdateSettings 更新超时设置
func (r *RedisCacheService) UpdateSettings(readTimeout, writeTimeout int) {
	r.readTimeout = time.Duration(readTimeout) * time.Millisecond
	r.writeTimeout = time.Duration(writeTimeout) * time.Millisecond
}

// Get 通用获取缓存方法
func (r *RedisCacheService) Get(key string) (string, bool) {
	ctx, cancel := context.WithTimeout(context.Background(), r.readTimeout)
	defer cancel()
	
	val, err := r.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return "", false
	} else if err != nil {
		if err == context.DeadlineExceeded {
		} else {
			r.logger.Error("Redis获取失败", zap.Error(err))
		}
		return "", false
	}
	return val, true
}

// Set 通用设置缓存方法
func (r *RedisCacheService) Set(key string, value string, ttl time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), r.writeTimeout)
	defer cancel()
	
	err := r.client.Set(ctx, key, value, ttl).Err()
	if err != nil {
		if err == context.DeadlineExceeded {
		} else {
			r.logger.Error("Redis设置失败", zap.Error(err))
		}
		return err
	}
	return nil
}

// Delete 删除缓存
func (r *RedisCacheService) Delete(keys ...string) error {
	ctx, cancel := context.WithTimeout(context.Background(), r.writeTimeout)
	defer cancel()
	
	if len(keys) == 0 {
		return nil
	}
	
	err := r.client.Del(ctx, keys...).Err()
	if err != nil {
		r.logger.Error("Redis删除失败", zap.Error(err), zap.Strings("keys", keys))
		return err
	}
	return nil
}

// GetHTML 获取HTML缓存（使用动态超时）
func (r *RedisCacheService) GetHTML(key string) (string, bool) {
	ctx, cancel := context.WithTimeout(context.Background(), r.readTimeout)
	defer cancel()
	
	val, err := r.client.Get(ctx, fmt.Sprintf("html:%s", key)).Result()
	if err == redis.Nil {
		return "", false
	} else if err != nil {
		if err == context.DeadlineExceeded {
		} else {
			r.logger.Error("Redis获取失败", zap.Error(err))
		}
		return "", false
	}
	return val, true
}

// SetHTML 设置HTML缓存（使用动态超时）
func (r *RedisCacheService) SetHTML(key string, value string, ttl time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), r.writeTimeout)
	defer cancel()
	
	err := r.client.Set(ctx, fmt.Sprintf("html:%s", key), value, ttl).Err()
	if err != nil {
		if err == context.DeadlineExceeded {
		} else {
			r.logger.Error("Redis写入失败", zap.Error(err))
		}
	}
	return err
}

// GetJSON 获取JSON对象
func (r *RedisCacheService) GetJSON(key string, v interface{}) error {
	val, err := r.client.Get(r.ctx, fmt.Sprintf("json:%s", key)).Result()
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(val), v)
}

// SetJSON 设置JSON对象
func (r *RedisCacheService) SetJSON(key string, v interface{}, ttl time.Duration) error {
	data, err := json.Marshal(v)
	if err != nil {
		return err
	}
	return r.client.Set(r.ctx, fmt.Sprintf("json:%s", key), data, ttl).Err()
}

// GetSiteConfig 获取站点配置（热点数据）
func (r *RedisCacheService) GetSiteConfig(domain string) (string, bool) {
	key := fmt.Sprintf("site:%s", domain)
	val, err := r.client.Get(r.ctx, key).Result()
	if err == redis.Nil {
		return "", false
	} else if err != nil {
		r.logger.Error("获取站点配置失败", 
			zap.String("key", key),
			zap.String("domain", domain),
			zap.Error(err))
		return "", false
	}
	return val, true
}

// SetSiteConfig 设置站点配置
func (r *RedisCacheService) SetSiteConfig(domain string, config string) error {
	key := fmt.Sprintf("site:%s", domain)
	// 站点配置缓存1小时
	err := r.client.Set(r.ctx, key, config, time.Hour).Err()
	if err != nil {
		r.logger.Error("设置站点配置失败", 
			zap.String("key", key),
			zap.String("domain", domain),
			zap.Error(err))
	} else {
		r.logger.Info("成功缓存站点配置到Redis", 
			zap.String("key", key),
			zap.String("domain", domain),
			zap.Int("config_length", len(config)))
	}
	return err
}

// GetSiteConfigByID 通过站点ID获取站点配置
func (r *RedisCacheService) GetSiteConfigByID(siteID uint) (string, bool) {
	key := fmt.Sprintf("site:id:%d", siteID)
	val, err := r.client.Get(r.ctx, key).Result()
	if err == redis.Nil {
		return "", false
	} else if err != nil {
		r.logger.Error("获取站点配置失败", 
			zap.String("key", key),
			zap.Uint("site_id", siteID),
			zap.Error(err))
		return "", false
	}
	
	return val, true
}

// SetSiteConfigByID 通过站点ID设置站点配置
func (r *RedisCacheService) SetSiteConfigByID(siteID uint, config string) error {
	key := fmt.Sprintf("site:id:%d", siteID)
	// 站点配置缓存1小时
	err := r.client.Set(r.ctx, key, config, time.Hour).Err()
	if err != nil {
		r.logger.Error("设置站点配置失败", 
			zap.String("key", key),
			zap.Uint("site_id", siteID),
			zap.Error(err))
	} else {
		r.logger.Info("成功缓存站点配置到Redis", 
			zap.String("key", key),
			zap.Uint("site_id", siteID),
			zap.Int("config_length", len(config)))
	}
	return err
}

// IncrementCounter 增加计数器（用于统计）
func (r *RedisCacheService) IncrementCounter(key string) error {
	return r.client.Incr(r.ctx, fmt.Sprintf("counter:%s", key)).Err()
}

// GetCounter 获取计数器
func (r *RedisCacheService) GetCounter(key string) (int64, error) {
	return r.client.Get(r.ctx, fmt.Sprintf("counter:%s", key)).Int64()
}

// SetWithLock 带分布式锁的设置（防止缓存击穿）
func (r *RedisCacheService) SetWithLock(key string, value string, ttl time.Duration, lockTTL time.Duration) error {
	lockKey := fmt.Sprintf("lock:%s", key)
	
	// 尝试获取锁
	locked, err := r.client.SetNX(r.ctx, lockKey, "1", lockTTL).Result()
	if err != nil {
		return err
	}
	
	if !locked {
		return fmt.Errorf("无法获取锁")
	}
	
	// 设置值
	defer r.client.Del(r.ctx, lockKey)
	return r.client.Set(r.ctx, key, value, ttl).Err()
}

// GetCacheStats 获取缓存统计信息
func (r *RedisCacheService) GetCacheStats() (map[string]string, error) {
	info, err := r.client.Info(r.ctx, "stats").Result()
	if err != nil {
		return nil, err
	}
	
	// 解析info字符串为map
	stats := make(map[string]string)
	lines := strings.Split(info, "\r\n")
	for _, line := range lines {
		if strings.Contains(line, ":") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				stats[parts[0]] = parts[1]
			}
		}
	}
	
	return stats, nil
}

// FlushCache 清空缓存（慎用）
func (r *RedisCacheService) FlushCache() error {
	return r.client.FlushDB(r.ctx).Err()
}

// Close 关闭连接
func (r *RedisCacheService) Close() error {
	return r.client.Close()
}

// Pipeline 批量操作
func (r *RedisCacheService) Pipeline() redis.Pipeliner {
	return r.client.Pipeline()
}

// SetBatch 批量设置
func (r *RedisCacheService) SetBatch(items map[string]interface{}, ttl time.Duration) error {
	pipe := r.client.Pipeline()
	
	for key, value := range items {
		var data string
		switch v := value.(type) {
		case string:
			data = v
		default:
			jsonData, err := json.Marshal(v)
			if err != nil {
				return err
			}
			data = string(jsonData)
		}
		pipe.Set(r.ctx, key, data, ttl)
	}
	
	_, err := pipe.Exec(r.ctx)
	return err
}

// GetBatch 批量获取
func (r *RedisCacheService) GetBatch(keys []string) (map[string]string, error) {
	pipe := r.client.Pipeline()
	cmds := make([]*redis.StringCmd, len(keys))
	
	for i, key := range keys {
		cmds[i] = pipe.Get(r.ctx, key)
	}
	
	_, err := pipe.Exec(r.ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}
	
	result := make(map[string]string)
	for i, cmd := range cmds {
		val, err := cmd.Result()
		if err == nil {
			result[keys[i]] = val
		}
	}
	
	return result, nil
}

// ExpireAt 设置过期时间
func (r *RedisCacheService) ExpireAt(key string, tm time.Time) error {
	return r.client.ExpireAt(r.ctx, key, tm).Err()
}

// TTL 获取剩余生存时间
func (r *RedisCacheService) TTL(key string) (time.Duration, error) {
	return r.client.TTL(r.ctx, key).Result()
}

// SetNX 只在键不存在时设置（用于分布式锁）
func (r *RedisCacheService) SetNX(key string, value string, ttl time.Duration) (bool, error) {
	return r.client.SetNX(r.ctx, key, value, ttl).Result()
}

// Del 删除键
func (r *RedisCacheService) Del(keys ...string) error {
	return r.client.Del(r.ctx, keys...).Err()
}

// DeleteByPattern 根据模式删除键
func (r *RedisCacheService) DeleteByPattern(pattern string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	// 使用 SCAN 命令安全地遍历键
	iter := r.client.Scan(ctx, 0, pattern, 0).Iterator()
	var keys []string
	
	for iter.Next(ctx) {
		keys = append(keys, iter.Val())
		
		// 批量删除，每100个键删除一次
		if len(keys) >= 100 {
			if err := r.client.Del(ctx, keys...).Err(); err != nil {
				r.logger.Error("批量删除缓存失败", zap.Error(err))
				return err
			}
			keys = keys[:0] // 清空切片
		}
	}
	
	// 删除剩余的键
	if len(keys) > 0 {
		if err := r.client.Del(ctx, keys...).Err(); err != nil {
			r.logger.Error("批量删除缓存失败", zap.Error(err))
			return err
		}
	}
	
	if err := iter.Err(); err != nil {
		r.logger.Error("遍历缓存键失败", zap.Error(err))
		return err
	}
	
	return nil
}