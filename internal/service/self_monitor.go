package service

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// SelfMonitor 自监控服务，实现应用级自动恢复
type SelfMonitor struct {
	logger         *zap.Logger
	healthChecker  func() error
	panicCount     int32
	restartCount   int32
	maxRestarts    int32
	checkInterval  time.Duration
	isRunning      atomic.Bool
	stopChan       chan struct{}
}

// NewSelfMonitor 创建自监控服务
func NewSelfMonitor(logger *zap.Logger) *SelfMonitor {
	return &SelfMonitor{
		logger:        logger,
		maxRestarts:   5,  // 最多重启5次
		checkInterval: 30 * time.Second,
		stopChan:      make(chan struct{}),
	}
}

// SetHealthChecker 设置健康检查函数
func (sm *SelfMonitor) SetHealthChecker(checker func() error) {
	sm.healthChecker = checker
}

// Start 启动自监控
func (sm *SelfMonitor) Start() {
	if sm.isRunning.Load() {
		return
	}
	sm.isRunning.Store(true)

	// 1. 设置全局panic恢复
	go sm.globalPanicRecovery()

	// 2. 启动健康检查
	go sm.healthCheckLoop()

	// 3. 启动资源监控
	go sm.resourceMonitor()

	sm.logger.Info("自监控服务已启动")
}

// globalPanicRecovery 全局panic恢复
func (sm *SelfMonitor) globalPanicRecovery() {
	defer func() {
		if r := recover(); r != nil {
			sm.handlePanic(r)
		}
	}()

	// 监控主程序
	for {
		select {
		case <-sm.stopChan:
			return
		case <-time.After(1 * time.Second):
			// 持续监控
		}
	}
}

// handlePanic 处理panic
func (sm *SelfMonitor) handlePanic(r interface{}) {
	atomic.AddInt32(&sm.panicCount, 1)
	sm.logger.Error("捕获到严重错误",
		zap.Any("panic", r),
		zap.Int32("panic_count", sm.panicCount))

	// 如果panic次数过多，执行重启
	if sm.panicCount > 3 {
		sm.performRestart("panic次数过多")
	}
}

// healthCheckLoop 健康检查循环
func (sm *SelfMonitor) healthCheckLoop() {
	ticker := time.NewTicker(sm.checkInterval)
	defer ticker.Stop()

	consecutiveFailures := 0
	for {
		select {
		case <-sm.stopChan:
			return
		case <-ticker.C:
			if sm.healthChecker != nil {
				if err := sm.healthChecker(); err != nil {
					consecutiveFailures++
					sm.logger.Warn("健康检查失败",
						zap.Error(err),
						zap.Int("failures", consecutiveFailures))

					// 连续失败3次，触发恢复
					if consecutiveFailures >= 3 {
						sm.performRecovery()
						consecutiveFailures = 0
					}
				} else {
					consecutiveFailures = 0
				}
			}
		}
	}
}

// resourceMonitor 资源监控
func (sm *SelfMonitor) resourceMonitor() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-sm.stopChan:
			return
		case <-ticker.C:
			sm.checkResources()
		}
	}
}

// checkResources 检查系统资源
func (sm *SelfMonitor) checkResources() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 检查内存使用
	allocMB := m.Alloc / 1024 / 1024
	sysMB := m.Sys / 1024 / 1024
	
	// 检查goroutine数量
	numGoroutine := runtime.NumGoroutine()

	sm.logger.Debug("资源使用情况",
		zap.Uint64("alloc_mb", allocMB),
		zap.Uint64("sys_mb", sysMB),
		zap.Int("goroutines", numGoroutine))

	// 如果goroutine过多，可能有泄露
	if numGoroutine > 10000 {
		sm.logger.Error("Goroutine数量异常",
			zap.Int("count", numGoroutine))
		sm.performRecovery()
	}

	// 如果内存使用过高（超过2GB），触发GC
	if allocMB > 2048 {
		sm.logger.Warn("内存使用过高，强制GC",
			zap.Uint64("alloc_mb", allocMB))
		runtime.GC()
		runtime.GC() // 执行两次确保清理
	}
}

// performRecovery 执行恢复操作
func (sm *SelfMonitor) performRecovery() {
	sm.logger.Info("开始执行恢复操作")

	// 1. 强制GC
	runtime.GC()
	runtime.GC()

	// 2. 清理缓存
	// TODO: 调用缓存清理服务

	// 3. 重置连接池
	// TODO: 重置数据库和Redis连接

	// 4. 如果还是不行，考虑重启
	time.Sleep(5 * time.Second)
	if sm.healthChecker != nil {
		if err := sm.healthChecker(); err != nil {
			sm.performRestart("恢复失败")
		}
	}
}

// performRestart 执行重启
func (sm *SelfMonitor) performRestart(reason string) {
	restarts := atomic.AddInt32(&sm.restartCount, 1)
	
	if restarts > sm.maxRestarts {
		sm.logger.Fatal("重启次数过多，停止服务",
			zap.Int32("restarts", restarts))
		os.Exit(1)
	}

	sm.logger.Warn("准备重启服务",
		zap.String("reason", reason),
		zap.Int32("restart_count", restarts))

	// 优雅关闭
	sm.gracefulShutdown()

	// 重启进程
	sm.restartProcess()
}

// gracefulShutdown 优雅关闭
func (sm *SelfMonitor) gracefulShutdown() {
	sm.logger.Info("开始优雅关闭")
	
	// 给30秒时间处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// TODO: 通知各服务停止接收新请求
	// TODO: 等待现有请求完成
	// TODO: 关闭数据库连接
	// TODO: 保存必要状态

	select {
	case <-ctx.Done():
		sm.logger.Warn("优雅关闭超时")
	default:
		sm.logger.Info("优雅关闭完成")
	}
}

// restartProcess 重启进程
func (sm *SelfMonitor) restartProcess() {
	executable, err := os.Executable()
	if err != nil {
		sm.logger.Error("获取可执行文件路径失败", zap.Error(err))
		return
	}

	// 使用exec.Command重启
	cmd := exec.Command(executable, os.Args[1:]...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin
	cmd.Env = os.Environ()

	// 启动新进程
	if err := cmd.Start(); err != nil {
		sm.logger.Error("启动新进程失败", zap.Error(err))
		return
	}

	sm.logger.Info("新进程已启动", zap.Int("pid", cmd.Process.Pid))

	// 退出当前进程
	os.Exit(0)
}

// Stop 停止自监控
func (sm *SelfMonitor) Stop() {
	if !sm.isRunning.Load() {
		return
	}
	
	sm.isRunning.Store(false)
	close(sm.stopChan)
	sm.logger.Info("自监控服务已停止")
}

// GetStats 获取监控统计
func (sm *SelfMonitor) GetStats() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"panic_count":    atomic.LoadInt32(&sm.panicCount),
		"restart_count":  atomic.LoadInt32(&sm.restartCount),
		"goroutines":     runtime.NumGoroutine(),
		"memory_alloc":   m.Alloc,
		"memory_sys":     m.Sys,
		"gc_count":       m.NumGC,
		"uptime":         time.Now().Unix(),
	}
}

// RecoverPanic 用于包装可能panic的函数
func RecoverPanic(fn func()) {
	defer func() {
		if r := recover(); r != nil {
			// 记录但不退出
			fmt.Printf("Recovered from panic: %v\n", r)
		}
	}()
	fn()
}