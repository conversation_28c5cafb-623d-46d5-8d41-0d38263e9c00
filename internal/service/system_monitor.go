package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
	"net"
	"syscall"
	
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
	"go.uber.org/zap"
)

type SystemMonitor struct {
	logger *zap.Logger
	startTime time.Time
	db *gorm.DB
	redisClient *redis.Client
}

func NewSystemMonitor(logger *zap.Logger) *SystemMonitor {
	return &SystemMonitor{
		logger: logger,
		startTime: time.Now(),
	}
}

// SetDB 设置数据库连接
func (sm *SystemMonitor) SetDB(db *gorm.DB) {
	sm.db = db
}

// SetRedisClient 设置Redis客户端
func (sm *SystemMonitor) SetRedisClient(client *redis.Client) {
	sm.redisClient = client
}

// GetSystemInfo 获取系统信息
func (sm *SystemMonitor) GetSystemInfo() map[string]interface{} {
	info := make(map[string]interface{})
	
	// 基本信息
	info["hostname"], _ = os.Hostname()
	info["os"] = runtime.GOOS
	info["arch"] = runtime.GOARCH
	info["go_version"] = runtime.Version()
	info["num_cpu"] = runtime.NumCPU()
	info["num_goroutine"] = runtime.NumGoroutine()
	
	// 内存统计
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 获取系统总内存（这是一个近似值，因为Go没有直接的方法获取系统总内存）
	totalSysMem := sm.getSystemTotalMemory()
	availableMem := totalSysMem - m.Sys
	if availableMem < 0 {
		availableMem = 0
	}
	
	info["memory"] = map[string]interface{}{
		"total_system":  sm.formatBytes(totalSysMem),       // 系统总内存
		"used":          sm.formatBytes(m.Sys),             // 程序使用的内存
		"available":     sm.formatBytes(uint64(availableMem)), // 可用内存
		"percent":       fmt.Sprintf("%.2f%%", float64(m.Sys)/float64(totalSysMem)*100), // 使用百分比
		"alloc":         sm.formatBytes(m.Alloc),           // 当前分配的内存
		"total_alloc":   sm.formatBytes(m.TotalAlloc),      // 总共分配的内存
		"sys":           sm.formatBytes(m.Sys),             // 从系统获得的内存
		"heap_alloc":    sm.formatBytes(m.HeapAlloc),       // 堆上分配的内存
		"heap_sys":      sm.formatBytes(m.HeapSys),         // 堆从系统获得的内存
		"heap_objects":  m.HeapObjects,                     // 堆对象数量
		"gc_count":      m.NumGC,                           // GC运行次数
	}
	
	// 磁盘信息（获取当前目录的磁盘使用情况）
	diskInfo := sm.getDiskUsage()
	info["disk"] = diskInfo
	
	// 网络状态
	networkInfo := sm.getNetworkStatus()
	info["network"] = networkInfo
	
	// 应用信息
	info["app_version"] = "1.4.3"
	info["start_time"] = sm.startTime.Format("2006-01-02 15:04:05")
	info["uptime"] = sm.formatUptime(uint64(time.Since(sm.startTime).Seconds()))
	
	// 缓存目录大小
	cacheSize := sm.getCacheDirectorySize()
	info["cache_size"] = sm.formatBytes(uint64(cacheSize))
	
	// 数据库状态
	info["database"] = sm.getDatabaseStatus()
	
	// Redis状态
	info["redis"] = sm.getRedisStatus()
	
	return info
}

// getDiskUsage 获取磁盘使用情况
func (sm *SystemMonitor) getDiskUsage() map[string]interface{} {
	pwd, _ := os.Getwd()
	
	diskInfo := map[string]interface{}{
		"working_directory": pwd,
		"cache_directory": "./cache",
	}
	
	// 获取磁盘统计信息
	if usage, err := sm.getDiskStats(pwd); err == nil {
		diskInfo["total"] = sm.formatBytes(usage.Total)
		diskInfo["used"] = sm.formatBytes(usage.Used)
		diskInfo["free"] = sm.formatBytes(usage.Free)
		diskInfo["percent"] = fmt.Sprintf("%.2f%%", usage.UsedPercent)
	}
	
	return diskInfo
}

// getNetworkStatus 获取网络状态
func (sm *SystemMonitor) getNetworkStatus() map[string]interface{} {
	networkInfo := make(map[string]interface{})
	
	// 获取网络接口信息
	interfaces := make([]map[string]interface{}, 0)
	if ifaces, err := net.Interfaces(); err == nil {
		for _, iface := range ifaces {
			if iface.Flags&net.FlagUp != 0 && iface.Flags&net.FlagLoopback == 0 {
				ifaceInfo := map[string]interface{}{
					"name":  iface.Name,
					"flags": iface.Flags.String(),
				}
				
				// 获取IP地址
				if addrs, err := iface.Addrs(); err == nil {
					ips := make([]string, 0)
					for _, addr := range addrs {
						ips = append(ips, addr.String())
					}
					ifaceInfo["addresses"] = ips
				}
				
				interfaces = append(interfaces, ifaceInfo)
			}
		}
	}
	networkInfo["interfaces"] = interfaces
	
	// 检查外网连接
	networkInfo["internet_connected"] = sm.checkInternetConnection()
	
	// 监听端口
	networkInfo["listening_port"] = "8080"
	
	return networkInfo
}

// checkInternetConnection 检查互联网连接
func (sm *SystemMonitor) checkInternetConnection() bool {
	// 尝试连接常见的DNS服务器
	conn, err := net.DialTimeout("tcp", "*******:53", 3*time.Second)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}

// getCacheDirectorySize 获取缓存目录大小
func (sm *SystemMonitor) getCacheDirectorySize() int64 {
	var size int64
	
	err := filepath.Walk("./cache", func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})
	
	if err != nil {
		sm.logger.Error("计算缓存目录大小失败", zap.Error(err))
	}
	
	return size
}

// formatBytes 格式化字节数
func (sm *SystemMonitor) formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.2f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// formatUptime 格式化运行时间
func (sm *SystemMonitor) formatUptime(seconds uint64) string {
	days := seconds / 86400
	hours := (seconds % 86400) / 3600
	minutes := (seconds % 3600) / 60
	secs := seconds % 60
	
	if days > 0 {
		return fmt.Sprintf("%d天 %d小时 %d分钟", days, hours, minutes)
	} else if hours > 0 {
		return fmt.Sprintf("%d小时 %d分钟", hours, minutes)
	} else if minutes > 0 {
		return fmt.Sprintf("%d分钟 %d秒", minutes, secs)
	}
	return fmt.Sprintf("%d秒", secs)
}

// DiskUsage 磁盘使用情况
type DiskUsage struct {
	Total       uint64
	Used        uint64
	Free        uint64
	UsedPercent float64
}

// getDiskStats 获取磁盘统计信息
func (sm *SystemMonitor) getDiskStats(path string) (*DiskUsage, error) {
	var stat syscall.Statfs_t
	err := syscall.Statfs(path, &stat)
	if err != nil {
		return nil, err
	}
	
	// 计算磁盘使用情况
	total := uint64(stat.Blocks) * uint64(stat.Bsize)
	free := uint64(stat.Bfree) * uint64(stat.Bsize)
	used := total - free
	usedPercent := float64(used) / float64(total) * 100
	
	return &DiskUsage{
		Total:       total,
		Used:        used,
		Free:        free,
		UsedPercent: usedPercent,
	}, nil
}

// getDatabaseStatus 获取数据库状态
func (sm *SystemMonitor) getDatabaseStatus() map[string]interface{} {
	dbInfo := map[string]interface{}{
		"connected": false,
		"type": "PostgreSQL",
	}
	
	if sm.db == nil {
		dbInfo["status"] = "未连接"
		dbInfo["error"] = "数据库连接未初始化"
		return dbInfo
	}
	
	// 检查数据库连接
	sqlDB, err := sm.db.DB()
	if err != nil {
		dbInfo["status"] = "连接失败"
		dbInfo["error"] = err.Error()
		return dbInfo
	}
	
	// Ping数据库
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	
	if err := sqlDB.PingContext(ctx); err != nil {
		dbInfo["status"] = "连接失败"
		dbInfo["error"] = err.Error()
		return dbInfo
	}
	
	dbInfo["connected"] = true
	dbInfo["status"] = "正常"
	
	// 获取连接池统计
	stats := sqlDB.Stats()
	dbInfo["stats"] = map[string]interface{}{
		"open_connections": stats.OpenConnections,
		"in_use": stats.InUse,
		"idle": stats.Idle,
		"wait_count": stats.WaitCount,
		"wait_duration": stats.WaitDuration.String(),
		"max_idle_closed": stats.MaxIdleClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}
	
	// 获取数据库大小（PostgreSQL特定）
	var dbSize string
	if err := sm.db.Raw("SELECT pg_size_pretty(pg_database_size(current_database()))").Scan(&dbSize).Error; err == nil {
		dbInfo["size"] = dbSize
	}
	
	// 获取表数量
	var tableCount int64
	if err := sm.db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'").Scan(&tableCount).Error; err == nil {
		dbInfo["table_count"] = tableCount
	}
	
	// 获取活动连接数
	var activeConnections int64
	if err := sm.db.Raw("SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active'").Scan(&activeConnections).Error; err == nil {
		dbInfo["active_queries"] = activeConnections
	}
	
	return dbInfo
}

// getRedisStatus 获取Redis状态
func (sm *SystemMonitor) getRedisStatus() map[string]interface{} {
	redisInfo := map[string]interface{}{
		"connected": false,
		"type": "Redis",
	}
	
	if sm.redisClient == nil {
		redisInfo["status"] = "未连接"
		redisInfo["error"] = "Redis客户端未初始化"
		return redisInfo
	}
	
	// Ping Redis
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	
	if err := sm.redisClient.Ping(ctx).Err(); err != nil {
		redisInfo["status"] = "连接失败"
		redisInfo["error"] = err.Error()
		return redisInfo
	}
	
	redisInfo["connected"] = true
	redisInfo["status"] = "正常"
	
	// 获取Redis信息
	info, err := sm.redisClient.Info(ctx).Result()
	if err == nil {
		// 解析Redis INFO命令结果
		redisStats := sm.parseRedisInfo(info)
		redisInfo["stats"] = redisStats
		
		// 获取数据库大小
		dbSize, _ := sm.redisClient.DBSize(ctx).Result()
		redisInfo["db_size"] = dbSize
	}
	
	// 获取连接池统计
	poolStats := sm.redisClient.PoolStats()
	redisInfo["pool_stats"] = map[string]interface{}{
		"hits": poolStats.Hits,
		"misses": poolStats.Misses,
		"timeouts": poolStats.Timeouts,
		"total_conns": poolStats.TotalConns,
		"idle_conns": poolStats.IdleConns,
		"stale_conns": poolStats.StaleConns,
	}
	
	return redisInfo
}

// parseRedisInfo 解析Redis INFO命令结果
func (sm *SystemMonitor) parseRedisInfo(info string) map[string]interface{} {
	stats := make(map[string]interface{})
	
	// 这里简化处理，只提取一些关键指标
	stats["version"] = sm.extractRedisValue(info, "redis_version:")
	stats["uptime_in_seconds"] = sm.extractRedisValue(info, "uptime_in_seconds:")
	stats["connected_clients"] = sm.extractRedisValue(info, "connected_clients:")
	stats["used_memory_human"] = sm.extractRedisValue(info, "used_memory_human:")
	stats["used_memory_peak_human"] = sm.extractRedisValue(info, "used_memory_peak_human:")
	stats["total_commands_processed"] = sm.extractRedisValue(info, "total_commands_processed:")
	stats["instantaneous_ops_per_sec"] = sm.extractRedisValue(info, "instantaneous_ops_per_sec:")
	stats["keyspace_hits"] = sm.extractRedisValue(info, "keyspace_hits:")
	stats["keyspace_misses"] = sm.extractRedisValue(info, "keyspace_misses:")
	
	// 计算命中率
	hits := sm.extractRedisValueAsInt(info, "keyspace_hits:")
	misses := sm.extractRedisValueAsInt(info, "keyspace_misses:")
	if hits+misses > 0 {
		hitRate := float64(hits) / float64(hits+misses) * 100
		stats["hit_rate"] = fmt.Sprintf("%.2f%%", hitRate)
	}
	
	return stats
}

// extractRedisValue 从Redis INFO输出中提取值
func (sm *SystemMonitor) extractRedisValue(info, key string) string {
	// 按行分割INFO输出
	lines := strings.Split(info, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, key) {
			// 提取键值对中的值
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				return strings.TrimSpace(parts[1])
			}
		}
	}
	return "N/A"
}

// extractRedisValueAsInt 从Redis INFO输出中提取整数值
func (sm *SystemMonitor) extractRedisValueAsInt(info, key string) int64 {
	valueStr := sm.extractRedisValue(info, key)
	if valueStr == "N/A" {
		return 0
	}
	
	var value int64
	fmt.Sscanf(valueStr, "%d", &value)
	return value
}

// getSystemTotalMemory 获取系统总内存
func (sm *SystemMonitor) getSystemTotalMemory() uint64 {
	// 在不同平台上获取系统内存的方式不同
	// 这里使用一个简化的方法，返回一个合理的估计值
	
	// 获取当前进程的内存状态
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 基于系统分配给进程的内存来估计总内存
	// 通常系统总内存会是进程系统内存的很多倍
	// 这里使用一个保守的估计
	if m.Sys > 0 {
		// 假设程序最多使用系统内存的10%
		return m.Sys * 10
	}
	
	// 默认返回8GB
	return 8 * 1024 * 1024 * 1024
}