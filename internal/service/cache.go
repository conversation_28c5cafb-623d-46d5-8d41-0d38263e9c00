package service

import (
	"crypto/md5"
	"encoding/hex"
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
)

type CacheService struct {
	memCache      *cache.Cache
	fileCachePath string
	mu            sync.RWMutex
}

func NewCacheService(duration time.Duration) *CacheService {
	return &CacheService{
		memCache: cache.New(duration, duration*2),
	}
}

func (cs *CacheService) Get(key string) (interface{}, bool) {
	return cs.memCache.Get(key)
}

func (cs *CacheService) Set(key string, value interface{}, duration time.Duration) {
	cs.memCache.Set(key, value, duration)
}

func (cs *CacheService) Delete(key string) {
	cs.memCache.Delete(key)
}

func (cs *CacheService) Clear() {
	cs.memCache.Flush()
}

func (cs *CacheService) GetCacheKey(url string) string {
	h := md5.New()
	h.Write([]byte(url))
	return hex.EncodeToString(h.Sum(nil))
}