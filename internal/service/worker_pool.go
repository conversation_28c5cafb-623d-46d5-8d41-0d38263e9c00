package service

import (
	"sync"
	"time"
)

// WorkerPool 工作池，用于限制并发goroutine数量
type WorkerPool struct {
	maxWorkers int
	tasks      chan func()
	wg         sync.WaitGroup
	quit       chan bool
}

// NewWorkerPool 创建新的工作池
func NewWorkerPool(maxWorkers int) *WorkerPool {
	if maxWorkers <= 0 {
		maxWorkers = 10 // 默认10个工作协程
	}
	
	pool := &WorkerPool{
		maxWorkers: maxWorkers,
		tasks:      make(chan func(), maxWorkers*2), // 缓冲区是工作协程数的2倍
		quit:       make(chan bool),
	}
	
	// 启动工作协程
	for i := 0; i < maxWorkers; i++ {
		go pool.worker()
	}
	
	return pool
}

// worker 工作协程
func (p *WorkerPool) worker() {
	for {
		select {
		case task := <-p.tasks:
			if task != nil {
				// 使用recover防止panic导致worker退出
				func() {
					defer func() {
						if r := recover(); r != nil {
							// 可以在这里记录panic信息
						}
					}()
					task()
				}()
			}
		case <-p.quit:
			return
		}
	}
}

// Submit 提交任务到工作池
func (p *WorkerPool) Submit(task func()) {
	select {
	case p.tasks <- task:
		// 任务成功提交
	case <-time.After(5 * time.Second):
		// 如果5秒内无法提交任务，直接在当前goroutine执行
		// 防止阻塞
		task()
	}
}

// SubmitWait 提交任务并等待完成
func (p *WorkerPool) SubmitWait(task func()) {
	done := make(chan bool, 1)
	p.Submit(func() {
		task()
		done <- true
	})
	<-done
}

// Stop 停止工作池
func (p *WorkerPool) Stop() {
	close(p.quit)
	// 等待所有任务完成
	close(p.tasks)
}