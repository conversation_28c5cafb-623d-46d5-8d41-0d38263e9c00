package service

import (
	"context"
	"runtime"
	"sync"
	"time"
	"go.uber.org/zap"
)

// PerformanceMonitor 性能监控服务
type PerformanceMonitor struct {
	logger  *zap.Logger
	metrics *Metrics
	mu      sync.RWMutex
	ctx     context.Context
	cancel  context.CancelFunc
	
	// 带宽计算相关
	bandwidthMu      sync.RWMutex
	uploadBytes      []timestampedBytes
	downloadBytes    []timestampedBytes
	
	// 数据库连接（用于监控）
	db interface{}
	// Redis客户端（用于监控）
	redisClient interface{}
}

// timestampedBytes 带时间戳的字节数
type timestampedBytes struct {
	bytes     uint64
	timestamp time.Time
}

// Metrics 性能指标
type Metrics struct {
	// 系统指标
	CPUUsage       float64
	MemoryUsage    uint64
	GoroutineCount int
	
	// 请求指标
	RequestCount   int64
	ErrorCount     int64
	AvgLatency     float64
	P95Latency     float64
	P99Latency     float64
	
	// 缓存指标
	CacheHitRate   float64
	CacheMissCount int64
	
	// 数据库指标
	DBConnections  int
	DBSlowQueries  int64
	
	// 并发指标
	ConcurrentReqs int32
	QueueSize      int
	
	// 站点指标
	ActiveSites    int
	TotalSites     int
	
	// 带宽指标（字节/秒）
	UploadSpeed    float64  // 上传速度
	DownloadSpeed  float64  // 下载速度
	TotalUpload    uint64   // 总上传字节数
	TotalDownload  uint64   // 总下载字节数
	
	// 时间戳
	UpdatedAt      time.Time
}

// NewPerformanceMonitor 创建性能监控
func NewPerformanceMonitor(logger *zap.Logger) *PerformanceMonitor {
	ctx, cancel := context.WithCancel(context.Background())
	
	pm := &PerformanceMonitor{
		logger:        logger,
		metrics:       &Metrics{},
		ctx:           ctx,
		cancel:        cancel,
		uploadBytes:   make([]timestampedBytes, 0),
		downloadBytes: make([]timestampedBytes, 0),
	}
	
	// 启动监控协程
	go pm.monitor()
	
	return pm
}

// monitor 执行监控
func (pm *PerformanceMonitor) monitor() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			pm.collectMetrics()
		case <-pm.ctx.Done():
			return
		}
	}
}

// SetDB 设置数据库连接
func (pm *PerformanceMonitor) SetDB(db interface{}) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.db = db
}

// SetRedisClient 设置Redis客户端
func (pm *PerformanceMonitor) SetRedisClient(client interface{}) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.redisClient = client
}

// collectMetrics 收集指标
func (pm *PerformanceMonitor) collectMetrics() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	// 系统指标
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	pm.metrics.MemoryUsage = m.Alloc
	pm.metrics.GoroutineCount = runtime.NumGoroutine()
	pm.metrics.UpdatedAt = time.Now()
	
	// CPU使用率（简化版）
	pm.metrics.CPUUsage = float64(runtime.NumCPU())
	
	// 收集数据库连接数
	if pm.db != nil {
		// 尝试获取数据库统计信息
		if db, ok := pm.db.(interface{ Stats() interface{} }); ok {
			if stats := db.Stats(); stats != nil {
				// 如果是sql.DBStats类型
				if dbStats, ok := stats.(struct{ OpenConnections int }); ok {
					pm.metrics.DBConnections = dbStats.OpenConnections
				}
			}
		}
	}
}

// RecordRequest 记录请求
func (pm *PerformanceMonitor) RecordRequest(latency time.Duration, isError bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.metrics.RequestCount++
	if isError {
		pm.metrics.ErrorCount++
	}
	
	// 更新平均延迟（滑动平均）
	pm.metrics.AvgLatency = pm.metrics.AvgLatency*0.95 + latency.Seconds()*0.05
}

// RecordCacheHit 记录缓存命中
func (pm *PerformanceMonitor) RecordCacheHit(hit bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	if !hit {
		pm.metrics.CacheMissCount++
	}
	
	// 计算命中率（最近1000次请求）
	totalRequests := pm.metrics.RequestCount
	if totalRequests > 0 {
		pm.metrics.CacheHitRate = 1.0 - float64(pm.metrics.CacheMissCount)/float64(totalRequests)
	}
}

// UpdateConcurrency 更新并发数
func (pm *PerformanceMonitor) UpdateConcurrency(delta int32) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	
	pm.metrics.ConcurrentReqs += delta
}

// GetMetrics 获取指标
func (pm *PerformanceMonitor) GetMetrics() *Metrics {
	pm.mu.RLock()
	defer pm.mu.RUnlock()
	
	// 返回副本
	metrics := *pm.metrics
	return &metrics
}

// LogMetrics 记录指标到日志
func (pm *PerformanceMonitor) LogMetrics() {
	metrics := pm.GetMetrics()
	
	pm.logger.Info("系统性能指标",
		zap.Float64("cpu_usage", metrics.CPUUsage),
		zap.Uint64("memory_mb", metrics.MemoryUsage/1024/1024),
		zap.Int("goroutines", metrics.GoroutineCount),
		zap.Int64("requests", metrics.RequestCount),
		zap.Int64("errors", metrics.ErrorCount),
		zap.Float64("avg_latency_ms", metrics.AvgLatency*1000),
		zap.Float64("cache_hit_rate", metrics.CacheHitRate),
		zap.Int32("concurrent_reqs", metrics.ConcurrentReqs),
	)
}

// GetHealthStatus 获取健康状态
func (pm *PerformanceMonitor) GetHealthStatus() map[string]interface{} {
	metrics := pm.GetMetrics()
	
	// 定义健康阈值
	healthy := true
	warnings := []string{}
	
	if metrics.ErrorCount > 100 {
		warnings = append(warnings, "高错误率")
		healthy = false
	}
	
	if metrics.MemoryUsage > 1024*1024*1024 { // 1GB
		warnings = append(warnings, "内存使用过高")
	}
	
	if metrics.GoroutineCount > 10000 {
		warnings = append(warnings, "协程数过多")
		healthy = false
	}
	
	if metrics.CacheHitRate < 0.5 {
		warnings = append(warnings, "缓存命中率低")
	}
	
	if metrics.AvgLatency > 1.0 { // 1秒
		warnings = append(warnings, "响应时间过长")
	}
	
	return map[string]interface{}{
		"health": map[string]interface{}{
			"healthy":  healthy,
			"warnings": warnings,
		},
		"metrics":   metrics,
		"timestamp": time.Now(),
	}
}

// RecordBandwidth 记录带宽使用
func (pm *PerformanceMonitor) RecordBandwidth(uploadBytes, downloadBytes uint64) {
	pm.bandwidthMu.Lock()
	defer pm.bandwidthMu.Unlock()
	
	now := time.Now()
	
	// 记录上传字节
	if uploadBytes > 0 {
		pm.uploadBytes = append(pm.uploadBytes, timestampedBytes{
			bytes:     uploadBytes,
			timestamp: now,
		})
		pm.metrics.TotalUpload += uploadBytes
	}
	
	// 记录下载字节
	if downloadBytes > 0 {
		pm.downloadBytes = append(pm.downloadBytes, timestampedBytes{
			bytes:     downloadBytes,
			timestamp: now,
		})
		pm.metrics.TotalDownload += downloadBytes
	}
	
	// 清理1分钟前的数据
	cutoff := now.Add(-time.Minute)
	pm.uploadBytes = pm.cleanOldData(pm.uploadBytes, cutoff)
	pm.downloadBytes = pm.cleanOldData(pm.downloadBytes, cutoff)
	
	// 计算速度（字节/秒）
	pm.calculateBandwidthSpeed()
}

// cleanOldData 清理旧数据
func (pm *PerformanceMonitor) cleanOldData(data []timestampedBytes, cutoff time.Time) []timestampedBytes {
	var cleaned []timestampedBytes
	for _, d := range data {
		if d.timestamp.After(cutoff) {
			cleaned = append(cleaned, d)
		}
	}
	return cleaned
}

// calculateBandwidthSpeed 计算带宽速度
func (pm *PerformanceMonitor) calculateBandwidthSpeed() {
	now := time.Now()
	window := 10 * time.Second // 10秒窗口
	cutoff := now.Add(-window)
	
	var uploadTotal uint64
	var downloadTotal uint64
	
	// 计算窗口内的总字节数
	for _, d := range pm.uploadBytes {
		if d.timestamp.After(cutoff) {
			uploadTotal += d.bytes
		}
	}
	
	for _, d := range pm.downloadBytes {
		if d.timestamp.After(cutoff) {
			downloadTotal += d.bytes
		}
	}
	
	// 计算速度（字节/秒）
	pm.metrics.UploadSpeed = float64(uploadTotal) / window.Seconds()
	pm.metrics.DownloadSpeed = float64(downloadTotal) / window.Seconds()
}

// Stop 停止监控
func (pm *PerformanceMonitor) Stop() {
	pm.cancel()
}