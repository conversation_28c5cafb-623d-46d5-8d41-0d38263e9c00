package service

import (
	"crypto/tls"
	"net"
	"net/http"
	"sync"
	"time"
	"site-cluster/internal/model"
)

// HTTPClientPool HTTP客户端池
type HTTPClientPool struct {
	clients        map[string]*http.Client
	mu             sync.RWMutex // 保护clients map的读写锁
	systemSettings *model.SystemSettings // 系统设置
	settingsMu     sync.RWMutex // 保护系统设置的读写锁
}

// NewHTTPClientPool 创建HTTP客户端池
func NewHTTPClientPool() *HTTPClientPool {
	return &HTTPClientPool{
		clients: make(map[string]*http.Client),
	}
}

// UpdateSettings 更新系统设置
func (p *HTTPClientPool) UpdateSettings(settings *model.SystemSettings) {
	p.settingsMu.Lock()
	p.systemSettings = settings
	p.settingsMu.Unlock()
	
	// 清空现有客户端，强制使用新配置重新创建
	p.mu.Lock()
	p.clients = make(map[string]*http.Client)
	p.mu.Unlock()
}

// GetClient 获取或创建客户端
func (p *HTTPClientPool) GetClient(key string) *http.Client {
	// 先尝试读锁获取
	p.mu.RLock()
	if client, exists := p.clients[key]; exists {
		p.mu.RUnlock()
		return client
	}
	p.mu.RUnlock()
	
	// 需要创建新客户端，使用写锁
	p.mu.Lock()
	defer p.mu.Unlock()
	
	// 双重检查，防止其他goroutine已经创建
	if client, exists := p.clients[key]; exists {
		return client
	}
	
	// 获取配置值，如果系统设置不存在则使用默认值
	maxIdleConns := 50
	maxIdleConnsPerHost := 5
	maxConnsPerHost := 10
	idleConnTimeout := 90
	
	p.settingsMu.RLock()
	if p.systemSettings != nil {
		if p.systemSettings.HTTPMaxIdleConns > 0 {
			maxIdleConns = p.systemSettings.HTTPMaxIdleConns
		}
		if p.systemSettings.HTTPMaxIdleConnsPerHost > 0 {
			maxIdleConnsPerHost = p.systemSettings.HTTPMaxIdleConnsPerHost
		}
		if p.systemSettings.HTTPMaxConnsPerHost > 0 {
			maxConnsPerHost = p.systemSettings.HTTPMaxConnsPerHost
		}
		if p.systemSettings.HTTPIdleConnTimeout > 0 {
			idleConnTimeout = p.systemSettings.HTTPIdleConnTimeout
		}
	}
	p.settingsMu.RUnlock()
	
	// 创建优化的HTTP客户端 - 使用系统设置的配置
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
				DualStack: false, // 关闭双栈避免过多连接
			}).DialContext,
			MaxIdleConns:          maxIdleConns,
			MaxIdleConnsPerHost:   maxIdleConnsPerHost,
			MaxConnsPerHost:       maxConnsPerHost,
			IdleConnTimeout:       time.Duration(idleConnTimeout) * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			DisableCompression:    false,
			DisableKeepAlives:     false, // 保持连接复用
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // 跳过证书验证（生产环境慎用）
			},
		},
	}
	
	p.clients[key] = client
	return client
}

// GetDefaultClient 获取默认客户端
func (p *HTTPClientPool) GetDefaultClient() *http.Client {
	return p.GetClient("default")
}