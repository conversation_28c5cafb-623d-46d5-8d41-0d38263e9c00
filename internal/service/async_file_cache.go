package service

import (
	"context"
	"sync"
	"time"
	"site-cluster/internal/model"
	"go.uber.org/zap"
)

// AsyncFileCacheService 异步文件缓存服务
type AsyncFileCacheService struct {
	*FileCacheService
	writeQueue chan *writeTask
	writeBatch map[string]*writeTask
	batchMutex sync.Mutex
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
}

type writeTask struct {
	domain  string
	content *model.CachedContent
	retries int
	callback func(error)
}

// NewAsyncFileCacheService 创建异步文件缓存服务
func NewAsyncFileCacheService(logger *zap.Logger, basePath string) *AsyncFileCacheService {
	ctx, cancel := context.WithCancel(context.Background())
	
	service := &AsyncFileCacheService{
		FileCacheService: NewFileCacheService(logger, basePath),
		writeQueue:       make(chan *writeTask, 10000), // 大缓冲队列
		writeBatch:       make(map[string]*writeTask),
		ctx:             ctx,
		cancel:          cancel,
	}
	
	// 启动写入工作协程
	for i := 0; i < 10; i++ { // 10个写入协程
		service.wg.Add(1)
		go service.writeWorker(i)
	}
	
	// 启动批量写入协程
	service.wg.Add(1)
	go service.batchWriter()
	
	return service
}

// SaveContentAsync 异步保存内容
func (s *AsyncFileCacheService) SaveContentAsync(domain string, content *model.CachedContent, callback func(error)) error {
	task := &writeTask{
		domain:   domain,
		content:  content,
		callback: callback,
	}
	
	select {
	case s.writeQueue <- task:
		return nil
	default:
		// 队列满了，降级为同步写入
		s.logger.Warn("写入队列已满，降级为同步写入")
		return s.SaveContent(domain, content)
	}
}

// writeWorker 写入工作协程
func (s *AsyncFileCacheService) writeWorker(id int) {
	defer s.wg.Done()
	
	for {
		select {
		case task := <-s.writeQueue:
			if task == nil {
				return
			}
			
			// 尝试加入批量写入
			if s.addToBatch(task) {
				continue
			}
			
			// 执行写入
			err := s.SaveContent(task.domain, task.content)
			if err != nil && task.retries < 3 {
				task.retries++
				s.writeQueue <- task // 重试
			} else if task.callback != nil {
				task.callback(err)
			}
			
		case <-s.ctx.Done():
			return
		}
	}
}

// addToBatch 添加到批量写入
func (s *AsyncFileCacheService) addToBatch(task *writeTask) bool {
	s.batchMutex.Lock()
	defer s.batchMutex.Unlock()
	
	key := task.domain + ":" + task.content.URL
	s.writeBatch[key] = task
	
	// 批量大小达到阈值
	return len(s.writeBatch) < 100
}

// batchWriter 批量写入协程
func (s *AsyncFileCacheService) batchWriter() {
	defer s.wg.Done()
	
	ticker := time.NewTicker(100 * time.Millisecond) // 100ms刷新一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			s.flushBatch()
		case <-s.ctx.Done():
			s.flushBatch() // 最后刷新
			return
		}
	}
}

// flushBatch 刷新批量写入
func (s *AsyncFileCacheService) flushBatch() {
	s.batchMutex.Lock()
	if len(s.writeBatch) == 0 {
		s.batchMutex.Unlock()
		return
	}
	
	batch := s.writeBatch
	s.writeBatch = make(map[string]*writeTask)
	s.batchMutex.Unlock()
	
	// 并发写入
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 20) // 限制并发数
	
	for _, task := range batch {
		wg.Add(1)
		semaphore <- struct{}{}
		
		go func(t *writeTask) {
			defer wg.Done()
			defer func() { <-semaphore }()
			
			err := s.SaveContent(t.domain, t.content)
			if t.callback != nil {
				t.callback(err)
			}
		}(task)
	}
	
	wg.Wait()
}

// GetQueueSize 获取队列大小
func (s *AsyncFileCacheService) GetQueueSize() int {
	return len(s.writeQueue)
}

// Stop 停止服务
func (s *AsyncFileCacheService) Stop() {
	s.cancel()
	close(s.writeQueue)
	s.wg.Wait()
}