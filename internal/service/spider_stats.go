package service

import (
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	"site-cluster/internal/model"
	"gorm.io/gorm"
	"go.uber.org/zap"
)

// SpiderStatsService 爬虫统计服务
type SpiderStatsService struct {
	db           *gorm.DB
	logger       *zap.Logger
	configCache  []model.SpiderConfig
	configMutex  sync.RWMutex
	lastCacheUpdate time.Time
}

// NewSpiderStatsService 创建爬虫统计服务
func NewSpiderStatsService(db *gorm.DB, logger *zap.Logger) *SpiderStatsService {
	service := &SpiderStatsService{
		db:              db,
		logger:          logger,
		configCache:     []model.SpiderConfig{},
		lastCacheUpdate: time.Now(),
	}
	// 初始加载配置到缓存
	go service.loadConfigCache() // 异步加载，避免阻塞启动
	return service
}

// RecordSpiderVisit 记录爬虫访问（保留兼容性）
func (s *SpiderStatsService) RecordSpiderVisit(domain, userAgent string) error {
	// 旧接口，只有域名没有站点ID，为了兼容性保留
	return s.RecordSpiderVisitWithSiteID(0, domain, userAgent)
}

// RecordSpiderVisitWithSiteID 记录爬虫访问（带站点ID）
func (s *SpiderStatsService) RecordSpiderVisitWithSiteID(siteID uint, domain, userAgent string) error {
	// 记录原始域名以便调试
	// s.logger.Info("收到爬虫访问记录请求", 
	//	zap.Uint("siteID", siteID),
	//	zap.String("originalDomain", domain),
	//	zap.String("userAgent", userAgent))
	
	// 识别爬虫类型
	spiderName := s.identifySpider(userAgent)
	if spiderName == "" {
		// 生产环境：静默处理未识别的爬虫
		// s.logger.Debug("未识别的爬虫", zap.String("userAgent", userAgent))
		return nil // 不是已知爬虫，不记录
	}
	
	// s.logger.Info("记录爬虫访问", 
	//	zap.Uint("siteID", siteID),
	//	zap.String("domain", domain),
	//	zap.String("spider", spiderName),
	//	zap.String("userAgent", userAgent))

	// 检查是否启用该爬虫的统计
	var config model.SpiderConfig
	if err := s.db.Where("name = ? AND enabled = ?", spiderName, true).First(&config).Error; err != nil {
		return nil // 未配置或未启用，不记录
	}

	now := time.Now()
	
	// 1. 更新分钟级全局统计（不分域名）
	minuteTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, now.Location())
	var minuteStats model.SpiderStatsMinute
	err := s.db.Where("spider_name = ? AND timestamp = ?", spiderName, minuteTime).First(&minuteStats).Error
	if err == gorm.ErrRecordNotFound {
		minuteStats = model.SpiderStatsMinute{
			SpiderName: spiderName,
			Count:      1,
			Timestamp:  minuteTime,
		}
		if err := s.db.Create(&minuteStats).Error; err != nil {
			// s.logger.Error("创建分钟级统计失败", zap.Error(err))
		}
	} else if err == nil {
		if err := s.db.Model(&minuteStats).Update("count", gorm.Expr("count + ?", 1)).Error; err != nil {
			// s.logger.Error("更新分钟级统计失败", zap.Error(err))
		}
	}
	
	// 2. 更新小时级站点统计
	hourTime := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
	var hourStats model.SpiderStatsHour
	
	// 如果有站点ID，使用站点ID查询；否则使用域名
	if siteID > 0 {
		err = s.db.Where("site_id = ? AND spider_name = ? AND timestamp = ?", siteID, spiderName, hourTime).First(&hourStats).Error
	} else {
		err = s.db.Where("domain = ? AND spider_name = ? AND timestamp = ?", domain, spiderName, hourTime).First(&hourStats).Error
	}
	
	if err == gorm.ErrRecordNotFound {
		hourStats = model.SpiderStatsHour{
			SiteID:     siteID,
			Domain:     domain,
			SpiderName: spiderName,
			Count:      1,
			Timestamp:  hourTime,
		}
		if err := s.db.Create(&hourStats).Error; err != nil {
			// s.logger.Error("创建小时级统计失败", zap.Error(err))
		}
	} else if err == nil {
		if err := s.db.Model(&hourStats).Update("count", gorm.Expr("count + ?", 1)).Error; err != nil {
			// s.logger.Error("更新小时级统计失败", zap.Error(err))
		}
	}

	// 3. 更新每日统计
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	
	var dailyStats model.SpiderStats
	// 如果有站点ID，使用站点ID查询；否则使用域名
	if siteID > 0 {
		err = s.db.Where("site_id = ? AND spider_name = ? AND date = ?", siteID, spiderName, today).First(&dailyStats).Error
	} else {
		err = s.db.Where("domain = ? AND spider_name = ? AND date = ?", domain, spiderName, today).First(&dailyStats).Error
	}
	
	if err == gorm.ErrRecordNotFound {
		dailyStats = model.SpiderStats{
			SiteID:     siteID,
			Domain:     domain,
			SpiderName: spiderName,
			Count:      1,
			Date:       today,
		}
		return s.db.Create(&dailyStats).Error
	} else if err == nil {
		return s.db.Model(&dailyStats).Update("count", gorm.Expr("count + ?", 1)).Error
	}
	
	return err
}

// IdentifySpider 识别爬虫类型（公开方法）
func (s *SpiderStatsService) IdentifySpider(userAgent string) string {
	return s.identifySpider(userAgent)
}

// normalizeDomain 标准化域名（保持原样，不做转换）
func (s *SpiderStatsService) normalizeDomain(domain string) string {
	// 移除端口号（如果有）
	if colonIndex := strings.IndexByte(domain, ':'); colonIndex != -1 {
		domain = domain[:colonIndex]
	}
	
	// 转换为小写
	domain = strings.ToLower(domain)
	
	// 直接返回域名，不做www转换
	return domain
}

// getDomainVariants 获取域名的所有可能变体（用于查询）
func (s *SpiderStatsService) getDomainVariants(domain string) []string {
	// 转换为小写
	domain = strings.ToLower(domain)
	
	variants := []string{domain}
	
	// 如果是www开头，添加不带www的版本
	if strings.HasPrefix(domain, "www.") {
		baseDomain := strings.TrimPrefix(domain, "www.")
		variants = append(variants, baseDomain)
	} else if strings.Contains(domain, ".") {
		// 如果不是www开头但是个域名，添加www版本
		variants = append(variants, "www."+domain)
	}
	
	return variants
}

// identifySpider 识别爬虫类型（内部实现）
// loadConfigCache 加载爬虫配置到缓存
func (s *SpiderStatsService) loadConfigCache() {
	var configs []model.SpiderConfig
	if err := s.db.Where("enabled = ?", true).Order("priority ASC").Find(&configs).Error; err != nil {
		// 生产环境：静默处理错误
		return
	}
	
	s.configMutex.Lock()
	s.configCache = configs
	s.lastCacheUpdate = time.Now()
	s.configMutex.Unlock()
}

// getConfigFromCache 从缓存获取配置，必要时刷新
func (s *SpiderStatsService) getConfigFromCache() []model.SpiderConfig {
	s.configMutex.RLock()
	// 如果缓存超过5分钟，触发异步刷新
	if time.Since(s.lastCacheUpdate) > 5*time.Minute {
		s.configMutex.RUnlock()
		// 异步刷新，不阻塞当前请求
		go s.loadConfigCache()
		// 使用现有缓存
		s.configMutex.RLock()
		defer s.configMutex.RUnlock()
		return s.configCache
	}
	defer s.configMutex.RUnlock()
	return s.configCache
}

func (s *SpiderStatsService) identifySpider(userAgent string) string {
	ua := strings.ToLower(userAgent)
	
	// 从缓存获取配置，避免每次查询数据库
	configs := s.getConfigFromCache()
	
	// 调试日志已禁用
	// s.logger.Debug("检查爬虫配置", 
	// 	zap.Int("configCount", len(configs)),
	// 	zap.String("userAgent", userAgent))
	
	// 根据配置的 UserAgent 特征匹配
	for _, config := range configs {
		if config.UserAgent != "" && strings.Contains(ua, strings.ToLower(config.UserAgent)) {
			// 生产环境：禁用调试日志
			// s.logger.Debug("匹配到爬虫", 
			// 	zap.String("spider", config.Name),
			// 	zap.String("pattern", config.UserAgent))
			return config.Name
		}
	}
	
	// 如果没有匹配到，返回空字符串
	return ""
}

// GetSpiderStats 获取爬虫统计数据（保留原方法以兼容）
func (s *SpiderStatsService) GetSpiderStats(timeRange string, filterParam string, page, pageSize int) (*model.GetSpiderStatsResponse, error) {
	// 默认按总计降序排序
	return s.GetSpiderStatsWithSort(timeRange, filterParam, page, pageSize, "total", "desc")
}

// GetSpiderStatsWithSort 获取爬虫统计数据（支持排序）
func (s *SpiderStatsService) GetSpiderStatsWithSort(timeRange string, filterParam string, page, pageSize int, sortBy, sortOrder string) (*model.GetSpiderStatsResponse, error) {
	response := &model.GetSpiderStatsResponse{
		Charts:      &model.SpiderChartData{},
		DomainStats: make([]*model.DomainSpiderStats, 0),
		Summary:     make(map[string]*model.SpiderSummary),
	}
	
	// 获取启用的爬虫配置
	var configs []model.SpiderConfig
	if err := s.db.Where("enabled = ?", true).Order("priority ASC").Find(&configs).Error; err != nil {
		return nil, err
	}
	
	// 生成图表数据（传入筛选参数，可能是域名或站点ID）
	if err := s.generateChartData(response.Charts, configs, timeRange, filterParam); err != nil {
		return nil, err
	}
	
	// 生成域名统计（传入排序参数）
	if err := s.generateDomainStatsWithSort(response, configs, filterParam, page, pageSize, sortBy, sortOrder, timeRange); err != nil {
		return nil, err
	}
	
	// 生成汇总数据
	if err := s.generateSummary(response.Summary, configs); err != nil {
		return nil, err
	}
	
	return response, nil
}

// generateChartData 生成图表数据
func (s *SpiderStatsService) generateChartData(chartData *model.SpiderChartData, configs []model.SpiderConfig, timeRange string, filterParam string) error {
	// 解析筛选参数：可能是站点ID或域名
	var siteIDs []uint
	var domainFilters []string
	
	if filterParam != "" {
		// 尝试解析为站点ID
		if siteID, err := strconv.ParseUint(filterParam, 10, 32); err == nil {
			// 是有效的站点ID
			siteIDs = append(siteIDs, uint(siteID))
			// s.logger.Info("使用站点ID筛选", zap.Uint64("siteID", siteID))
		} else {
			// 不是数字，当作域名处理
			// 1. 首先查找sites表中的匹配项
			var sites []model.Site
			cleanDomain := strings.TrimPrefix(filterParam, "www.")
			if err := s.db.Where("domain = ? OR domain = ? OR domain = ? OR domain = ?", 
				filterParam, "www."+filterParam, cleanDomain, "www."+cleanDomain).Find(&sites).Error; err == nil && len(sites) > 0 {
				for _, site := range sites {
					siteIDs = append(siteIDs, site.ID)
					// s.logger.Info("根据域名找到站点", 
					//	zap.String("domain", filterParam),
					//	zap.String("siteDomain", site.Domain),
					//	zap.Uint("siteID", site.ID))
				}
			}
			
			// 2. 同时准备直接域名筛选（用于查找spider_stats表中可能存在但sites表中没有的记录）
			domainFilters = append(domainFilters, filterParam)
			if strings.HasPrefix(filterParam, "www.") {
				domainFilters = append(domainFilters, strings.TrimPrefix(filterParam, "www."))
			} else {
				domainFilters = append(domainFilters, "www."+filterParam)
			}
			
			// s.logger.Info("域名筛选准备", 
			//	zap.String("filterParam", filterParam),
			//	zap.Any("siteIDs", siteIDs),
			//	zap.Any("domainFilters", domainFilters))
		}
	}
	now := time.Now()
	var startTime time.Time
	var interval time.Duration
	var format string
	var useMinuteData bool
	
	switch timeRange {
	case "hour": // 最近1小时，每分钟一个点
		startTime = now.Add(-time.Hour)
		interval = time.Minute
		format = "15:04"
		useMinuteData = true
	case "day": // 最近24小时，每小时一个点
		startTime = now.Add(-24 * time.Hour)
		interval = time.Hour
		format = "15:04"
		useMinuteData = true  // 24小时内仍使用分钟级数据聚合
	case "week": // 最近7天，每天一个点
		startTime = now.AddDate(0, 0, -7)
		interval = 24 * time.Hour
		format = "01-02"
		useMinuteData = false  // 7天使用日统计数据
	case "15days": // 最近15天，每天一个点
		startTime = now.AddDate(0, 0, -15)
		interval = 24 * time.Hour
		format = "01-02"
		useMinuteData = false  // 15天使用日统计数据
	case "month": // 最近30天，每天一个点
		startTime = now.AddDate(0, 0, -30)
		interval = 24 * time.Hour
		format = "01-02"
		useMinuteData = false  // 30天使用日统计数据
	default: // 默认显示最近1小时
		startTime = now.Add(-time.Hour)
		interval = time.Minute
		format = "15:04"
		useMinuteData = true
	}
	
	// 现在分钟级数据也有域名字段了，不需要强制切换
	
	// 生成时间标签（包含今天）
	labels := make([]string, 0)
	endTime := now.Add(interval) // 确保包含当前时间点
	for t := startTime; t.Before(endTime); t = t.Add(interval) {
		labels = append(labels, t.Format(format))
	}
	chartData.Labels = labels
	
	// 为每个爬虫生成数据集（限制最多显示10个）
	maxChartSpiders := 10
	configsToShow := configs
	if len(configs) > maxChartSpiders {
		configsToShow = configs[:maxChartSpiders]
	}
	
	for _, config := range configsToShow {
		dataset := &model.ChartDataset{
			Label:           config.DisplayName,
			BorderColor:     config.Color,
			BackgroundColor: config.Color + "20", // 添加透明度
			Fill:            false,
			Data:            make([]int64, len(labels)),
		}
		
		if useMinuteData && len(siteIDs) == 0 {
			// 使用分钟级全局统计（无域名筛选时）
			if interval == time.Minute {
				// 1小时范围，直接使用分钟数据
				var stats []model.SpiderStatsMinute
				s.db.Where("spider_name = ? AND timestamp >= ? AND timestamp <= ?", 
					config.Name, startTime, now).Find(&stats)
				
				// 创建时间到数据的映射
				dataMap := make(map[string]int64)
				for _, stat := range stats {
					key := stat.Timestamp.Format(format)
					dataMap[key] = stat.Count
				}
				
				// 填充数据
				for i, label := range labels {
					if count, ok := dataMap[label]; ok {
						dataset.Data[i] = count
					}
				}
			} else {
				// 24小时范围，从小时级数据聚合（如果有域名筛选）或从分钟级数据聚合（全局）
				for i, t := 0, startTime; i < len(labels) && t.Before(now); t, i = t.Add(interval), i+1 {
					endTime := t.Add(interval)
					if endTime.After(now) {
						endTime = now
					}
					
					var total int64
					// 全局统计，从分钟级数据聚合
					s.db.Model(&model.SpiderStatsMinute{}).
						Where("spider_name = ? AND timestamp >= ? AND timestamp < ?", 
							config.Name, t, endTime).
						Select("COALESCE(SUM(count), 0)").
						Scan(&total)
					
					dataset.Data[i] = total
				}
			}
		} else if useMinuteData && (len(siteIDs) > 0 || len(domainFilters) > 0) {
			// 有筛选条件时，使用今天的总数据平均分布（因为没有小时级别的筛选数据）
			// s.logger.Info("查询筛选的分钟级数据（使用日统计平均分布）", 
			//	zap.Any("siteIDs", siteIDs),
			//	zap.Any("domainFilters", domainFilters),
			//	zap.String("timeRange", timeRange))
			
			// 使用今天的总数据并平均分布
			today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			var todayTotal int64
			
			// 构建查询
			query := s.db.Model(&model.SpiderStats{}).
				Where("spider_name = ? AND date = ?", config.Name, today)
			
			// 构建OR条件
			var conditions []string
			var args []interface{}
			
			// 添加站点ID条件
			if len(siteIDs) > 0 {
				conditions = append(conditions, "site_id IN ?")
				args = append(args, siteIDs)
			}
			
			// 添加域名条件
			if len(domainFilters) > 0 {
				conditions = append(conditions, "domain IN ?")
				args = append(args, domainFilters)
			}
			
			// 组合条件
			if len(conditions) > 0 {
				whereClause := "(" + strings.Join(conditions, " OR ") + ")"
				query = query.Where(whereClause, args...)
			}
			
			result := query.Select("COALESCE(SUM(count), 0)").Scan(&todayTotal)
			
			if result.Error == nil && todayTotal > 0 {
				// 将今天的总数平均分布到各个时间点
				avgPerPoint := todayTotal / int64(len(labels))
				remainder := todayTotal % int64(len(labels))
				
				for i := 0; i < len(labels); i++ {
					dataset.Data[i] = avgPerPoint
					// 将余数分配到前几个点
					if int64(i) < remainder {
						dataset.Data[i]++
					}
				}
				
				// s.logger.Info("筛选数据平均分布完成", 
				//	zap.Int64("todayTotal", todayTotal),
				//	zap.Int64("avgPerPoint", avgPerPoint),
				//	zap.String("spider", config.Name))
			}
		} else {
			// 使用日统计数据（用于7天以上范围或指定域名筛选）
			// s.logger.Info("使用日统计数据查询", 
			//	zap.String("timeRange", timeRange),
			//	zap.Any("siteIDs", siteIDs),
			//	zap.Int("labelsCount", len(labels)))
			
			totalEndTime := endTime
			for i, t := 0, startTime; i < len(labels) && t.Before(totalEndTime); t, i = t.Add(interval), i+1 {
				periodEnd := t.Add(interval)
				if periodEnd.After(now) {
					periodEnd = now
				}
				
				var total int64
				
				if len(siteIDs) > 0 || len(domainFilters) > 0 {
					// 有筛选条件，查询spider_stats表
					dateStart := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
					// 对于日统计，periodEnd应该包含当天，所以使用<=而不是<
					dateEnd := time.Date(periodEnd.Year(), periodEnd.Month(), periodEnd.Day(), 23, 59, 59, 0, periodEnd.Location())
					
					query := s.db.Model(&model.SpiderStats{}).
						Where("spider_name = ? AND date >= ? AND date <= ?", config.Name, dateStart, dateEnd)
					
					// 构建OR条件
					var conditions []string
					var args []interface{}
					
					// 添加站点ID条件
					if len(siteIDs) > 0 {
						conditions = append(conditions, "site_id IN ?")
						args = append(args, siteIDs)
					}
					
					// 添加域名条件
					if len(domainFilters) > 0 {
						conditions = append(conditions, "domain IN ?")
						args = append(args, domainFilters)
					}
					
					// 组合条件
					if len(conditions) > 0 {
						whereClause := "(" + strings.Join(conditions, " OR ") + ")"
						query = query.Where(whereClause, args...)
					}
					
					err := query.Select("COALESCE(SUM(count), 0)").Scan(&total).Error
					
					// s.logger.Info("筛选查询结果", 
					//	zap.String("spider", config.Name),
					//	zap.Any("siteIDs", siteIDs),
					//	zap.Any("domainFilters", domainFilters),
					//	zap.Time("dateStart", dateStart),
					//	zap.Time("dateEnd", dateEnd),
					//	zap.Int64("total", total),
					//	zap.Error(err))
					
					if err != nil {
						// s.logger.Error("查询筛选数据失败", 
						//	zap.Error(err),
						//	zap.String("filterParam", filterParam),
						//	zap.String("spider", config.Name))
					}
				} else {
					// 没有指定筛选条件，使用分钟级数据聚合（全局统计）
					// 修复：统一时间边界计算，使用<=确保包含当天数据
					dateStart := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
					dateEnd := time.Date(periodEnd.Year(), periodEnd.Month(), periodEnd.Day(), 23, 59, 59, 0, periodEnd.Location())
					
					s.db.Model(&model.SpiderStatsMinute{}).
						Where("spider_name = ? AND timestamp >= ? AND timestamp <= ?", 
							config.Name, dateStart, dateEnd).
						Select("COALESCE(SUM(count), 0)").
						Scan(&total)
						
					// s.logger.Info("无筛选查询结果", 
					//	zap.String("spider", config.Name),
					//	zap.Time("dateStart", dateStart),
					//	zap.Time("dateEnd", dateEnd),
					//	zap.Int64("total", total))
				}
				
				dataset.Data[i] = total
			}
		}
		
		chartData.Datasets = append(chartData.Datasets, dataset)
	}
	
	return nil
}

// generateDomainStats 生成域名统计（保留原方法以兼容）
func (s *SpiderStatsService) generateDomainStats(response *model.GetSpiderStatsResponse, configs []model.SpiderConfig, domainFilter string, page, pageSize int) error {
	// 默认按域名字母排序
	return s.generateDomainStatsWithSort(response, configs, domainFilter, page, pageSize, "domain", "asc", "day")
}

// generateDomainStatsWithSort 生成域名统计（支持排序）
func (s *SpiderStatsService) generateDomainStatsWithSort(response *model.GetSpiderStatsResponse, configs []model.SpiderConfig, domainFilter string, page, pageSize int, sortBy, sortOrder, timeRange string) error {
	// 获取域名列表（聚合@和www）
	var allDomains []string
	query := s.db.Model(&model.SpiderStats{}).Distinct("domain")
	
	if domainFilter != "" {
		query = query.Where("domain LIKE ?", "%"+domainFilter+"%")
	}
	
	// 获取所有域名
	query.Pluck("domain", &allDomains)
	
	// 合并@和www域名
	domainMap := make(map[string]bool)
	for _, domain := range allDomains {
		// 标准化域名：将@转换为www，主域名添加www前缀
		normalizedDomain := s.normalizeDomain(domain)
		domainMap[normalizedDomain] = true
	}
	
	// 转换为切片
	domains := make([]string, 0, len(domainMap))
	for domain := range domainMap {
		domains = append(domains, domain)
	}
	
	// 时间范围（先计算，后面会用到）
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	yesterday := today.AddDate(0, 0, -1)
	fiveDaysAgo := today.AddDate(0, 0, -5)
	
	// 获取所有域名的统计数据（用于排序）
	type domainWithStats struct {
		domain string
		total  int64
		stats  map[string]int64 // 每个爬虫的统计
	}
	
	allDomainStats := make([]domainWithStats, 0, len(domains))
	
	for _, domain := range domains {
		ds := domainWithStats{
			domain: domain,
			total:  0,
			stats:  make(map[string]int64),
		}
		
		// 不再合并域名变体，每个域名只显示自己的统计
		// domainVariants := s.getDomainVariants(domain)
		
		// 获取每个爬虫的统计
		for _, config := range configs {
			var count int64
			
			// 根据时间范围选择查询
			switch timeRange {
			case "hour":
				// 最近1小时
				s.db.Model(&model.SpiderStats{}).
					Where("domain = ? AND spider_name = ? AND created_at >= ?", 
						domain, config.Name, now.Add(-time.Hour)).
					Select("COALESCE(SUM(count), 0)").
					Scan(&count)
			case "day":
				// 今天
				s.db.Model(&model.SpiderStats{}).
					Where("domain = ? AND spider_name = ? AND date = ?", 
						domain, config.Name, today).
					Select("COALESCE(SUM(count), 0)").
					Scan(&count)
			case "week":
				// 最近7天
				weekAgo := today.AddDate(0, 0, -7)
				s.db.Model(&model.SpiderStats{}).
					Where("domain = ? AND spider_name = ? AND date >= ?", 
						domain, config.Name, weekAgo).
					Select("COALESCE(SUM(count), 0)").
					Scan(&count)
			default:
				// 默认今天
				s.db.Model(&model.SpiderStats{}).
					Where("domain = ? AND spider_name = ? AND date = ?", 
						domain, config.Name, today).
					Select("COALESCE(SUM(count), 0)").
					Scan(&count)
			}
			
			ds.stats[config.Name] = count
			ds.total += count
		}
		
		allDomainStats = append(allDomainStats, ds)
	}
	
	// 根据排序参数排序
	sort.Slice(allDomainStats, func(i, j int) bool {
		switch sortBy {
		case "domain":
			// 域名排序
			if sortOrder == "desc" {
				return allDomainStats[i].domain > allDomainStats[j].domain
			}
			return allDomainStats[i].domain < allDomainStats[j].domain
		case "total":
			// 总计排序
			if sortOrder == "desc" {
				return allDomainStats[i].total > allDomainStats[j].total
			}
			return allDomainStats[i].total < allDomainStats[j].total
		default:
			// 按特定爬虫排序
			// 尝试不同的名称格式（处理可能的大小写问题）
			countI, foundI := allDomainStats[i].stats[sortBy]
			if !foundI {
				// 尝试不同的大小写组合
				for key := range allDomainStats[i].stats {
					if strings.EqualFold(key, sortBy) {
						countI = allDomainStats[i].stats[key]
						foundI = true
						break
					}
				}
			}
			
			countJ, foundJ := allDomainStats[j].stats[sortBy]
			if !foundJ {
				// 尝试不同的大小写组合
				for key := range allDomainStats[j].stats {
					if strings.EqualFold(key, sortBy) {
						countJ = allDomainStats[j].stats[key]
						foundJ = true
						break
					}
				}
			}
			
			// 如果找不到对应的爬虫，视为0
			if !foundI {
				countI = 0
			}
			if !foundJ {
				countJ = 0
			}
			
			if sortOrder == "desc" {
				return countI > countJ
			}
			return countI < countJ
		}
	})
	
	// 计算分页
	total := int64(len(allDomainStats))
	offset := (page - 1) * pageSize
	end := offset + pageSize
	if end > len(allDomainStats) {
		end = len(allDomainStats)
	}
	
	// 应用分页
	var pagedDomainStats []domainWithStats
	if offset < len(allDomainStats) {
		pagedDomainStats = allDomainStats[offset:end]
	} else {
		pagedDomainStats = []domainWithStats{}
	}
	
	// 设置分页信息
	response.Pagination = &model.Pagination{
		Page:      page,
		PageSize:  pageSize,
		Total:     total,
		TotalPage: int((total + int64(pageSize) - 1) / int64(pageSize)),
	}
	
	// 获取分页后每个域名的详细统计
	for _, ds := range pagedDomainStats {
		domainStats := &model.DomainSpiderStats{
			Domain: ds.domain,
			Stats:  make(map[string]*model.PeriodStats),
		}
		
		// 不再合并域名变体，每个域名独立统计
		for _, config := range configs {
			stats := &model.PeriodStats{}
			
			// 今天（只统计当前域名）
			s.db.Model(&model.SpiderStats{}).
				Where("domain = ? AND spider_name = ? AND date = ?", ds.domain, config.Name, today).
				Select("COALESCE(SUM(count), 0)").
				Scan(&stats.Today)
			
			// 昨天（只统计当前域名）
			s.db.Model(&model.SpiderStats{}).
				Where("domain = ? AND spider_name = ? AND date = ?", ds.domain, config.Name, yesterday).
				Select("COALESCE(SUM(count), 0)").
				Scan(&stats.Yesterday)
			
			// 最近5天（只统计当前域名）
			s.db.Model(&model.SpiderStats{}).
				Where("domain = ? AND spider_name = ? AND date >= ?", ds.domain, config.Name, fiveDaysAgo).
				Select("COALESCE(SUM(count), 0)").
				Scan(&stats.FiveDays)
			
			domainStats.Stats[config.Name] = stats
		}
		
		response.DomainStats = append(response.DomainStats, domainStats)
	}
	
	return nil
}

// generateSummary 生成汇总数据
func (s *SpiderStatsService) generateSummary(summary map[string]*model.SpiderSummary, configs []model.SpiderConfig) error {
	var total int64
	
	// 获取每个爬虫的总数
	for _, config := range configs {
		var count int64
		s.db.Model(&model.SpiderStats{}).
			Where("spider_name = ?", config.Name).
			Select("COALESCE(SUM(count), 0)").
			Scan(&count)
		
		summary[config.Name] = &model.SpiderSummary{
			Name:        config.Name,
			DisplayName: config.DisplayName,
			Total:       count,
		}
		total += count
	}
	
	// 计算百分比
	if total > 0 {
		for _, sum := range summary {
			sum.Percentage = float64(sum.Total) / float64(total) * 100
		}
	}
	
	return nil
}

// GetSpiderConfigs 获取爬虫配置
func (s *SpiderStatsService) GetSpiderConfigs() ([]model.SpiderConfig, error) {
	var configs []model.SpiderConfig
	err := s.db.Order("priority ASC").Find(&configs).Error
	return configs, err
}

// SaveSpiderConfig 保存爬虫配置
func (s *SpiderStatsService) SaveSpiderConfig(config *model.SpiderConfig) error {
	if config.ID > 0 {
		return s.db.Save(config).Error
	}
	return s.db.Create(config).Error
}

// DeleteSpiderConfig 删除爬虫配置
func (s *SpiderStatsService) DeleteSpiderConfig(id uint) error {
	return s.db.Delete(&model.SpiderConfig{}, id).Error
}

// InitDefaultSpiderConfigs 已删除 - 不再自动创建爬虫配置
// 所有爬虫配置需要通过管理后台手动添加

// ClearAllStats 清空所有爬虫统计数据
func (s *SpiderStatsService) ClearAllStats() error {
	// 使用事务清空两个统计表
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 清空分钟级统计数据 - 使用GORM的方式，让它自动处理表名
		if err := tx.Where("1 = 1").Delete(&model.SpiderStatsMinute{}).Error; err != nil {
			// s.logger.Error("清空分钟级统计失败", zap.Error(err))
			return err
		}
		
		// 清空主统计数据
		if err := tx.Where("1 = 1").Delete(&model.SpiderStats{}).Error; err != nil {
			// s.logger.Error("清空主统计失败", zap.Error(err))
			return err
		}
		
		// s.logger.Info("已清空所有爬虫统计数据")
		return nil
	})
}

// DeleteDomainStats 删除指定域名的统计数据
func (s *SpiderStatsService) DeleteDomainStats(domain string) error {
	// 获取域名的所有变体（www, @, 主域名）
	domainVariants := s.getDomainVariants(domain)
	
	// 使用事务删除统计表中的域名数据
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 1. 删除小时级统计数据（按域名）
		result1 := tx.Where("domain IN ?", domainVariants).Delete(&model.SpiderStatsHour{})
		if result1.Error != nil {
			// s.logger.Error("删除域名小时级统计失败", zap.Error(result1.Error), zap.String("domain", domain))
			return result1.Error
		}
		
		// 2. 删除日统计数据（按域名）
		result2 := tx.Where("domain IN ?", domainVariants).Delete(&model.SpiderStats{})
		if result2.Error != nil {
			// s.logger.Error("删除域名日统计失败", zap.Error(result2.Error), zap.String("domain", domain))
			return result2.Error
		}
		
		// 注意：不删除分钟级统计，因为它是全局的
		_ = result1.RowsAffected + result2.RowsAffected
		// totalDeleted := result1.RowsAffected + result2.RowsAffected
		// s.logger.Info("已删除域名统计数据", 
		//	zap.String("domain", domain), 
		//	zap.Int64("hour_stats_deleted", result1.RowsAffected),
		//	zap.Int64("daily_stats_deleted", result2.RowsAffected),
		//	zap.Int64("total_deleted", totalDeleted))
		return nil
	})
}

// CleanOldStats 清理旧的统计数据（定时任务调用）
func (s *SpiderStatsService) CleanOldStats() error {
	// 使用事务清理旧数据
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 1. 清理1小时前的分钟级数据（全局统计，保留最近1小时）
		cutoffMinute := time.Now().Add(-1 * time.Hour)
		result1 := tx.Where("timestamp < ?", cutoffMinute).Delete(&model.SpiderStatsMinute{})
		if result1.Error != nil {
			// s.logger.Error("清理旧分钟级统计失败", zap.Error(result1.Error))
			return result1.Error
		}
		
		// 2. 清理7天前的小时级数据
		cutoffHour := time.Now().AddDate(0, 0, -7)
		result2 := tx.Where("timestamp < ?", cutoffHour).Delete(&model.SpiderStatsHour{})
		if result2.Error != nil {
			// s.logger.Error("清理旧小时级统计失败", zap.Error(result2.Error))
			return result2.Error
		}
		
		// 3. 清理30天前的日统计数据
		cutoffDay := time.Now().AddDate(0, 0, -30)
		result3 := tx.Where("date < ?", cutoffDay).Delete(&model.SpiderStats{})
		if result3.Error != nil {
			// s.logger.Error("清理旧日统计失败", zap.Error(result3.Error))
			return result3.Error
		}
		
		if result1.RowsAffected > 0 || result2.RowsAffected > 0 || result3.RowsAffected > 0 {
			// s.logger.Info("已清理旧统计数据",
			//	zap.Int64("minute_stats_cleaned", result1.RowsAffected),
			//	zap.Int64("hour_stats_cleaned", result2.RowsAffected),
			//	zap.Int64("daily_stats_cleaned", result3.RowsAffected))
		}
		
		return nil
	})
}
