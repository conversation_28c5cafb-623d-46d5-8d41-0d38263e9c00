package service

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"
	"encoding/xml"
	
	"site-cluster/internal/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"go.uber.org/zap"
)

// SitemapService sitemap服务
type SitemapService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// SitemapSettings sitemap设置
type SitemapSettings struct {
	SitemapRefreshInterval int        `json:"sitemap_refresh_interval"`
	LastSitemapRefreshTime *time.Time `json:"last_sitemap_refresh_time"`
}

// NewSitemapService 创建sitemap服务
func NewSitemapService(db *gorm.DB, logger *zap.Logger) *SitemapService {
	return &SitemapService{
		db:     db,
		logger: logger,
	}
}

// CleanStaticResources 清理数据库中的静态资源条目
func (s *SitemapService) CleanStaticResources() error {
	// 定义排除的文件扩展名
	excludeExts := []string{
		".js", ".css", ".jpg", ".jpeg", ".png", ".gif", ".svg", ".ico",
		".woff", ".woff2", ".ttf", ".eot", ".otf",
		".mp4", ".webm", ".mp3", ".wav",
		".pdf", ".zip", ".rar", ".tar", ".gz",
		".json", ".xml", ".txt", ".log",
	}
	
	// 构建删除条件
	var conditions []string
	for range excludeExts {
		conditions = append(conditions, "url LIKE ?")
	}
	
	// 构建查询参数
	var args []interface{}
	for _, ext := range excludeExts {
		args = append(args, "%"+ext)
	}
	
	// 执行删除
	result := s.db.Where(strings.Join(conditions, " OR "), args...).Delete(&model.SitemapEntry{})
	
	if result.Error != nil {
		s.logger.Error("清理静态资源条目失败", zap.Error(result.Error))
		return result.Error
	}
	
	s.logger.Info("清理静态资源条目完成", zap.Int64("deleted", result.RowsAffected))
	return nil
}

// URLSet sitemap URL集合
type URLSet struct {
	XMLName xml.Name `xml:"urlset"`
	Xmlns   string   `xml:"xmlns,attr"`
	URLs    []URL    `xml:"url"`
}

// URL sitemap URL条目
type URL struct {
	Loc        string  `xml:"loc"`
	LastMod    string  `xml:"lastmod,omitempty"`
	ChangeFreq string  `xml:"changefreq,omitempty"`
	Priority   float32 `xml:"priority,omitempty"`
}

// AddURL 添加URL到sitemap索引
func (s *SitemapService) AddURL(siteID uint, domain, url string) error {
	// 检查站点是否启用sitemap
	var site model.Site
	if err := s.db.First(&site, siteID).Error; err != nil {
		return err
	}
	
	if !site.EnableSitemap {
		return nil // 未启用sitemap，直接返回
	}
	
	// 过滤静态资源文件，只保留页面
	// 排除的文件扩展名
	excludeExts := []string{
		".js", ".css", ".jpg", ".jpeg", ".png", ".gif", ".svg", ".ico",
		".woff", ".woff2", ".ttf", ".eot", ".otf",
		".mp4", ".webm", ".mp3", ".wav",
		".pdf", ".zip", ".rar", ".tar", ".gz",
		".json", ".xml", ".txt", ".log",
	}
	
	// 检查URL是否包含排除的扩展名
	lowerURL := strings.ToLower(url)
	for _, ext := range excludeExts {
		if strings.HasSuffix(lowerURL, ext) {
			s.logger.Debug("跳过静态资源", 
				zap.String("url", url),
				zap.String("ext", ext))
			return nil // 跳过静态资源
		}
	}
	
	// 只包含HTML页面和目录
	// 允许的页面：/, /path/, /page.html, /page.htm, /page.php, /page.asp, /page.aspx, /page.jsp, /page (无扩展名)
	
	// 构建完整URL
	fullURL := fmt.Sprintf("https://%s%s", domain, url)
	
	// 创建或更新sitemap条目
	priority := site.SitemapPriority
	// 首页设置最高优先级
	if url == "/" || url == "" || url == "/index.html" || url == "/index.htm" {
		priority = 1.0
	} else if strings.Count(url, "/") <= 2 && !strings.Contains(url, ".") {
		// 一级目录页面优先级较高
		priority = 0.8
	} else if priority == 0 {
		// 如果没有设置优先级，使用默认值
		priority = 0.5
	}
	
	entry := model.SitemapEntry{
		SiteID:     siteID,
		Domain:     domain,
		URL:        url,
		Loc:        fullURL,
		LastMod:    time.Now(),
		ChangeFreq: site.SitemapChangefreq,
		Priority:   priority,
	}
	
	// 使用site_id和URL作为唯一标识，domain存储站点配置的域名
	result := s.db.Where("site_id = ? AND url = ?", siteID, url).FirstOrCreate(&entry)
	if result.Error != nil {
		s.logger.Error("添加sitemap条目失败", zap.Error(result.Error))
		return result.Error
	}
	
	// 如果是更新，则更新最后修改时间和域名
	if result.RowsAffected == 0 {
		s.db.Model(&entry).Where("site_id = ? AND url = ?", siteID, url).
			Updates(map[string]interface{}{
				"last_mod": time.Now(),
				"domain":   domain, // 确保域名是站点配置的域名
			})
	}
	
	s.logger.Debug("添加sitemap条目", 
		zap.String("domain", domain),
		zap.String("url", url))
	
	// 不再立即生成sitemap文件，只记录到数据库
	// sitemap文件将由定时任务或手动触发生成
	
	return nil
}

// shouldUpdateSitemap 检查是否需要更新sitemap
func (s *SitemapService) shouldUpdateSitemap(site *model.Site) bool {
	// 如果从未生成过，立即生成
	if site.SitemapLastUpdate.IsZero() {
		return true
	}
	
	// 检查更新间隔
	interval := time.Duration(site.SitemapUpdateInterval) * time.Minute
	return time.Since(site.SitemapLastUpdate) > interval
}

// GenerateDynamicSitemap 动态生成sitemap内容（使用指定的域名和协议）
func (s *SitemapService) GenerateDynamicSitemap(siteID uint, requestDomain string, protocol string) (string, error) {
	// 获取站点配置
	var site model.Site
	if err := s.db.First(&site, siteID).Error; err != nil {
		return "", err
	}
	
	if !site.EnableSitemap {
		return "", nil
	}
	
	// 获取sitemap条目
	var entries []model.SitemapEntry
	query := s.db.Where("site_id = ?", siteID)
	
	// 如果设置了限制，只获取指定数量（默认1000条）
	maxEntries := 1000
	query = query.Limit(maxEntries)
	
	// 按优先级和最后修改时间排序
	query = query.Order("priority DESC, last_mod DESC")
	
	if err := query.Find(&entries).Error; err != nil {
		s.logger.Error("获取sitemap条目失败", zap.Error(err))
		return "", err
	}
	
	// 如果没有指定协议，自动判断
	if protocol == "" {
		protocol = "https"
		if strings.Contains(requestDomain, "localhost") || strings.Contains(requestDomain, "127.0.0.1") {
			protocol = "http"
		}
	}
	
	// 构建sitemap XML结构
	urlSet := URLSet{
		Xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9",
	}
	
	// 过滤静态资源
	excludeExts := []string{
		".jpg", ".jpeg", ".png", ".gif", ".svg", ".ico",
		".css", ".js", ".map",
		".woff", ".woff2", ".ttf", ".eot", ".otf",
		".mp4", ".webm", ".mp3", ".wav",
		".pdf", ".zip", ".rar", ".tar", ".gz",
		".json", ".xml", ".txt", ".log",
	}
	
	// 首先添加主域名（首页）- 确保它在第一位
	homeAdded := false
	homeURL := protocol + "://" + requestDomain + "/"
	
	// 使用map去重
	addedURLs := make(map[string]bool)
	
	for _, entry := range entries {
		// 检查是否是首页
		if entry.URL == "/" || entry.URL == "" || entry.URL == "/index.html" {
			if !homeAdded {
				urlSet.URLs = append(urlSet.URLs, URL{
					Loc:        homeURL,
					LastMod:    entry.LastMod.Format("2006-01-02"),
					ChangeFreq: entry.ChangeFreq,
					Priority:   1.0, // 首页始终最高优先级
				})
				homeAdded = true
				addedURLs[homeURL] = true
			}
			continue // 跳过，避免重复添加
		}
		
		// 过滤静态资源
		shouldExclude := false
		lowerURL := strings.ToLower(entry.URL)
		for _, ext := range excludeExts {
			if strings.HasSuffix(lowerURL, ext) {
				shouldExclude = true
				break
			}
		}
		
		if !shouldExclude {
			// 使用请求的域名构建URL
			loc := protocol + "://" + requestDomain + entry.URL
			
			// 检查是否已添加
			if !addedURLs[loc] {
				urlSet.URLs = append(urlSet.URLs, URL{
					Loc:        loc,
					LastMod:    entry.LastMod.Format("2006-01-02"),
					ChangeFreq: entry.ChangeFreq,
					Priority:   entry.Priority,
				})
				addedURLs[loc] = true
			}
		}
	}
	
	// 如果还没有添加首页，确保添加
	if !homeAdded {
		urlSet.URLs = append([]URL{{
			Loc:        homeURL,
			LastMod:    time.Now().Format("2006-01-02"),
			ChangeFreq: site.SitemapChangefreq,
			Priority:   1.0,
		}}, urlSet.URLs...)
	}
	
	// 生成XML
	output, err := xml.MarshalIndent(urlSet, "", "  ")
	if err != nil {
		s.logger.Error("生成sitemap XML失败", zap.Error(err))
		return "", err
	}
	
	// 添加XML声明
	xmlContent := xml.Header + string(output)
	
	return xmlContent, nil
}

// GenerateSitemap 生成sitemap.xml文件
func (s *SitemapService) GenerateSitemap(siteID uint, domain string) error {
	// 获取站点配置
	var site model.Site
	if err := s.db.First(&site, siteID).Error; err != nil {
		return err
	}
	
	if !site.EnableSitemap {
		return nil
	}
	
	// 首先确保首页存在（使用 FirstOrCreate 避免并发冲突）
	homeURL := "/"
	homeEntry := model.SitemapEntry{
		SiteID:     siteID,
		Domain:     domain, // 使用站点配置的域名
		URL:        homeURL,
		Loc:        fmt.Sprintf("https://%s/", domain),
		LastMod:    time.Now(),
		ChangeFreq: site.SitemapChangefreq,
		Priority:   1.0, // 首页优先级最高
	}
	
	// 使用 FirstOrCreate 来避免并发时的重复插入问题
	// 只根据 site_id 和 url 判断是否存在
	if err := s.db.Where("site_id = ? AND url = ?", siteID, homeURL).
		Attrs(homeEntry). // 如果不存在，使用这些属性创建
		FirstOrCreate(&homeEntry).Error; err != nil {
		// 只有在真正的错误时才记录，忽略唯一性约束冲突
		if !strings.Contains(err.Error(), "duplicate key") && !strings.Contains(err.Error(), "UNIQUE constraint") {
			s.logger.Error("添加首页到sitemap失败", zap.Error(err))
		}
	}
	
	// 获取该站点下的所有URL条目
	var entries []model.SitemapEntry
	query := s.db.Where("site_id = ?", siteID).Order("priority DESC, updated_at DESC")
	
	// 限制最大URL数量
	if site.SitemapMaxUrls > 0 {
		query = query.Limit(site.SitemapMaxUrls)
	}
	
	if err := query.Find(&entries).Error; err != nil {
		s.logger.Error("获取sitemap条目失败", zap.Error(err))
		return err
	}
	
	// 构建sitemap XML
	urlSet := URLSet{
		Xmlns: "http://www.sitemaps.org/schemas/sitemap/0.9",
		URLs:  make([]URL, 0, len(entries)+1),
	}
	
	// 定义排除的文件扩展名
	excludeExts := []string{
		".js", ".css", ".jpg", ".jpeg", ".png", ".gif", ".svg", ".ico",
		".woff", ".woff2", ".ttf", ".eot", ".otf",
		".mp4", ".webm", ".mp3", ".wav",
		".pdf", ".zip", ".rar", ".tar", ".gz",
		".json", ".xml", ".txt", ".log",
	}
	
	// 使用map去重
	addedURLs := make(map[string]bool)
	homeAdded := false
	
	// 首先处理首页，确保它在第一位
	for _, entry := range entries {
		if entry.URL == "/" || entry.URL == "" || entry.URL == "/index.html" {
			if !homeAdded {
				loc := fmt.Sprintf("https://%s/", domain)
				urlSet.URLs = append(urlSet.URLs, URL{
					Loc:        loc,
					LastMod:    entry.LastMod.Format("2006-01-02"),
					ChangeFreq: entry.ChangeFreq,
					Priority:   1.0, // 首页始终最高优先级
				})
				homeAdded = true
				addedURLs[loc] = true
			}
			break
		}
	}
	
	// 如果没有找到首页条目，手动添加
	if !homeAdded {
		loc := fmt.Sprintf("https://%s/", domain)
		urlSet.URLs = append(urlSet.URLs, URL{
			Loc:        loc,
			LastMod:    time.Now().Format("2006-01-02"),
			ChangeFreq: site.SitemapChangefreq,
			Priority:   1.0,
		})
		addedURLs[loc] = true
	}
	
	// 然后添加其他URL
	for _, entry := range entries {
		// 跳过首页相关的URL，已经处理过了
		if entry.URL == "/" || entry.URL == "" || entry.URL == "/index.html" {
			continue
		}
		
		// 过滤静态资源
		shouldExclude := false
		lowerURL := strings.ToLower(entry.URL)
		for _, ext := range excludeExts {
			if strings.HasSuffix(lowerURL, ext) {
				shouldExclude = true
				break
			}
		}
		
		if !shouldExclude {
			// 确保使用 https 协议（兼容旧数据）
			loc := entry.Loc
			if strings.HasPrefix(loc, "http://") {
				loc = "https://" + strings.TrimPrefix(loc, "http://")
			}
			
			// 检查是否已添加（避免重复）
			if !addedURLs[loc] {
				urlSet.URLs = append(urlSet.URLs, URL{
					Loc:        loc,
					LastMod:    entry.LastMod.Format("2006-01-02"),
					ChangeFreq: entry.ChangeFreq,
					Priority:   entry.Priority,
				})
				addedURLs[loc] = true
			}
		}
	}
	
	// 生成XML
	output, err := xml.MarshalIndent(urlSet, "", "  ")
	if err != nil {
		s.logger.Error("生成sitemap XML失败", zap.Error(err))
		return err
	}
	
	// 添加XML声明
	xmlContent := xml.Header + string(output)
	
	// 保存到文件
	sitemapPath := filepath.Join("./cache", domain, "sitemap.xml")
	
	// 确保目录存在
	dir := filepath.Dir(sitemapPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		s.logger.Error("创建sitemap目录失败", zap.Error(err))
		return err
	}
	
	// 写入sitemap文件
	if err := ioutil.WriteFile(sitemapPath, []byte(xmlContent), 0644); err != nil {
		s.logger.Error("写入sitemap文件失败", zap.Error(err))
		return err
	}
	
	// 生成robots.txt文件
	if err := s.generateRobotsTxt(domain); err != nil {
		s.logger.Error("生成robots.txt失败", zap.Error(err))
		// 不返回错误，让sitemap生成继续完成
	}
	
	// 更新站点的sitemap更新时间
	s.db.Model(&site).Update("sitemap_last_update", time.Now())
	
	s.logger.Info("生成sitemap成功", 
		zap.String("domain", domain),
		zap.Int("urls", len(entries)),
		zap.String("path", sitemapPath))
	
	return nil
}

// GetSitemapStats 获取sitemap统计信息
func (s *SitemapService) GetSitemapStats(siteID uint) (map[string]interface{}, error) {
	var count int64
	s.db.Model(&model.SitemapEntry{}).Where("site_id = ?", siteID).Count(&count)
	
	var lastUpdate time.Time
	s.db.Model(&model.SitemapEntry{}).
		Where("site_id = ?", siteID).
		Order("updated_at DESC").
		Limit(1).
		Pluck("updated_at", &lastUpdate)
	
	stats := map[string]interface{}{
		"total_urls":  count,
		"last_update": lastUpdate,
	}
	
	return stats, nil
}

// GetTotalURLCount 批量获取多个站点的URL总数
func (s *SitemapService) GetTotalURLCount(siteIDs []uint) (int, error) {
	if len(siteIDs) == 0 {
		return 0, nil
	}
	
	var count int64
	err := s.db.Model(&model.SitemapEntry{}).
		Where("site_id IN ?", siteIDs).
		Count(&count).Error
	
	if err != nil {
		return 0, err
	}
	
	return int(count), nil
}

// GetSitemapCount 获取站点的sitemap条目数量
func (s *SitemapService) GetSitemapCount(siteID uint) (int, error) {
	var count int64
	err := s.db.Model(&model.SitemapEntry{}).Where("site_id = ?", siteID).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

// BatchGetSitemapCount 批量获取多个站点的sitemap数量
func (s *SitemapService) BatchGetSitemapCount(siteIDs []uint) (map[uint]int, error) {
	if len(siteIDs) == 0 {
		return make(map[uint]int), nil
	}
	
	// 定义结果结构
	type SitemapCount struct {
		SiteID uint `gorm:"column:site_id"`
		Count  int  `gorm:"column:count"`
	}
	
	var results []SitemapCount
	
	// 使用GROUP BY批量查询
	err := s.db.Model(&model.SitemapEntry{}).
		Select("site_id, COUNT(*) as count").
		Where("site_id IN ?", siteIDs).
		Group("site_id").
		Scan(&results).Error
		
	if err != nil {
		return nil, err
	}
	
	// 转换为map
	countMap := make(map[uint]int)
	for _, r := range results {
		countMap[r.SiteID] = r.Count
	}
	
	// 确保所有请求的站点都有值（即使是0）
	for _, siteID := range siteIDs {
		if _, exists := countMap[siteID]; !exists {
			countMap[siteID] = 0
		}
	}
	
	return countMap, nil
}

// ClearSitemapByDomain 根据域名清空sitemap条目（废弃，保留向后兼容）
func (s *SitemapService) ClearSitemapByDomain(domain string) (int64, error) {
	// 直接按域名删除sitemap条目
	result := s.db.Where("domain = ?", domain).Delete(&model.SitemapEntry{})
	if result.Error != nil {
		return 0, fmt.Errorf("删除sitemap条目失败: %w", result.Error)
	}
	
	s.logger.Info("清空域名sitemap条目", 
		zap.String("domain", domain),
		zap.Int64("deleted_count", result.RowsAffected))
	
	return result.RowsAffected, nil
}

// ClearSitemapBySiteID 根据站点ID清空sitemap条目
func (s *SitemapService) ClearSitemapBySiteID(siteID uint) (int64, error) {
	// 按site_id删除sitemap条目
	result := s.db.Where("site_id = ?", siteID).Delete(&model.SitemapEntry{})
	if result.Error != nil {
		return 0, fmt.Errorf("删除sitemap条目失败: %w", result.Error)
	}
	
	s.logger.Info("清空站点sitemap条目", 
		zap.Uint("site_id", siteID),
		zap.Int64("deleted_count", result.RowsAffected))
	
	return result.RowsAffected, nil
}

// CleanupOldEntries 清理过期的sitemap条目
func (s *SitemapService) CleanupOldEntries(siteID uint, days int) error {
	cutoff := time.Now().AddDate(0, 0, -days)
	
	result := s.db.Where("site_id = ? AND updated_at < ?", siteID, cutoff).
		Delete(&model.SitemapEntry{})
	
	if result.Error != nil {
		s.logger.Error("清理sitemap条目失败", zap.Error(result.Error))
		return result.Error
	}
	
	s.logger.Info("清理sitemap条目", 
		zap.Uint("site_id", siteID),
		zap.Int64("deleted", result.RowsAffected))
	
	return nil
}

// ScanCacheDirectory 扫描缓存目录生成sitemap
func (s *SitemapService) ScanCacheDirectory(siteID uint, domain string) error {
	cacheDir := filepath.Join("./cache", domain)
	
	// 先清理现有的错误条目
	s.db.Where("site_id = ? AND (url LIKE '%<%' OR url LIKE '%>%' OR url LIKE '% %' OR url LIKE '%\"%')", siteID).Delete(&model.SitemapEntry{})
	
	var entries []model.SitemapEntry
	
	err := filepath.Walk(cacheDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 跳过目录
		if info.IsDir() {
			return nil
		}
		
		// 只处理HTML文件
		if !strings.HasSuffix(path, ".html") && !strings.HasSuffix(path, ".htm") {
			return nil
		}
		
		// 获取相对路径作为URL
		relPath, err := filepath.Rel(cacheDir, path)
		if err != nil {
			return err
		}
		
		// 转换为URL格式
		url := "/" + strings.ReplaceAll(relPath, string(os.PathSeparator), "/")
		
		// 过滤包含特殊字符的URL（这些通常是错误的文件名）
		if strings.ContainsAny(url, "<>\"") || strings.Contains(url, "--") {
			s.logger.Debug("跳过无效URL", zap.String("url", url))
			return nil
		}
		
		// 设置优先级
		priority := float32(0.5)
		if url == "/" || url == "/index.html" || url == "/index.htm" {
			priority = 1.0
		} else if strings.Count(url, "/") <= 2 && !strings.Contains(url, ".") {
			// 一级目录页面优先级较高
			priority = 0.8
		} else if strings.Count(url, "/") <= 3 {
			// 二级页面
			priority = 0.6
		}
		
		entries = append(entries, model.SitemapEntry{
			SiteID:     siteID,
			Domain:     domain,
			URL:        url,
			Loc:        fmt.Sprintf("https://%s%s", domain, url),
			LastMod:    info.ModTime(),
			ChangeFreq: "daily",
			Priority:   priority,
		})
		
		return nil
	})
	
	if err != nil {
		s.logger.Error("扫描缓存目录失败", zap.Error(err))
		return err
	}
	
	// 批量保存到数据库
	if len(entries) > 0 {
		s.db.Clauses(clause.OnConflict{
			UpdateAll: true,
		}).CreateInBatches(entries, 100)
	}
	
	s.logger.Info("扫描缓存目录完成", 
		zap.String("domain", domain),
		zap.Int("files", len(entries)))
	
	return s.GenerateSitemap(siteID, domain)
}

// generateRobotsTxt 生成robots.txt文件
func (s *SitemapService) generateRobotsTxt(domain string) error {
	// robots.txt内容模板
	robotsContent := fmt.Sprintf(`User-agent: *
Allow: /

# Sitemap
Sitemap: https://%s/sitemap.xml
`, domain)

	// robots.txt文件路径
	robotsPath := filepath.Join("./cache", domain, "robots.txt")
	
	// 写入robots.txt文件
	if err := ioutil.WriteFile(robotsPath, []byte(robotsContent), 0644); err != nil {
		s.logger.Error("写入robots.txt文件失败", zap.Error(err))
		return err
	}
	
	s.logger.Info("生成robots.txt成功", 
		zap.String("domain", domain),
		zap.String("path", robotsPath))
	
	return nil
}

// GenerateDynamicRobotsTxt 生成动态的robots.txt内容
func (s *SitemapService) GenerateDynamicRobotsTxt(siteID uint, requestDomain string, protocol string) (string, error) {
	// 构建动态的robots.txt内容
	robotsContent := fmt.Sprintf(`User-agent: *
Allow: /

# Sitemap
Sitemap: %s://%s/sitemap.xml
`, protocol, requestDomain)
	
	// 可以根据站点配置添加更多规则
	// 例如：爬虫延迟、禁止某些路径等
	
	return robotsContent, nil
}

// GetSitemapSettings 获取sitemap设置
func (s *SitemapService) GetSitemapSettings() (*SitemapSettings, error) {
	var settings model.SystemSettings
	if err := s.db.First(&settings).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 返回默认设置
			return &SitemapSettings{
				SitemapRefreshInterval: 720,
				LastSitemapRefreshTime: nil,
			}, nil
		}
		return nil, err
	}
	
	return &SitemapSettings{
		SitemapRefreshInterval: settings.SitemapRefreshInterval,
		LastSitemapRefreshTime: settings.LastSitemapRefreshTime,
	}, nil
}

// UpdateSitemapSettings 更新sitemap设置
func (s *SitemapService) UpdateSitemapSettings(refreshInterval int) error {
	var settings model.SystemSettings
	
	// 获取当前设置
	if err := s.db.First(&settings).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新设置
			settings = model.SystemSettings{
				SitemapRefreshInterval: refreshInterval,
			}
			return s.db.Create(&settings).Error
		}
		return err
	}
	
	// 更新设置
	updates := map[string]interface{}{
		"sitemap_refresh_interval": refreshInterval,
	}
	
	return s.db.Model(&settings).Updates(updates).Error
}

// GetSitemapEntryCount 获取站点地图条目数量
func (s *SitemapService) GetSitemapEntryCount(domain string) (int, error) {
	var count int64
	err := s.db.Model(&model.SitemapEntry{}).Where("domain = ?", domain).Count(&count).Error
	return int(count), err
}

// GetLastUpdateTime 获取最后更新时间
func (s *SitemapService) GetLastUpdateTime(domain string) (time.Time, error) {
	var entry model.SitemapEntry
	err := s.db.Where("domain = ?", domain).Order("updated_at DESC").First(&entry).Error
	if err != nil {
		return time.Time{}, err
	}
	return entry.UpdatedAt, nil
}

// ClearSitemapEntries 清空站点地图条目
func (s *SitemapService) ClearSitemapEntries(domain string) error {
	// 删除数据库中的条目
	if err := s.db.Where("domain = ?", domain).Delete(&model.SitemapEntry{}).Error; err != nil {
		return err
	}
	
	// 删除sitemap.xml文件
	sitemapPath := filepath.Join("./cache", domain, "sitemap.xml")
	if err := os.Remove(sitemapPath); err != nil && !os.IsNotExist(err) {
		s.logger.Warn("删除sitemap文件失败", 
			zap.String("path", sitemapPath),
			zap.Error(err))
	}
	
	return nil
}