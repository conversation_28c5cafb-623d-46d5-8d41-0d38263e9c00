package service

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"site-cluster/internal/model"
)

// StaticRefreshService 静态资源刷新服务
type StaticRefreshService struct {
	db               *gorm.DB
	logger           *zap.Logger
	analyticsService *AnalyticsService
	refreshCache     *cache.Cache
	mu               sync.RWMutex
}

// RefreshStatus 刷新状态
type RefreshStatus struct {
	SiteID      uint      `json:"site_id"`
	Domain      string    `json:"domain"`
	Status      string    `json:"status"`      // pending, refreshing, completed, failed
	LastRefresh time.Time `json:"last_refresh"`
	FilesCount  int       `json:"files_count"`
	Message     string    `json:"message"`
}

// NewStaticRefreshService 创建静态资源刷新服务
func NewStaticRefreshService(db *gorm.DB, logger *zap.Logger, analyticsService *AnalyticsService) *StaticRefreshService {
	return &StaticRefreshService{
		db:               db,
		logger:           logger,
		analyticsService: analyticsService,
		refreshCache:     cache.New(5*time.Minute, 10*time.Minute),
	}
}

// RefreshSiteStaticResources 刷新指定站点的静态资源
func (s *StaticRefreshService) RefreshSiteStaticResources(siteID uint) (*RefreshStatus, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 获取站点信息
	var site model.Site
	if err := s.db.First(&site, siteID).Error; err != nil {
		return nil, fmt.Errorf("站点不存在: %v", err)
	}

	status := &RefreshStatus{
		SiteID:      site.ID,
		Domain:      site.Domain,
		Status:      "refreshing",
		LastRefresh: time.Now(),
	}

	// 更新缓存状态
	s.refreshCache.Set(fmt.Sprintf("status_%d", siteID), status, cache.DefaultExpiration)

	// 获取站点缓存目录
	cacheDir := filepath.Join("./cache", site.Domain)
	if _, err := os.Stat(cacheDir); os.IsNotExist(err) {
		status.Status = "failed"
		status.Message = "缓存目录不存在"
		return status, fmt.Errorf("缓存目录不存在: %s", cacheDir)
	}

	// 刷新统计JS文件
	if err := s.refreshAnalyticsJS(site.Domain); err != nil {
		s.logger.Error("刷新统计JS失败", 
			zap.String("domain", site.Domain),
			zap.Error(err))
		status.Message = fmt.Sprintf("刷新统计JS失败: %v", err)
	}

	// 搜索并替换缓存的HTML文件中的统计代码
	filesRefreshed, err := s.refreshCachedHTMLFiles(cacheDir, site.Domain)
	if err != nil {
		status.Status = "failed"
		status.Message = fmt.Sprintf("刷新失败: %v", err)
		return status, err
	}

	status.Status = "completed"
	status.FilesCount = filesRefreshed
	status.Message = fmt.Sprintf("成功刷新 %d 个文件", filesRefreshed)

	// 更新缓存状态
	s.refreshCache.Set(fmt.Sprintf("status_%d", siteID), status, cache.DefaultExpiration)

	return status, nil
}

// refreshAnalyticsJS 刷新统计JS文件
func (s *StaticRefreshService) refreshAnalyticsJS(domain string) error {
	// 生成新的统计JS文件
	return s.analyticsService.GenerateAnalyticsJS(domain)
}

// refreshCachedHTMLFiles 刷新缓存的HTML文件
func (s *StaticRefreshService) refreshCachedHTMLFiles(cacheDir string, domain string) (int, error) {
	count := 0
	
	// 获取新的统计脚本标签
	newScript := s.analyticsService.GetAnalyticsScript(domain)
	if newScript == "" {
		return 0, nil // 统计功能未启用
	}

	// 遍历缓存目录中的所有HTML文件
	err := filepath.Walk(cacheDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理HTML文件
		if !strings.HasSuffix(path, ".html") && !strings.HasSuffix(path, ".htm") {
			return nil
		}

		// 读取文件内容
		content, err := ioutil.ReadFile(path)
		if err != nil {
			s.logger.Warn("读取文件失败", 
				zap.String("path", path),
				zap.Error(err))
			return nil
		}

		// 查找并替换统计脚本
		contentStr := string(content)
		updated := false

		// 匹配现有的统计脚本标签（格式：<script src="/xxxxxxxxxxxx.js?t=xxx"></script>）
		scriptPattern := `<script src="/[a-f0-9]{12}\.js\?t=[0-9]+" type="text/javascript"></script>`
		if strings.Contains(contentStr, "script src=\"/") {
			// 使用正则表达式替换
			re := regexp.MustCompile(scriptPattern)
			newContent := re.ReplaceAllString(contentStr, newScript)
			
			if newContent != contentStr {
				updated = true
				contentStr = newContent
			}
		}

		// 如果文件被更新，写回文件
		if updated {
			if err := ioutil.WriteFile(path, []byte(contentStr), info.Mode()); err != nil {
				s.logger.Error("写入文件失败",
					zap.String("path", path),
					zap.Error(err))
				return nil
			}
			count++
			s.logger.Info("更新缓存文件",
				zap.String("path", path))
		}

		return nil
	})

	return count, err
}

// RefreshMultipleSites 批量刷新多个站点
func (s *StaticRefreshService) RefreshMultipleSites(siteIDs []uint) map[uint]*RefreshStatus {
	results := make(map[uint]*RefreshStatus)
	
	var wg sync.WaitGroup
	resultMu := sync.Mutex{}
	
	for _, siteID := range siteIDs {
		wg.Add(1)
		go func(id uint) {
			defer wg.Done()
			
			status, err := s.RefreshSiteStaticResources(id)
			if err != nil {
				s.logger.Error("刷新站点失败",
					zap.Uint("site_id", id),
					zap.Error(err))
				status = &RefreshStatus{
					SiteID:  id,
					Status:  "failed",
					Message: err.Error(),
				}
			}
			
			resultMu.Lock()
			results[id] = status
			resultMu.Unlock()
		}(siteID)
	}
	
	wg.Wait()
	return results
}

// RefreshAllSites 刷新所有站点
func (s *StaticRefreshService) RefreshAllSites() (map[uint]*RefreshStatus, error) {
	var sites []model.Site
	if err := s.db.Find(&sites).Error; err != nil {
		return nil, err
	}
	
	siteIDs := make([]uint, len(sites))
	for i, site := range sites {
		siteIDs[i] = site.ID
	}
	
	return s.RefreshMultipleSites(siteIDs), nil
}

// GetRefreshStatus 获取刷新状态
func (s *StaticRefreshService) GetRefreshStatus(siteID uint) *RefreshStatus {
	if status, found := s.refreshCache.Get(fmt.Sprintf("status_%d", siteID)); found {
		return status.(*RefreshStatus)
	}
	return nil
}

// GetAllRefreshStatus 获取所有站点的刷新状态
func (s *StaticRefreshService) GetAllRefreshStatus() map[uint]*RefreshStatus {
	var sites []model.Site
	if err := s.db.Find(&sites).Error; err != nil {
		return nil
	}
	
	statuses := make(map[uint]*RefreshStatus)
	for _, site := range sites {
		if status := s.GetRefreshStatus(site.ID); status != nil {
			statuses[site.ID] = status
		} else {
			// 如果没有缓存状态，创建默认状态
			statuses[site.ID] = &RefreshStatus{
				SiteID: site.ID,
				Domain: site.Domain,
				Status: "pending",
			}
		}
	}
	
	return statuses
}