package service

import (
	"fmt"
	"math/rand"
	"site-cluster/internal/model"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type CompanyLibraryService struct {
	db     *gorm.DB
	logger *zap.Logger
}

func NewCompanyLibraryService(db *gorm.DB, logger *zap.Logger) *CompanyLibraryService {
	// 初始化随机种子
	rand.Seed(time.Now().UnixNano())
	
	return &CompanyLibraryService{
		db:     db,
		logger: logger,
	}
}

// GetLibraries 获取所有企业名称库
func (s *CompanyLibraryService) GetLibraries() ([]model.CompanyLibrary, error) {
	var libraries []model.CompanyLibrary
	
	// 获取库列表
	if err := s.db.Where("is_active = ?", true).Find(&libraries).Error; err != nil {
		return nil, err
	}
	
	// 统计每个库的企业名称数量
	for i := range libraries {
		var count int64
		s.db.Model(&model.CompanyName{}).Where("library_id = ?", libraries[i].ID).Count(&count)
		libraries[i].CompanyCount = int(count)
	}
	
	return libraries, nil
}

// GetLibrary 获取单个企业名称库
func (s *CompanyLibraryService) GetLibrary(id uint) (*model.CompanyLibrary, error) {
	var library model.CompanyLibrary
	if err := s.db.First(&library, id).Error; err != nil {
		return nil, err
	}
	
	// 统计企业名称数量
	var count int64
	s.db.Model(&model.CompanyName{}).Where("library_id = ?", id).Count(&count)
	library.CompanyCount = int(count)
	
	return &library, nil
}

// CreateLibrary 创建企业名称库
func (s *CompanyLibraryService) CreateLibrary(library *model.CompanyLibrary) error {
	return s.db.Create(library).Error
}

// UpdateLibrary 更新企业名称库
func (s *CompanyLibraryService) UpdateLibrary(id uint, updates map[string]interface{}) error {
	return s.db.Model(&model.CompanyLibrary{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteLibrary 删除企业名称库
func (s *CompanyLibraryService) DeleteLibrary(id uint) error {
	// 使用事务删除库和相关的企业名称
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除相关的企业名称
		if err := tx.Where("library_id = ?", id).Delete(&model.CompanyName{}).Error; err != nil {
			return err
		}
		// 删除库
		return tx.Delete(&model.CompanyLibrary{}, id).Error
	})
}

// GetCompanyNames 获取库中的企业名称
func (s *CompanyLibraryService) GetCompanyNames(libraryID uint, page, limit int) ([]model.CompanyName, int64, error) {
	var names []model.CompanyName
	var total int64
	
	query := s.db.Model(&model.CompanyName{}).Where("library_id = ?", libraryID)
	
	// 统计总数
	query.Count(&total)
	
	// 分页查询
	offset := (page - 1) * limit
	err := query.Offset(offset).Limit(limit).Order("id DESC").Find(&names).Error
	
	return names, total, err
}

// AddCompanyName 添加企业名称
func (s *CompanyLibraryService) AddCompanyName(name *model.CompanyName) error {
	// 检查是否已存在
	var count int64
	s.db.Model(&model.CompanyName{}).
		Where("library_id = ? AND name = ?", name.LibraryID, name.Name).
		Count(&count)
	
	if count > 0 {
		return fmt.Errorf("企业名称已存在")
	}
	
	return s.db.Create(name).Error
}

// BatchAddCompanyNames 批量添加企业名称
func (s *CompanyLibraryService) BatchAddCompanyNames(libraryID uint, names []string) error {
	var companyNames []model.CompanyName
	
	for _, name := range names {
		name = strings.TrimSpace(name)
		if name == "" {
			continue
		}
		
		// 检查是否已存在
		var count int64
		s.db.Model(&model.CompanyName{}).
			Where("library_id = ? AND name = ?", libraryID, name).
			Count(&count)
		
		if count == 0 {
			companyNames = append(companyNames, model.CompanyName{
				LibraryID: libraryID,
				Name:      name,
			})
		}
	}
	
	if len(companyNames) > 0 {
		return s.db.CreateInBatches(companyNames, 100).Error
	}
	
	return nil
}

// UpdateCompanyName 更新企业名称
func (s *CompanyLibraryService) UpdateCompanyName(id uint, updates map[string]interface{}) error {
	return s.db.Model(&model.CompanyName{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteCompanyName 删除企业名称
func (s *CompanyLibraryService) DeleteCompanyName(id uint) error {
	return s.db.Delete(&model.CompanyName{}, id).Error
}

// GetRandomCompanyName 从指定库中随机获取一个企业名称
func (s *CompanyLibraryService) GetRandomCompanyName(libraryID uint) (string, error) {
	var names []model.CompanyName
	
	// 获取库中所有企业名称
	if err := s.db.Where("library_id = ?", libraryID).Find(&names).Error; err != nil {
		return "", err
	}
	
	if len(names) == 0 {
		return "", fmt.Errorf("企业名称库为空")
	}
	
	// 随机选择一个
	selected := names[rand.Intn(len(names))]
	
	// 增加使用次数
	s.db.Model(&model.CompanyName{}).Where("id = ?", selected.ID).
		UpdateColumn("use_count", gorm.Expr("use_count + ?", 1))
	
	return selected.Name, nil
}

// CreateDefaultLibraries 创建默认企业名称库
func (s *CompanyLibraryService) CreateDefaultLibraries() error {
	defaultLibraries := []model.CompanyLibrary{
		{
			Name:        "科技公司",
			Description: "科技类企业名称",
			Type:        "tech",
		},
		{
			Name:        "教育公司",
			Description: "教育培训类企业名称",
			Type:        "edu",
		},
		{
			Name:        "商贸公司",
			Description: "商贸类企业名称",
			Type:        "trade",
		},
		{
			Name:        "服务公司",
			Description: "服务类企业名称",
			Type:        "service",
		},
	}
	
	for _, lib := range defaultLibraries {
		var count int64
		s.db.Model(&model.CompanyLibrary{}).Where("name = ?", lib.Name).Count(&count)
		if count == 0 {
			if err := s.db.Create(&lib).Error; err != nil {
				s.logger.Error("创建默认企业名称库失败", zap.Error(err))
			} else {
				// 添加一些示例企业名称
				s.addSampleCompanyNames(lib.ID, lib.Type)
			}
		}
	}
	
	return nil
}

// addSampleCompanyNames 添加示例企业名称
func (s *CompanyLibraryService) addSampleCompanyNames(libraryID uint, libType string) {
	var sampleNames []string
	
	switch libType {
	case "tech":
		sampleNames = []string{
			"深圳智慧科技有限公司",
			"北京创新软件有限公司",
			"上海云端科技有限公司",
			"广州数据技术有限公司",
			"杭州智能科技有限公司",
		}
	case "edu":
		sampleNames = []string{
			"北京博学教育咨询有限公司",
			"上海智慧教育科技有限公司",
			"广州育才教育培训有限公司",
			"深圳明德教育咨询有限公司",
			"杭州启航教育科技有限公司",
		}
	case "trade":
		sampleNames = []string{
			"上海国际贸易有限公司",
			"深圳进出口贸易有限公司",
			"广州商贸发展有限公司",
			"北京环球贸易有限公司",
			"杭州跨境电商有限公司",
		}
	case "service":
		sampleNames = []string{
			"北京专业服务有限公司",
			"上海企业管理咨询有限公司",
			"深圳商务服务有限公司",
			"广州信息服务有限公司",
			"杭州技术服务有限公司",
		}
	}
	
	for _, name := range sampleNames {
		companyName := model.CompanyName{
			LibraryID: libraryID,
			Name:      name,
		}
		s.db.Create(&companyName)
	}
}