package service

import (
	"errors"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	logger          *zap.Logger
	name            string
	maxRequests     uint32        // 半开状态最大请求数
	interval        time.Duration // 统计间隔
	timeout         time.Duration // 熔断持续时间
	threshold       uint32        // 失败阈值
	failureRate     float64       // 失败率阈值
	
	state           int32  // 0: 关闭, 1: 开启, 2: 半开
	failures        uint32 // 失败计数
	successes       uint32 // 成功计数
	requests        uint32 // 总请求数
	lastFailureTime time.Time
	mu              sync.RWMutex
	
	// 站点级别熔断器
	siteBreakers    map[string]*SiteCircuitBreaker
	siteMu          sync.RWMutex
}

// SiteCircuitBreaker 站点级别熔断器
type SiteCircuitBreaker struct {
	domain          string
	state           int32
	failures        uint32
	lastFailureTime time.Time
	lastResetTime   time.Time
}

const (
	StateClosed   = iota // 正常状态
	StateOpen            // 熔断状态
	StateHalfOpen        // 半开状态
)

var (
	ErrCircuitOpen = errors.New("circuit breaker is open")
	ErrTooManyRequests = errors.New("too many requests in half-open state")
)

// NewCircuitBreaker 创建熔断器
func NewCircuitBreaker(logger *zap.Logger, name string) *CircuitBreaker {
	return &CircuitBreaker{
		logger:       logger,
		name:         name,
		maxRequests:  5,
		interval:     60 * time.Second,
		timeout:      30 * time.Second,
		threshold:    10,
		failureRate:  0.5,
		siteBreakers: make(map[string]*SiteCircuitBreaker),
	}
}

// Call 执行函数调用（带熔断保护）
func (cb *CircuitBreaker) Call(fn func() error) error {
	if err := cb.beforeCall(); err != nil {
		return err
	}
	
	err := fn()
	cb.afterCall(err)
	return err
}

// CallWithSite 站点级别的熔断调用
func (cb *CircuitBreaker) CallWithSite(domain string, fn func() error) error {
	// 检查站点级别熔断
	if err := cb.beforeSiteCall(domain); err != nil {
		return err
	}
	
	// 检查全局熔断
	if err := cb.beforeCall(); err != nil {
		return err
	}
	
	err := fn()
	
	// 更新状态
	cb.afterCall(err)
	cb.afterSiteCall(domain, err)
	
	return err
}

// beforeCall 调用前检查
func (cb *CircuitBreaker) beforeCall() error {
	cb.mu.Lock()
	defer cb.mu.Unlock()
	
	state := atomic.LoadInt32(&cb.state)
	
	switch state {
	case StateOpen:
		// 检查是否可以转换到半开状态
		if time.Since(cb.lastFailureTime) > cb.timeout {
			cb.logger.Info("熔断器转换到半开状态", zap.String("name", cb.name))
			atomic.StoreInt32(&cb.state, StateHalfOpen)
			atomic.StoreUint32(&cb.requests, 0)
			atomic.StoreUint32(&cb.failures, 0)
			atomic.StoreUint32(&cb.successes, 0)
			return nil
		}
		return ErrCircuitOpen
		
	case StateHalfOpen:
		// 半开状态限制请求数
		if atomic.LoadUint32(&cb.requests) >= cb.maxRequests {
			return ErrTooManyRequests
		}
		atomic.AddUint32(&cb.requests, 1)
		return nil
		
	default: // StateClosed
		return nil
	}
}

// afterCall 调用后更新状态
func (cb *CircuitBreaker) afterCall(err error) {
	cb.mu.Lock()
	defer cb.mu.Unlock()
	
	state := atomic.LoadInt32(&cb.state)
	
	if err != nil {
		atomic.AddUint32(&cb.failures, 1)
		cb.lastFailureTime = time.Now()
		
		switch state {
		case StateHalfOpen:
			// 半开状态下失败，立即熔断
			cb.logger.Warn("半开状态失败，重新熔断", 
				zap.String("name", cb.name),
				zap.Error(err))
			atomic.StoreInt32(&cb.state, StateOpen)
			
		case StateClosed:
			// 检查是否需要熔断
			failures := atomic.LoadUint32(&cb.failures)
			requests := atomic.LoadUint32(&cb.requests)
			
			if failures >= cb.threshold {
				cb.logger.Error("触发熔断", 
					zap.String("name", cb.name),
					zap.Uint32("failures", failures))
				atomic.StoreInt32(&cb.state, StateOpen)
			} else if requests > 0 && float64(failures)/float64(requests) > cb.failureRate {
				cb.logger.Error("失败率过高，触发熔断", 
					zap.String("name", cb.name),
					zap.Float64("rate", float64(failures)/float64(requests)))
				atomic.StoreInt32(&cb.state, StateOpen)
			}
		}
	} else {
		atomic.AddUint32(&cb.successes, 1)
		
		if state == StateHalfOpen {
			successes := atomic.LoadUint32(&cb.successes)
			if successes >= cb.maxRequests {
				// 半开状态下全部成功，恢复正常
				cb.logger.Info("熔断器恢复正常", zap.String("name", cb.name))
				atomic.StoreInt32(&cb.state, StateClosed)
				atomic.StoreUint32(&cb.failures, 0)
				atomic.StoreUint32(&cb.requests, 0)
			}
		}
	}
	
	atomic.AddUint32(&cb.requests, 1)
}

// beforeSiteCall 站点调用前检查
func (cb *CircuitBreaker) beforeSiteCall(domain string) error {
	cb.siteMu.Lock()
	defer cb.siteMu.Unlock()
	
	breaker, exists := cb.siteBreakers[domain]
	if !exists {
		breaker = &SiteCircuitBreaker{
			domain:        domain,
			state:         StateClosed,
			lastResetTime: time.Now(),
		}
		cb.siteBreakers[domain] = breaker
	}
	
	// 检查站点熔断状态
	if atomic.LoadInt32(&breaker.state) == StateOpen {
		// 检查是否可以恢复
		if time.Since(breaker.lastFailureTime) > cb.timeout {
			atomic.StoreInt32(&breaker.state, StateClosed)
			atomic.StoreUint32(&breaker.failures, 0)
			cb.logger.Info("站点熔断器恢复", zap.String("domain", domain))
		} else {
			return errors.New("site circuit breaker is open")
		}
	}
	
	return nil
}

// afterSiteCall 站点调用后更新
func (cb *CircuitBreaker) afterSiteCall(domain string, err error) {
	cb.siteMu.Lock()
	defer cb.siteMu.Unlock()
	
	breaker, exists := cb.siteBreakers[domain]
	if !exists {
		return
	}
	
	if err != nil {
		atomic.AddUint32(&breaker.failures, 1)
		breaker.lastFailureTime = time.Now()
		
		// 站点级别的熔断阈值（更严格）
		if atomic.LoadUint32(&breaker.failures) >= 5 {
			atomic.StoreInt32(&breaker.state, StateOpen)
			cb.logger.Warn("站点触发熔断", 
				zap.String("domain", domain),
				zap.Uint32("failures", breaker.failures))
		}
	} else {
		// 成功后逐步恢复
		if failures := atomic.LoadUint32(&breaker.failures); failures > 0 {
			atomic.AddUint32(&breaker.failures, ^uint32(0)) // 减1
		}
	}
	
	// 定期重置计数（每小时）
	if time.Since(breaker.lastResetTime) > time.Hour {
		atomic.StoreUint32(&breaker.failures, 0)
		breaker.lastResetTime = time.Now()
	}
}

// GetState 获取熔断器状态
func (cb *CircuitBreaker) GetState() string {
	state := atomic.LoadInt32(&cb.state)
	switch state {
	case StateOpen:
		return "open"
	case StateHalfOpen:
		return "half-open"
	default:
		return "closed"
	}
}

// GetStats 获取统计信息
func (cb *CircuitBreaker) GetStats() map[string]interface{} {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	
	return map[string]interface{}{
		"name":      cb.name,
		"state":     cb.GetState(),
		"failures":  atomic.LoadUint32(&cb.failures),
		"successes": atomic.LoadUint32(&cb.successes),
		"requests":  atomic.LoadUint32(&cb.requests),
	}
}

// GetSiteStats 获取站点统计
func (cb *CircuitBreaker) GetSiteStats() map[string]interface{} {
	cb.siteMu.RLock()
	defer cb.siteMu.RUnlock()
	
	stats := make(map[string]interface{})
	for domain, breaker := range cb.siteBreakers {
		state := "closed"
		if atomic.LoadInt32(&breaker.state) == StateOpen {
			state = "open"
		}
		stats[domain] = map[string]interface{}{
			"state":    state,
			"failures": atomic.LoadUint32(&breaker.failures),
		}
	}
	return stats
}

// Reset 重置熔断器
func (cb *CircuitBreaker) Reset() {
	cb.mu.Lock()
	defer cb.mu.Unlock()
	
	atomic.StoreInt32(&cb.state, StateClosed)
	atomic.StoreUint32(&cb.failures, 0)
	atomic.StoreUint32(&cb.successes, 0)
	atomic.StoreUint32(&cb.requests, 0)
	
	cb.logger.Info("熔断器已重置", zap.String("name", cb.name))
}