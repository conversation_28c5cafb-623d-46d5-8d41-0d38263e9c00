package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"
	"site-cluster/internal/repository/gorm"

	"go.uber.org/zap"
)

type KeywordService struct {
	logger     *zap.Logger
	repo       repository.KeywordRepository
	redisCache *RedisCacheService
}

func NewKeywordService(logger *zap.Logger, repo repository.KeywordRepository) *KeywordService {
	return &KeywordService{
		logger: logger,
		repo:   repo,
	}
}

// SetRedisCache 设置Redis缓存服务
func (s *KeywordService) SetRedisCache(redisCache *RedisCacheService) {
	s.redisCache = redisCache
}

// GetLibraries 获取关键词库列表
func (s *KeywordService) GetLibraries(page, pageSize int) ([]*model.KeywordLibrary, int64, error) {
	offset := (page - 1) * pageSize
	
	libraries, err := s.repo.ListLibraries(offset, pageSize)
	if err != nil {
		s.logger.Error("获取关键词库列表失败", zap.Error(err))
		return nil, 0, err
	}
	
	total, err := s.repo.CountLibraries()
	if err != nil {
		s.logger.Error("获取关键词库总数失败", zap.Error(err))
		return nil, 0, err
	}
	
	return libraries, total, nil
}

// GetLibrary 获取单个关键词库
func (s *KeywordService) GetLibrary(id uint) (*model.KeywordLibrary, error) {
	library, err := s.repo.GetLibraryByID(id)
	if err != nil {
		s.logger.Error("获取关键词库失败", zap.Uint("id", id), zap.Error(err))
		return nil, err
	}
	
	if library == nil {
		return nil, errors.New("关键词库不存在")
	}
	
	return library, nil
}

// CreateLibrary 创建关键词库
func (s *KeywordService) CreateLibrary(library *model.KeywordLibrary) error {
	// 检查名称是否已存在
	existing, err := s.repo.GetLibraryByName(library.Name)
	if err != nil {
		s.logger.Error("检查关键词库名称失败", zap.Error(err))
		return err
	}
	
	if existing != nil {
		return errors.New("关键词库名称已存在")
	}
	
	if err := s.repo.CreateLibrary(library); err != nil {
		s.logger.Error("创建关键词库失败", zap.Error(err))
		return err
	}
	
	s.logger.Info("关键词库创建成功", 
		zap.Uint("id", library.ID),
		zap.String("name", library.Name))
	
	return nil
}

// UpdateLibrary 更新关键词库
func (s *KeywordService) UpdateLibrary(library *model.KeywordLibrary) error {
	if err := s.repo.UpdateLibrary(library); err != nil {
		s.logger.Error("更新关键词库失败", zap.Error(err))
		return err
	}
	
	s.logger.Info("关键词库更新成功", 
		zap.Uint("id", library.ID),
		zap.String("name", library.Name))
	
	return nil
}

// DeleteLibrary 删除关键词库
func (s *KeywordService) DeleteLibrary(id uint) error {
	// 先删除库中的所有关键词
	if err := s.repo.DeleteKeywordsByLibrary(id); err != nil {
		s.logger.Error("删除关键词失败", zap.Error(err))
		return err
	}
	
	// 再删除关键词库
	if err := s.repo.DeleteLibrary(id); err != nil {
		s.logger.Error("删除关键词库失败", zap.Error(err))
		return err
	}
	
	s.logger.Info("关键词库删除成功", zap.Uint("id", id))
	
	return nil
}

// GetKeywords 获取关键词列表
func (s *KeywordService) GetKeywords(libraryID uint, page, pageSize int, search string) ([]*model.Keyword, int64, error) {
	offset := (page - 1) * pageSize
	
	keywords, err := s.repo.ListKeywords(libraryID, offset, pageSize, search)
	if err != nil {
		s.logger.Error("获取关键词列表失败", zap.Error(err))
		return nil, 0, err
	}
	
	total, err := s.repo.CountKeywords(libraryID, search)
	if err != nil {
		s.logger.Error("获取关键词总数失败", zap.Error(err))
		return nil, 0, err
	}
	
	return keywords, total, nil
}

// GetAllKeywords 获取词库所有关键词
func (s *KeywordService) GetAllKeywords(libraryID uint) ([]*model.Keyword, error) {
	keywords, err := s.repo.GetAllKeywordsByLibrary(libraryID)
	if err != nil {
		s.logger.Error("获取所有关键词失败", zap.Error(err))
		return nil, err
	}
	
	return keywords, nil
}

// GetKeyword 获取单个关键词
func (s *KeywordService) GetKeyword(id uint) (*model.Keyword, error) {
	keyword, err := s.repo.GetKeywordByID(id)
	if err != nil {
		s.logger.Error("获取关键词失败", zap.Uint("id", id), zap.Error(err))
		return nil, err
	}
	
	if keyword == nil {
		return nil, errors.New("关键词不存在")
	}
	
	return keyword, nil
}

// AddKeyword 添加关键词
func (s *KeywordService) AddKeyword(keyword *model.Keyword) error {
	// 检查是否已存在
	existing, err := s.repo.GetKeywordByText(keyword.LibraryID, keyword.Keyword)
	if err != nil {
		s.logger.Error("检查关键词失败", zap.Error(err))
		return err
	}
	
	if existing != nil {
		return errors.New("关键词已存在")
	}
	
	if err := s.repo.CreateKeyword(keyword); err != nil {
		s.logger.Error("添加关键词失败", zap.Error(err))
		return err
	}
	
	// 清理Redis缓存
	s.invalidateLibraryCache(keyword.LibraryID)
	
	s.logger.Info("关键词添加成功", 
		zap.Uint("id", keyword.ID),
		zap.String("keyword", keyword.Keyword))
	
	return nil
}

// UpdateKeyword 更新关键词
func (s *KeywordService) UpdateKeyword(keyword *model.Keyword) error {
	if err := s.repo.UpdateKeyword(keyword); err != nil {
		s.logger.Error("更新关键词失败", zap.Error(err))
		return err
	}
	
	// 清理Redis缓存
	s.invalidateLibraryCache(keyword.LibraryID)
	
	s.logger.Info("关键词更新成功", 
		zap.Uint("id", keyword.ID),
		zap.String("keyword", keyword.Keyword))
	
	return nil
}

// DeleteKeyword 删除关键词
func (s *KeywordService) DeleteKeyword(id uint) error {
	// 先获取关键词信息以获取libraryID
	keyword, err := s.repo.GetKeywordByID(id)
	if err != nil {
		s.logger.Error("获取关键词失败", zap.Error(err))
		return err
	}
	
	if err := s.repo.DeleteKeyword(id); err != nil {
		s.logger.Error("删除关键词失败", zap.Error(err))
		return err
	}
	
	// 清理Redis缓存
	if keyword != nil {
		s.invalidateLibraryCache(keyword.LibraryID)
	}
	
	s.logger.Info("关键词删除成功", zap.Uint("id", id))
	
	return nil
}

// invalidateLibraryCache 清理指定词库的缓存
func (s *KeywordService) invalidateLibraryCache(libraryID uint) {
	if s.redisCache != nil {
		cacheKey := fmt.Sprintf("keyword_lib:%d", libraryID)
		if err := s.redisCache.Delete(cacheKey); err != nil {
			s.logger.Warn("清理关键词库缓存失败", zap.Uint("libraryID", libraryID), zap.Error(err))
		} else {
			s.logger.Debug("关键词库缓存已清理", zap.Uint("libraryID", libraryID))
		}
	}
}

// BatchImportKeywords 批量导入关键词
func (s *KeywordService) BatchImportKeywords(libraryID uint, keywords []string) (success, failed int, err error) {
	for _, kw := range keywords {
		kw = strings.TrimSpace(kw)
		if kw == "" {
			continue
		}
		
		keyword := &model.Keyword{
			LibraryID: libraryID,
			Keyword:   kw,
			Weight:    1,
		}
		
		if err := s.AddKeyword(keyword); err != nil {
			if strings.Contains(err.Error(), "已存在") {
				// 跳过已存在的关键词
				continue
			}
			s.logger.Error("导入关键词失败", 
				zap.String("keyword", kw),
				zap.Error(err))
			failed++
		} else {
			success++
		}
	}
	
	return success, failed, nil
}

// GetKeywordsByLibraryID 根据关键词库ID获取所有关键词（带Redis缓存）
func (s *KeywordService) GetKeywordsByLibraryID(libraryID uint) ([]*model.Keyword, error) {
	// 尝试从Redis缓存获取
	cacheKey := fmt.Sprintf("keyword_lib:%d", libraryID)
	if s.redisCache != nil {
		if cachedData, found := s.redisCache.Get(cacheKey); found {
			var keywords []*model.Keyword
			if err := json.Unmarshal([]byte(cachedData), &keywords); err == nil {
				s.logger.Debug("从Redis缓存获取关键词库", zap.Uint("libraryID", libraryID), zap.Int("count", len(keywords)))
				return keywords, nil
			}
		}
	}
	
	// 缓存未命中，从数据库获取
	keywords, err := s.GetAllKeywords(libraryID)
	if err != nil {
		s.logger.Error("获取关键词库关键词失败", zap.Uint("libraryID", libraryID), zap.Error(err))
		return nil, err
	}
	
	// 写入Redis缓存（缓存24小时）
	if s.redisCache != nil && len(keywords) > 0 {
		if data, err := json.Marshal(keywords); err == nil {
			s.redisCache.Set(cacheKey, string(data), 24*time.Hour)
			s.logger.Debug("关键词库已缓存到Redis", zap.Uint("libraryID", libraryID), zap.Int("count", len(keywords)))
		}
	}
	
	return keywords, nil
}

// GetKeywordsByLibraryIDs 根据关键词库ID列表获取所有关键词
func (s *KeywordService) GetKeywordsByLibraryIDs(libraryIDs []uint) ([]string, error) {
	// 限制总数为200条，随机获取
	const maxKeywords = 200
	var allKeywords []string
	keywordMap := make(map[string]bool) // 用于去重
	
	// 计算每个库应该获取的数量
	perLibraryLimit := maxKeywords
	if len(libraryIDs) > 0 {
		perLibraryLimit = maxKeywords / len(libraryIDs)
		if perLibraryLimit < 10 {
			perLibraryLimit = 10 // 每个库至少获取10个
		}
	}
	
	for _, libraryID := range libraryIDs {
		// 使用随机获取方法
		keywords, err := s.GetRandomKeywords(libraryID, perLibraryLimit)
		if err != nil {
			s.logger.Error("获取关键词库关键词失败", zap.Uint("libraryID", libraryID), zap.Error(err))
			continue
		}
		
		for _, kw := range keywords {
			if !keywordMap[kw.Keyword] {
				keywordMap[kw.Keyword] = true
				allKeywords = append(allKeywords, kw.Keyword)
				// 达到总数限制后停止
				if len(allKeywords) >= maxKeywords {
					return allKeywords, nil
				}
			}
		}
	}
	
	return allKeywords, nil
}

// GetRandomKeywords 随机获取指定数量的关键词
func (s *KeywordService) GetRandomKeywords(libraryID uint, limit int) ([]*model.Keyword, error) {
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("keyword:library:%d:random:%d", libraryID, limit)
	if s.redisCache != nil {
		cached, found := s.redisCache.Get(cacheKey)
		if found && cached != "" {
			var keywords []*model.Keyword
			if err := json.Unmarshal([]byte(cached), &keywords); err == nil {
				s.logger.Debug("从Redis缓存获取随机关键词", zap.Uint("libraryID", libraryID), zap.Int("count", len(keywords)))
				return keywords, nil
			}
		}
	}
	
	// 使用新的随机获取方法
	var keywords []*model.Keyword
	var err error
	
	// 检查是否实现了随机获取方法
	if repo, ok := s.repo.(*gorm.KeywordRepository); ok {
		keywords, err = repo.GetRandomKeywordsByLibrary(libraryID, limit)
	} else {
		// 降级到获取所有关键词然后随机选择
		allKeywords, err := s.repo.GetAllKeywordsByLibrary(libraryID)
		if err != nil {
			s.logger.Error("获取关键词失败", zap.Uint("libraryID", libraryID), zap.Error(err))
			return nil, err
		}
		// 随机选择
		if len(allKeywords) > limit {
			rand.Shuffle(len(allKeywords), func(i, j int) {
				allKeywords[i], allKeywords[j] = allKeywords[j], allKeywords[i]
			})
			keywords = allKeywords[:limit]
		} else {
			keywords = allKeywords
		}
	}
	
	if err != nil {
		s.logger.Error("获取随机关键词失败", zap.Uint("libraryID", libraryID), zap.Error(err))
		return nil, err
	}
	
	// 写入Redis缓存（缓存1小时，因为是随机的）
	if s.redisCache != nil && len(keywords) > 0 {
		if data, err := json.Marshal(keywords); err == nil {
			s.redisCache.Set(cacheKey, string(data), 1*time.Hour)
			s.logger.Debug("随机关键词已缓存到Redis", zap.Uint("libraryID", libraryID), zap.Int("count", len(keywords)))
		}
	}
	
	return keywords, nil
}

// BatchAddKeywords 批量添加关键词
func (s *KeywordService) BatchAddKeywords(libraryID uint, keywords []string, weight int) (success, failed int, err error) {
	for _, kw := range keywords {
		kw = strings.TrimSpace(kw)
		if kw == "" {
			continue
		}
		
		keyword := &model.Keyword{
			LibraryID: libraryID,
			Keyword:   kw,
			Weight:    weight,
		}
		
		if err := s.AddKeyword(keyword); err != nil {
			if strings.Contains(err.Error(), "已存在") {
				// 跳过已存在的关键词
				continue
			}
			s.logger.Error("添加关键词失败", 
				zap.String("keyword", kw),
				zap.Error(err))
			failed++
		} else {
			success++
		}
	}
	
	return success, failed, nil
}

// GetKeywordStats 获取关键词统计
func (s *KeywordService) GetKeywordStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// 获取词库数量
	libraryCount, err := s.repo.CountLibraries()
	if err != nil {
		s.logger.Error("获取词库数量失败", zap.Error(err))
		return nil, err
	}
	
	// 获取关键词总数
	keywordCount, err := s.repo.CountAllKeywords()
	if err != nil {
		s.logger.Error("获取关键词总数失败", zap.Error(err))
		return nil, err
	}
	
	// 获取各类型词库统计
	typeStats, err := s.repo.CountLibrariesByType()
	if err != nil {
		s.logger.Error("获取词库类型统计失败", zap.Error(err))
		typeStats = make(map[string]int64)
	}
	
	stats["total_libraries"] = libraryCount
	stats["total_keywords"] = keywordCount
	stats["library_types"] = typeStats
	stats["total"] = keywordCount // 为前端兼容性
	
	return stats, nil
}