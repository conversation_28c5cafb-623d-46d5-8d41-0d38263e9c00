package service

import (
	"regexp"
	"site-cluster/internal/model"
	"strings"
	"sync"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type SpiderBlockService struct {
	db         *gorm.DB
	logger     *zap.Logger
	cache      map[uint]*compiledRule // 缓存编译后的正则表达式
	cacheMu    sync.RWMutex           // 保护cache的读写锁
	statsService *SpiderBlockStatsService // 统计服务
	systemSettingsService *SystemSettingsService // 系统设置服务
}

type compiledRule struct {
	rule    *model.SpiderBlock
	pattern *regexp.Regexp
}

func NewSpiderBlockService(db *gorm.DB, logger *zap.Logger) *SpiderBlockService {
	sbs := &SpiderBlockService{
		db:     db,
		logger: logger,
		cache:  make(map[uint]*compiledRule),
		statsService: NewSpiderBlockStatsService(db, logger),
	}
	
	// 生产模式：不再检查可疑规则
	// 不创建任何默认规则 - 所有规则必须手动添加
	
	// 初始化时加载所有规则
	sbs.loadRules()
	
	// 已删除createDefaultRules函数，避免自动生成数据
	// 确保不会自动创建任何spider_blocks规则
	
	return sbs
}

// loadRules 从数据库加载所有启用的规则
func (sbs *SpiderBlockService) loadRules() {
	var rules []model.SpiderBlock
	if err := sbs.db.Where("enabled = ?", true).Find(&rules).Error; err != nil {
		sbs.logger.Error("加载蜘蛛规则失败", zap.Error(err))
		return
	}
	
	// 创建新的缓存map
	newCache := make(map[uint]*compiledRule)
	
	// 编译正则表达式
	for _, rule := range rules {
		pattern, err := regexp.Compile("(?i)" + regexp.QuoteMeta(rule.UserAgent))
		if err != nil {
			sbs.logger.Error("编译蜘蛛规则失败", zap.String("user_agent", rule.UserAgent), zap.Error(err))
			continue
		}
		
		newCache[rule.ID] = &compiledRule{
			rule:    &rule,
			pattern: pattern,
		}
	}
	
	// 使用写锁替换整个缓存
	sbs.cacheMu.Lock()
	sbs.cache = newCache
	sbs.cacheMu.Unlock()
	
	sbs.logger.Debug("规则加载完成", zap.Int("count", len(newCache)))
}

// CheckUserAgent 检查用户代理是否被屏蔽
func (sbs *SpiderBlockService) CheckUserAgent(userAgent string) (bool, int) {
	return sbs.CheckUserAgentWithDomain(userAgent, "")
}

// CheckUserAgentWithDomain 检查用户代理是否被屏蔽（带域名）
func (sbs *SpiderBlockService) CheckUserAgentWithDomain(userAgent string, domain string) (bool, int) {
	// 首先检查全局蜘蛛屏蔽开关是否启用
	// 如果全局开关未启用，直接返回false（不屏蔽）
	if sbs.systemSettingsService != nil {
		settings, _ := sbs.systemSettingsService.GetSystemSettings()
		if settings == nil || !settings.EnableGlobalSpiderBlock {
			// 全局蜘蛛屏蔽未启用，不执行任何屏蔽
			return false, 0
		}
	}
	
	userAgent = strings.ToLower(userAgent)
	
	// 使用读锁保护cache的访问
	sbs.cacheMu.RLock()
	defer sbs.cacheMu.RUnlock()
	
	for _, compiled := range sbs.cache {
		if compiled.pattern.MatchString(userAgent) {
			// 增加命中计数（异步）
			go func(rule *model.SpiderBlock) {
				sbs.db.Model(&model.SpiderBlock{}).Where("id = ?", rule.ID).UpdateColumn("hit_count", gorm.Expr("hit_count + ?", 1))
				// 记录到统计服务
				if sbs.statsService != nil {
					sbs.statsService.RecordBlock(rule.UserAgent, domain)
				}
			}(compiled.rule)
			
			return true, compiled.rule.ReturnCode
		}
	}
	
	return false, 0
}

// GetRules 获取所有规则
func (sbs *SpiderBlockService) GetRules() ([]model.SpiderBlock, error) {
	var rules []model.SpiderBlock
	err := sbs.db.Order("id DESC").Find(&rules).Error // 默认降序
	return rules, err
}

// GetRulesWithSort 获取所有规则（支持排序）
func (sbs *SpiderBlockService) GetRulesWithSort(sortBy string, sortOrder string) ([]model.SpiderBlock, error) {
	var rules []model.SpiderBlock
	
	// 验证排序字段
	validSortFields := map[string]bool{
		"id":          true,
		"return_code": true,
		"hit_count":   true,
		"enabled":     true,
		"created_at":  true,
		"updated_at":  true,
	}
	
	if !validSortFields[sortBy] {
		sortBy = "id"
	}
	
	// 验证排序顺序
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "asc"
	}
	
	// 构建排序查询
	orderClause := sortBy + " " + sortOrder
	
	err := sbs.db.Order(orderClause).Find(&rules).Error
	return rules, err
}

// GetRulesWithPagination 获取分页规则（支持排序和搜索）
func (sbs *SpiderBlockService) GetRulesWithPagination(page, pageSize int, sortBy, sortOrder, search string) ([]model.SpiderBlock, int64, error) {
	var rules []model.SpiderBlock
	var total int64
	
	// 验证分页参数
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}
	
	// 验证排序字段
	validSortFields := map[string]bool{
		"id":          true,
		"return_code": true,
		"hit_count":   true,
		"enabled":     true,
		"created_at":  true,
		"updated_at":  true,
	}
	
	if !validSortFields[sortBy] {
		sortBy = "id"
	}
	
	// 验证排序顺序
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc"
	}
	
	// 构建查询
	query := sbs.db.Model(&model.SpiderBlock{})
	
	// 添加搜索条件
	if search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where("user_agent LIKE ? OR description LIKE ?", searchPattern, searchPattern)
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 构建排序查询
	orderClause := sortBy + " " + sortOrder
	
	// 计算偏移量
	offset := (page - 1) * pageSize
	
	// 执行分页查询
	err := query.Order(orderClause).Offset(offset).Limit(pageSize).Find(&rules).Error
	return rules, total, err
}

// GetRule 获取单个规则
func (sbs *SpiderBlockService) GetRule(id uint) (*model.SpiderBlock, error) {
	var rule model.SpiderBlock
	err := sbs.db.First(&rule, id).Error
	return &rule, err
}

// CreateRule 创建规则
func (sbs *SpiderBlockService) CreateRule(rule *model.SpiderBlock) error {
	if err := sbs.db.Create(rule).Error; err != nil {
		sbs.logger.Error("创建Spider Block规则失败", 
			zap.String("user_agent", rule.UserAgent),
			zap.Error(err))
		return err
	}
	
	// 重新加载规则
	sbs.loadRules()
	return nil
}

// GetRuleByID 根据ID获取规则
func (sbs *SpiderBlockService) GetRuleByID(id uint) (*model.SpiderBlock, error) {
	var rule model.SpiderBlock
	if err := sbs.db.First(&rule, id).Error; err != nil {
		return nil, err
	}
	return &rule, nil
}

// UpdateRule 更新规则（接受对象）
func (sbs *SpiderBlockService) UpdateRule(rule *model.SpiderBlock) error {
	if err := sbs.db.Save(rule).Error; err != nil {
		return err
	}
	
	// 重新加载规则
	sbs.loadRules()
	return nil
}

// UpdateRuleByID 更新规则（接受ID和更新字段）
func (sbs *SpiderBlockService) UpdateRuleByID(id uint, updates map[string]interface{}) error {
	if err := sbs.db.Model(&model.SpiderBlock{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return err
	}
	
	// 重新加载规则
	sbs.loadRules()
	return nil
}

// DeleteRule 删除规则
func (sbs *SpiderBlockService) DeleteRule(id uint) error {
	if err := sbs.db.Delete(&model.SpiderBlock{}, id).Error; err != nil {
		return err
	}
	
	// 重新加载规则
	sbs.loadRules()
	return nil
}

// ToggleRule 切换规则启用状态
func (sbs *SpiderBlockService) ToggleRule(id uint) error {
	var rule model.SpiderBlock
	if err := sbs.db.First(&rule, id).Error; err != nil {
		return err
	}
	
	rule.Enabled = !rule.Enabled
	if err := sbs.db.Save(&rule).Error; err != nil {
		return err
	}
	
	// 重新加载规则
	sbs.loadRules()
	return nil
}

// ResetHitCount 重置命中计数
func (sbs *SpiderBlockService) ResetHitCount(id uint) error {
	return sbs.db.Model(&model.SpiderBlock{}).Where("id = ?", id).UpdateColumn("hit_count", 0).Error
}

// ResetAllHitCounts 重置所有命中计数并清空统计数据
func (sbs *SpiderBlockService) ResetAllHitCounts() error {
	sbs.logger.Info("开始重置所有命中计数")
	
	// 重置所有规则的命中计数 - 使用Exec避免WHERE条件要求
	result := sbs.db.Exec("UPDATE spider_blocks SET hit_count = 0")
	if result.Error != nil {
		sbs.logger.Error("重置命中计数失败", zap.Error(result.Error))
		return result.Error
	}
	sbs.logger.Info("重置命中计数成功", zap.Int64("affected", result.RowsAffected))
	
	// 清空统计数据（调用已有的清空统计功能）
	if sbs.statsService != nil {
		sbs.logger.Info("开始清空统计数据")
		// 清空数据库中的统计数据
		if err := sbs.statsService.ClearStats(); err != nil {
			sbs.logger.Warn("清空统计数据失败", zap.Error(err))
			// 不返回错误，继续执行
		}
		
		// 清空内存中的待写入统计数据
		sbs.statsService.mu.Lock()
		sbs.statsService.pendingStats = make(map[string]*model.SpiderBlockHourlyStats)
		sbs.statsService.mu.Unlock()
		sbs.logger.Info("统计数据清空完成")
	} else {
		sbs.logger.Warn("统计服务未初始化")
	}
	
	return nil
}

// GetStatsService 获取统计服务
func (sbs *SpiderBlockService) GetStatsService() *SpiderBlockStatsService {
	return sbs.statsService
}

// SetSystemSettingsService 设置系统设置服务
func (sbs *SpiderBlockService) SetSystemSettingsService(service *SystemSettingsService) {
	sbs.systemSettingsService = service
}

// ClearAllRules 清空所有规则
func (sbs *SpiderBlockService) ClearAllRules() error {
	// 开启事务
	tx := sbs.db.Begin()
	
	// 清空数据库中的所有规则
	if err := tx.Where("1 = 1").Delete(&model.SpiderBlock{}).Error; err != nil {
		tx.Rollback()
		return err
	}
	
	// 清空小时统计数据
	if err := tx.Where("1 = 1").Delete(&model.SpiderBlockHourlyStats{}).Error; err != nil {
		tx.Rollback()
		return err
	}
	
	// 清空日统计数据
	if err := tx.Where("1 = 1").Delete(&model.SpiderBlockDailyStats{}).Error; err != nil {
		tx.Rollback()
		return err
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return err
	}
	
	// 重新加载规则（这会清空缓存）
	sbs.loadRules()
	
	// 清空统计服务的缓存
	if sbs.statsService != nil {
		sbs.statsService.ClearCache()
	}
	
	sbs.logger.Info("已清空所有蜘蛛屏蔽规则和统计数据")
	return nil
}
