package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"site-cluster/internal/model"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PseudoService struct {
	db         *gorm.DB
	logger     *zap.Logger
	redisCache *RedisCacheService
}

func NewPseudoService(db *gorm.DB, logger *zap.Logger) *PseudoService {
	return &PseudoService{
		db:     db,
		logger: logger,
	}
}

// SetRedisCache 设置Redis缓存服务
func (ps *PseudoService) SetRedisCache(redisCache *RedisCacheService) {
	ps.redisCache = redisCache
}

// GetLibraries 获取所有词库
func (ps *PseudoService) GetLibraries() ([]model.PseudoLibrary, error) {
	var libraries []model.PseudoLibrary
	if err := ps.db.Find(&libraries).Error; err != nil {
		ps.logger.Error("获取词库列表失败", zap.Error(err))
		return nil, err
	}
	return libraries, nil
}

// GetLibrary 获取词库详情
func (ps *PseudoService) GetLibrary(id uint) (*model.PseudoLibrary, error) {
	var library model.PseudoLibrary
	if err := ps.db.Preload("Words").First(&library, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, err
		}
		ps.logger.Error("获取词库失败", zap.Error(err), zap.Uint("id", id))
		return nil, err
	}
	return &library, nil
}

// CreateLibrary 创建词库
func (ps *PseudoService) CreateLibrary(library *model.PseudoLibrary) error {
	if err := ps.db.Create(library).Error; err != nil {
		ps.logger.Error("创建词库失败", zap.Error(err))
		return err
	}
	return nil
}

// UpdateLibrary 更新词库
func (ps *PseudoService) UpdateLibrary(library *model.PseudoLibrary) error {
	if err := ps.db.Save(library).Error; err != nil {
		ps.logger.Error("更新词库失败", zap.Error(err))
		return err
	}
	return nil
}

// DeleteLibrary 删除词库
func (ps *PseudoService) DeleteLibrary(id uint) error {
	// 先删除词库的所有词条
	if err := ps.db.Where("library_id = ?", id).Delete(&model.PseudoWord{}).Error; err != nil {
		ps.logger.Error("删除词库词条失败", zap.Error(err))
		return err
	}
	
	// 再删除词库
	if err := ps.db.Delete(&model.PseudoLibrary{}, id).Error; err != nil {
		ps.logger.Error("删除词库失败", zap.Error(err))
		return err
	}
	
	ps.logger.Info("词库删除成功", zap.Uint("id", id))
	return nil
}

// GetWords 获取词库的词条列表
func (ps *PseudoService) GetWords(libraryID uint) ([]model.PseudoWord, error) {
	var words []model.PseudoWord
	if err := ps.db.Where("library_id = ?", libraryID).Find(&words).Error; err != nil {
		ps.logger.Error("获取词条列表失败", zap.Error(err))
		return nil, err
	}
	return words, nil
}

// GetWordsPaginated 分页获取词条列表
func (ps *PseudoService) GetWordsPaginated(libraryID uint, page, limit int, search string) ([]model.PseudoWord, int64, error) {
	var words []model.PseudoWord
	var total int64
	
	query := ps.db.Where("library_id = ?", libraryID)
	
	// 如果有搜索条件
	if search != "" {
		query = query.Where("original ILIKE ? OR synonyms::text ILIKE ?", "%"+search+"%", "%"+search+"%")
	}
	
	// 获取总数
	if err := query.Model(&model.PseudoWord{}).Count(&total).Error; err != nil {
		ps.logger.Error("获取词条总数失败", zap.Error(err))
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Find(&words).Error; err != nil {
		ps.logger.Error("获取词条列表失败", zap.Error(err))
		return nil, 0, err
	}
	
	return words, total, nil
}

// GetWord 获取词条
func (ps *PseudoService) GetWord(id uint) (*model.PseudoWord, error) {
	var word model.PseudoWord
	if err := ps.db.First(&word, id).Error; err != nil {
		return nil, err
	}
	return &word, nil
}

// GetWordsByLibraryIDs 根据词库ID列表获取所有词条（带Redis缓存）
func (ps *PseudoService) GetWordsByLibraryIDs(libraryIDs []uint) ([]model.PseudoWord, error) {
	if len(libraryIDs) == 0 {
		return []model.PseudoWord{}, nil
	}
	
	// 生成缓存键
	cacheKey := fmt.Sprintf("pseudo_libs:%v", libraryIDs)
	
	// 尝试从Redis缓存获取
	if ps.redisCache != nil {
		if cachedData, found := ps.redisCache.Get(cacheKey); found {
			var words []model.PseudoWord
			if err := json.Unmarshal([]byte(cachedData), &words); err == nil {
				ps.logger.Debug("从Redis缓存获取伪原创词条", 
					zap.Uints("library_ids", libraryIDs), 
					zap.Int("count", len(words)))
				return words, nil
			}
		}
	}
	
	// 缓存未命中，从数据库获取
	var words []model.PseudoWord
	if err := ps.db.Where("library_id IN ?", libraryIDs).Find(&words).Error; err != nil {
		ps.logger.Error("根据词库ID获取词条失败", zap.Error(err))
		return nil, err
	}
	
	// 写入Redis缓存（缓存24小时）
	if ps.redisCache != nil && len(words) > 0 {
		if data, err := json.Marshal(words); err == nil {
			ps.redisCache.Set(cacheKey, string(data), 24*time.Hour)
			ps.logger.Debug("伪原创词条已缓存到Redis", 
				zap.Uints("library_ids", libraryIDs), 
				zap.Int("count", len(words)))
		}
	}
	
	ps.logger.Info("获取伪原创词条",
		zap.Uints("library_ids", libraryIDs),
		zap.Int("word_count", len(words)))
	
	return words, nil
}

// CreateWord 创建词条
func (ps *PseudoService) CreateWord(word *model.PseudoWord) error {
	if err := ps.db.Create(word).Error; err != nil {
		ps.logger.Error("创建词条失败", zap.Error(err))
		return err
	}
	// 清理词库缓存
	ps.invalidateLibraryCache(word.LibraryID)
	return nil
}

// invalidateLibraryCache 清理指定词库的缓存
func (ps *PseudoService) invalidateLibraryCache(libraryID uint) {
	if ps.redisCache != nil {
		// 清理单个词库缓存
		cacheKey := fmt.Sprintf("pseudo_lib:%d", libraryID)
		ps.redisCache.Delete(cacheKey)
		
		// 清理所有包含该词库的组合缓存
		// 使用模糊匹配删除
		pattern := fmt.Sprintf("pseudo_libs:*%d*", libraryID)
		ps.clearCacheByPattern(pattern)
		
		ps.logger.Debug("伪原创词库缓存已清理", zap.Uint("libraryID", libraryID))
	}
}

// clearCacheByPattern 根据模式清理缓存
func (ps *PseudoService) clearCacheByPattern(pattern string) {
	if ps.redisCache == nil || ps.redisCache.client == nil {
		return
	}
	
	ctx := context.Background()
	iter := ps.redisCache.client.Scan(ctx, 0, pattern, 0).Iterator()
	var keys []string
	for iter.Next(ctx) {
		keys = append(keys, iter.Val())
	}
	if len(keys) > 0 {
		ps.redisCache.Delete(keys...)
	}
}

// UpdateWord 更新词条
func (ps *PseudoService) UpdateWord(word *model.PseudoWord) error {
	if err := ps.db.Save(word).Error; err != nil {
		ps.logger.Error("更新词条失败", zap.Error(err))
		return err
	}
	// 清理词库缓存
	ps.invalidateLibraryCache(word.LibraryID)
	return nil
}

// DeleteWord 删除词条
func (ps *PseudoService) DeleteWord(id uint) error {
	// 先获取词条信息以获取libraryID
	var word model.PseudoWord
	if err := ps.db.First(&word, id).Error; err == nil {
		// 删除词条
		if err := ps.db.Delete(&model.PseudoWord{}, id).Error; err != nil {
			ps.logger.Error("删除词条失败", zap.Error(err))
			return err
		}
		// 清理词库缓存
		ps.invalidateLibraryCache(word.LibraryID)
	} else {
		if err := ps.db.Delete(&model.PseudoWord{}, id).Error; err != nil {
			ps.logger.Error("删除词条失败", zap.Error(err))
			return err
		}
	}
	return nil
}

// ImportWords 批量导入词条
// 格式：每行一个，原词和同义词用|分隔，同义词之间用,分隔
// 例如：优秀|卓越,杰出,出色
func (ps *PseudoService) ImportWords(libraryID uint, content string) (int, error) {
	lines := strings.Split(content, "\n")
	count := 0
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		
		parts := strings.Split(line, "|")
		if len(parts) != 2 {
			continue
		}
		
		original := strings.TrimSpace(parts[0])
		synonymsStr := strings.TrimSpace(parts[1])
		
		if original == "" || synonymsStr == "" {
			continue
		}
		
		synonymsParts := strings.Split(synonymsStr, ",")
		var synonyms []string
		for _, syn := range synonymsParts {
			syn = strings.TrimSpace(syn)
			if syn != "" {
				synonyms = append(synonyms, syn)
			}
		}
		
		if len(synonyms) == 0 {
			continue
		}
		
		word := &model.PseudoWord{
			LibraryID: libraryID,
			Original:  original,
			Synonyms:  synonyms,
		}
		
		if err := ps.db.Create(word).Error; err != nil {
			ps.logger.Warn("导入词条失败", zap.Error(err), zap.String("original", original))
			continue
		}
		
		count++
	}
	
	return count, nil
}

// GetLibrariesByIDs 根据ID列表获取词库
func (ps *PseudoService) GetLibrariesByIDs(ids []uint) ([]model.PseudoLibrary, error) {
	var libraries []model.PseudoLibrary
	if len(ids) == 0 {
		return libraries, nil
	}
	
	if err := ps.db.Preload("Words").Where("id IN ?", ids).Find(&libraries).Error; err != nil {
		ps.logger.Error("根据ID获取词库失败", zap.Error(err))
		return nil, err
	}
	return libraries, nil
}