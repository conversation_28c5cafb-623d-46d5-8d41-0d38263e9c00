package model

import (
	"time"
)

// CrawlJob 爬取任务
type CrawlJob struct {
	ID             uint       `gorm:"primarykey" json:"id"`
	SiteID         uint       `gorm:"index" json:"site_id"`
	URL            string     `json:"url"`
	Type           string     `json:"type"` // crawl, refresh, preload
	Status         string     `gorm:"index" json:"status"` // pending, processing, completed, failed
	Priority       int        `json:"priority"` // 优先级
	ProcessedPages int        `json:"processed_pages"` // 已处理页面数
	RetryCount     int        `json:"retry_count"` // 重试次数
	Error          string     `json:"error,omitempty"`
	StartedAt      *time.Time `json:"started_at,omitempty"`
	CompletedAt    *time.Time `json:"completed_at,omitempty"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
	
	Site *Site `gorm:"foreignKey:SiteID" json:"site,omitempty"`
}