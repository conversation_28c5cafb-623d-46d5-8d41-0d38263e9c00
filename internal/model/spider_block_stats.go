package model

import (
	"time"
)

// SpiderBlockHourlyStats 蜘蛛屏蔽小时统计
type SpiderBlockHourlyStats struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Hour        time.Time `json:"hour" gorm:"index:idx_hour_spider,unique;not null"` // 整点时间
	SpiderName  string    `json:"spider_name" gorm:"index:idx_hour_spider,unique;size:100"` // 蜘蛛名称
	BlockCount  int64     `json:"block_count"`  // 屏蔽次数
	Domain      string    `json:"domain" gorm:"index:idx_domain;size:255"` // 域名（可选）
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SpiderBlockHourlyStats) TableName() string {
	return "spider_block_hourly_stats"
}

// SpiderBlockDailyStats 蜘蛛屏蔽日统计（用于长期存储）
type SpiderBlockDailyStats struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Date        time.Time `json:"date" gorm:"index:idx_date_spider,unique;type:date;not null"` // 日期
	SpiderName  string    `json:"spider_name" gorm:"index:idx_date_spider,unique;size:100"` // 蜘蛛名称
	BlockCount  int64     `json:"block_count"`  // 屏蔽次数
	Domain      string    `json:"domain" gorm:"index;size:255"` // 域名（可选）
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SpiderBlockDailyStats) TableName() string {
	return "spider_block_daily_stats"
}