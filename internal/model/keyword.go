package model

import (
	"time"
)

// KeywordLibrary 关键词库
type KeywordLibrary struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"unique;not null"`
	Description string    `json:"description"`
	Type        string    `json:"type"` // main, synonym, pseudo
	Keywords    []Keyword `json:"keywords" gorm:"foreignKey:LibraryID"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Keyword 关键词
type Keyword struct {
	ID        uint        `json:"id" gorm:"primaryKey"`
	LibraryID uint        `json:"library_id"`
	Keyword   string      `json:"keyword" gorm:"column:keyword"` // 关键词
	Synonyms  StringSlice `json:"synonyms" gorm:"type:json"`  // 同义词列表
	Weight    int         `json:"weight"`
	CreatedAt time.Time   `json:"created_at"`
	UpdatedAt time.Time   `json:"updated_at"`
}

// PseudoLibrary 伪原创词库
type PseudoLibrary struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"unique;not null"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Words       []PseudoWord `json:"words" gorm:"foreignKey:LibraryID"`
}

// PseudoWord 伪原创词条
type PseudoWord struct {
	ID        uint        `json:"id" gorm:"primaryKey"`
	LibraryID uint        `json:"library_id"`
	Original  string      `json:"original" gorm:"not null"`    // 原词
	Synonyms  StringSlice `json:"synonyms" gorm:"type:json"`   // 同义词列表
	CreatedAt time.Time   `json:"created_at"`
	UpdatedAt time.Time   `json:"updated_at"`
}