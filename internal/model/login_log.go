package model

import (
	"time"
)

// LoginLog 登录日志
type LoginLog struct {
	ID         uint      `gorm:"primaryKey" json:"id"`
	Username   string    `gorm:"size:50;index" json:"username"`         // 用户名
	IP         string    `gorm:"size:50" json:"ip"`                     // 登录IP
	UserAgent  string    `gorm:"size:500" json:"user_agent"`            // 用户代理
	Status     string    `gorm:"size:20" json:"status"`                 // 状态：success/failed
	FailReason string    `gorm:"size:200" json:"fail_reason,omitempty"` // 失败原因
	LoginTime  time.Time `gorm:"index" json:"login_time"`               // 登录时间
	CreatedAt  time.Time `json:"created_at"`
}

// TableName 表名
func (LoginLog) TableName() string {
	return "login_logs"
}