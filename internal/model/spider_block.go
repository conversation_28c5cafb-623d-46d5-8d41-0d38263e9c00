package model

import (
	"time"
)

// SpiderBlock 蜘蛛屏蔽规则
type SpiderBlock struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	UserAgent   string    `gorm:"type:text;not null" json:"user_agent"`    // 用户代理特征
	Description string    `gorm:"type:text" json:"description"`            // 描述
	ReturnCode  int       `gorm:"default:403" json:"return_code"`          // 返回状态码（403、404等）
	Enabled     bool      `gorm:"default:true" json:"enabled"`             // 是否启用
	HitCount    int64     `gorm:"default:0" json:"hit_count"`              // 命中次数
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SpiderBlock) TableName() string {
	return "spider_blocks"
}