package model

import (
	"time"
)

// SystemSettings 系统设置
type SystemSettings struct {
	ID                      uint      `json:"id" gorm:"primaryKey"`
	EnableGlobalUACheck     bool      `json:"enable_global_ua_check" gorm:"default:false"` // 全局UA判断开关
	DefaultNonSpiderHTML    string    `json:"default_non_spider_html" gorm:"type:text"`    // 默认的非爬虫访问HTML模板
	DefaultAllowedUA        string    `json:"default_allowed_ua" gorm:"type:text"`         // 默认允许的UA列表（用|分隔）
	DefaultSpiderBlockUA    string    `json:"default_spider_block_ua" gorm:"type:text"` // 默认蜘蛛屏蔽UA（每行一个）
	UserAgentList           string    `json:"user_agent_list" gorm:"type:text"`         // User-Agent列表（每行一个）
	// CrawlerConcurrency 已被 MaxHTTPRequests 替代
	// CrawlerTimeout 已被 CrawlerTaskTimeout 替代
	DefaultCacheHomeTTL     int       `json:"default_cache_home_ttl"`                   // 默认首页缓存时间（分钟）
	DefaultCacheOtherTTL    int       `json:"default_cache_other_ttl"`                  // 默认其他页面缓存时间（分钟）
	DefaultEnableRedisCache bool      `json:"default_enable_redis_cache"`               // 默认是否启用Redis缓存
	EnableGlobalSpiderBlock bool      `json:"enable_global_spider_block"`               // 全局蜘蛛屏蔽开关
	SpiderBlock403Template  string    `json:"spider_block_403_template" gorm:"type:text"` // 蜘蛛屏蔽403响应模板
	SiteNotFoundHTML        string    `json:"site_not_found_html" gorm:"type:text"`     // 站点未配置时的HTML页面
	Default404HTML          string    `json:"default_404_html" gorm:"type:text"`        // 目标站点返回404时的默认页面
	Enable404Cache          bool      `json:"enable_404_cache" gorm:"default:true"`     // 是否启用404缓存
	Cache404TTL             int       `json:"cache_404_ttl" gorm:"default:86400"`       // 404缓存时间（秒，默认24小时）
	
	// Redis连接池配置（超时已被 RedisOpTimeout 统一管理）
	RedisMaxPoolSize        int       `json:"redis_max_pool_size" gorm:"default:30"`         // Redis最大连接数（默认30）
	
	FileReadTimeout         int       `json:"file_read_timeout" gorm:"default:5000"`         // 文件读超时（默认5秒）
	FileWriteTimeout        int       `json:"file_write_timeout" gorm:"default:10000"`       // 文件写超时（默认10秒）
	AsyncQueueSize          int       `json:"async_queue_size" gorm:"default:10000"`         // 异步队列大小（默认10000）
	AsyncWorkerCount        int       `json:"async_worker_count" gorm:"default:10"`          // 异步工作协程数（默认10）
	
	// HTTPClientTimeout 已被 HTTPRequestTimeout 替代
	ProxyRequestTimeout     int       `json:"proxy_request_timeout" gorm:"default:60000"`    // 代理请求超时（默认60秒）
	
	// 域名跳转配置
	GlobalRedirectWWW       bool      `json:"global_redirect_www" gorm:"default:false"`      // 全局 @ 跳转到 www
	
	// 来源判断配置
	EnableGlobalRefererCheck   bool      `json:"enable_global_referer_check" gorm:"default:false"`  // 全局启用来源判断
	GlobalAllowedReferers      string    `json:"global_allowed_referers" gorm:"type:text"`         // 全局允许的来源域名列表
	GlobalRefererBlockCode     int       `json:"global_referer_block_code" gorm:"default:403"`     // 全局拒绝状态码
	GlobalRefererBlockHTML     string    `json:"global_referer_block_html" gorm:"type:text"`       // 全局拒绝HTML内容
	
	// 内容优化配置
	EnableGlobalPinyin         bool      `json:"enable_global_pinyin" gorm:"default:false"`         // 全局启用拼音标注
	EnablePinyinSpecialChars   bool      `json:"enable_pinyin_special_chars" gorm:"default:false"`  // 启用拼音特殊字符
	PinyinSpecialCharsRatio    float32   `json:"pinyin_special_chars_ratio" gorm:"default:0.3"`     // 特殊字符插入比例
	PinyinSpecialChars         string    `json:"pinyin_special_chars" gorm:"type:text;default:'…|⁖|⸪|⸬|⸫'"` // 特殊字符列表
	EnableGlobalKeyword        bool      `json:"enable_global_keyword" gorm:"default:false"`        // 全局启用关键词注入
	KeywordMaxPerPage          int       `json:"keyword_max_per_page" gorm:"default:10"`            // 每页最大关键词数
	KeywordDensity             float32   `json:"keyword_density" gorm:"default:3.0"`                // 关键词密度
	EnableGlobalPseudo         bool      `json:"enable_global_pseudo" gorm:"default:false"`         // 全局启用伪原创
	PseudoStrength             string    `json:"pseudo_strength" gorm:"default:'medium'"`           // 伪原创强度
	EnableGlobalTraditional    bool      `json:"enable_global_traditional" gorm:"default:false"`    // 全局启用简繁转换
	
	// 数据库连接池配置
	DBMaxOpenConns          int       `json:"db_max_open_conns" gorm:"default:100"`          // 数据库最大连接数（建议100-300）
	DBMaxIdleConns          int       `json:"db_max_idle_conns" gorm:"default:50"`           // 数据库最大空闲连接数（建议50-100）
	DBConnMaxLifetime       int       `json:"db_conn_max_lifetime" gorm:"default:600"`       // 数据库连接最大生命周期（秒，默认10分钟）
	DBSlaveMaxOpenConns     int       `json:"db_slave_max_open_conns" gorm:"default:50"`     // 从库最大连接数
	DBSlaveMaxIdleConns     int       `json:"db_slave_max_idle_conns" gorm:"default:25"`     // 从库最大空闲连接数
	
	// HTTP客户端配置
	HTTPMaxIdleConns        int       `json:"http_max_idle_conns" gorm:"default:200"`        // HTTP最大空闲连接数
	HTTPMaxIdleConnsPerHost int       `json:"http_max_idle_conns_per_host" gorm:"default:50"` // 每个主机的最大空闲连接数
	HTTPMaxConnsPerHost     int       `json:"http_max_conns_per_host" gorm:"default:100"`    // 每个主机的最大连接数
	HTTPIdleConnTimeout     int       `json:"http_idle_conn_timeout" gorm:"default:90"`      // 空闲连接超时（秒）
	
	// 速率限制配置（SchedulerMaxWorkers 和 SchedulerQueueSize 已被信号量机制替代）
	CrawlerRateLimit        float64   `json:"crawler_rate_limit" gorm:"default:2000"`        // 爬虫速率限制（QPS）
	
	// 工作池配置
	WorkerPoolMode          string    `json:"worker_pool_mode" gorm:"default:'auto'"`        // 工作池模式 (auto, fixed, dynamic)
	WorkerPoolSize          int       `json:"worker_pool_size" gorm:"default:0"`             // 固定工作池大小，0表示自动计算
	WorkerPoolMinSize       int       `json:"worker_pool_min_size" gorm:"default:100"`       // 动态工作池最小大小
	WorkerPoolMaxSize       int       `json:"worker_pool_max_size" gorm:"default:2000"`      // 动态工作池最大大小
	WorkerPoolScaleRatio    float64   `json:"worker_pool_scale_ratio" gorm:"default:1.2"`    // 工作池大小相对于总并发的比例
	
	// 缓存锁配置
	CacheLockTimeout        int       `json:"cache_lock_timeout" gorm:"default:1000"`        // 缓存锁超时（毫秒）
	CacheLockRetryInterval  int       `json:"cache_lock_retry_interval" gorm:"default:100"`  // 缓存锁重试间隔（毫秒）
	
	// 验证码配置
	EnableCaptcha           bool      `json:"enable_captcha" gorm:"default:false"`           // 是否启用验证码
	CaptchaLength           int       `json:"captcha_length" gorm:"default:4"`               // 验证码长度
	CaptchaExpiry           int       `json:"captcha_expiry" gorm:"default:300"`             // 验证码过期时间（秒）
	
	// CSRF配置
	EnableCSRF              bool      `json:"enable_csrf" gorm:"default:false"`              // 是否启用CSRF保护
	
	// UA统计配置
	EnableUAStats           bool      `json:"enable_ua_stats" gorm:"default:false"`          // 是否启用UA统计
	
	// 日志配置
	LogEnabled              bool      `json:"log_enabled" gorm:"default:true"`               // 是否启用日志
	LogLevel                string    `json:"log_level" gorm:"default:'info'"`               // 日志级别 (debug, info, warn, error)
	LogStorage              string    `json:"log_storage" gorm:"default:'file'"`             // 存储方式 (file, database, both)
	LogRetentionDays        int       `json:"log_retention_days" gorm:"default:7"`           // 日志保留天数
	LogMaxSize              int       `json:"log_max_size" gorm:"default:100"`               // 单个日志文件最大大小(MB)
	LogMaxBackups           int       `json:"log_max_backups" gorm:"default:10"`             // 最大备份文件数
	LogAccessEnabled        bool      `json:"log_access_enabled" gorm:"default:true"`        // 是否记录访问日志
	LogErrorEnabled         bool      `json:"log_error_enabled" gorm:"default:true"`         // 是否记录错误日志
	
	// 站点地图配置
	SitemapRefreshInterval    int       `json:"sitemap_refresh_interval" gorm:"default:720"`       // 站点地图刷新间隔（分钟，默认12小时，0表示禁用）
	LastSitemapRefreshTime    *time.Time `json:"last_sitemap_refresh_time"`                        // 上次站点地图刷新时间
	
	// 资源限流配置（新增）
	MaxDatabaseConn         int       `json:"max_database_conn" gorm:"default:80"`           // 最大数据库并发连接数
	MaxRedisConn            int       `json:"max_redis_conn" gorm:"default:160"`             // 最大Redis并发连接数
	MaxHTTPRequests         int       `json:"max_http_requests" gorm:"default:40"`           // 最大HTTP并发请求数
	MaxFileOps              int       `json:"max_file_ops" gorm:"default:24"`                // 最大文件并发操作数
	MaxCrawlerTasks         int       `json:"max_crawler_tasks" gorm:"default:16"`           // 最大爬虫并发任务数
	
	// 统一超时配置（新增，替代原有分散的超时配置）
	RouteTimeout            int       `json:"route_timeout" gorm:"default:1200"`             // 路由处理总超时（毫秒）
	DatabaseQueryTimeout    int       `json:"database_query_timeout" gorm:"default:800"`     // 数据库查询超时（毫秒）
	RedisOpTimeout          int       `json:"redis_op_timeout" gorm:"default:200"`           // Redis操作超时（毫秒）
	HTTPRequestTimeout      int       `json:"http_request_timeout" gorm:"default:500"`       // HTTP请求超时（毫秒）
	FileOpTimeout           int       `json:"file_op_timeout" gorm:"default:1000"`           // 文件操作超时（毫秒）
	CrawlerTaskTimeout      int       `json:"crawler_task_timeout" gorm:"default:10000"`     // 爬虫任务超时（毫秒）
	
	CreatedAt               time.Time `json:"created_at"`
	UpdatedAt               time.Time `json:"updated_at"`
}