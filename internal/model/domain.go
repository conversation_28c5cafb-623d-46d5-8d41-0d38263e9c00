package model

type Domain struct {
	Host      string `json:"host"`
	TargetURL string `json:"target_url"` // 目标站点URL
	Group     string `json:"group"`
	Enabled   bool   `json:"enabled"`
}

type Content struct {
	URL         string
	Title       string
	Body        string
	ContentType string
	Data        []byte
	CachedAt    int64
}

type Resource struct {
	URL         string
	ContentType string
	Data        []byte
	CachedAt    int64
}