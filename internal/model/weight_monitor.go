package model

import (
	"time"
)

// WeightMonitorConfig 权重监测配置
type WeightMonitorConfig struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	Enabled       bool      `gorm:"column:enabled" json:"enabled"`                         // 是否启用监测
	APIKey        string    `gorm:"column:api_key" json:"api_key"`                        // 爱站API密钥
	CheckInterval int       `gorm:"column:check_interval" json:"check_interval"`          // 检查间隔（分钟）
	BatchSize     int       `gorm:"column:batch_size" json:"batch_size"`                  // 每批域名数量
	BatchDelay    int       `gorm:"column:batch_delay" json:"batch_delay"`                // 批次间隔（秒）
	CycleWait     int       `gorm:"column:cycle_wait" json:"cycle_wait"`                  // 循环等待时间（小时）
	LastCheckTime time.Time `gorm:"column:last_check_time" json:"last_check_time"`        // 最后检查时间
	CreatedAt     time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// WeightHistory 权重历史记录
type WeightHistory struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	Domain    string    `gorm:"index;not null" json:"domain"`      // 站点域名（不是目标URL）
	SiteID    uint      `gorm:"index" json:"site_id"`              // 关联站点ID
	PCBR      int       `gorm:"column:pc_br" json:"pc_br"`         // PC端百度权重
	MobileBR  int       `gorm:"column:m_br" json:"m_br"`           // 移动端百度权重
	IP        string    `gorm:"column:ip" json:"ip"`               // 预估总流量
	PCIP      string    `gorm:"column:pc_ip" json:"pc_ip"`         // PC端预估流量
	MobileIP  string    `gorm:"column:m_ip" json:"m_ip"`           // 移动端预估流量
	CheckTime time.Time `gorm:"index;column:check_time" json:"check_time"` // 检查时间
	CreatedAt time.Time `json:"created_at"`
}

// TableName 指定表名
func (WeightMonitorConfig) TableName() string {
	return "weight_monitor_config"
}

// TableName 指定表名
func (WeightHistory) TableName() string {
	return "weight_history"
}

// WeightTrend 权重趋势数据
type WeightTrend struct {
	Date     string `json:"date"`
	Domain   string `json:"domain"`
	PCBR     int    `json:"pc_br"`
	MobileBR int    `json:"m_br"`
	TotalIP  string `json:"total_ip"`
}

// WeightStats 权重统计数据
type WeightStats struct {
	Domain          string    `json:"domain"`
	CurrentPCBR     int       `json:"current_pc_br"`
	CurrentMobileBR int       `json:"current_m_br"`
	CurrentIP       string    `json:"current_ip"`
	CurrentPCIP     string    `json:"current_pc_ip"`     // PC端预估流量
	CurrentMIP      string    `json:"current_m_ip"`      // 移动端预估流量
	PrevPCBR        int       `json:"prev_pc_br"`
	PrevMobileBR    int       `json:"prev_m_br"`
	PrevIP          string    `json:"prev_ip"`
	PrevPCIP        string    `json:"prev_pc_ip"`        // 前一次PC端预估流量
	PrevMIP         string    `json:"prev_m_ip"`         // 前一次移动端预估流量
	PCBRChange      int       `json:"pc_br_change"`      // 权重变化
	MobileBRChange  int       `json:"m_br_change"`       // 权重变化
	LastCheckTime   time.Time `json:"last_check_time"`
}