package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// StringSlice 自定义类型，用于处理 PostgreSQL 的 JSON 数组
type StringSlice []string

// Scan 实现 sql.Scanner 接口
func (s *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*s = []string{}
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, s)
	case string:
		return json.Unmarshal([]byte(v), s)
	default:
		return errors.New("cannot scan value into StringSlice")
	}
}

// Value 实现 driver.Valuer 接口
func (s StringSlice) Value() (driver.Value, error) {
	if s == nil {
		return "[]", nil
	}
	return json.Marshal(s)
}

// UintSlice 自定义类型，用于处理 PostgreSQL 的 JSON 数组
type UintSlice []uint

// Scan 实现 sql.Scanner 接口
func (u *UintSlice) Scan(value interface{}) error {
	if value == nil {
		*u = []uint{}
		return nil
	}
	
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, u)
	case string:
		return json.Unmarshal([]byte(v), u)
	default:
		return errors.New("cannot scan value into UintSlice")
	}
}

// Value 实现 driver.Valuer 接口
func (u UintSlice) Value() (driver.Value, error) {
	if u == nil {
		return "[]", nil
	}
	return json.Marshal(u)
}