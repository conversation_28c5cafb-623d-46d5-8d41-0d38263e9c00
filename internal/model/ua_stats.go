package model

import (
	"time"
)

// UAStatsSummary UA统计汇总表
type UAStatsSummary struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	UAHash      string    `gorm:"type:varchar(32);uniqueIndex;not null" json:"ua_hash"`      // UA的MD5哈希值
	UserAgent   string    `gorm:"type:text;not null" json:"user_agent"`                      // 完整的User-Agent字符串
	Browser     string    `gorm:"type:varchar(100)" json:"browser"`                          // 浏览器名称
	BrowserVer  string    `gorm:"type:varchar(50)" json:"browser_version"`                   // 浏览器版本
	OS          string    `gorm:"type:varchar(100)" json:"os"`                               // 操作系统
	OSVer       string    `gorm:"type:varchar(50)" json:"os_version"`                        // 操作系统版本
	Device      string    `gorm:"type:varchar(100)" json:"device"`                           // 设备类型(Desktop/Mobile/Tablet/Bot)
	DeviceName  string    `gorm:"type:varchar(100)" json:"device_name"`                      // 设备名称
	SpiderName  string    `gorm:"type:varchar(100);index" json:"spider_name"`                // 爬虫名称(如果是爬虫)
	SpiderKey   string    `gorm:"type:varchar(100)" json:"spider_key"`                       // 爬虫标识(用于拦截，如bingbot)
	IsSpider    bool      `gorm:"default:false;index" json:"is_spider"`                      // 是否是爬虫
	HitCount    int64     `gorm:"default:0;index:idx_hit_count" json:"hit_count"`            // 总请求次数
	
	// 新增会话相关字段
	SessionCount   int     `gorm:"default:0" json:"session_count"`                            // 独立会话数
	PageViews      int     `gorm:"default:0" json:"page_views"`                              // 页面浏览量(HTML请求)
	ResourceHits   int     `gorm:"default:0" json:"resource_hits"`                           // 资源请求数
	VisitorType    string  `gorm:"type:varchar(50);default:'unknown'" json:"visitor_type"`    // 访问者类型: real_user/crawler/bot/unknown
	Confidence     float32 `gorm:"default:0" json:"confidence"`                              // 判断置信度(0-1)
	AvgResources   float32 `gorm:"default:0" json:"avg_resources"`                           // 平均每会话加载资源数
	LastIP         string  `gorm:"type:varchar(45)" json:"last_ip"`                          // 最后访问IP
	
	LastSeenAt  time.Time `json:"last_seen_at"`                                              // 最后访问时间
	FirstSeenAt time.Time `json:"first_seen_at"`                                             // 首次访问时间
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	// 非数据库字段
	IsBlocked   bool      `gorm:"-" json:"is_blocked"`                                       // 是否已被拦截
	BlockRuleID uint      `gorm:"-" json:"block_rule_id"`                                    // 拦截规则ID（用于删除）
}

// TableName 指定表名
func (UAStatsSummary) TableName() string {
	return "ua_stats_summaries"
}

// UAStatsBuffer 内存缓冲区项
type UAStatsBuffer struct {
	UAHash     string
	UserAgent  string
	Browser    string
	BrowserVer string
	OS         string
	OSVer      string
	Device     string
	DeviceName string
	SpiderName string
	SpiderKey  string  // 爬虫标识（用于拦截）
	IsSpider   bool
	Count      int64
	LastSeen   time.Time
	
	// 会话相关
	Sessions     map[string]*VisitorSession // key: sessionID
	PageViews    int
	ResourceHits int
}

// VisitorSession 访问者会话
type VisitorSession struct {
	SessionID    string              `json:"session_id"`    // IP+UA的MD5
	IP           string              `json:"ip"`
	UserAgent    string              `json:"user_agent"`
	FirstSeenAt  time.Time           `json:"first_seen_at"`
	LastSeenAt   time.Time           `json:"last_seen_at"`
	
	// 资源加载统计
	ResourceStats map[string]int     `json:"resource_stats"` // {"html": 1, "css": 3, "js": 5, "image": 10}
	TotalRequests int                `json:"total_requests"`
	PageViews     int                `json:"page_views"`     // HTML页面数
	
	// 访问者类型判断
	VisitorType  string              `json:"visitor_type"`   // real_user/crawler/bot/unknown
	Confidence   float32             `json:"confidence"`     // 置信度
}