package model

// MenuGroup 菜单分组
type MenuGroup struct {
	ID       string     `json:"id"`
	Title    string     `json:"title"`
	Icon     string     `json:"icon"`
	Order    int        `json:"order"`
	Items    []MenuItem `json:"items"`
	Expanded bool       `json:"expanded"`
}

// MenuItem 菜单项
type MenuItem struct {
	ID       string `json:"id"`
	Title    string `json:"title"`
	Icon     string `json:"icon"`
	URL      string `json:"url"`
	Active   string `json:"active"`
	Order    int    `json:"order"`
	Badge    string `json:"badge,omitempty"`
	Disabled bool   `json:"disabled"`
}

// GetMenuGroups 获取菜单分组配置
func GetMenuGroups() []MenuGroup {
	return []MenuGroup{
		{
			ID:       "dashboard",
			Title:    "仪表盘",
			Icon:     "fas fa-tachometer-alt",
			Order:    1,
			Expanded: true,
			Items: []MenuItem{
				{ID: "console", Title: "控制台", Icon: "fas fa-chart-line", URL: "/admin/", Active: "dashboard", Order: 1},
				{ID: "monitor", Title: "系统监控", Icon: "fas fa-desktop", URL: "/admin/monitor", Active: "monitor", Order: 2},
			},
		},
		{
			ID:       "site-management",
			Title:    "站点管理",
			Icon:     "fas fa-globe",
			Order:    2,
			Expanded: true,
			Items: []MenuItem{
				{ID: "sites", Title: "站点列表", Icon: "fas fa-sitemap", URL: "/admin/sites", Active: "sites", Order: 1},
				{ID: "categories", Title: "分类管理", Icon: "fas fa-folder-tree", URL: "/admin/site-categories", Active: "site-categories", Order: 2},
				{ID: "batch-replace", Title: "批量替换", Icon: "fas fa-search-replace", URL: "/admin/batch-replace", Active: "batch-replace", Order: 3},
			},
		},
		{
			ID:       "content-optimization",
			Title:    "内容优化",
			Icon:     "fas fa-edit",
			Order:    3,
			Expanded: true,
			Items: []MenuItem{
				{ID: "keywords", Title: "关键词管理", Icon: "fas fa-tags", URL: "/admin/keywords", Active: "keywords", Order: 1},
				{ID: "pseudo", Title: "伪原创管理", Icon: "fas fa-pen-fancy", URL: "/admin/pseudo", Active: "pseudo", Order: 2},
				{ID: "company", Title: "公司名称库", Icon: "fas fa-building", URL: "/admin/company-libraries", Active: "company-libraries", Order: 3},
			},
		},
		{
			ID:       "spider-management",
			Title:    "爬虫管理",
			Icon:     "fas fa-spider",
			Order:    4,
			Expanded: true,
			Items: []MenuItem{
				{ID: "spider-stats", Title: "爬虫统计", Icon: "fas fa-chart-bar", URL: "/admin/spider-stats", Active: "spider-stats", Order: 1},
				{ID: "spider-block", Title: "爬虫屏蔽", Icon: "fas fa-shield-alt", URL: "/admin/spider-block", Active: "spider-block", Order: 2},
				{ID: "ua-stats", Title: "UA统计", Icon: "fas fa-fingerprint", URL: "/admin/ua-stats", Active: "ua-stats", Order: 3},
			},
		},
		{
			ID:       "system-management",
			Title:    "系统管理",
			Icon:     "fas fa-cogs",
			Order:    5,
			Expanded: true,
			Items: []MenuItem{
				{ID: "cache", Title: "缓存管理", Icon: "fas fa-database", URL: "/admin/cache", Active: "cache", Order: 1},
				{ID: "admins", Title: "管理员管理", Icon: "fas fa-users-cog", URL: "/admin/admins", Active: "admins", Order: 2},
				{ID: "login-logs", Title: "登录日志", Icon: "fas fa-history", URL: "/admin/login-logs", Active: "login-logs", Order: 3},
				{ID: "settings", Title: "系统设置", Icon: "fas fa-sliders-h", URL: "/admin/settings", Active: "settings", Order: 4},
			},
		},
		{
			ID:       "performance",
			Title:    "性能优化",
			Icon:     "fas fa-rocket",
			Order:    6,
			Expanded: true,
			Items: []MenuItem{
				{ID: "weight-monitor", Title: "权重监控", Icon: "fas fa-balance-scale", URL: "/admin/weight-monitor", Active: "weight-monitor", Order: 1},
			},
		},
	}
}