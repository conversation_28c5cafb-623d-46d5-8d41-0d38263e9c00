package model

import (
	"time"
)

// CompanyLibrary 企业名称库
type CompanyLibrary struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"size:100;not null;uniqueIndex"` // 库名称
	Description string    `json:"description" gorm:"size:500"`                // 描述
	Type        string    `json:"type" gorm:"size:50;default:'general'"`     // 类型：general(通用), tech(科技), edu(教育), finance(金融)等
	IsActive    bool      `json:"is_active" gorm:"default:true"`              // 是否启用
	CompanyCount int      `json:"company_count" gorm:"-"`                     // 企业名称数量（非数据库字段）
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (CompanyLibrary) TableName() string {
	return "company_libraries"
}

// CompanyName 企业名称
type CompanyName struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	LibraryID uint      `json:"library_id" gorm:"index;not null"`           // 所属库ID
	Name      string    `json:"name" gorm:"size:255;not null"`              // 企业名称
	Industry  string    `json:"industry" gorm:"size:100"`                   // 行业
	Region    string    `json:"region" gorm:"size:100"`                     // 地区
	UseCount  int       `json:"use_count" gorm:"default:0"`                 // 使用次数
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	
	Library   *CompanyLibrary `json:"library,omitempty" gorm:"foreignKey:LibraryID"`
}

// TableName 指定表名
func (CompanyName) TableName() string {
	return "company_names"
}