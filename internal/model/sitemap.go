package model

import (
	"time"
)

// SitemapEntry sitemap条目
type SitemapEntry struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	SiteID     uint      `json:"site_id" gorm:"index"`
	Domain     string    `json:"domain" gorm:"index"`
	URL        string    `json:"url" gorm:"uniqueIndex"`
	Loc        string    `json:"loc"`                              // 完整URL
	LastMod    time.Time `json:"lastmod"`                          // 最后修改时间
	ChangeFreq string    `json:"changefreq" gorm:"default:'daily'"` // 更新频率
	Priority   float32   `json:"priority" gorm:"default:0.5"`      // 优先级
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SitemapEntry) TableName() string {
	return "sitemap_entries"
}