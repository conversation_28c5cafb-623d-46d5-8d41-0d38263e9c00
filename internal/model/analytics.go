package model

import (
	"time"
)

// Analytics 统计设置
type Analytics struct {
	ID              uint      `json:"id" gorm:"primaryKey"`
	Code            string    `json:"code" gorm:"type:text"`                    // 统计代码
	Enabled         bool      `json:"enabled"`                                  // 是否启用
	RefreshInterval int       `json:"refresh_interval" gorm:"default:60"`       // 刷新间隔(分钟)
	AutoRefresh     bool      `json:"auto_refresh" gorm:"default:false"`        // 是否启用自动刷新
	LastRefresh     time.Time `json:"last_refresh"`                             // 最后刷新时间
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}