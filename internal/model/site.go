package model

import (
	"time"
)

// Site 站点配置
type Site struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	Domain        string    `json:"domain" gorm:"unique;not null"` // 绑定的域名
	TargetURL     string    `json:"target_url"`                    // 目标站点URL
	CategoryID    *uint     `json:"category_id" gorm:"index"`      // 分类ID（可为空）
	Category      *SiteCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID;constraint:OnDelete:SET NULL"` // 分类信息
	CrawlDepth    int       `json:"crawl_depth"`                   // 爬取深度
	EnablePreload bool      `json:"enable_preload"`                // 是否预加载下一层
	CacheDir      string    `json:"cache_dir"`                     // 缓存目录
	Status        string    `json:"status"`                        // 状态: active, paused, error
	LastCrawlAt   time.Time `json:"last_crawl_at"`                 // 最后爬取时间
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	
	// 资源下载配置
	DownloadExternalResources bool `json:"download_external_resources"` // 是否下载外部资源
	
	// HTTPS检查配置
	EnableHTTPSCheck bool `json:"enable_https_check"` // 是否启用HTTPS检查
	
	// 简繁转换配置
	EnableTraditionalConvert bool `json:"enable_traditional_convert"` // 是否启用简繁转换
	
	// 缓存配置
	EnableCache bool `json:"enable_cache" gorm:"default:true"` // 是否启用缓存
	
	// UA判断配置
	UseGlobalUACheck *bool  `json:"use_global_ua_check" gorm:"default:null"` // 是否使用全局UA设置(null=使用全局,true=使用全局,false=使用站点独立设置)
	EnableUACheck    bool   `json:"enable_ua_check"`                         // 站点独立的UA判断开关
	AllowedUA        string `json:"allowed_ua"`                              // 允许的UA关键词
	NonSpiderHTML    string `json:"non_spider_html" gorm:"type:text"`        // 非爬虫访问时返回的HTML
	SpiderUA         string `json:"spider_ua"`                               // 自定义爬虫UA规则
	
	// 注入配置
	InjectConfig  *InjectConfig `json:"inject_config" gorm:"foreignKey:SiteID"`
	// 路由规则
	RouteRules    []RouteRule   `json:"route_rules" gorm:"foreignKey:SiteID"`
	
	// 缓存配置
	UseGlobalCache   bool   `json:"use_global_cache"`   // 是否使用全局缓存设置
	CacheMaxSize     int64  `json:"cache_max_size"`     // 站点最大缓存大小（MB）
	CacheHomeTTL     int    `json:"cache_home_ttl"`     // 首页缓存时间（分钟）
	CacheOtherTTL    int    `json:"cache_other_ttl"`    // 其他页面缓存时间（分钟）
	EnableRedisCache bool   `json:"enable_redis_cache"` // 是否启用Redis缓存（仅首页）
	
	// 蜘蛛屏蔽配置
	EnableSpiderBlock bool   `json:"enable_spider_block" gorm:"column:enable_spider_block"` // 是否启用蜘蛛屏蔽（站点级别）
	UseGlobalSpiderUA bool   `json:"use_global_spider_ua" gorm:"column:use_global_spider_ua"` // 是否使用全局爬虫UA规则
	CustomSpiderUA    string `json:"custom_spider_ua" gorm:"column:custom_spider_ua"`    // 站点自定义爬虫UA规则
	SpiderBlock403Template string `json:"spider_block_403_template" gorm:"column:spider_block_403_template;type:text"` // 站点自定义403模板
	
	// 来源判断配置
	UseGlobalRefererCheck *bool  `json:"use_global_referer_check" gorm:"default:null"`   // 是否使用全局来源判断(null=使用全局,true=独立开启,false=独立关闭)
	EnableRefererCheck    bool   `json:"enable_referer_check"`                          // 站点独立的来源判断开关
	AllowedReferers       string `json:"allowed_referers" gorm:"type:text"`             // 允许的来源域名列表
	RefererBlockCode      int    `json:"referer_block_code" gorm:"default:403"`         // 拒绝状态码
	RefererBlockHTML      string `json:"referer_block_html" gorm:"type:text"`           // 拒绝时显示的HTML
	
	// Sitemap配置
	EnableSitemap             bool       `json:"enable_sitemap" gorm:"default:false"`               // 是否启用sitemap
	SitemapUpdateInterval     int        `json:"sitemap_update_interval" gorm:"default:60"`         // sitemap更新间隔(分钟)
	SitemapChangefreq         string     `json:"sitemap_changefreq" gorm:"default:'daily'"`         // 页面更新频率
	SitemapPriority           float32    `json:"sitemap_priority" gorm:"default:0.5"`               // 默认优先级
	SitemapMaxUrls            int        `json:"sitemap_max_urls" gorm:"default:50000"`             // 最大URL数量
	SitemapLastUpdate         time.Time  `json:"sitemap_last_update"`                               // 上次更新时间
	
	// 404统计
	Count404      int           `json:"count_404" gorm:"column:count404;default:0"`  // 404页面累计数量
	
	// 缓存状态
	CacheStatus   string        `json:"cache_status" gorm:"default:'pending'"`       // 缓存状态: pending(待获取), success(成功), failed(失败), updating(更新中)
	CacheError    string        `json:"cache_error" gorm:"type:text"`                // 缓存错误信息
	CacheUpdateAt time.Time     `json:"cache_update_at"`                             // 缓存状态更新时间
	
	// 域名跳转配置
	RedirectWWW   *bool         `json:"redirect_www" gorm:"default:null"`     // @ 跳转到 www (null=使用全局设置, true=开启, false=关闭)
	
	// 企业名称配置
	EnableCompanyName    bool   `json:"enable_company_name" gorm:"default:false"`    // 是否启用企业名称
	CompanyName          string `json:"company_name" gorm:"size:255"`                // 企业名称
	CompanyLibraryID     uint   `json:"company_library_id"`                          // 企业名称库ID
	
	// 统计设置配置  
	UseGlobalAnalytics   *bool  `json:"use_global_analytics" gorm:"default:null"`    // 是否使用全局统计设置(null=使用全局,false=禁用统计)
	EnableAnalytics      bool   `json:"enable_analytics" gorm:"default:false"`       // 站点独立的统计开关（暂时保留但前端不使用）
	
	// 非数据库字段
	CacheSize     int64         `json:"cache_size" gorm:"-"`     // 当前缓存大小（字节）
	SitemapCount  int           `json:"sitemap_count" gorm:"-"`  // Sitemap条目数量
	Aliases       []SiteAlias   `json:"aliases" gorm:"foreignKey:SiteID"` // 站点别名/子域名
}

// OrphanedData 孤立数据结构
type OrphanedData struct {
	Type        string `json:"type"`        // 数据类型
	ID          uint   `json:"id"`          // 记录ID
	SiteID      uint   `json:"site_id"`     // 关联的站点ID
	Domain      string `json:"domain"`      // 域名（如果能确定的话）
	Description string `json:"description"` // 描述信息
	CreatedAt   string `json:"created_at"`  // 创建时间
}

// InjectConfig 注入配置
type InjectConfig struct {
	ID               uint     `json:"id" gorm:"primaryKey"`
	SiteID           uint     `json:"site_id"`
	EnableKeyword    bool     `json:"enable_keyword"`    // 启用关键词注入
	EnableStructure  bool     `json:"enable_structure"`  // 启用结构注入
	EnablePseudo     bool     `json:"enable_pseudo"`     // 启用伪原创
	FilterExternalLinks bool  `json:"filter_external_links"` // 过滤外部链接
	Keywords         StringSlice `json:"keywords" gorm:"type:json"`           // 关键词列表
	// 按库ID分组的关键词
	KeywordsByLibrary map[uint][]string `json:"keywords_by_library" gorm:"-"` // 按库ID分组的关键词（运行时使用）
	TitleTemplate    string   `json:"title_template"`    // 标题模板
	MetaTemplate     string   `json:"meta_template"`     // Meta模板
	// 新增字段
	HomeTitle        string   `json:"home_title"`        // 首页标题
	HomeDescription  string   `json:"home_description"`  // 首页描述
	HomeKeywords     string   `json:"home_keywords"`     // 首页关键词
	EnableUnicode    bool     `json:"enable_unicode"`    // 是否启用Unicode转码(兼容旧版)
	
	// Unicode转码细粒度控制
	UnicodeScope            string    `json:"unicode_scope"`               // 应用范围: homepage(仅首页), all(全站)
	EnableUnicodeTitle      bool      `json:"enable_unicode_title"`        // 启用标题Unicode转码
	EnableUnicodeKeywords   bool      `json:"enable_unicode_keywords"`     // 启用关键词Unicode转码
	EnableUnicodeDesc       bool      `json:"enable_unicode_desc"`         // 启用描述Unicode转码
	// 随机字符串注入
	EnableRandomString bool    `json:"enable_random_string"` // 是否启用随机字符串
	RandomStringLength int     `json:"random_string_length"` // 随机字符串长度
	RandomStringPrefix string  `json:"random_string_prefix"` // 随机字符串前缀（用于识别）
	// 拼音转换
	UseGlobalPinyin   *bool    `json:"use_global_pinyin"`   // 拼音设置模式(null=使用全局,true=独立开启,false=独立关闭)
	EnablePinyin      bool     `json:"enable_pinyin"`       // 是否启用拼音转换(仅当UseGlobalPinyin为true时生效)
	// 拼音特殊字符注入
	EnablePinyinSpecialChars bool    `json:"enable_pinyin_special_chars"` // 是否在拼音后插入特殊字符
	PinyinSpecialCharsRatio  float32 `json:"pinyin_special_chars_ratio"`  // 插入比例 (0-1)
	PinyinSpecialChars       string  `json:"pinyin_special_chars"`        // 特殊字符列表，用|分隔
	
	// 关键词注入详细配置
	KeywordInjectTitle      bool    `json:"keyword_inject_title"`      // 注入到标题
	KeywordInjectMeta       bool    `json:"keyword_inject_meta"`       // 注入到Meta标签
	KeywordInjectDesc       bool    `json:"keyword_inject_desc"`       // 注入到描述
	KeywordInjectBody       bool    `json:"keyword_inject_body"`       // 注入到正文
	KeywordInjectHidden     bool    `json:"keyword_inject_hidden"`     // 注入隐藏元素
	KeywordInjectH1         bool    `json:"keyword_inject_h1"`         // 注入到H1标签
	KeywordInjectH2         bool    `json:"keyword_inject_h2"`         // 注入到H2标签
	KeywordInjectAlt        bool    `json:"keyword_inject_alt"`        // 注入到图片Alt属性
	KeywordMaxPerPage       int     `json:"keyword_max_per_page"`      // 每页最大注入数量
	KeywordInjectRatio      float32 `json:"keyword_inject_ratio"`      // 注入比例(0-1)
	KeywordMinWordCount     int     `json:"keyword_min_word_count"`    // 最小段落字数(少于此数不注入)
	KeywordDensity          float32 `json:"keyword_density"`           // 关键词密度控制(百分比)
	// 关键词模板配置
	KeywordTitleTemplate    string  `json:"keyword_title_template"`     // 标题模板 如: {keyword1}-{keyword2}-{original}
	KeywordMetaTemplate     string  `json:"keyword_meta_template"`      // 关键词模板 如: {keyword1},{keyword2},{keyword3}
	KeywordDescTemplate     string  `json:"keyword_desc_template"`      // 描述模板 如: {keyword1}是{keyword2}的专业服务
	// 隐藏HTML注入
	EnableHiddenHTML   bool     `json:"enable_hidden_html"`    // 是否启用隐藏HTML注入
	HiddenHTMLLength   int      `json:"hidden_html_length"`    // 隐藏HTML长度（元素数量）
	HiddenHTMLRandomID bool     `json:"hidden_html_random_id"` // 是否使用随机ID
	HiddenHTMLPosition string   `json:"hidden_html_position"`  // 注入位置: both, top, bottom
	
	// H1标签注入（仅首页）
	EnableH1Tag       bool     `json:"enable_h1_tag"`        // 是否启用H1标签注入（使用home_title作为内容）
	H1TagPosition     string   `json:"h1_tag_position"`      // 注入位置: both, top, bottom
	
	// 统计代码（保留但前端不使用，使用全局统计代码）
	AnalyticsCode     string   `json:"analytics_code" gorm:"type:text"` // 统计代码（如Google Analytics、百度统计等）
	// 伪原创词库
	PseudoLibraryIDs UintSlice `json:"pseudo_library_ids" gorm:"type:json"` // 选中的伪原创词库ID列表
	// 结构注入配置
	StructureLibraryIDs UintSlice `json:"structure_library_ids" gorm:"type:json"` // 结构注入使用的关键词库ID列表
	StructureMinPerPage int      `json:"structure_min_per_page"`   // 每页最少注入数量
	StructureMaxPerPage int      `json:"structure_max_per_page"`   // 每页最多注入数量
	// 关键词库 - 支持不同位置使用不同的关键词库
	KeywordLibraryIDs UintSlice `json:"keyword_library_ids" gorm:"type:json"` // 选中的关键词库ID列表（兼容旧版）
	TitleKeywordLibraryIDs UintSlice `json:"title_keyword_library_ids" gorm:"type:json"` // 标题使用的关键词库ID列表
	MetaKeywordLibraryIDs UintSlice `json:"meta_keyword_library_ids" gorm:"type:json"` // 关键词Meta使用的关键词库ID列表  
	DescKeywordLibraryIDs UintSlice `json:"desc_keyword_library_ids" gorm:"type:json"` // 描述使用的关键词库ID列表
	BodyKeywordLibraryIDs UintSlice `json:"body_keyword_library_ids" gorm:"type:json"` // 正文使用的关键词库ID列表
	
	// 首页关键词注入
	EnableHomeKeywordInject     bool      `json:"enable_home_keyword_inject"`      // 启用首页关键词注入
	HomeKeywordLibraryIDs       UintSlice `json:"home_keyword_library_ids" gorm:"type:json"` // 首页关键词库ID列表
	HomeKeywordInjectCount      int       `json:"home_keyword_inject_count"`       // 注入数量
	EnableHomeKeywordUnicode    bool      `json:"enable_home_keyword_unicode"`     // 是否对注入的关键词进行Unicode转码
	
	InjectRules      []InjectRule `json:"inject_rules" gorm:"foreignKey:ConfigID"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// InjectRule 注入规则
type InjectRule struct {
	ID         uint   `json:"id" gorm:"primaryKey"`
	ConfigID   uint   `json:"config_id"`
	Type       string `json:"type"`       // keyword, structure, pseudo
	Target     string `json:"target"`     // 目标位置: title, meta, body, div
	Content    string `json:"content"`    // 注入内容
	Position   string `json:"position"`   // 位置: before, after, replace
	Probability float32 `json:"probability"` // 注入概率
	CreatedAt  time.Time `json:"created_at"`
}

// RouteRule 路由规则
type RouteRule struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	SiteID    uint   `json:"site_id"`
	Path      string `json:"path"`      // 路径匹配规则
	Action    string `json:"action"`    // 动作: mirror, block, redirect
	Target    string `json:"target"`    // 重定向目标
	Priority  int    `json:"priority"`  // 优先级
	CreatedAt time.Time `json:"created_at"`
}

// CachedContent 缓存内容
type CachedContent struct {
	URL         string            `json:"url"`
	ContentType string            `json:"content_type"`
	Data        []byte            `json:"data"`
	Headers     map[string]string `json:"headers"`
	CachedAt    time.Time        `json:"cached_at"`
	ExpiresAt   time.Time        `json:"expires_at"`
}

// RoutingRule 路由规则（与RouteRule相同，为兼容性保留）
type RoutingRule = RouteRule