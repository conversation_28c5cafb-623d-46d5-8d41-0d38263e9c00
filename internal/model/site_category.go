package model

import (
	"time"
)

// SiteCategory 站点分类
type SiteCategory struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Name        string    `gorm:"type:varchar(100);not null;uniqueIndex" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	Icon        string    `gorm:"type:varchar(50)" json:"icon"`        // 图标类名
	Color       string    `gorm:"type:varchar(50)" json:"color"`       // 颜色
	Sort        int       `gorm:"default:0" json:"sort"`               // 排序
	SiteCount   int       `gorm:"-" json:"site_count"`                 // 站点数量（非数据库字段）
	
	// 分类独立统计配置
	UseIndependentAnalytics bool   `json:"use_independent_analytics" gorm:"default:false"` // 是否使用独立统计
	AnalyticsCode          string  `json:"analytics_code" gorm:"type:text"`               // 分类独立的统计代码
	
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func (SiteCategory) TableName() string {
	return "site_categories"
}