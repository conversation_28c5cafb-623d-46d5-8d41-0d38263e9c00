package model

import (
	"time"
	"gorm.io/gorm"
)

// SpiderStats 爬虫统计数据
type SpiderStats struct {
	ID         uint      `gorm:"primarykey" json:"id"`
	SiteID     uint      `gorm:"index:idx_spider_stats_site,priority:1" json:"site_id"`          // 站点ID
	Domain     string    `gorm:"index:idx_spider_stats,priority:1;not null" json:"domain"`
	SpiderName string    `gorm:"index:idx_spider_stats,priority:2;not null" json:"spider_name"` // 爬虫名称: google, baidu, bing等
	Count      int64     `gorm:"default:0" json:"count"`                                         // 访问次数
	Date       time.Time `gorm:"type:date;index:idx_spider_stats,priority:3" json:"date"`        // 统计日期
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// SpiderStatsMinute 爬虫分钟级统计（全局统计，用于实时展示）
type SpiderStatsMinute struct {
	ID         uint      `gorm:"primarykey" json:"id"`
	SpiderName string    `gorm:"index:idx_spider_minute,priority:1;not null" json:"spider_name"`
	Count      int64     `gorm:"default:0" json:"count"`
	Timestamp  time.Time `gorm:"index:idx_spider_minute,priority:2" json:"timestamp"` // 精确到分钟
	CreatedAt  time.Time `json:"created_at"`
}

// SpiderStatsHour 爬虫小时级统计（按站点统计）
type SpiderStatsHour struct {
	ID         uint      `gorm:"primarykey" json:"id"`
	SiteID     uint      `gorm:"index:idx_spider_hour_site,priority:1" json:"site_id"`          // 站点ID
	Domain     string    `gorm:"index:idx_spider_hour,priority:1;not null" json:"domain"`
	SpiderName string    `gorm:"index:idx_spider_hour,priority:2;not null" json:"spider_name"`
	Count      int64     `gorm:"default:0" json:"count"`
	Timestamp  time.Time `gorm:"index:idx_spider_hour,priority:3" json:"timestamp"` // 精确到小时
	CreatedAt  time.Time `json:"created_at"`
}

// SpiderConfig 爬虫配置
type SpiderConfig struct {
	ID          uint      `gorm:"primarykey" json:"id"`
	Name        string    `gorm:"unique;not null" json:"name"`        // 爬虫标识: google, baidu, bing
	DisplayName string    `json:"display_name"`                       // 显示名称: 谷歌, 百度, 必应
	UserAgent   string    `gorm:"type:text" json:"user_agent"`        // User-Agent特征
	Enabled     bool      `gorm:"default:true" json:"enabled"`        // 是否启用统计
	Priority    int       `gorm:"default:0" json:"priority"`          // 显示优先级
	Color       string    `gorm:"default:'#1890ff'" json:"color"`     // 图表颜色
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// 自动迁移时清理旧的分钟级数据
func (SpiderStatsMinute) AfterMigrate(tx *gorm.DB) error {
	// 删除1小时前的分钟级数据（只保留最近1小时用于实时展示）
	result := tx.Where("timestamp < ?", time.Now().Add(-1*time.Hour)).Delete(&SpiderStatsMinute{})
	return result.Error
}

// 自动迁移时清理旧的小时级数据
func (SpiderStatsHour) AfterMigrate(tx *gorm.DB) error {
	// 删除7天前的小时级数据
	result := tx.Where("timestamp < ?", time.Now().AddDate(0, 0, -7)).Delete(&SpiderStatsHour{})
	return result.Error
}

// CleanOldSpiderStats 清理旧的爬虫统计数据（建议定时任务调用）
func CleanOldSpiderStats(db *gorm.DB, retentionDays int) error {
	// 删除超过指定天数的日统计数据
	cutoffDate := time.Now().AddDate(0, 0, -retentionDays)
	result := db.Where("date < ?", cutoffDate).Delete(&SpiderStats{})
	return result.Error
}

// GetSpiderStatsResponse 爬虫统计响应
type GetSpiderStatsResponse struct {
	Charts      *SpiderChartData            `json:"charts"`       // 图表数据
	DomainStats []*DomainSpiderStats        `json:"domain_stats"` // 域名统计
	Summary     map[string]*SpiderSummary   `json:"summary"`      // 汇总数据
	Pagination  *Pagination                 `json:"pagination"`   // 分页信息
}

// SpiderChartData 图表数据
type SpiderChartData struct {
	Labels  []string              `json:"labels"`  // 时间标签
	Datasets []*ChartDataset      `json:"datasets"` // 数据集
}

// ChartDataset 图表数据集
type ChartDataset struct {
	Label           string    `json:"label"`
	Data            []int64   `json:"data"`
	BorderColor     string    `json:"borderColor"`
	BackgroundColor string    `json:"backgroundColor"`
	Fill            bool      `json:"fill"`
}

// DomainSpiderStats 域名爬虫统计
type DomainSpiderStats struct {
	Domain  string                     `json:"domain"`
	Stats   map[string]*PeriodStats    `json:"stats"` // key: spider_name
}

// PeriodStats 时段统计
type PeriodStats struct {
	Today     int64 `json:"today"`
	Yesterday int64 `json:"yesterday"`
	FiveDays  int64 `json:"five_days"`
}

// SpiderSummary 爬虫汇总
type SpiderSummary struct {
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Total       int64  `json:"total"`
	Percentage  float64 `json:"percentage"`
}

// Pagination 分页信息
type Pagination struct {
	Page      int   `json:"page"`
	PageSize  int   `json:"page_size"`
	Total     int64 `json:"total"`
	TotalPage int   `json:"total_page"`
}