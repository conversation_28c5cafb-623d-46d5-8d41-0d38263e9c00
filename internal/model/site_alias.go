package model

import (
	"time"
)

// SiteAlias 站点别名/子域名映射
type SiteAlias struct {
	ID          int       `json:"id" gorm:"primaryKey"`
	SiteID      int       `json:"site_id" gorm:"not null;index"`
	AliasDomain string    `json:"alias_domain" gorm:"type:varchar(255);not null;uniqueIndex"`
	IsActive    bool      `json:"is_active" gorm:"default:true;index"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SiteAlias) TableName() string {
	return "site_aliases"
}