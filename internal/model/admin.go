package model

import (
	"time"
)

// Admin 管理员用户
type Admin struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Username  string    `json:"username" gorm:"unique;not null"`
	Password  string    `json:"-" gorm:"not null"` // 密码哈希，不返回给前端
	Nickname  string    `json:"nickname"`
	Email     string    `json:"email"`
	LastLogin time.Time `json:"last_login"`
	Status    string    `json:"status"` // active, disabled
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Session 登录会话
type Session struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	AdminID   uint      `json:"admin_id"`
	Token     string    `json:"token" gorm:"unique;not null"`
	ExpiredAt time.Time `json:"expired_at"`
	CreatedAt time.Time `json:"created_at"`
}