package database

import (
	"fmt"
	"os"
	"time"
	"site-cluster/internal/model"
	
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"
)

// InitOptimizedDB 初始化优化的数据库连接
func InitOptimizedDB() (*gorm.DB, error) {
	
	// 获取环境变量，设置默认值
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "sitecluster")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "sitecluster")
	
	
	// PostgreSQL连接
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Shanghai",
		dbHost,
		dbUser,
		dbPassword,
		dbName,
		dbPort,
	)
	
	
	// 配置数据库连接
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 生产模式：关闭SQL日志
		PrepareStmt: true, // 预编译语句
		CreateBatchSize: 100, // 批量创建大小
	})
	
	if err != nil {
		return nil, fmt.Errorf("连接PostgreSQL失败: %w", err)
	}
	
	
	// 先尝试从系统设置中读取配置
	var settings model.SystemSettings
	db.First(&settings)
	
	
	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	
	// 使用系统设置中的配置，如果为0则使用默认值
	maxOpenConns := 50   // 恢复连接数，避免连接不足
	maxIdleConns := 25   // 恢复空闲连接数
	connMaxLifetime := 300 // 恢复为5分钟
	
	if settings.DBMaxOpenConns > 0 {
		maxOpenConns = settings.DBMaxOpenConns
	}
	if settings.DBMaxIdleConns > 0 {
		maxIdleConns = settings.DBMaxIdleConns
	}
	if settings.DBConnMaxLifetime > 0 {
		connMaxLifetime = settings.DBConnMaxLifetime
	}
	
	// 设置最大连接数
	sqlDB.SetMaxOpenConns(maxOpenConns)
	// 设置最大空闲连接数
	sqlDB.SetMaxIdleConns(maxIdleConns)
	// 设置连接最大生命周期
	sqlDB.SetConnMaxLifetime(time.Duration(connMaxLifetime) * time.Second)
	// 设置连接最大空闲时间
	sqlDB.SetConnMaxIdleTime(5 * time.Minute)
	
	// 配置读写分离（如果有从库）
	if os.Getenv("DB_READ_HOST") != "" {
		readDSN := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Shanghai",
			os.Getenv("DB_READ_HOST"),
			dbUser,
			dbPassword,
			dbName,
			dbPort,
		)
		
		// 使用系统设置中的从库配置
		slaveMaxOpenConns := 10  // 大幅降低从库连接数
		slaveMaxIdleConns := 5   // 降低从库空闲连接数
		
		if settings.DBSlaveMaxOpenConns > 0 {
			slaveMaxOpenConns = settings.DBSlaveMaxOpenConns
		}
		if settings.DBSlaveMaxIdleConns > 0 {
			slaveMaxIdleConns = settings.DBSlaveMaxIdleConns
		}
		
		db.Use(dbresolver.Register(dbresolver.Config{
			Sources:  []gorm.Dialector{postgres.Open(dsn)},  // 写库
			Replicas: []gorm.Dialector{postgres.Open(readDSN)}, // 读库
			Policy:   dbresolver.RandomPolicy{}, // 随机选择从库
		}).
		SetMaxOpenConns(slaveMaxOpenConns).
		SetMaxIdleConns(slaveMaxIdleConns).
		SetConnMaxLifetime(time.Duration(connMaxLifetime) * time.Second))
	}
	
	
	// 自动迁移表结构
	err = db.AutoMigrate(
		&model.SiteCategory{},
		&model.Site{},
		&model.InjectConfig{},
		&model.InjectRule{},
		&model.RouteRule{},
		&model.CrawlJob{},
		&model.TaskLog{},
		&model.Keyword{},
		&model.KeywordLibrary{},
		&model.SitemapEntry{},
		&model.Admin{},
		&model.Session{},
		&model.PseudoLibrary{},
		&model.PseudoWord{},
		&model.Analytics{},
		&model.SpiderBlock{},
		&model.SpiderStats{},
		&model.SpiderStatsMinute{},
		&model.SpiderConfig{},
		&model.SpiderBlockHourlyStats{},
		&model.SpiderBlockDailyStats{},
		&model.CompanyLibrary{},
		&model.CompanyName{},
		&model.SystemSettings{},
		&model.WeightMonitorConfig{},
		&model.WeightHistory{},
		&model.LoginLog{},
		&model.SiteAlias{}, // 添加站点别名表
		&model.UAStatsSummary{}, // 添加UA统计表
	)
	if err != nil {
		return nil, err
	}
	
	
	// 创建索引（如果不存在）
	createIndexes(db)
	
	
	// 修复 JSON 数组字段格式
	if err := MigrateJSONArrayFields(db); err != nil {
		// 不中断启动，只记录错误
		// fmt.Printf("修复 JSON 数组字段失败: %v\n", err)
	}
	
	
	// 添加新字段（如果不存在）
	if err := AddNewColumns(db); err != nil {
		// 不中断启动，只记录错误
		// fmt.Printf("添加新字段失败: %v\n", err)
	}
	
	
	// 初始化默认数据
	InitDefaultSystemSettings(db)
	
	InitDefaultAdmin(db)
	
	// InitDefaultSpiderConfigs已删除 - 不再自动创建爬虫配置
	// 所有爬虫配置需要通过管理后台手动添加
	
	// InitDefaultSpiderBlockRules函数已删除 - 不再自动创建爬虫屏蔽规则
	// 重要: spider_blocks表的所有数据必须通过管理后台手动添加
	// 系统不会自动创建任何拦截规则
	
	InitDefaultCompanyLibraries(db)
	
	// 添加缓存状态字段迁移
	MigrateCacheStatusFields(db)
	
	// 添加站点别名表迁移
	MigrateSiteAliases(db)
	
	// 添加UA统计表迁移
	MigrateUAStats(db)
	
	return db, nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// createIndexes 创建优化索引
func createIndexes(db *gorm.DB) {
	// 复合索引优化查询
	db.Exec("CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sites_status_domain ON sites(status, domain)")
	db.Exec("CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_crawl_jobs_site_status_created ON crawl_jobs(site_id, status, created_at DESC)")
	db.Exec("CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_task_logs_task_timestamp ON task_logs(task_id, timestamp DESC)")
	
	// 部分索引减少索引大小
	db.Exec("CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sites_active ON sites(id) WHERE status = 'active'")
	db.Exec("CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_crawl_jobs_pending ON crawl_jobs(created_at) WHERE status = 'pending'")
	
	// 爬虫统计索引
	db.Exec("CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_stats_domain_spider_date ON spider_stats(domain, spider_name, date DESC)")
	db.Exec("CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_spider_minute_spider_time ON spider_stats_minutes(spider_name, timestamp DESC)")
}