package database

import (
	"fmt"
	"time"
	
	"site-cluster/internal/model"
	"site-cluster/internal/service"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// InitDefaultSystemSettings 初始化默认系统设置
func InitDefaultSystemSettings(db *gorm.DB) {
	var count int64
	db.Model(&model.SystemSettings{}).Count(&count)
	
	if count == 0 {
		settings := &model.SystemSettings{
			DefaultAllowedUA:        "", // 留空则不进行UA判断
			DefaultNonSpiderHTML:    "<html><body><h1>访问受限</h1><p>请使用搜索引擎访问</p></body></html>",
			DefaultSpiderBlockUA:    "AhrefsBot\nSemrushBot\nMJ12bot\nDotBot\nSEOkicks",
			UserAgentList:           "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			DefaultCacheHomeTTL:     30,
			DefaultCacheOtherTTL:    1440,
			DefaultEnableRedisCache: false,
			CreatedAt:               time.Now(),
			UpdatedAt:               time.Now(),
		}
		
		if err := db.Create(settings).Error; err != nil {
			fmt.Printf("创建默认系统设置失败: %v\n", err)
		} else {
			fmt.Println("默认系统设置创建成功")
		}
	}
}

// InitDefaultAdmin 初始化默认管理员账号
func InitDefaultAdmin(db *gorm.DB) {
	var count int64
	db.Model(&model.Admin{}).Count(&count)
	
	if count == 0 {
		// 生成密码哈希
		password := "admin123"
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			fmt.Printf("生成管理员密码失败: %v\n", err)
			return
		}
		
		admin := &model.Admin{
			Username:  "admin",
			Password:  string(hashedPassword),
			Nickname:  "管理员",
			Email:     "<EMAIL>",
			Status:    "active",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		
		if err := db.Create(admin).Error; err != nil {
			fmt.Printf("创建默认管理员失败: %v\n", err)
		} else {
			fmt.Println("默认管理员账号创建成功 (用户名: admin, 密码: admin123)")
		}
	}
}

// InitDefaultSpiderConfigs 已删除 - 不再自动创建爬虫配置
// 所有爬虫配置需要通过管理后台手动添加

// InitDefaultSpiderBlockRules 已删除 - 不再自动创建爬虫屏蔽规则
// 所有爬虫屏蔽规则需要通过管理后台手动添加

// InitDefaultCompanyLibraries 初始化默认企业名称库
func InitDefaultCompanyLibraries(db *gorm.DB) {
	var count int64
	db.Model(&model.CompanyLibrary{}).Count(&count)
	
	if count == 0 {
		companyService := service.NewCompanyLibraryService(db, nil)
		if err := companyService.CreateDefaultLibraries(); err != nil {
			fmt.Printf("初始化默认企业名称库失败: %v\n", err)
		} else {
			fmt.Println("初始化默认企业名称库成功")
		}
	}
}