package database

import (
	"fmt"
	"gorm.io/gorm"
)

// MigrateJSONArrayFields 修复 JSON 数组字段格式
func MigrateJSONArrayFields(db *gorm.DB) error {
	// 首先检查表是否存在
	var tableExists bool
	err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'inject_configs')").Scan(&tableExists).Error
	if err != nil {
		return fmt.Errorf("检查表存在失败: %v", err)
	}
	
	if !tableExists {
		// 表不存在，跳过迁移
		return nil
	}
	
	// 检查列是否存在并获取类型
	var columnType string
	err = db.Raw(`
		SELECT data_type 
		FROM information_schema.columns 
		WHERE table_name = 'inject_configs' 
		AND column_name = 'pseudo_library_ids'
	`).Scan(&columnType).Error
	
	if err != nil || columnType == "" {
		// 列不存在，跳过
		// fmt.Println("pseudo_library_ids 列不存在，跳过迁移")
		return nil
	}
	
	// 根据列类型执行不同的迁移策略
	if columnType == "json" || columnType == "jsonb" {
		// PostgreSQL JSON/JSONB 类型
		// 使用简单的方式，避免使用可能不存在的jsonb_typeof函数
		sql := `
			UPDATE inject_configs 
			SET pseudo_library_ids = '[]'::jsonb
			WHERE pseudo_library_ids IS NULL 
			   OR pseudo_library_ids::text = 'null'
			   OR pseudo_library_ids::text = '""'
			   OR pseudo_library_ids::text = '';
		`
		
		if err := db.Exec(sql).Error; err != nil {
			// 忽略错误，静默处理
			// // fmt.Printf("修复 pseudo_library_ids 失败（可忽略）: %v\n", err)
		}
		
		// 修复 keyword_library_ids 字段
		sql2 := `
			UPDATE inject_configs 
			SET keyword_library_ids = '[]'::jsonb
			WHERE keyword_library_ids IS NULL 
			   OR keyword_library_ids::text = 'null'
			   OR keyword_library_ids::text = '""'
			   OR keyword_library_ids::text = '';
		`
		
		if err := db.Exec(sql2).Error; err != nil {
			// 忽略错误，静默处理
			// // fmt.Printf("修复 keyword_library_ids 失败（可忽略）: %v\n", err)
		}
	} else {
		// 其他类型（如text），尝试更新为JSON格式
		sql := `
			UPDATE inject_configs 
			SET pseudo_library_ids = '[]'
			WHERE pseudo_library_ids IS NULL 
			   OR pseudo_library_ids = ''
			   OR pseudo_library_ids = 'null';
		`
		
		if err := db.Exec(sql).Error; err != nil {
			// 静默处理
		}
		
		sql2 := `
			UPDATE inject_configs 
			SET keyword_library_ids = '[]'
			WHERE keyword_library_ids IS NULL 
			   OR keyword_library_ids = ''
			   OR keyword_library_ids = 'null';
		`
		
		if err := db.Exec(sql2).Error; err != nil {
			// 静默处理
		}
	}
	
	return nil
}

// AddNewColumns 添加新的数据库字段
func AddNewColumns(db *gorm.DB) error {
	// 添加 enable_csrf 字段到 system_settings 表
	var csrfFieldExists bool
	err := db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'system_settings' 
			AND column_name = 'enable_csrf'
		)
	`).Scan(&csrfFieldExists).Error
	
	if err != nil {
		return fmt.Errorf("检查enable_csrf字段是否存在失败: %v", err)
	}
	
	// 如果字段不存在，则添加
	if !csrfFieldExists {
		sql := "ALTER TABLE system_settings ADD COLUMN enable_csrf BOOLEAN DEFAULT false"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 enable_csrf 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: system_settings.enable_csrf")
	} else {
		// fmt.Println("字段已存在: system_settings.enable_csrf")
	}
	
	// 添加 enable_global_ua_check 字段到 system_settings 表
	var columnExists bool
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'system_settings' 
			AND column_name = 'enable_global_ua_check'
		)
	`).Scan(&columnExists).Error
	
	if err != nil {
		return fmt.Errorf("检查字段是否存在失败: %v", err)
	}
	
	// 如果字段不存在，则添加
	if !columnExists {
		sql := "ALTER TABLE system_settings ADD COLUMN enable_global_ua_check BOOLEAN DEFAULT false"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 enable_global_ua_check 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: system_settings.enable_global_ua_check")
	} else {
		// fmt.Println("字段已存在: system_settings.enable_global_ua_check")
	}
	
	// 添加 count404 字段到 sites 表（使用count404而不是count_404）
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'sites' 
			AND column_name = 'count404'
		)
	`).Scan(&columnExists).Error
	
	if err != nil {
		return fmt.Errorf("检查count404字段是否存在失败: %v", err)
	}
	
	// 如果字段不存在，则添加
	if !columnExists {
		sql := "ALTER TABLE sites ADD COLUMN count404 INTEGER DEFAULT 0"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 count404 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: sites.count404")
	} else {
		// fmt.Println("字段已存在: sites.count404")
	}
	
	// 添加 redirect_www 字段到 sites 表
	var redirectWWWExists bool
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'sites' 
			AND column_name = 'redirect_www'
		)
	`).Scan(&redirectWWWExists).Error
	
	if err != nil {
		return fmt.Errorf("检查redirect_www字段是否存在失败: %v", err)
	}
	
	if !redirectWWWExists {
		sql := "ALTER TABLE sites ADD COLUMN redirect_www BOOLEAN DEFAULT NULL"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 redirect_www 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: sites.redirect_www")
	} else {
		// fmt.Println("字段已存在: sites.redirect_www")
	}
	
	// 添加 global_redirect_www 字段到 system_settings 表
	var globalRedirectWWWExists bool
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'system_settings' 
			AND column_name = 'global_redirect_www'
		)
	`).Scan(&globalRedirectWWWExists).Error
	
	if err != nil {
		return fmt.Errorf("检查global_redirect_www字段是否存在失败: %v", err)
	}
	
	if !globalRedirectWWWExists {
		sql := "ALTER TABLE system_settings ADD COLUMN global_redirect_www BOOLEAN DEFAULT FALSE"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 global_redirect_www 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: system_settings.global_redirect_www")
	} else {
		// fmt.Println("字段已存在: system_settings.global_redirect_www")
	}
	
	// 添加 enable_traditional_convert 字段到 sites 表
	var traditionalConvertExists bool
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'sites' 
			AND column_name = 'enable_traditional_convert'
		)
	`).Scan(&traditionalConvertExists).Error
	
	if err != nil {
		return fmt.Errorf("检查enable_traditional_convert字段是否存在失败: %v", err)
	}
	
	if !traditionalConvertExists {
		sql := "ALTER TABLE sites ADD COLUMN enable_traditional_convert BOOLEAN DEFAULT FALSE"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 enable_traditional_convert 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: sites.enable_traditional_convert")
	} else {
		// fmt.Println("字段已存在: sites.enable_traditional_convert")
	}
	
	// 添加 enable_https_check 字段到 sites 表
	var httpsCheckExists bool
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'sites' 
			AND column_name = 'enable_https_check'
		)
	`).Scan(&httpsCheckExists).Error
	
	if err != nil {
		return fmt.Errorf("检查enable_https_check字段是否存在失败: %v", err)
	}
	
	if !httpsCheckExists {
		sql := "ALTER TABLE sites ADD COLUMN enable_https_check BOOLEAN DEFAULT FALSE"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 enable_https_check 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: sites.enable_https_check")
	} else {
		// fmt.Println("字段已存在: sites.enable_https_check")
	}
	
	// 添加 spider_block_403_template 字段到 sites 表
	var spiderBlock403Exists bool
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'sites' 
			AND column_name = 'spider_block_403_template'
		)
	`).Scan(&spiderBlock403Exists).Error
	
	if err != nil {
		return fmt.Errorf("检查spider_block_403_template字段是否存在失败: %v", err)
	}
	
	if !spiderBlock403Exists {
		sql := "ALTER TABLE sites ADD COLUMN spider_block_403_template TEXT DEFAULT ''"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 spider_block_403_template 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: sites.spider_block_403_template")
	} else {
		// fmt.Println("字段已存在: sites.spider_block_403_template")
	}
	
	// 添加内容优化相关字段到 system_settings 表
	contentFields := []struct{
		name string
		sql string
	}{
		{"enable_global_pinyin", "ALTER TABLE system_settings ADD COLUMN enable_global_pinyin BOOLEAN DEFAULT FALSE"},
		{"enable_pinyin_special_chars", "ALTER TABLE system_settings ADD COLUMN enable_pinyin_special_chars BOOLEAN DEFAULT FALSE"},
		{"pinyin_special_chars_ratio", "ALTER TABLE system_settings ADD COLUMN pinyin_special_chars_ratio REAL DEFAULT 0.3"},
		{"pinyin_special_chars", "ALTER TABLE system_settings ADD COLUMN pinyin_special_chars TEXT DEFAULT '…|⁖|⸪|⸬|⸫'"},
		{"enable_global_keyword", "ALTER TABLE system_settings ADD COLUMN enable_global_keyword BOOLEAN DEFAULT FALSE"},
		{"keyword_max_per_page", "ALTER TABLE system_settings ADD COLUMN keyword_max_per_page INTEGER DEFAULT 10"},
		{"keyword_density", "ALTER TABLE system_settings ADD COLUMN keyword_density REAL DEFAULT 3.0"},
		{"enable_global_pseudo", "ALTER TABLE system_settings ADD COLUMN enable_global_pseudo BOOLEAN DEFAULT FALSE"},
		{"pseudo_strength", "ALTER TABLE system_settings ADD COLUMN pseudo_strength TEXT DEFAULT 'medium'"},
		{"enable_global_traditional", "ALTER TABLE system_settings ADD COLUMN enable_global_traditional BOOLEAN DEFAULT FALSE"},
		// 来源判断相关字段
		{"enable_global_referer_check", "ALTER TABLE system_settings ADD COLUMN enable_global_referer_check BOOLEAN DEFAULT FALSE"},
		{"global_allowed_referers", "ALTER TABLE system_settings ADD COLUMN global_allowed_referers TEXT DEFAULT ''"},
		{"global_referer_block_code", "ALTER TABLE system_settings ADD COLUMN global_referer_block_code INTEGER DEFAULT 403"},
		{"global_referer_block_html", "ALTER TABLE system_settings ADD COLUMN global_referer_block_html TEXT DEFAULT '<h1>403 Forbidden</h1><p>Access denied</p>'"},
	}
	
	for _, field := range contentFields {
		var exists bool
		err = db.Raw(`
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_name = 'system_settings' 
				AND column_name = ?
			)
		`, field.name).Scan(&exists).Error
		
		if err != nil {
			// fmt.Printf("检查字段%s是否存在失败（可忽略）: %v\n", field.name, err)
			continue
		}
		
		if !exists {
			if err := db.Exec(field.sql).Error; err != nil {
				// fmt.Printf("添加字段%s失败（可忽略）: %v\n", field.name, err)
			} else {
				// fmt.Printf("成功添加字段: system_settings.%s\n", field.name)
			}
		}
	}
	
	// 为sites表添加来源判断相关字段
	siteRefererFields := []struct{
		name string
		sql string
	}{
		{"use_global_referer_check", "ALTER TABLE sites ADD COLUMN use_global_referer_check BOOLEAN DEFAULT NULL"},
		{"enable_referer_check", "ALTER TABLE sites ADD COLUMN enable_referer_check BOOLEAN DEFAULT FALSE"},
		{"allowed_referers", "ALTER TABLE sites ADD COLUMN allowed_referers TEXT DEFAULT ''"},
		{"referer_block_code", "ALTER TABLE sites ADD COLUMN referer_block_code INTEGER DEFAULT 403"},
		{"referer_block_html", "ALTER TABLE sites ADD COLUMN referer_block_html TEXT DEFAULT '<h1>403 Forbidden</h1>'"},
	}
	
	for _, field := range siteRefererFields {
		var exists bool
		err = db.Raw(`
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_name = 'sites' 
				AND column_name = ?
			)
		`, field.name).Scan(&exists).Error
		
		if err != nil {
			// fmt.Printf("检查字段%s是否存在失败（可忽略）: %v\n", field.name, err)
			continue
		}
		
		if !exists {
			if err := db.Exec(field.sql).Error; err != nil {
				// fmt.Printf("添加字段%s失败（可忽略）: %v\n", field.name, err)
			} else {
				// fmt.Printf("成功添加字段: sites.%s\n", field.name)
			}
		}
	}
	
	// 添加工作池配置字段到system_settings表
	workerPoolFields := []struct {
		name string
		sql  string
	}{
		{"worker_pool_mode", "ALTER TABLE system_settings ADD COLUMN worker_pool_mode VARCHAR(20) DEFAULT 'auto'"},
		{"worker_pool_size", "ALTER TABLE system_settings ADD COLUMN worker_pool_size INTEGER DEFAULT 0"},
		{"worker_pool_min_size", "ALTER TABLE system_settings ADD COLUMN worker_pool_min_size INTEGER DEFAULT 100"},
		{"worker_pool_max_size", "ALTER TABLE system_settings ADD COLUMN worker_pool_max_size INTEGER DEFAULT 2000"},
		{"worker_pool_scale_ratio", "ALTER TABLE system_settings ADD COLUMN worker_pool_scale_ratio DECIMAL(3,2) DEFAULT 1.2"},
	}
	
	// 添加UA统计开关字段到system_settings表
	uaStatsFields := []struct {
		name string
		sql  string
	}{
		{"enable_ua_stats", "ALTER TABLE system_settings ADD COLUMN enable_ua_stats BOOLEAN DEFAULT FALSE"},
	}
	
	for _, field := range workerPoolFields {
		var exists bool
		err = db.Raw(`
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_name = 'system_settings' 
				AND column_name = ?
			)
		`, field.name).Scan(&exists).Error
		
		if err != nil {
			// fmt.Printf("检查字段%s是否存在失败（可忽略）: %v\n", field.name, err)
			continue
		}
		
		if !exists {
			if err := db.Exec(field.sql).Error; err != nil {
				// fmt.Printf("添加字段%s失败（可忽略）: %v\n", field.name, err)
			} else {
				// fmt.Printf("成功添加字段: system_settings.%s\n", field.name)
			}
		}
	}
	
	// 添加UA统计字段
	for _, field := range uaStatsFields {
		var exists bool
		err = db.Raw(`
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_name = 'system_settings' 
				AND column_name = ?
			)
		`, field.name).Scan(&exists).Error
		
		if err != nil {
			// fmt.Printf("检查字段%s是否存在失败（可忽略）: %v\n", field.name, err)
			continue
		}
		
		if !exists {
			if err := db.Exec(field.sql).Error; err != nil {
				// fmt.Printf("添加字段%s失败（可忽略）: %v\n", field.name, err)
			} else {
				// fmt.Printf("成功添加字段: system_settings.%s\n", field.name)
			}
		}
	}
	
	// 删除错误添加到sites表的首页关键词字段
	wrongSiteFields := []string{
		"enable_home_keyword_inject",
		"home_keyword_library_ids", 
		"home_keyword_inject_count",
		"enable_home_keyword_unicode",
	}
	
	for _, fieldName := range wrongSiteFields {
		var exists bool
		err = db.Raw(`
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_name = 'sites' 
				AND column_name = ?
			)
		`, fieldName).Scan(&exists).Error
		
		if err != nil {
			// fmt.Printf("检查sites表字段%s是否存在失败（可忽略）: %v\n", fieldName, err)
			continue
		}
		
		if exists {
			dropSQL := fmt.Sprintf("ALTER TABLE sites DROP COLUMN IF EXISTS %s", fieldName)
			if err := db.Exec(dropSQL).Error; err != nil {
				// fmt.Printf("删除sites表错误字段%s失败（可忽略）: %v\n", fieldName, err)
			} else {
				// fmt.Printf("成功删除sites表错误字段: %s\n", fieldName)
			}
		}
	}
	
	// 添加首页关键词注入配置字段到inject_configs表
	homeKeywordFields := []struct {
		name string
		sql  string
	}{
		{"enable_home_keyword_inject", "ALTER TABLE inject_configs ADD COLUMN enable_home_keyword_inject BOOLEAN DEFAULT FALSE"},
		{"home_keyword_library_ids", "ALTER TABLE inject_configs ADD COLUMN home_keyword_library_ids JSONB DEFAULT '[]'::jsonb"},
		{"home_keyword_inject_count", "ALTER TABLE inject_configs ADD COLUMN home_keyword_inject_count INTEGER DEFAULT 5"},
		{"enable_home_keyword_unicode", "ALTER TABLE inject_configs ADD COLUMN enable_home_keyword_unicode BOOLEAN DEFAULT FALSE"},
	}
	
	for _, field := range homeKeywordFields {
		var exists bool
		err = db.Raw(`
			SELECT EXISTS (
				SELECT 1 
				FROM information_schema.columns 
				WHERE table_name = 'inject_configs' 
				AND column_name = ?
			)
		`, field.name).Scan(&exists).Error
		
		if err != nil {
			// fmt.Printf("检查字段%s是否存在失败（可忽略）: %v\n", field.name, err)
			continue
		}
		
		if !exists {
			if err := db.Exec(field.sql).Error; err != nil {
				// fmt.Printf("添加字段%s失败（可忽略）: %v\n", field.name, err)
			} else {
				// fmt.Printf("成功添加字段: inject_configs.%s\n", field.name)
			}
		}
	}
	
	return nil
}

// MigrateCacheStatusFields 添加缓存状态相关字段
func MigrateCacheStatusFields(db *gorm.DB) error {
	// 检查cache_status字段是否存在
	var cacheStatusExists bool
	err := db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'sites' 
			AND column_name = 'cache_status'
		)
	`).Scan(&cacheStatusExists).Error
	
	if err != nil {
		return fmt.Errorf("检查cache_status字段是否存在失败: %v", err)
	}
	
	// 如果字段不存在，则添加
	if !cacheStatusExists {
		sql := "ALTER TABLE sites ADD COLUMN cache_status VARCHAR(20) DEFAULT 'pending'"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 cache_status 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: sites.cache_status")
	}
	
	// 检查cache_error字段是否存在
	var cacheErrorExists bool
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'sites' 
			AND column_name = 'cache_error'
		)
	`).Scan(&cacheErrorExists).Error
	
	if err != nil {
		return fmt.Errorf("检查cache_error字段是否存在失败: %v", err)
	}
	
	// 如果字段不存在，则添加
	if !cacheErrorExists {
		sql := "ALTER TABLE sites ADD COLUMN cache_error TEXT"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 cache_error 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: sites.cache_error")
	}
	
	// 检查cache_update_at字段是否存在
	var cacheUpdateAtExists bool
	err = db.Raw(`
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_name = 'sites' 
			AND column_name = 'cache_update_at'
		)
	`).Scan(&cacheUpdateAtExists).Error
	
	if err != nil {
		return fmt.Errorf("检查cache_update_at字段是否存在失败: %v", err)
	}
	
	// 如果字段不存在，则添加
	if !cacheUpdateAtExists {
		sql := "ALTER TABLE sites ADD COLUMN cache_update_at TIMESTAMP WITH TIME ZONE"
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("添加 cache_update_at 字段失败: %v", err)
		}
		// fmt.Println("成功添加字段: sites.cache_update_at")
	}
	
	// 为cache_status字段创建索引
	db.Exec("CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sites_cache_status ON sites(cache_status)")
	
	// 更新现有记录的cache_status为success（如果有缓存的话）
	db.Exec(`
		UPDATE sites 
		SET cache_status = 'success', cache_update_at = NOW() 
		WHERE cache_status IS NULL OR cache_status = ''
	`)
	
	return nil
}

// MigrateSiteAliases 创建站点别名表用于支持多子域名
func MigrateSiteAliases(db *gorm.DB) error {
	// 创建site_aliases表
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS site_aliases (
		id SERIAL PRIMARY KEY,
		site_id INTEGER NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
		alias_domain VARCHAR(255) NOT NULL,
		is_active BOOLEAN DEFAULT true,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		UNIQUE(alias_domain)
	);
	`
	
	if err := db.Exec(createTableSQL).Error; err != nil {
		return fmt.Errorf("创建site_aliases表失败: %v", err)
	}
	// fmt.Println("成功创建表: site_aliases")
	
	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_site_aliases_domain ON site_aliases(alias_domain)",
		"CREATE INDEX IF NOT EXISTS idx_site_aliases_site_id ON site_aliases(site_id)",
		"CREATE INDEX IF NOT EXISTS idx_site_aliases_active ON site_aliases(is_active)",
	}
	
	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			// fmt.Printf("创建索引失败（可忽略）: %v\n", err)
		}
	}
	
	return nil
}

// MigrateUAStats 创建UA统计表
func MigrateUAStats(db *gorm.DB) error {
	// 创建ua_stats_summaries表
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS ua_stats_summaries (
		id SERIAL PRIMARY KEY,
		ua_hash VARCHAR(32) NOT NULL,
		user_agent TEXT NOT NULL,
		browser VARCHAR(100),
		browser_ver VARCHAR(50),
		os VARCHAR(100),
		os_ver VARCHAR(50),
		device VARCHAR(100),
		device_name VARCHAR(100),
		spider_name VARCHAR(100),
		is_spider BOOLEAN DEFAULT FALSE,
		hit_count BIGINT DEFAULT 0,
		session_count INTEGER DEFAULT 0,
		page_views INTEGER DEFAULT 0,
		resource_hits INTEGER DEFAULT 0,
		visitor_type VARCHAR(50) DEFAULT 'unknown',
		confidence REAL DEFAULT 0,
		avg_resources REAL DEFAULT 0,
		last_ip VARCHAR(45),
		last_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		first_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		UNIQUE(ua_hash)
	);
	`
	
	if err := db.Exec(createTableSQL).Error; err != nil {
		return fmt.Errorf("创建ua_stats_summaries表失败: %v", err)
	}
	// fmt.Println("成功创建表: ua_stats_summaries")
	
	// 为现有表添加新字段
	alterColumns := []string{
		"ALTER TABLE ua_stats_summaries ADD COLUMN IF NOT EXISTS session_count INTEGER DEFAULT 0",
		"ALTER TABLE ua_stats_summaries ADD COLUMN IF NOT EXISTS page_views INTEGER DEFAULT 0",
		"ALTER TABLE ua_stats_summaries ADD COLUMN IF NOT EXISTS resource_hits INTEGER DEFAULT 0",
		"ALTER TABLE ua_stats_summaries ADD COLUMN IF NOT EXISTS visitor_type VARCHAR(50) DEFAULT 'unknown'",
		"ALTER TABLE ua_stats_summaries ADD COLUMN IF NOT EXISTS confidence REAL DEFAULT 0",
		"ALTER TABLE ua_stats_summaries ADD COLUMN IF NOT EXISTS avg_resources REAL DEFAULT 0",
		"ALTER TABLE ua_stats_summaries ADD COLUMN IF NOT EXISTS last_ip VARCHAR(45)",
		"ALTER TABLE ua_stats_summaries ADD COLUMN IF NOT EXISTS spider_key VARCHAR(100)",
	}
	
	for _, alterSQL := range alterColumns {
		if err := db.Exec(alterSQL).Error; err != nil {
			// fmt.Printf("添加列失败（可忽略）: %v\n", err)
		}
	}
	
	// 创建索引
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_ua_hash ON ua_stats_summaries(ua_hash)",
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_spider_name ON ua_stats_summaries(spider_name)",
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_is_spider ON ua_stats_summaries(is_spider)",
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_hit_count ON ua_stats_summaries(hit_count DESC)",
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_browser ON ua_stats_summaries(browser)",
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_os ON ua_stats_summaries(os)",
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_device ON ua_stats_summaries(device)",
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_visitor_type ON ua_stats_summaries(visitor_type)",
		"CREATE INDEX IF NOT EXISTS idx_ua_stats_summaries_session_count ON ua_stats_summaries(session_count DESC)",
	}
	
	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			// fmt.Printf("创建索引失败（可忽略）: %v\n", err)
		}
	}
	
	// 添加权重表索引优化
	if err := AddWeightHistoryIndexes(db); err != nil {
		// fmt.Printf("创建权重表索引失败（可忽略）: %v\n", err)
	}
	
	// 添加分类统计功能字段
	if err := AddCategoryAnalyticsFields(db); err != nil {
		// fmt.Printf("添加分类统计字段失败（可忽略）: %v\n", err)
	}
	
	return nil
}

// AddCategoryAnalyticsFields 添加分类统计功能字段
func AddCategoryAnalyticsFields(db *gorm.DB) error {
	// 检查site_categories表是否存在
	var tableExists bool
	err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'site_categories')").Scan(&tableExists).Error
	if err != nil || !tableExists {
		// 表不存在，跳过
		return nil
	}
	
	// 添加分类统计字段
	alterColumns := []string{
		"ALTER TABLE site_categories ADD COLUMN IF NOT EXISTS use_independent_analytics BOOLEAN DEFAULT FALSE",
		"ALTER TABLE site_categories ADD COLUMN IF NOT EXISTS analytics_code TEXT",
	}
	
	for _, alterSQL := range alterColumns {
		if err := db.Exec(alterSQL).Error; err != nil {
			// 静默处理错误，不影响启动
			// fmt.Printf("添加分类统计字段失败（可忽略）: %v\n", err)
		}
	}
	
	// 创建索引以优化查询
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_site_categories_use_analytics ON site_categories(use_independent_analytics)",
	}
	
	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			// 静默处理错误，不影响启动
			// fmt.Printf("创建分类统计索引失败（可忽略）: %v\n", err)
		}
	}
	
	return nil
}

// AddWeightHistoryIndexes 添加权重历史表的优化索引
func AddWeightHistoryIndexes(db *gorm.DB) error {
	// 检查weight_history表是否存在
	var tableExists bool
	err := db.Raw("SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'weight_history')").Scan(&tableExists).Error
	if err != nil || !tableExists {
		// 表不存在，跳过
		return nil
	}
	
	// 创建优化查询性能的索引
	indexes := []string{
		// 复合索引：用于DISTINCT ON (domain) ORDER BY domain, id DESC查询
		"CREATE INDEX IF NOT EXISTS idx_weight_history_domain_id ON weight_history(domain, id DESC)",
		
		// 部分索引：只索引有权重的记录，加速筛选
		"CREATE INDEX IF NOT EXISTS idx_weight_history_weights ON weight_history(pc_br, mobile_br) WHERE pc_br > 0 OR mobile_br > 0",
		
		// 单列索引：用于快速统计不同域名
		"CREATE INDEX IF NOT EXISTS idx_weight_history_domain ON weight_history(domain)",
		
		// 时间索引：用于按时间查询
		"CREATE INDEX IF NOT EXISTS idx_weight_history_created_at ON weight_history(created_at DESC)",
	}
	
	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			// 静默处理错误，不影响启动
			// fmt.Printf("创建权重表索引失败（可忽略）: %v\n", err)
		}
	}
	
	// 分析表以更新统计信息，优化查询计划
	db.Exec("ANALYZE weight_history")
	
	return nil
}