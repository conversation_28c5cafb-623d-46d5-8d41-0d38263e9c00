package scheduler

import (
	"context"
	"sync"
	"sync/atomic"
	"time"
	"go.uber.org/zap"
	"site-cluster/internal/interfaces"
)

// DynamicWorkerPool 动态工作池
type DynamicWorkerPool struct {
	logger       *zap.Logger
	minWorkers   int32
	maxWorkers   int32
	currentCount int32
	taskChan     chan Task
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
	
	// 服务依赖
	crawler      interfaces.Crawler
	siteService  interfaces.SiteService
	cacheService interfaces.CacheService
	
	// 性能指标
	processingTasks  int32
	completedTasks   int64
	failedTasks      int64
	avgResponseTime  atomic.Value  // 存储float64
	lastScaleTime    atomic.Value  // 存储time.Time
	
	// 并发控制
	scaleMu      sync.Mutex     // 保护伸缩操作
	stopping     int32          // 停止标志
	workerExitMu sync.Mutex     // 保护worker退出
}

// NewDynamicWorkerPool 创建动态工作池
func NewDynamicWorkerPool(logger *zap.Logger, minWorkers, maxWorkers int) *DynamicWorkerPool {
	ctx, cancel := context.WithCancel(context.Background())
	
	pool := &DynamicWorkerPool{
		logger:      logger,
		minWorkers:  int32(minWorkers),
		maxWorkers:  int32(maxWorkers),
		taskChan:    make(chan Task, maxWorkers*10), // 大缓冲
		ctx:         ctx,
		cancel:      cancel,
	}
	
	// 初始化原子值
	pool.avgResponseTime.Store(float64(0))
	pool.lastScaleTime.Store(time.Now())
	
	return pool
}

// SetCrawler 设置爬虫服务
func (p *DynamicWorkerPool) SetCrawler(crawler interfaces.Crawler) {
	p.crawler = crawler
}

// SetSiteService 设置站点服务
func (p *DynamicWorkerPool) SetSiteService(siteService interfaces.SiteService) {
	p.siteService = siteService
}

// SetCacheService 设置缓存服务
func (p *DynamicWorkerPool) SetCacheService(cacheService interfaces.CacheService) {
	p.cacheService = cacheService
}

// Start 启动工作池
func (p *DynamicWorkerPool) Start() {
	// 启动初始工作协程
	for i := 0; i < int(p.minWorkers); i++ {
		p.addWorker()
	}
	
	// 启动自动伸缩协程
	go p.autoScale()
	
	// 启动指标收集协程
	go p.collectMetrics()
	
	// p.logger.Info("动态工作池已启动",
	// 	zap.Int32("min_workers", p.minWorkers),
	// 	zap.Int32("max_workers", p.maxWorkers))
}

// Submit 提交任务
func (p *DynamicWorkerPool) Submit(task Task) error {
	// 检查是否正在停止
	if atomic.LoadInt32(&p.stopping) == 1 {
		return ErrPoolStopping
	}
	
	select {
	case p.taskChan <- task:
		return nil
	case <-p.ctx.Done():
		return context.Canceled
	default:
		// 队列满了，尝试增加worker
		if atomic.LoadInt32(&p.currentCount) < p.maxWorkers {
			p.addWorker()
			// 重试一次
			select {
			case p.taskChan <- task:
				return nil
			default:
				return ErrQueueFull
			}
		}
		return ErrQueueFull
	}
}

// addWorker 添加工作协程
func (p *DynamicWorkerPool) addWorker() {
	atomic.AddInt32(&p.currentCount, 1)
	p.wg.Add(1)
	
	go func() {
		defer p.wg.Done()
		defer atomic.AddInt32(&p.currentCount, -1)
		
		for {
			select {
			case task := <-p.taskChan:
				if task == nil {
					return
				}
				
				atomic.AddInt32(&p.processingTasks, 1)
				start := time.Now()
				
				err := p.executeTask(task)
				
				atomic.AddInt32(&p.processingTasks, -1)
				
				if err != nil {
					atomic.AddInt64(&p.failedTasks, 1)
				} else {
					atomic.AddInt64(&p.completedTasks, 1)
				}
				
				// 更新平均响应时间
				p.updateAvgResponseTime(time.Since(start).Seconds())
				
			case <-p.ctx.Done():
				return
			case <-time.After(30 * time.Second): // 空闲30秒
				// 检查是否可以缩容
				if p.shouldWorkerExit() {
					return // 退出该worker
				}
			}
		}
	}()
}

// executeTask 执行任务
func (p *DynamicWorkerPool) executeTask(task Task) error {
	defer func() {
		if r := recover(); r != nil {
			p.logger.Error("任务执行崩溃", zap.Any("panic", r))
		}
	}()
	
	ctx, cancel := context.WithTimeout(p.ctx, 30*time.Minute)
	defer cancel()
	
	return task.Execute(ctx)
}

// autoScale 自动伸缩
func (p *DynamicWorkerPool) autoScale() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			p.scale()
		case <-p.ctx.Done():
			return
		}
	}
}

// scale 执行伸缩
func (p *DynamicWorkerPool) scale() {
	// 使用互斥锁保护伸缩操作
	p.scaleMu.Lock()
	defer p.scaleMu.Unlock()
	
	// 避免频繁伸缩
	lastScale := p.lastScaleTime.Load().(time.Time)
	if time.Since(lastScale) < 30*time.Second {
		return
	}
	
	queueSize := len(p.taskChan)
	processingTasks := atomic.LoadInt32(&p.processingTasks)
	currentWorkers := atomic.LoadInt32(&p.currentCount)
	
	// 计算负载
	load := float64(queueSize+int(processingTasks)) / float64(currentWorkers)
	
	// 扩容条件：负载高于80%
	if load > 0.8 && currentWorkers < p.maxWorkers {
		// 增加20%的worker，至少增加1个
		newWorkers := int(float64(currentWorkers) * 0.2)
		if newWorkers < 1 {
			newWorkers = 1
		}
		
		for i := 0; i < newWorkers && atomic.LoadInt32(&p.currentCount) < p.maxWorkers; i++ {
			p.addWorker()
		}
		
		p.logger.Info("工作池扩容", 
			zap.Int32("current", atomic.LoadInt32(&p.currentCount)),
			zap.Float64("load", load))
		p.lastScaleTime.Store(time.Now())
	}
	
	// 缩容条件：负载低于20%
	if load < 0.2 && currentWorkers > p.minWorkers {
		// 通过不重新创建worker来自然缩容
		p.logger.Info("工作池开始缩容", 
			zap.Int32("current", currentWorkers),
			zap.Float64("load", load))
		p.lastScaleTime.Store(time.Now())
	}
}

// updateAvgResponseTime 更新平均响应时间
func (p *DynamicWorkerPool) updateAvgResponseTime(responseTime float64) {
	// 使用原子操作更新滑动平均
	for {
		oldAvg := p.avgResponseTime.Load().(float64)
		newAvg := oldAvg*0.9 + responseTime*0.1
		if p.avgResponseTime.CompareAndSwap(oldAvg, newAvg) {
			break
		}
	}
}

// collectMetrics 收集指标
func (p *DynamicWorkerPool) collectMetrics() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			p.logger.Info("工作池指标",
				zap.Int32("workers", atomic.LoadInt32(&p.currentCount)),
				zap.Int("queue_size", len(p.taskChan)),
				zap.Int32("processing", atomic.LoadInt32(&p.processingTasks)),
				zap.Int64("completed", atomic.LoadInt64(&p.completedTasks)),
				zap.Int64("failed", atomic.LoadInt64(&p.failedTasks)),
				zap.Float64("avg_response_time", p.avgResponseTime.Load().(float64)),
			)
		case <-p.ctx.Done():
			return
		}
	}
}

// GetMetrics 获取指标
func (p *DynamicWorkerPool) GetMetrics() map[string]interface{} {
	return map[string]interface{}{
		"workers":           atomic.LoadInt32(&p.currentCount),
		"queue_size":        len(p.taskChan),
		"processing_tasks":  atomic.LoadInt32(&p.processingTasks),
		"completed_tasks":   atomic.LoadInt64(&p.completedTasks),
		"failed_tasks":      atomic.LoadInt64(&p.failedTasks),
		"avg_response_time": p.avgResponseTime.Load().(float64),
	}
}

// shouldWorkerExit 判断是否应该退出worker
func (p *DynamicWorkerPool) shouldWorkerExit() bool {
	p.workerExitMu.Lock()
	defer p.workerExitMu.Unlock()
	
	current := atomic.LoadInt32(&p.currentCount)
	if current <= p.minWorkers {
		return false
	}
	
	// 使用CAS确保只有一个worker退出
	if atomic.CompareAndSwapInt32(&p.currentCount, current, current-1) {
		return true
	}
	return false
}

// Stop 停止工作池
func (p *DynamicWorkerPool) Stop() {
	// 1. 先设置停止标志，阻止新任务提交
	atomic.StoreInt32(&p.stopping, 1)
	
	// 2. 取消context，通知所有goroutine退出
	p.cancel()
	
	// 3. 等待所有goroutine结束（带超时）
	done := make(chan struct{})
	go func() {
		p.wg.Wait()
		close(done)
	}()
	
	// 超时5秒强制退出
	select {
	case <-done:
		// p.logger.Info("工作池已正常停止",
		// 	zap.Int32("workers", atomic.LoadInt32(&p.currentCount)))
		// 4. 所有worker已退出，安全关闭channel
		close(p.taskChan)
	case <-time.After(5 * time.Second):
		p.logger.Warn("工作池停止超时",
			zap.Int32("remaining_workers", atomic.LoadInt32(&p.currentCount)))
		// 超时也要关闭channel，但可能会有panic风险
		// 可以选择不关闭，让GC处理
	}
}

var (
	ErrQueueFull = &QueueFullError{}
	ErrPoolStopping = &PoolStoppingError{}
)

type QueueFullError struct{}

func (e *QueueFullError) Error() string {
	return "task queue is full"
}

type PoolStoppingError struct{}

func (e *PoolStoppingError) Error() string {
	return "worker pool is stopping"
}