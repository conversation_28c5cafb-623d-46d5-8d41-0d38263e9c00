package scheduler

import (
	"context"
	"sync"
	"time"

	"site-cluster/internal/injector"
	"site-cluster/internal/interfaces"
	"site-cluster/internal/model"

	"go.uber.org/zap"
)

// WorkerPool 工作池
type WorkerPool struct {
	logger          *zap.Logger
	workers         int
	taskChan        chan Task
	wg              sync.WaitGroup
	ctx             context.Context
	cancel          context.CancelFunc
	
	// 服务依赖
	crawler         interfaces.Crawler
	keywordInjector *injector.KeywordInjector
	structInjector  *injector.StructureInjector
	pseudoProcessor *injector.PseudoOriginalProcessor
	siteService     interfaces.SiteService
	cacheService    interfaces.CacheService
}

func NewWorkerPool(
	logger *zap.Logger,
	workers int,
	crawler interfaces.Crawler,
	siteService interfaces.SiteService,
	cacheService interfaces.CacheService,
) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &WorkerPool{
		logger:          logger,
		workers:         workers,
		taskChan:        make(chan Task, workers*2),
		ctx:             ctx,
		cancel:          cancel,
		crawler:         crawler,
		keywordInjector: injector.NewKeywordInjector(logger),
		structInjector:  injector.NewStructureInjector(logger),
		pseudoProcessor: injector.NewPseudoOriginalProcessor(logger),
		siteService:     siteService,
		cacheService:    cacheService,
	}
}

// Start 启动工作池
func (wp *WorkerPool) Start() {
	for i := 0; i < wp.workers; i++ {
		wp.wg.Add(1)
		go wp.worker(i)
	}
	// wp.logger.Info("工作池已启动", zap.Int("workers", wp.workers))
}

// Stop 停止工作池
func (wp *WorkerPool) Stop() {
	wp.cancel()
	close(wp.taskChan)
	wp.wg.Wait()
	// wp.logger.Info("工作池已停止")
}

// Submit 提交任务
func (wp *WorkerPool) Submit(task Task) error {
	select {
	case wp.taskChan <- task:
		return nil
	case <-wp.ctx.Done():
		return context.Canceled
	default:
		// 队列满了，直接执行
		go wp.executeTask(task)
		return nil
	}
}

// worker 工作协程
func (wp *WorkerPool) worker(id int) {
	defer wp.wg.Done()
	
	for {
		select {
		case task, ok := <-wp.taskChan:
			if !ok {
				return
			}
			wp.executeTask(task)
			
		case <-wp.ctx.Done():
			return
		}
	}
}

// executeTask 执行任务
func (wp *WorkerPool) executeTask(task Task) {
	defer func() {
		if r := recover(); r != nil {
			wp.logger.Error("任务执行崩溃",
				zap.String("task_id", task.GetID()),
				zap.Any("panic", r),
			)
		}
	}()
	
	startTime := time.Now()
	ctx, cancel := context.WithTimeout(wp.ctx, 30*time.Minute)
	defer cancel()
	
	// wp.logger.Debug("开始执行任务", zap.String("task_id", task.GetID()))
	
	err := task.Execute(ctx)
	
	duration := time.Since(startTime)
	
	if err != nil {
		wp.logger.Error("任务执行失败",
			zap.String("task_id", task.GetID()),
			zap.Duration("duration", duration),
			zap.Error(err),
		)
	} else {
		// wp.logger.Info("任务执行成功",
		//	zap.String("task_id", task.GetID()),
		//	zap.Duration("duration", duration),
		// )
	}
}

// ProcessCrawlTask 处理爬取任务
func (wp *WorkerPool) ProcessCrawlTask(ctx context.Context, job *model.CrawlJob) error {
	// 获取站点信息
	site, err := wp.siteService.GetSite(job.SiteID)
	if err != nil {
		return err
	}
	
	// 更新任务状态
	job.Status = "processing"
	now := time.Now()
	job.StartedAt = &now
	
	// 执行爬取
	err = wp.crawler.CrawlSite(site, job)
	
	if err != nil {
		job.Status = "failed"
	} else {
		job.Status = "completed"
	}
	
	completedTime := time.Now()
	job.CompletedAt = &completedTime
	
	// 更新站点最后爬取时间
	site.LastCrawlAt = time.Now()
	wp.siteService.UpdateSite(site)
	
	return err
}

// ProcessInjectTask 处理注入任务
func (wp *WorkerPool) ProcessInjectTask(ctx context.Context, siteID uint, pageURL string) error {
	// 获取站点和注入配置
	site, err := wp.siteService.GetSite(siteID)
	if err != nil {
		return err
	}
	
	if site.InjectConfig == nil {
		return nil // 没有注入配置
	}
	
	// 从缓存读取页面内容
	content, found := wp.cacheService.GetContent(site.Domain, pageURL, 24*time.Hour)
	if !found {
		return nil // 内容不存在
	}
	
	html := string(content.Data)
	
	// 执行关键词注入
	if site.InjectConfig.EnableKeyword {
		html, err = wp.keywordInjector.InjectKeywords(html, site.InjectConfig)
		if err != nil {
			wp.logger.Error("关键词注入失败", zap.Error(err))
		}
	}
	
	// 执行结构注入
	if site.InjectConfig.EnableStructure {
		html, err = wp.structInjector.InjectStructures(html, site.InjectConfig)
		if err != nil {
			wp.logger.Error("结构注入失败", zap.Error(err))
		}
	}
	
	// 执行伪原创
	if site.InjectConfig.EnablePseudo {
		// 获取关键词库
		keywordPtrs, _ := wp.siteService.GetKeywords(site.InjectConfig.ID)
		// 转换为值类型
		keywords := make([]model.Keyword, len(keywordPtrs))
		for i, kw := range keywordPtrs {
			keywords[i] = *kw
		}
		html, err = wp.pseudoProcessor.ProcessContent(html, site.InjectConfig, keywords)
		if err != nil {
			wp.logger.Error("伪原创处理失败", zap.Error(err))
		}
	}
	
	// 更新缓存
	content.Data = []byte(html)
	return wp.cacheService.SaveContent(site.Domain, content)
}

// GetPoolSize 获取当前任务队列大小
func (wp *WorkerPool) GetPoolSize() int {
	return len(wp.taskChan)
}

// GetWorkerCount 获取工作协程数
func (wp *WorkerPool) GetWorkerCount() int {
	return wp.workers
}