package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/service"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

type CronScheduler struct {
	logger           *zap.Logger
	cron             *cron.Cron
	jobs             map[string]cron.EntryID
	mu               sync.RWMutex
	siteService      *service.SiteService
	sitemapService   *service.SitemapService
	analyticsService *service.AnalyticsService
	taskQueue        *TaskQueue
}

func NewCronScheduler(
	logger *zap.Logger,
	siteService *service.SiteService,
	taskQueue *TaskQueue,
) *CronScheduler {
	return &CronScheduler{
		logger:      logger,
		cron:        cron.New(cron.WithSeconds()),
		jobs:        make(map[string]cron.EntryID),
		siteService: siteService,
		taskQueue:   taskQueue,
	}
}

// SetSitemapService 设置Sitemap服务
func (cs *CronScheduler) SetSitemapService(sitemapService *service.SitemapService) {
	cs.sitemapService = sitemapService
	// 注册sitemap生成任务
	cs.registerSitemapJobs()
}

// SetAnalyticsService 设置Analytics服务
func (cs *CronScheduler) SetAnalyticsService(analyticsService *service.AnalyticsService) {
	cs.analyticsService = analyticsService
	// 注册analytics自动刷新任务
	cs.registerAnalyticsJobs()
}

// registerSitemapJobs 注册sitemap相关任务
func (cs *CronScheduler) registerSitemapJobs() {
	if cs.sitemapService == nil {
		return
	}
	
	// 每30分钟生成一次sitemap（默认频率）
	cs.AddJob("generate_sitemaps", "0 */30 * * * *", func() {
		cs.generateSitemaps()
	})
}

// Start 启动调度器
func (cs *CronScheduler) Start() {
	cs.cron.Start()
	// cs.logger.Info("定时任务调度器已启动")
	
	// 注册默认任务
	cs.registerDefaultJobs()
}

// Stop 停止调度器
func (cs *CronScheduler) Stop() {
	ctx := cs.cron.Stop()
	<-ctx.Done()
	// cs.logger.Info("定时任务调度器已停止")
}

// registerDefaultJobs 注册默认任务
func (cs *CronScheduler) registerDefaultJobs() {
	// 每小时检查需要更新的站点
	cs.AddJob("check_sites", "0 0 * * * *", func() {
		cs.checkAndUpdateSites()
	})
	
	// 每天凌晨清理过期缓存
	cs.AddJob("clean_cache", "0 0 2 * * *", func() {
		cs.cleanExpiredCache()
	})
	
	// 每5分钟检查失败的任务
	cs.AddJob("retry_failed", "0 */5 * * * *", func() {
		cs.retryFailedTasks()
	})
}

// AddJob 添加定时任务
func (cs *CronScheduler) AddJob(name, spec string, cmd func()) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	
	// 如果任务已存在，先移除
	if id, exists := cs.jobs[name]; exists {
		cs.cron.Remove(id)
	}
	
	id, err := cs.cron.AddFunc(spec, func() {
		// cs.logger.Info("执行定时任务", zap.String("job", name))
		defer func() {
			if r := recover(); r != nil {
				cs.logger.Error("定时任务执行失败",
					zap.String("job", name),
					zap.Any("error", r),
				)
			}
		}()
		cmd()
	})
	
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %w", err)
	}
	
	cs.jobs[name] = id
	// cs.logger.Info("定时任务已添加",
	// 	zap.String("job", name),
	// 	zap.String("spec", spec),
	// )
	
	return nil
}

// RemoveJob 移除定时任务
func (cs *CronScheduler) RemoveJob(name string) {
	cs.mu.Lock()
	defer cs.mu.Unlock()
	
	if id, exists := cs.jobs[name]; exists {
		cs.cron.Remove(id)
		delete(cs.jobs, name)
		// cs.logger.Info("定时任务已移除", zap.String("job", name))
	}
}

// checkAndUpdateSites 检查并更新站点
func (cs *CronScheduler) checkAndUpdateSites() {
	sites, err := cs.siteService.GetActiveSites()
	if err != nil {
		cs.logger.Error("获取活跃站点失败", zap.Error(err))
		return
	}
	
	for _, site := range sites {
		// 检查是否需要更新
		if cs.shouldUpdateSite(site) {
			// 创建爬取任务
			job := &model.CrawlJob{
				SiteID:   site.ID,
				URL:      site.TargetURL,
				Type:     "scheduled",
				Status:   "pending",
				Priority: 5,
			}
			
			if err := cs.taskQueue.EnqueueCrawlJob(job); err != nil {
				cs.logger.Error("添加爬取任务失败",
					zap.String("site", site.Domain),
					zap.Error(err),
				)
			}
		}
	}
}

// shouldUpdateSite 判断是否需要更新站点
func (cs *CronScheduler) shouldUpdateSite(site *model.Site) bool {
	// 如果从未爬取过，需要更新
	if site.LastCrawlAt.IsZero() {
		return true
	}
	
	// 根据配置的更新频率判断
	// 这里可以根据站点配置设置不同的更新策略
	updateInterval := 24 * time.Hour // 默认24小时更新一次
	
	return time.Since(site.LastCrawlAt) > updateInterval
}

// cleanExpiredCache 清理过期缓存
func (cs *CronScheduler) cleanExpiredCache() {
	cs.logger.Info("开始清理过期缓存")
	
	// 获取所有站点
	sites, err := cs.siteService.GetAllSites()
	if err != nil {
		cs.logger.Error("获取站点列表失败", zap.Error(err))
		return
	}
	
	for _, site := range sites {
		// 创建清理任务
		task := &CleanCacheTask{
			SiteID:   site.ID,
			Domain:   site.Domain,
			MaxAge:   7 * 24 * time.Hour, // 7天
		}
		
		if err := cs.taskQueue.EnqueueTask(task); err != nil {
			cs.logger.Error("添加清理任务失败",
				zap.String("site", site.Domain),
				zap.Error(err),
			)
		}
	}
}

// retryFailedTasks 重试失败的任务
func (cs *CronScheduler) retryFailedTasks() {
	tasks, err := cs.siteService.GetFailedTasks(10) // 获取最近10个失败任务
	if err != nil {
		cs.logger.Error("获取失败任务失败", zap.Error(err))
		return
	}
	
	for _, job := range tasks {
		if job.RetryCount < 3 { // 最多重试3次
			job.Status = "pending"
			job.RetryCount++
			
			if err := cs.taskQueue.EnqueueCrawlJob(job); err != nil {
				cs.logger.Error("重新入队失败",
					zap.Uint("job_id", job.ID),
					zap.Error(err),
				)
			}
		}
	}
}

// ScheduleSiteCrawl 为站点安排定时爬取
func (cs *CronScheduler) ScheduleSiteCrawl(site *model.Site, cronSpec string) error {
	jobName := fmt.Sprintf("crawl_site_%d", site.ID)
	
	return cs.AddJob(jobName, cronSpec, func() {
		job := &model.CrawlJob{
			SiteID:   site.ID,
			URL:      site.TargetURL,
			Type:     "scheduled",
			Status:   "pending",
			Priority: 5,
		}
		
		if err := cs.taskQueue.EnqueueCrawlJob(job); err != nil {
			cs.logger.Error("添加爬取任务失败",
				zap.String("site", site.Domain),
				zap.Error(err),
			)
		}
	})
}

// CancelSiteCrawl 取消站点的定时爬取
func (cs *CronScheduler) CancelSiteCrawl(siteID uint) {
	jobName := fmt.Sprintf("crawl_site_%d", siteID)
	cs.RemoveJob(jobName)
}

// RegisterSiteCrawlJob 注册站点爬取任务 (接口实现)
func (cs *CronScheduler) RegisterSiteCrawlJob(site *model.Site) {
	// 使用默认的爬取频率，每小时爬取一次
	cs.ScheduleSiteCrawl(site, "0 * * * *")
}

// RemoveSiteCrawlJob 移除站点爬取任务 (接口实现)
func (cs *CronScheduler) RemoveSiteCrawlJob(siteID uint) {
	cs.CancelSiteCrawl(siteID)
}

// GetJobs 获取所有定时任务
func (cs *CronScheduler) GetJobs() []CronJob {
	cs.mu.RLock()
	defer cs.mu.RUnlock()
	
	entries := cs.cron.Entries()
	jobs := make([]CronJob, 0, len(entries))
	
	for name, id := range cs.jobs {
		for _, entry := range entries {
			if entry.ID == id {
				jobs = append(jobs, CronJob{
					Name:     name,
					Schedule: "unknown", // cron v3 不再暴露 Spec 字段
					Next:     entry.Next,
					Prev:     entry.Prev,
				})
				break
			}
		}
	}
	
	return jobs
}

type CronJob struct {
	Name     string    `json:"name"`
	Schedule string    `json:"schedule"`
	Next     time.Time `json:"next"`
	Prev     time.Time `json:"prev"`
}

type CleanCacheTask struct {
	SiteID uint
	Domain string
	MaxAge time.Duration
}

func (t *CleanCacheTask) Execute(ctx context.Context) error {
	// 这里应该调用缓存服务进行清理
	// 暂时返回 nil
	return nil
}

func (t *CleanCacheTask) GetPriority() int {
	return 1 // 清理任务低优先级
}

func (t *CleanCacheTask) GetID() string {
	return fmt.Sprintf("clean_cache_%d", t.SiteID)
}

// generateSitemaps 生成所有启用站点的sitemap
func (cs *CronScheduler) generateSitemaps() {
	if cs.sitemapService == nil {
		return
	}
	
	cs.logger.Info("开始生成sitemap文件")
	
	// 获取所有启用sitemap的站点
	sites, err := cs.siteService.GetAllSites()
	if err != nil {
		cs.logger.Error("获取站点列表失败", zap.Error(err))
		return
	}
	
	successCount := 0
	failedCount := 0
	
	for _, site := range sites {
		if !site.EnableSitemap {
			continue
		}
		
		// 检查是否需要更新（基于站点配置的更新间隔）
		if !cs.shouldUpdateSitemap(site) {
			continue
		}
		
		// 生成sitemap
		if err := cs.sitemapService.GenerateSitemap(site.ID, site.Domain); err != nil {
			failedCount++
			cs.logger.Error("生成sitemap失败", 
				zap.String("domain", site.Domain),
				zap.Error(err))
		} else {
			successCount++
			cs.logger.Debug("成功生成sitemap", 
				zap.String("domain", site.Domain))
		}
	}
	
	cs.logger.Info("sitemap生成任务完成", 
		zap.Int("success", successCount),
		zap.Int("failed", failedCount))
}

// shouldUpdateSitemap 判断是否需要更新sitemap
func (cs *CronScheduler) shouldUpdateSitemap(site *model.Site) bool {
	// 如果从未生成过，需要生成
	if site.SitemapLastUpdate.IsZero() {
		return true
	}
	
	// 根据站点配置的更新间隔判断
	interval := time.Duration(site.SitemapUpdateInterval) * time.Minute
	if interval == 0 {
		interval = 30 * time.Minute // 默认30分钟
	}
	
	return time.Since(site.SitemapLastUpdate) > interval
}

// registerAnalyticsJobs 注册analytics统计代码自动刷新任务
func (cs *CronScheduler) registerAnalyticsJobs() {
	if cs.analyticsService == nil {
		return
	}
	
	// 每分钟检查是否需要自动刷新统计代码
	cs.AddJob("analytics_auto_refresh", "0 * * * * *", func() {
		if shouldRefresh, err := cs.analyticsService.ShouldRefresh(); err == nil && shouldRefresh {
			// 获取所有站点并刷新统计JS文件
			var sites []struct {
				Domain string
			}
			if err := cs.analyticsService.GetDB().Table("sites").Select("domain").Find(&sites).Error; err != nil {
				cs.logger.Error("获取站点列表失败", zap.Error(err))
				return
			}
			
			updatedCount := 0
			for _, site := range sites {
				if err := cs.analyticsService.GenerateAnalyticsJS(site.Domain); err != nil {
					cs.logger.Error("自动刷新统计JS失败", 
						zap.String("domain", site.Domain),
						zap.Error(err))
				} else {
					updatedCount++
				}
			}
			
			// 更新最后刷新时间
			if err := cs.analyticsService.UpdateLastRefresh(); err != nil {
				cs.logger.Error("更新统计代码最后刷新时间失败", zap.Error(err))
			} else {
				cs.logger.Info("统计代码自动刷新完成", 
					zap.Int("updated_count", updatedCount),
					zap.Int("total_count", len(sites)))
			}
		}
	})
}