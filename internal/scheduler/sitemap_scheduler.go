package scheduler

import (
	"site-cluster/internal/model"
	"site-cluster/internal/service"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SitemapScheduler 站点地图刷新调度器
type SitemapScheduler struct {
	db                    *gorm.DB
	logger                *zap.Logger
	sitemapService        *service.SitemapService
	systemSettingsService *service.SystemSettingsService
	stopChan              chan struct{}
	mu                    sync.RWMutex
	isRunning             bool
	lastRefreshTime       time.Time
	ticker                *time.Ticker
}

// NewSitemapScheduler 创建站点地图调度器
func NewSitemapScheduler(
	db *gorm.DB,
	logger *zap.Logger,
	sitemapService *service.SitemapService,
	systemSettingsService *service.SystemSettingsService,
) *SitemapScheduler {
	return &SitemapScheduler{
		db:                    db,
		logger:                logger,
		sitemapService:        sitemapService,
		systemSettingsService: systemSettingsService,
		stopChan:              make(chan struct{}),
	}
}

// Start 启动调度器
func (s *SitemapScheduler) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return nil
	}

	// 获取系统设置
	settings, err := s.systemSettingsService.GetSystemSettings()
	if err != nil {
		s.logger.Error("获取系统设置失败", zap.Error(err))
		return err
	}

	// 如果刷新间隔小于等于0，表示禁用自动刷新
	if settings.SitemapRefreshInterval <= 0 {
		// s.logger.Info("站点地图自动刷新未启用（间隔为0）")
		return nil
	}

	// 设置刷新间隔，默认12小时
	interval := time.Duration(settings.SitemapRefreshInterval) * time.Minute
	if interval < time.Hour {
		interval = 12 * time.Hour
	}

	s.isRunning = true
	s.ticker = time.NewTicker(interval)

	// 如果上次刷新时间为空或者已经超过间隔时间，立即执行一次刷新
	if settings.LastSitemapRefreshTime == nil || 
	   time.Since(*settings.LastSitemapRefreshTime) > interval {
		go s.refreshAllSitemaps()
	}

	// 启动定时任务
	go s.run()

	// s.logger.Info("站点地图自动刷新调度器已启动", 
	// 	zap.Duration("interval", interval),
	// 	zap.Time("next_refresh", time.Now().Add(interval)))

	return nil
}

// Stop 停止调度器
func (s *SitemapScheduler) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return
	}

	if s.ticker != nil {
		s.ticker.Stop()
	}

	close(s.stopChan)
	s.isRunning = false

	// s.logger.Info("站点地图自动刷新调度器已停止")
}

// run 运行调度器
func (s *SitemapScheduler) run() {
	for {
		select {
		case <-s.ticker.C:
			// 检查是否仍然启用自动刷新
			settings, err := s.systemSettingsService.GetSystemSettings()
			if err != nil {
				s.logger.Error("获取系统设置失败", zap.Error(err))
				continue
			}

			if settings.SitemapRefreshInterval <= 0 {
				// s.logger.Info("站点地图自动刷新已禁用（间隔为0），停止调度器")
				s.Stop()
				return
			}

			// 如果间隔时间发生变化，重新设置定时器
			newInterval := time.Duration(settings.SitemapRefreshInterval) * time.Minute
			currentInterval := time.Duration(settings.SitemapRefreshInterval) * time.Minute
			if s.lastRefreshTime.IsZero() || newInterval != currentInterval {
				s.ticker.Stop()
				s.ticker = time.NewTicker(newInterval)
				// s.logger.Info("站点地图刷新间隔已更新", zap.Duration("new_interval", newInterval))
			}

			// 执行刷新
			s.refreshAllSitemaps()

		case <-s.stopChan:
			return
		}
	}
}

// refreshAllSitemaps 刷新所有站点地图
func (s *SitemapScheduler) refreshAllSitemaps() {
	// s.logger.Info("开始自动刷新所有站点地图")
	startTime := time.Now()

	// 获取所有启用站点地图的站点
	var sites []model.Site
	err := s.db.Where("enable_sitemap = ? AND status = ?", true, "active").Find(&sites).Error
	if err != nil {
		s.logger.Error("获取站点列表失败", zap.Error(err))
		return
	}

	successCount := 0
	failCount := 0

	for _, site := range sites {
		// 刷新站点地图
		err := s.sitemapService.GenerateSitemap(site.ID, site.Domain)
		if err != nil {
			s.logger.Error("刷新站点地图失败", 
				zap.Uint("site_id", site.ID),
				zap.String("domain", site.Domain),
				zap.Error(err))
			failCount++
		} else {
			// s.logger.Info("站点地图刷新成功", 
			// 	zap.Uint("site_id", site.ID),
			// 	zap.String("domain", site.Domain))
			successCount++
		}

		// 避免并发过高，稍作延迟
		time.Sleep(100 * time.Millisecond)
	}

	// 更新最后刷新时间
	now := time.Now()
	settings, _ := s.systemSettingsService.GetSystemSettings()
	if settings != nil {
		settings.LastSitemapRefreshTime = &now
		s.db.Save(settings)
	}

	s.lastRefreshTime = now
	_ = time.Since(startTime) // 忽略未使用的变量

	// s.logger.Info("站点地图自动刷新完成", 
	// 	zap.Int("total", len(sites)),
	// 	zap.Int("success", successCount),
	// 	zap.Int("failed", failCount),
	// 	zap.Duration("duration", duration))
}

// Restart 重启调度器（用于设置更新后）
func (s *SitemapScheduler) Restart() error {
	// s.logger.Info("重启站点地图自动刷新调度器")
	
	s.Stop()
	time.Sleep(100 * time.Millisecond) // 短暂等待确保停止完成
	return s.Start()
}

// GetStatus 获取调度器状态
func (s *SitemapScheduler) GetStatus() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	settings, _ := s.systemSettingsService.GetSystemSettings()
	
	nextRefreshTime := time.Time{}
	if s.isRunning && s.ticker != nil && settings != nil {
		interval := time.Duration(settings.SitemapRefreshInterval) * time.Minute
		if s.lastRefreshTime.IsZero() && settings.LastSitemapRefreshTime != nil {
			nextRefreshTime = settings.LastSitemapRefreshTime.Add(interval)
		} else if !s.lastRefreshTime.IsZero() {
			nextRefreshTime = s.lastRefreshTime.Add(interval)
		}
	}

	return map[string]interface{}{
		"is_running":           s.isRunning,
		"last_refresh_time":    s.lastRefreshTime,
		"next_refresh_time":    nextRefreshTime,
		"auto_refresh_enabled": settings != nil && settings.SitemapRefreshInterval > 0,
		"refresh_interval":     settings.SitemapRefreshInterval,
	}
}