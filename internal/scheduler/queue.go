package scheduler

import (
	"container/heap"
	"context"
	"fmt"
	"sync"
	"time"

	"site-cluster/internal/model"

	"go.uber.org/zap"
)

// Task 任务接口
type Task interface {
	Execute(ctx context.Context) error
	GetPriority() int
	GetID() string
}

// TaskQueue 任务队列
type TaskQueue struct {
	logger    *zap.Logger
	queue     PriorityQueue
	mu        sync.Mutex
	cond      *sync.Cond
	workers   int
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
}

func NewTaskQueue(logger *zap.Logger, workers int) *TaskQueue {
	ctx, cancel := context.WithCancel(context.Background())
	tq := &TaskQueue{
		logger:  logger,
		queue:   make(PriorityQueue, 0),
		workers: workers,
		ctx:     ctx,
		cancel:  cancel,
	}
	tq.cond = sync.NewCond(&tq.mu)
	heap.Init(&tq.queue)
	return tq
}

// Start 启动任务队列
func (tq *TaskQueue) Start() {
	for i := 0; i < tq.workers; i++ {
		tq.wg.Add(1)
		go tq.worker(i)
	}
	// tq.logger.Info("任务队列已启动", zap.Int("workers", tq.workers))
}

// Stop 停止任务队列
func (tq *TaskQueue) Stop() {
	tq.cancel()
	tq.cond.Broadcast()
	tq.wg.Wait()
	// tq.logger.Info("任务队列已停止")
}

// worker 工作协程
func (tq *TaskQueue) worker(id int) {
	defer tq.wg.Done()
	
	for {
		select {
		case <-tq.ctx.Done():
			return
		default:
			task := tq.dequeue()
			if task == nil {
				continue
			}
			
			tq.logger.Debug("开始执行任务",
				zap.Int("worker", id),
				zap.String("task_id", task.GetID()),
			)
			
			ctx, cancel := context.WithTimeout(tq.ctx, 30*time.Minute)
			err := task.Execute(ctx)
			cancel()
			
			if err != nil {
				tq.logger.Error("任务执行失败",
					zap.Int("worker", id),
					zap.String("task_id", task.GetID()),
					zap.Error(err),
				)
			} else {
				tq.logger.Debug("任务执行完成",
					zap.Int("worker", id),
					zap.String("task_id", task.GetID()),
				)
			}
		}
	}
}

// Enqueue 添加任务到队列
func (tq *TaskQueue) Enqueue(task Task) error {
	tq.mu.Lock()
	defer tq.mu.Unlock()
	
	item := &Item{
		task:     task,
		priority: task.GetPriority(),
	}
	
	heap.Push(&tq.queue, item)
	tq.cond.Signal()
	
	return nil
}

// dequeue 从队列取出任务
func (tq *TaskQueue) dequeue() Task {
	tq.mu.Lock()
	defer tq.mu.Unlock()
	
	for tq.queue.Len() == 0 {
		select {
		case <-tq.ctx.Done():
			return nil
		default:
			tq.cond.Wait()
		}
	}
	
	item := heap.Pop(&tq.queue).(*Item)
	return item.task
}

// EnqueueCrawlJob 添加爬取任务
func (tq *TaskQueue) EnqueueCrawlJob(job *model.CrawlJob) error {
	task := &CrawlTask{
		Job: job,
	}
	return tq.Enqueue(task)
}

// EnqueueTask 添加通用任务
func (tq *TaskQueue) EnqueueTask(task Task) error {
	return tq.Enqueue(task)
}

// GetQueueSize 获取队列大小
func (tq *TaskQueue) GetQueueSize() int {
	tq.mu.Lock()
	defer tq.mu.Unlock()
	return tq.queue.Len()
}

// Item 优先队列项
type Item struct {
	task     Task
	priority int
	index    int
}

// PriorityQueue 优先队列
type PriorityQueue []*Item

func (pq PriorityQueue) Len() int { return len(pq) }

func (pq PriorityQueue) Less(i, j int) bool {
	return pq[i].priority > pq[j].priority
}

func (pq PriorityQueue) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
	pq[i].index = i
	pq[j].index = j
}

func (pq *PriorityQueue) Push(x interface{}) {
	n := len(*pq)
	item := x.(*Item)
	item.index = n
	*pq = append(*pq, item)
}

func (pq *PriorityQueue) Pop() interface{} {
	old := *pq
	n := len(old)
	item := old[n-1]
	old[n-1] = nil
	item.index = -1
	*pq = old[0 : n-1]
	return item
}

// CrawlTask 爬取任务
type CrawlTask struct {
	Job *model.CrawlJob
}

func (ct *CrawlTask) Execute(ctx context.Context) error {
	// 这里应该调用爬虫服务执行爬取
	// 暂时返回nil
	return nil
}

func (ct *CrawlTask) GetPriority() int {
	return 5 // 默认优先级
}

func (ct *CrawlTask) GetID() string {
	return fmt.Sprintf("crawl_%d", ct.Job.ID)
}

// CleanTask 清理任务
type CleanTask struct {
	SiteID uint
	Domain string
}

func (ct *CleanTask) Execute(ctx context.Context) error {
	// 执行清理逻辑
	return nil
}

func (ct *CleanTask) GetPriority() int {
	return 3 // 较低优先级
}

func (ct *CleanTask) GetID() string {
	return fmt.Sprintf("clean_%d", ct.SiteID)
}