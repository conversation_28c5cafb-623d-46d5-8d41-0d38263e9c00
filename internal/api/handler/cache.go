package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CacheHandler struct {
	logger          *zap.Logger
	cacheService    *service.FileCacheService
	cache404Service *service.Cache404Service
	siteService     *service.SiteService
	sitemapService  *service.SitemapService
}

func NewCacheHandler(logger *zap.Logger, cacheService *service.FileCacheService) *CacheHandler {
	return &CacheHandler{
		logger:       logger,
		cacheService: cacheService,
	}
}

// SetCache404Service 设置404缓存服务
func (ch *CacheHandler) SetCache404Service(cache404Service *service.Cache404Service) {
	ch.cache404Service = cache404Service
}

// SetSiteService 设置站点服务
func (ch *CacheHandler) SetSiteService(siteService *service.SiteService) {
	ch.siteService = siteService
}

// SetSitemapService 设置sitemap服务
func (ch *CacheHandler) SetSitemapService(sitemapService *service.SitemapService) {
	ch.sitemapService = sitemapService
}

// GetCacheService 获取缓存服务
func (ch *CacheHandler) GetCacheService() *service.FileCacheService {
	return ch.cacheService
}

// GetStats 获取缓存统计信息
func (ch *CacheHandler) GetStats(c *gin.Context) {
	stats, err := ch.cacheService.GetStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// ClearSiteCache 清除站点缓存（通过域名，兼容性接口）
func (ch *CacheHandler) ClearSiteCache(c *gin.Context) {
	domain := c.Param("domain")
	if domain == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名不能为空",
		})
		return
	}

	deletedCount, err := ch.cacheService.ClearSiteCache(domain)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 清空404缓存并重置统计
	if ch.cache404Service != nil {
		ch.logger.Info("清空404缓存和统计", zap.String("domain", domain))
		ch.cache404Service.Clear404Cache(domain)
		// Reset404Stats 现在会同时更新数据库
		ch.cache404Service.Reset404Stats(domain)
	}
	
	// 通过域名查找站点ID，然后清空sitemap数据
	var sitemapDeletedCount int64
	if ch.sitemapService != nil && ch.siteService != nil {
		// 先通过域名查找站点
		site, err := ch.siteService.GetSiteByDomain(domain)
		if err != nil {
			ch.logger.Error("查找站点失败", zap.String("domain", domain), zap.Error(err))
			// 降级使用域名清空（兼容旧数据）
			sitemapCount, err := ch.sitemapService.ClearSitemapByDomain(domain)
			if err != nil {
				ch.logger.Error("清空sitemap数据失败", zap.String("domain", domain), zap.Error(err))
			} else {
				sitemapDeletedCount = sitemapCount
			}
		} else {
			// 使用站点ID清空
			sitemapCount, err := ch.sitemapService.ClearSitemapBySiteID(site.ID)
			if err != nil {
				ch.logger.Error("清空sitemap数据失败", zap.Uint("site_id", site.ID), zap.Error(err))
			} else {
				sitemapDeletedCount = sitemapCount
				ch.logger.Info("清空站点sitemap数据", zap.Uint("site_id", site.ID), zap.String("domain", domain), zap.Int64("deleted_count", sitemapDeletedCount))
			}
			
			// 更新站点缓存状态为待获取
			if err := ch.siteService.UpdateCacheStatus(site.ID, "pending", "缓存已清空"); err != nil {
				ch.logger.Error("更新站点缓存状态失败", 
					zap.Uint("site_id", site.ID),
					zap.String("domain", domain),
					zap.Error(err))
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"deleted_count": deletedCount,
			"sitemap_deleted_count": sitemapDeletedCount,
		},
		"message": "站点缓存清除成功",
	})
}

// ClearSiteCacheByID 通过站点ID清除站点缓存（推荐使用）
func (ch *CacheHandler) ClearSiteCacheByID(c *gin.Context) {
	siteIDStr := c.Param("siteId")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "站点ID格式错误",
		})
		return
	}

	// 获取站点信息
	if ch.siteService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "站点服务未初始化",
		})
		return
	}

	site, err := ch.siteService.GetSite(uint(siteID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "站点不存在",
		})
		return
	}

	// 清空文件缓存
	deletedCount, err := ch.cacheService.ClearSiteCache(site.Domain)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 清空404缓存并重置统计
	if ch.cache404Service != nil {
		ch.logger.Info("清空404缓存和统计", zap.String("domain", site.Domain))
		ch.cache404Service.Clear404Cache(site.Domain)
		ch.cache404Service.Reset404Stats(site.Domain)
	}
	
	// 通过站点ID清空sitemap数据
	var sitemapDeletedCount int64
	if ch.sitemapService != nil {
		sitemapCount, err := ch.sitemapService.ClearSitemapBySiteID(site.ID)
		if err != nil {
			ch.logger.Error("清空sitemap数据失败", zap.Uint("site_id", site.ID), zap.Error(err))
		} else {
			sitemapDeletedCount = sitemapCount
			ch.logger.Info("清空站点sitemap数据", zap.Uint("site_id", site.ID), zap.String("domain", site.Domain), zap.Int64("deleted_count", sitemapDeletedCount))
		}
	}
	
	// 更新站点缓存状态为待获取
	if ch.siteService != nil {
		if err := ch.siteService.UpdateCacheStatus(site.ID, "pending", "缓存已清空"); err != nil {
			ch.logger.Error("更新站点缓存状态失败", 
				zap.Uint("site_id", site.ID),
				zap.Error(err))
			// 不影响主流程，继续返回成功
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"site_id": site.ID,
			"domain": site.Domain,
			"deleted_count": deletedCount,
			"sitemap_deleted_count": sitemapDeletedCount,
		},
		"message": "站点缓存清除成功",
	})
}

// ClearExpiredCache 清除过期缓存
func (ch *CacheHandler) ClearExpiredCache(c *gin.Context) {
	deletedCount, err := ch.cacheService.ClearExpiredCache()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"deleted_count": deletedCount,
		},
		"message": "过期缓存清除成功",
	})
}

// GetCacheSize 获取缓存大小
func (ch *CacheHandler) GetCacheSize(c *gin.Context) {
	domain := c.Query("domain")
	
	size, err := ch.cacheService.GetCacheSize(domain)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"size":       size,
			"size_mb":    float64(size) / 1024 / 1024,
			"size_human": formatBytes(size),
		},
	})
}

// GetCachedURLs 获取已缓存的URL列表
func (ch *CacheHandler) GetCachedURLs(c *gin.Context) {
	domain := c.Param("domain")
	if domain == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名不能为空",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "50"))

	urls, total, err := ch.cacheService.GetCachedURLs(domain, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"urls":      urls,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// RefreshCache 刷新缓存
func (ch *CacheHandler) RefreshCache(c *gin.Context) {
	var req RefreshCacheRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 触发重新爬取任务
	job, err := ch.cacheService.TriggerRefresh(req.Domain, req.URL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    job,
		"message": "缓存刷新任务已创建",
	})
}

// GetCacheConfig 获取缓存配置
func (ch *CacheHandler) GetCacheConfig(c *gin.Context) {
	config := ch.cacheService.GetConfig()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// UpdateCacheConfig 更新缓存配置
func (ch *CacheHandler) UpdateCacheConfig(c *gin.Context) {
	var req UpdateCacheConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	config := &service.CacheConfig{
		MaxSize:        req.MaxSize,
		DefaultTTL:     time.Duration(req.DefaultTTL) * time.Hour,
		CleanupInterval: time.Duration(req.CleanupInterval) * time.Minute,
	}

	if err := ch.cacheService.UpdateConfig(config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
		"message": "缓存配置更新成功",
	})
}

// PreloadCache 预加载缓存
func (ch *CacheHandler) PreloadCache(c *gin.Context) {
	var req PreloadCacheRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	job, err := ch.cacheService.PreloadCache(req.Domain, req.URLs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    job,
		"message": "预加载任务已创建",
	})
}

// GetCacheDetails 获取缓存详情
func (ch *CacheHandler) GetCacheDetails(c *gin.Context) {
	domain := c.Param("domain")
	url := c.Query("url")
	
	if domain == "" || url == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名和URL不能为空",
		})
		return
	}

	details, err := ch.cacheService.GetCacheDetails(domain, url)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    details,
	})
}

// 辅助函数
func formatBytes(bytes int64) string {
	const (
		KB = 1024
		MB = KB * 1024
		GB = MB * 1024
	)
	
	switch {
	case bytes >= GB:
		return fmt.Sprintf("%.2f GB", float64(bytes)/GB)
	case bytes >= MB:
		return fmt.Sprintf("%.2f MB", float64(bytes)/MB)
	case bytes >= KB:
		return fmt.Sprintf("%.2f KB", float64(bytes)/KB)
	default:
		return fmt.Sprintf("%d B", bytes)
	}
}

// 请求结构体
type RefreshCacheRequest struct {
	Domain string `json:"domain" binding:"required"`
	URL    string `json:"url"` // 可选，指定刷新特定URL
}

type UpdateCacheConfigRequest struct {
	MaxSize         int64 `json:"max_size"`         // 最大缓存大小（字节）
	DefaultTTL      int   `json:"default_ttl"`      // 默认过期时间（小时）
	CleanupInterval int   `json:"cleanup_interval"` // 清理间隔（分钟）
}

type PreloadCacheRequest struct {
	Domain string   `json:"domain" binding:"required"`
	URLs   []string `json:"urls" binding:"required"`
}