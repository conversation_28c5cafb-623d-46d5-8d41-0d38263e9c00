package handler

import (
	"net/http"
	"site-cluster/internal/model"

	"github.com/gin-gonic/gin"
)

// MenuHandler 菜单处理器
type MenuHandler struct{}

// NewMenuHandler 创建菜单处理器
func NewMenuHandler() *MenuHandler {
	return &MenuHandler{}
}

// GetMenuGroups 获取菜单分组配置
func (h *MenuHandler) GetMenuGroups(c *gin.Context) {
	// 获取当前活跃的菜单项（从前端传递）
	active := c.Query("active")
	
	// 获取菜单分组配置
	menuGroups := model.GetMenuGroups()
	
	// 根据活跃项设置分组状态
	for i := range menuGroups {
		for j := range menuGroups[i].Items {
			if menuGroups[i].Items[j].Active == active {
				// 当前分组包含活跃项，确保展开
				menuGroups[i].Expanded = true
				break
			}
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    menuGroups,
	})
}

// GetMenuConfig 获取菜单配置（包含权限等信息）
func (h *MenuHandler) GetMenuConfig(c *gin.Context) {
	// TODO: 根据用户权限过滤菜单项
	adminID := c.GetUint("admin_id")
	
	// 获取基础菜单配置
	menuGroups := model.GetMenuGroups()
	
	// 这里可以根据管理员权限进行过滤
	// 示例：如果不是超级管理员，隐藏某些菜单
	if adminID != 1 { // 假设ID为1的是超级管理员
		// 过滤掉系统管理分组中的某些敏感菜单
		for i := range menuGroups {
			if menuGroups[i].ID == "system-management" {
				// 过滤掉管理员管理菜单
				filteredItems := []model.MenuItem{}
				for _, item := range menuGroups[i].Items {
					if item.ID != "admins" {
						filteredItems = append(filteredItems, item)
					}
				}
				menuGroups[i].Items = filteredItems
			}
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"menuGroups": menuGroups,
			"adminID":    adminID,
		},
	})
}