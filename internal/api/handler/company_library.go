package handler

import (
	"net/http"
	"strconv"
	"strings"
	
	"site-cluster/internal/model"
	"site-cluster/internal/service"
	
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CompanyLibraryHandler struct {
	logger  *zap.Logger
	service *service.CompanyLibraryService
}

func NewCompanyLibraryHandler(logger *zap.Logger, service *service.CompanyLibraryService) *CompanyLibraryHandler {
	return &CompanyLibraryHandler{
		logger:  logger,
		service: service,
	}
}

// GetLibraries 获取所有企业名称库
func (h *CompanyLibraryHandler) GetLibraries(c *gin.Context) {
	libraries, err := h.service.GetLibraries()
	if err != nil {
		h.logger.Error("获取企业名称库失败", zap.Error(err))
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取企业名称库失败",
		})
		return
	}
	
	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    libraries,
	})
}

// GetLibrary 获取单个企业名称库
func (h *CompanyLibraryHandler) GetLibrary(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的库ID",
		})
		return
	}
	
	library, err := h.service.GetLibrary(uint(id))
	if err != nil {
		h.logger.Error("获取企业名称库失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取企业名称库失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    library,
	})
}

// CreateLibrary 创建企业名称库
func (h *CompanyLibraryHandler) CreateLibrary(c *gin.Context) {
	var library model.CompanyLibrary
	if err := c.ShouldBindJSON(&library); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
		})
		return
	}
	
	if err := h.service.CreateLibrary(&library); err != nil {
		h.logger.Error("创建企业名称库失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建企业名称库失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    library,
		"message": "创建成功",
	})
}

// UpdateLibrary 更新企业名称库
func (h *CompanyLibraryHandler) UpdateLibrary(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的库ID",
		})
		return
	}
	
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
		})
		return
	}
	
	if err := h.service.UpdateLibrary(uint(id), updates); err != nil {
		h.logger.Error("更新企业名称库失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新企业名称库失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
	})
}

// DeleteLibrary 删除企业名称库
func (h *CompanyLibraryHandler) DeleteLibrary(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的库ID",
		})
		return
	}
	
	if err := h.service.DeleteLibrary(uint(id)); err != nil {
		h.logger.Error("删除企业名称库失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除企业名称库失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// GetCompanyNames 获取库中的企业名称
func (h *CompanyLibraryHandler) GetCompanyNames(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的库ID",
		})
		return
	}
	
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	
	names, total, err := h.service.GetCompanyNames(uint(libraryID), page, limit)
	if err != nil {
		h.logger.Error("获取企业名称失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取企业名称失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    names,
		"total":   total,
		"page":    page,
		"limit":   limit,
	})
}

// AddCompanyName 添加企业名称
func (h *CompanyLibraryHandler) AddCompanyName(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的库ID",
		})
		return
	}
	
	var req struct {
		Name     string `json:"name"`
		Industry string `json:"industry"`
		Region   string `json:"region"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
		})
		return
	}
	
	name := &model.CompanyName{
		LibraryID: uint(libraryID),
		Name:      req.Name,
		Industry:  req.Industry,
		Region:    req.Region,
	}
	
	if err := h.service.AddCompanyName(name); err != nil {
		h.logger.Error("添加企业名称失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    name,
		"message": "添加成功",
	})
}

// BatchAddCompanyNames 批量添加企业名称
func (h *CompanyLibraryHandler) BatchAddCompanyNames(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的库ID",
		})
		return
	}
	
	var req struct {
		Names string `json:"names"` // 多个名称用换行分隔
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
		})
		return
	}
	
	// 分割名称
	names := strings.Split(req.Names, "\n")
	
	if err := h.service.BatchAddCompanyNames(uint(libraryID), names); err != nil {
		h.logger.Error("批量添加企业名称失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "批量添加失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量添加成功",
	})
}

// UpdateCompanyName 更新企业名称
func (h *CompanyLibraryHandler) UpdateCompanyName(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("nameId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的名称ID",
		})
		return
	}
	
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
		})
		return
	}
	
	if err := h.service.UpdateCompanyName(uint(id), updates); err != nil {
		h.logger.Error("更新企业名称失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
	})
}

// DeleteCompanyName 删除企业名称
func (h *CompanyLibraryHandler) DeleteCompanyName(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("nameId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的名称ID",
		})
		return
	}
	
	if err := h.service.DeleteCompanyName(uint(id)); err != nil {
		h.logger.Error("删除企业名称失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}