package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"site-cluster/internal/model"
	"site-cluster/internal/service"
)

// SiteCategoryHandler 站点分类处理器
type SiteCategoryHandler struct {
	categoryService  *service.SiteCategoryService
	siteService      *service.SiteService
	analyticsService *service.AnalyticsService
}

// NewSiteCategoryHandler 创建站点分类处理器
func NewSiteCategoryHandler(categoryService *service.SiteCategoryService, siteService *service.SiteService, analyticsService *service.AnalyticsService) *SiteCategoryHandler {
	return &SiteCategoryHandler{
		categoryService:  categoryService,
		siteService:      siteService,
		analyticsService: analyticsService,
	}
}

// GetCategories 获取分类列表
func (h *SiteCategoryHandler) GetCategories(c *gin.Context) {
	categories, err := h.categoryService.GetAll()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取分类列表失败",
		})
		return
	}

	// 获取每个分类下的站点数量
	for i := range categories {
		count, _ := h.siteService.CountByCategoryID(categories[i].ID)
		categories[i].SiteCount = count
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    categories,
	})
}

// GetCategory 获取分类详情
func (h *SiteCategoryHandler) GetCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的分类ID",
		})
		return
	}

	category, err := h.categoryService.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "分类不存在",
		})
		return
	}

	// 获取站点数量
	count, _ := h.siteService.CountByCategoryID(category.ID)
	category.SiteCount = count

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    category,
	})
}

// CreateCategory 创建分类
func (h *SiteCategoryHandler) CreateCategory(c *gin.Context) {
	var category model.SiteCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}

	if category.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "分类名称不能为空",
		})
		return
	}

	// 设置默认值
	if category.Color == "" {
		category.Color = "blue"
	}
	if category.Icon == "" {
		category.Icon = "fa-folder"
	}

	if err := h.categoryService.Create(&category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "创建分类失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    category,
	})
}

// UpdateCategory 更新分类
func (h *SiteCategoryHandler) UpdateCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的分类ID",
		})
		return
	}

	var category model.SiteCategory
	if err := c.ShouldBindJSON(&category); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}

	category.ID = uint(id)
	if err := h.categoryService.Update(&category); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "更新分类失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
		"data":    category,
	})
}

// DeleteCategory 删除分类
func (h *SiteCategoryHandler) DeleteCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的分类ID",
		})
		return
	}

	// 检查分类下是否有站点
	count, _ := h.siteService.CountByCategoryID(uint(id))
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "该分类下还有站点，无法删除",
		})
		return
	}

	if err := h.categoryService.Delete(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "删除分类失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// RefreshCategoryAnalytics 刷新分类下所有站点的统计JS
func (h *SiteCategoryHandler) RefreshCategoryAnalytics(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的分类ID",
		})
		return
	}

	// 获取分类信息
	category, err := h.categoryService.GetByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "分类不存在",
		})
		return
	}

	// 获取该分类下的所有站点
	sites, err := h.siteService.GetSitesByCategoryID(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取站点列表失败",
		})
		return
	}
	
	// 如果没有站点，直接返回
	if len(sites) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "该分类下没有站点",
			"refreshed": 0,
			"failed":   0,
		})
		return
	}

	// 刷新每个站点的统计JS文件
	refreshedCount := 0
	failedCount := 0
	var failedDomains []string
	
	for _, site := range sites {
		// 确保站点有分类信息
		site.Category = category
		site.CategoryID = &category.ID
		
		// 如果分类启用了独立统计，使用分类的统计代码
		if category.UseIndependentAnalytics {
			if err := h.analyticsService.GenerateCategoryAnalyticsJS(site.Domain, category.AnalyticsCode); err != nil {
				failedCount++
				failedDomains = append(failedDomains, site.Domain)
			} else {
				refreshedCount++
			}
		} else {
			// 否则使用全局统计
			if err := h.analyticsService.GenerateAnalyticsJS(site.Domain); err != nil {
				failedCount++
				failedDomains = append(failedDomains, site.Domain)
			} else {
				refreshedCount++
			}
		}
	}
	
	// 构建返回消息
	message := fmt.Sprintf("刷新完成：成功 %d 个，失败 %d 个", refreshedCount, failedCount)
	if len(failedDomains) > 0 {
		message += fmt.Sprintf("。失败的域名：%v", failedDomains)
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"message":  message,
		"refreshed": refreshedCount,
		"failed":   failedCount,
	})
}