package handler

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"site-cluster/internal/model"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"
)

// MirrorRequestProcessor 镜像请求处理器
type MirrorRequestProcessor struct {
	handler *MirrorHandler
	site    *model.Site
	ctx     context.Context
}

// NewMirrorRequestProcessor 创建新的请求处理器
func (h *MirrorHandler) NewMirrorRequestProcessor(site *model.Site, ctx context.Context) *MirrorRequestProcessor {
	return &MirrorRequestProcessor{
		handler: h,
		site:    site,
		ctx:     ctx,
	}
}

// PrepareRequest 准备HTTP请求
func (p *MirrorRequestProcessor) PrepareRequest(targetURL string) (*http.Request, error) {
	req, err := http.NewRequestWithContext(p.ctx, "GET", targetURL, nil)
	if err != nil {
		p.handler.logger.Error("创建请求失败", 
			zap.String("url", targetURL),
			zap.Error(err))
		return nil, fmt.Errorf("request creation failed")
	}

	// 设置请求头
	p.setRequestHeaders(req)
	return req, nil
}

// setRequestHeaders 设置请求头
func (p *MirrorRequestProcessor) setRequestHeaders(req *http.Request) {
	// 获取User-Agent
	userAgent := p.getUserAgent()
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
}

// getUserAgent 获取User-Agent
func (p *MirrorRequestProcessor) getUserAgent() string {
	defaultUA := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
	if p.handler.systemSettingsService == nil {
		return defaultUA
	}
	
	// 从系统设置获取UA列表
	settings, err := p.handler.systemSettingsService.GetSystemSettings()
	if err != nil || settings.UserAgentList == "" {
		return defaultUA
	}
	
	// 随机选择一个UA
	uas := strings.Split(settings.UserAgentList, "\n")
	if len(uas) > 0 {
		return strings.TrimSpace(uas[0])
	}
	return defaultUA
}

// FetchContent 获取内容
func (p *MirrorRequestProcessor) FetchContent(targetURL string) (*http.Response, error) {
	req, err := p.PrepareRequest(targetURL)
	if err != nil {
		return nil, err
	}

	// 获取HTTP客户端
	client := p.getHTTPClient()
	
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		p.handler.logger.Error("发送请求失败",
			zap.String("url", targetURL),
			zap.Error(err))
		return nil, fmt.Errorf("request failed")
	}

	return resp, nil
}

// getHTTPClient 获取HTTP客户端
func (p *MirrorRequestProcessor) getHTTPClient() *http.Client {
	timeout := 60
	if p.handler.systemSettingsService != nil {
		timeout = p.handler.systemSettingsService.GetProxyRequestTimeout()
	}
	return p.handler.getOrCreateHTTPClient(timeout)
}

// ProcessResponse 处理响应内容
func (p *MirrorRequestProcessor) ProcessResponse(resp *http.Response, requestPath string) (*model.CachedContent, error) {
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return p.handleNonOKStatus(resp.StatusCode)
	}

	// 读取响应内容
	body, err := p.readResponseBody(resp)
	if err != nil {
		return nil, err
	}

	// 获取内容类型
	contentType := resp.Header.Get("Content-Type")
	
	// 根据内容类型处理
	if p.isHTMLContent(contentType) {
		return p.processHTMLContent(body, requestPath, contentType)
	}
	
	// 非HTML内容直接返回
	return &model.CachedContent{
		Data:        body,
		ContentType: contentType,
	}, nil
}

// handleNonOKStatus 处理非200状态码
func (p *MirrorRequestProcessor) handleNonOKStatus(statusCode int) (*model.CachedContent, error) {
	p.handler.logger.Warn("目标页面返回非200状态", zap.Int("status", statusCode))
	
	if statusCode == http.StatusNotFound {
		return p.handler.handle404Response(p.site)
	}
	
	return nil, fmt.Errorf("unexpected status code: %d", statusCode)
}

// readResponseBody 读取响应体
func (p *MirrorRequestProcessor) readResponseBody(resp *http.Response) ([]byte, error) {
	// 限制最大读取大小为10MB
	maxSize := int64(10 * 1024 * 1024)
	limitedReader := io.LimitReader(resp.Body, maxSize)
	
	body, err := io.ReadAll(limitedReader)
	if err != nil {
		p.handler.logger.Error("读取响应内容失败", zap.Error(err))
		return nil, fmt.Errorf("failed to read response")
	}
	
	return body, nil
}

// isHTMLContent 判断是否为HTML内容
func (p *MirrorRequestProcessor) isHTMLContent(contentType string) bool {
	return strings.Contains(strings.ToLower(contentType), "text/html")
}

// processHTMLContent 处理HTML内容
func (p *MirrorRequestProcessor) processHTMLContent(body []byte, requestPath string, contentType string) (*model.CachedContent, error) {
	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		p.handler.logger.Error("解析HTML失败", zap.Error(err))
		return nil, fmt.Errorf("HTML parsing failed")
	}

	// 处理链接
	p.processLinks(doc, requestPath)
	
	// 处理资源
	p.processResources(doc, requestPath)
	
	// 应用内容注入
	if p.site.InjectConfig != nil {
		p.applyContentInjection(doc)
	}

	// 生成最终HTML
	finalHTML, err := doc.Html()
	if err != nil {
		return nil, fmt.Errorf("failed to generate HTML")
	}

	return &model.CachedContent{
		Data:        []byte(finalHTML),
		ContentType: contentType,
	}, nil
}

// processLinks 处理页面中的链接
func (p *MirrorRequestProcessor) processLinks(doc *goquery.Document, requestPath string) {
	// 处理 a 标签
	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if !exists {
			return
		}
		
		newHref := p.convertURL(href, requestPath)
		s.SetAttr("href", newHref)
	})
	
	// 处理 form 标签
	doc.Find("form[action]").Each(func(i int, s *goquery.Selection) {
		action, exists := s.Attr("action")
		if !exists {
			return
		}
		
		newAction := p.convertURL(action, requestPath)
		s.SetAttr("action", newAction)
	})
}

// processResources 处理页面资源
func (p *MirrorRequestProcessor) processResources(doc *goquery.Document, requestPath string) {
	// 处理图片
	doc.Find("img[src]").Each(func(i int, s *goquery.Selection) {
		src, exists := s.Attr("src")
		if !exists {
			return
		}
		
		newSrc := p.convertResourceURL(src, requestPath)
		s.SetAttr("src", newSrc)
	})
	
	// 处理CSS
	doc.Find("link[rel='stylesheet']").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if !exists {
			return
		}
		
		newHref := p.convertResourceURL(href, requestPath)
		s.SetAttr("href", newHref)
	})
	
	// 处理脚本
	doc.Find("script[src]").Each(func(i int, s *goquery.Selection) {
		src, exists := s.Attr("src")
		if !exists {
			return
		}
		
		newSrc := p.convertResourceURL(src, requestPath)
		s.SetAttr("src", newSrc)
	})
}

// convertURL 转换URL
func (p *MirrorRequestProcessor) convertURL(originalURL, requestPath string) string {
	// 如果是绝对URL，直接返回
	if strings.HasPrefix(originalURL, "http://") || strings.HasPrefix(originalURL, "https://") {
		return originalURL
	}
	
	// 如果是协议相对URL
	if strings.HasPrefix(originalURL, "//") {
		return "https:" + originalURL
	}
	
	// 处理相对URL
	targetURL, _ := url.Parse(p.site.TargetURL)
	if strings.HasPrefix(originalURL, "/") {
		// 绝对路径
		return targetURL.Scheme + "://" + targetURL.Host + originalURL
	}
	
	// 相对路径
	basePath := requestPath
	if idx := strings.LastIndex(basePath, "/"); idx > 0 {
		basePath = basePath[:idx]
	}
	
	return targetURL.Scheme + "://" + targetURL.Host + basePath + "/" + originalURL
}

// convertResourceURL 转换资源URL
func (p *MirrorRequestProcessor) convertResourceURL(resourceURL, requestPath string) string {
	// 对于资源，保持原始URL或转换为代理URL
	if p.site.DownloadExternalResources {
		// 如果需要下载外部资源，转换为本地缓存路径
		return "/cache/" + p.site.Domain + "/resources/" + p.hashURL(resourceURL)
	}
	
	// 否则使用原始URL
	return p.convertURL(resourceURL, requestPath)
}

// hashURL 生成URL的哈希值
func (p *MirrorRequestProcessor) hashURL(url string) string {
	// 简单的哈希实现，实际应该使用更好的哈希算法
	return fmt.Sprintf("%x", []byte(url))
}

// applyContentInjection 应用内容注入
func (p *MirrorRequestProcessor) applyContentInjection(doc *goquery.Document) {
	// 这里调用原有的注入逻辑
	if p.handler.keywordInjector != nil && p.site.InjectConfig != nil && p.site.InjectConfig.EnableKeyword {
		// 获取HTML字符串
		html, _ := doc.Html()
		// 调用关键词注入
		processedHTML, _ := p.handler.keywordInjector.InjectKeywords(html, p.site.InjectConfig)
		// 重新解析HTML
		newDoc, _ := goquery.NewDocumentFromReader(strings.NewReader(processedHTML))
		*doc = *newDoc
	}
}

// handle404Response 处理404响应
func (h *MirrorHandler) handle404Response(site *model.Site) (*model.CachedContent, error) {
	// 获取404页面内容
	var html404 string
	
	// 使用系统默认404
	settings, _ := h.systemSettingsService.GetSystemSettings()
	if settings != nil && settings.Default404HTML != "" {
		html404 = settings.Default404HTML
	} else {
		// 使用内置404页面
		html404 = `
		<!DOCTYPE html>
		<html>
		<head>
			<meta charset="UTF-8">
			<title>404 - 页面未找到</title>
			<style>
				body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
				h1 { color: #333; }
				p { color: #666; }
			</style>
		</head>
		<body>
			<h1>404 - 页面未找到</h1>
			<p>抱歉，您访问的页面不存在。</p>
		</body>
		</html>`
	}
	
	return &model.CachedContent{
		Data:        []byte(html404),
		ContentType: "text/html; charset=utf-8",
	}, nil
}