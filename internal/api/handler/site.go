package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SiteHandler struct {
	logger      *zap.Logger
	siteService *service.SiteService
	cacheService *service.FileCacheService
	cache404Service *service.Cache404Service
	sitemapService *service.SitemapService
	redisCache *service.RedisCacheService
	optimizedMirrorHandler *OptimizedMirrorHandler // 添加镜像处理器引用
}

func NewSiteHandler(logger *zap.Logger, siteService *service.SiteService, cacheService *service.FileCacheService) *SiteHandler {
	return &SiteHandler{
		logger:      logger,
		siteService: siteService,
		cacheService: cacheService,
	}
}

// SetCache404Service 设置404缓存服务
func (sh *SiteHandler) SetCache404Service(cache404Service *service.Cache404Service) {
	sh.cache404Service = cache404Service
}

// SetSitemapService 设置sitemap服务
func (sh *SiteHandler) SetSitemapService(sitemapService *service.SitemapService) {
	sh.sitemapService = sitemapService
}

// SetRedisCache 设置Redis缓存服务
func (sh *SiteHandler) SetRedisCache(redisCache *service.RedisCacheService) {
	sh.redisCache = redisCache
}

// SetOptimizedMirrorHandler 设置优化的镜像处理器
func (sh *SiteHandler) SetOptimizedMirrorHandler(handler *OptimizedMirrorHandler) {
	sh.optimizedMirrorHandler = handler
}

// GetSiteService 获取站点服务
func (sh *SiteHandler) GetSiteService() *service.SiteService {
	return sh.siteService
}

// clearSiteListCache 清除站点列表缓存
func (sh *SiteHandler) clearSiteListCache() {
	if sh.redisCache == nil {
		return
	}
	
	// 使用模糊匹配删除所有站点列表缓存
	go func() {
		// 删除所有以 "site_list:" 开头的缓存键
		pattern := "site_list:*"
		if err := sh.redisCache.DeleteByPattern(pattern); err != nil {
			sh.logger.Warn("清除站点列表缓存失败", zap.Error(err))
		} else {
			sh.logger.Debug("已清除站点列表缓存")
		}
	}()
}

// detectProtocol 自动检测域名使用的协议
func (sh *SiteHandler) detectProtocol(domain string) string {
	// 移除可能的路径部分，只保留域名
	if idx := strings.Index(domain, "/"); idx != -1 {
		domain = domain[:idx]
	}
	
	// 先尝试HTTPS
	httpsURL := "https://" + domain
	client := &http.Client{
		Timeout: 5 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 允许重定向，但限制次数
			if len(via) >= 3 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}
	
	resp, err := client.Head(httpsURL)
	if err == nil && resp.StatusCode < 500 {
		resp.Body.Close()
		return httpsURL
	}
	if resp != nil {
		resp.Body.Close()
	}
	
	// HTTPS失败，尝试HTTP
	httpURL := "http://" + domain
	resp, err = client.Head(httpURL)
	if err == nil && resp.StatusCode < 500 {
		resp.Body.Close()
		return httpURL
	}
	if resp != nil {
		resp.Body.Close()
	}
	
	// 两个都失败，返回空字符串，让调用者使用默认值
	return ""
}

// GetSites 获取站点列表（优化版，带Redis缓存）
func (sh *SiteHandler) GetSites(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	// 支持两种参数名：limit 和 page_size，优先使用 limit
	limit := c.Query("limit")
	pageSizeStr := c.Query("page_size")
	if limit != "" {
		pageSizeStr = limit
	}
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if pageSizeStr != "" {
		if parsedPageSize, err := strconv.Atoi(pageSizeStr); err == nil {
			pageSize = parsedPageSize
		}
	}
	// 限制最大分页数量为1000
	if pageSize > 1000 {
		pageSize = 1000
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	status := c.Query("status")
	categoryID, _ := strconv.ParseUint(c.Query("category_id"), 10, 32)
	keyword := c.Query("keyword")
	sortBy := c.DefaultQuery("sort_by", "id")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	
	// 生成缓存键
	cacheKey := fmt.Sprintf("site_list:%d:%d:%s:%d:%s:%s:%s", 
		page, pageSize, status, categoryID, keyword, sortBy, sortOrder)
	
	// 尝试从Redis缓存获取
	if sh.redisCache != nil {
		if cachedData, found := sh.redisCache.Get(cacheKey); found && cachedData != "" {
			// 直接返回缓存的JSON响应
			c.Header("X-Cache", "HIT")
			c.Data(http.StatusOK, "application/json", []byte(cachedData))
			return
		}
	}
	
	sites, total, err := sh.siteService.GetSites(page, pageSize, status, uint(categoryID), keyword, sortBy, sortOrder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 批量获取sitemap数量
	if sh.sitemapService != nil && len(sites) > 0 {
		// 收集所有站点ID
		siteIDs := make([]uint, len(sites))
		for i, site := range sites {
			siteIDs[i] = site.ID
		}
		
		// 批量查询
		counts, err := sh.sitemapService.BatchGetSitemapCount(siteIDs)
		if err == nil {
			for _, site := range sites {
				if count, ok := counts[site.ID]; ok {
					site.SitemapCount = count
				}
			}
		} else {
			sh.logger.Error("批量获取sitemap数量失败", zap.Error(err))
		}
	}

	// 如果是按照sitemap_count排序，需要在应用层排序
	if sortBy == "sitemap_count" {
		sort.Slice(sites, func(i, j int) bool {
			if sortOrder == "desc" {
				return sites[i].SitemapCount > sites[j].SitemapCount
			}
			return sites[i].SitemapCount < sites[j].SitemapCount
		})
	}

	// 构建响应
	response := gin.H{
		"success": true,
		"data": gin.H{
			"sites": sites,
			"total": total,
			"page":  page,
			"page_size": pageSize,
		},
	}
	
	// 缓存响应到Redis（10秒TTL）
	if sh.redisCache != nil {
		go func() {
			if jsonData, err := json.Marshal(response); err == nil {
				if err := sh.redisCache.Set(cacheKey, string(jsonData), 10*time.Second); err != nil {
					sh.logger.Warn("缓存站点列表到Redis失败", zap.Error(err))
				}
			}
		}()
	}
	
	c.Header("X-Cache", "MISS")
	c.JSON(http.StatusOK, response)
}

// SearchSites 批量搜索站点（POST方法）
func (sh *SiteHandler) SearchSites(c *gin.Context) {
	// 定义请求结构
	var req struct {
		Keywords   []string `json:"keywords"`   // 搜索关键词列表
		Page       int      `json:"page"`       // 页码
		PageSize   int      `json:"page_size"`  // 每页大小
		Status     string   `json:"status"`     // 状态筛选
		CategoryID uint     `json:"category_id"`// 分类ID
		SortBy     string   `json:"sort_by"`    // 排序字段
		SortOrder  string   `json:"sort_order"` // 排序方向
	}
	
	// 解析请求
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}
	
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	// 限制最大分页数量为1000
	if req.PageSize > 1000 {
		req.PageSize = 1000
	}
	if req.SortBy == "" {
		req.SortBy = "id"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}
	
	// 将关键词数组转换为逗号分隔的字符串
	keyword := strings.Join(req.Keywords, ",")
	
	// 调用服务层方法
	sites, total, err := sh.siteService.GetSites(req.Page, req.PageSize, req.Status, req.CategoryID, keyword, req.SortBy, req.SortOrder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 为每个站点添加sitemap条目数量
	for _, site := range sites {
		// 获取sitemap条目数量
		if sh.sitemapService != nil {
			sitemapCount, err := sh.sitemapService.GetSitemapCount(site.ID)
			if err == nil {
				site.SitemapCount = sitemapCount
			}
		}
	}

	// 如果是按照sitemap_count排序，需要在应用层排序
	if req.SortBy == "sitemap_count" {
		sort.Slice(sites, func(i, j int) bool {
			if req.SortOrder == "desc" {
				return sites[i].SitemapCount > sites[j].SitemapCount
			}
			return sites[i].SitemapCount < sites[j].SitemapCount
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"sites":     sites,
			"total":     total,
			"page":      req.Page,
			"page_size": req.PageSize,
		},
	})
}

// GetSite 获取单个站点
func (sh *SiteHandler) GetSite(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}

	site, err := sh.siteService.GetSite(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "站点不存在",
		})
		return
	}

	// 调试日志：打印UA设置的实际值
	sh.logger.Info("获取站点详情，UA设置调试",
		zap.Uint("id", site.ID),
		zap.String("domain", site.Domain),
		zap.Bool("use_global_ua_check_is_nil", site.UseGlobalUACheck == nil),
		zap.Bool("use_global_ua_check_value", site.UseGlobalUACheck != nil && *site.UseGlobalUACheck),
		zap.Bool("enable_ua_check", site.EnableUACheck),
	)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

// CreateSite 创建站点
func (sh *SiteHandler) CreateSite(c *gin.Context) {
	var req CreateSiteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 自动检测并补充协议
	targetURL := req.TargetURL
	if !strings.HasPrefix(targetURL, "http://") && !strings.HasPrefix(targetURL, "https://") {
		// 尝试检测最佳协议
		checkedURL := sh.detectProtocol(targetURL)
		if checkedURL != "" {
			targetURL = checkedURL
		}
		// 如果检测失败，保持原样不添加协议
	}

	site := &model.Site{
		Domain:        req.Domain,
		TargetURL:     targetURL,
		CategoryID:    req.CategoryID,
		CrawlDepth:    req.CrawlDepth,
		EnableCache:   req.EnableCache,
		EnablePreload: req.EnablePreload,
		DownloadExternalResources: req.DownloadExternalResources,
		EnableHTTPSCheck:          req.EnableHTTPSCheck,
		EnableTraditionalConvert:  req.EnableTraditionalConvert,
		UseGlobalUACheck:          req.UseGlobalUACheck,
		EnableUACheck:             req.EnableUACheck,
		AllowedUA:                 req.AllowedUA,
		NonSpiderHTML:             req.NonSpiderHTML,
		EnableSpiderBlock:         req.EnableSpiderBlock,
		UseGlobalSpiderUA:         req.UseGlobalSpiderUA,
		CustomSpiderUA:            req.CustomSpiderUA,
		SpiderBlock403Template:    req.SpiderBlock403Template,
		Status:        "active",
		// 缓存配置
		UseGlobalCache:            req.UseGlobalCache,
		CacheMaxSize:              int64(req.CacheMaxSize),
		CacheHomeTTL:              req.CacheHomeTTL,
		CacheOtherTTL:             req.CacheOtherTTL,
		EnableRedisCache:          req.EnableRedisCache,
		// Sitemap配置
		EnableSitemap:             req.EnableSitemap,
		SitemapUpdateInterval:     req.SitemapUpdateInterval,
		SitemapChangefreq:         req.SitemapChangefreq,
		SitemapPriority:           req.SitemapPriority,
		SitemapMaxUrls:            req.SitemapMaxUrls,
		// 域名跳转配置
		RedirectWWW:               req.RedirectWWW,
		// 企业名称配置
		EnableCompanyName:         req.EnableCompanyName,
		CompanyLibraryID:          req.CompanyLibraryID,
		CompanyName:               req.CompanyName,
		// 来源判断配置
		UseGlobalRefererCheck:     req.UseGlobalRefererCheck,
		EnableRefererCheck:        req.EnableRefererCheck,
		AllowedReferers:           req.AllowedReferers,
		RefererBlockCode:          req.RefererBlockCode,
		RefererBlockHTML:          req.RefererBlockHTML,
	}

	// 创建注入配置
	if req.InjectConfig != nil {
		site.InjectConfig = &model.InjectConfig{
			EnableKeyword:   req.InjectConfig.EnableKeyword,
			EnableStructure: req.InjectConfig.EnableStructure,
			EnablePseudo:    req.InjectConfig.EnablePseudo,
			FilterExternalLinks: req.InjectConfig.FilterExternalLinks,
			Keywords:        req.InjectConfig.Keywords,
			TitleTemplate:   req.InjectConfig.TitleTemplate,
			MetaTemplate:    req.InjectConfig.MetaTemplate,
			HomeTitle:       req.InjectConfig.HomeTitle,
			HomeDescription: req.InjectConfig.HomeDescription,
			HomeKeywords:    req.InjectConfig.HomeKeywords,
			EnableUnicode:   req.InjectConfig.EnableUnicode,
			// Unicode转码细粒度控制
			UnicodeScope:           req.InjectConfig.UnicodeScope,
			EnableUnicodeTitle:     req.InjectConfig.EnableUnicodeTitle,
			EnableUnicodeKeywords:  req.InjectConfig.EnableUnicodeKeywords,
			EnableUnicodeDesc:      req.InjectConfig.EnableUnicodeDesc,
			EnableRandomString: req.InjectConfig.EnableRandomString,
			RandomStringLength: req.InjectConfig.RandomStringLength,
			// 拼音配置
			UseGlobalPinyin:          req.InjectConfig.UseGlobalPinyin,
			EnablePinyin:             req.InjectConfig.EnablePinyin,
			EnablePinyinSpecialChars: req.InjectConfig.EnablePinyinSpecialChars,
			PinyinSpecialCharsRatio:  req.InjectConfig.PinyinSpecialCharsRatio,
			EnableHiddenHTML:   req.InjectConfig.EnableHiddenHTML,
			HiddenHTMLLength:   req.InjectConfig.HiddenHTMLLength,
			HiddenHTMLRandomID: req.InjectConfig.HiddenHTMLRandomID,
			HiddenHTMLPosition: req.InjectConfig.HiddenHTMLPosition,
			PseudoLibraryIDs:   req.InjectConfig.PseudoLibraryIDs,
			KeywordLibraryIDs:  req.InjectConfig.KeywordLibraryIDs,
			TitleKeywordLibraryIDs: req.InjectConfig.TitleKeywordLibraryIDs,
			MetaKeywordLibraryIDs:  req.InjectConfig.MetaKeywordLibraryIDs,
			DescKeywordLibraryIDs:  req.InjectConfig.DescKeywordLibraryIDs,
			// 结构注入配置
			StructureLibraryIDs: req.InjectConfig.StructureLibraryIDs,
			StructureMinPerPage: req.InjectConfig.StructureMinPerPage,
			StructureMaxPerPage: req.InjectConfig.StructureMaxPerPage,
			// 关键词注入详细配置
			KeywordInjectTitle:      req.InjectConfig.KeywordInjectTitle,
			KeywordInjectMeta:       req.InjectConfig.KeywordInjectMeta,
			KeywordInjectDesc:       req.InjectConfig.KeywordInjectDesc,
			KeywordInjectBody:       req.InjectConfig.KeywordInjectBody,
			KeywordInjectHidden:     req.InjectConfig.KeywordInjectHidden,
			KeywordInjectH1:         req.InjectConfig.KeywordInjectH1,
			KeywordInjectH2:         req.InjectConfig.KeywordInjectH2,
			KeywordInjectAlt:        req.InjectConfig.KeywordInjectAlt,
			KeywordMaxPerPage:       req.InjectConfig.KeywordMaxPerPage,
			KeywordInjectRatio:      req.InjectConfig.KeywordInjectRatio,
			KeywordMinWordCount:     req.InjectConfig.KeywordMinWordCount,
			KeywordDensity:          req.InjectConfig.KeywordDensity,
			KeywordTitleTemplate:    req.InjectConfig.KeywordTitleTemplate,
			KeywordMetaTemplate:     req.InjectConfig.KeywordMetaTemplate,
			KeywordDescTemplate:     req.InjectConfig.KeywordDescTemplate,
			// H1标签注入配置
			EnableH1Tag:     req.InjectConfig.EnableH1Tag,
			H1TagPosition:   req.InjectConfig.H1TagPosition,
			// 首页关键词注入配置
			EnableHomeKeywordInject:     req.InjectConfig.EnableHomeKeywordInject,
			HomeKeywordLibraryIDs:       req.InjectConfig.HomeKeywordLibraryIDs,
			HomeKeywordInjectCount:      req.InjectConfig.HomeKeywordInjectCount,
			EnableHomeKeywordUnicode:    req.InjectConfig.EnableHomeKeywordUnicode,
			// 统计代码
			AnalyticsCode:   req.InjectConfig.AnalyticsCode,
		}
	}

	// 如果有子域名，则使用带子域名的创建方法
	if len(req.AliasPrefixes) > 0 {
		if err := sh.siteService.CreateSiteWithAliases(site, req.AliasPrefixes); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	} else {
		if err := sh.siteService.CreateSite(site); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	// 清除站点列表缓存
	sh.clearSiteListCache()
	
	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    site,
		"message": "站点创建成功",
	})
}

// UpdateSite 更新站点
func (sh *SiteHandler) UpdateSite(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}

	var req UpdateSiteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	site, err := sh.siteService.GetSite(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "站点不存在",
		})
		return
	}

	// 更新字段
	if req.Domain != "" {
		site.Domain = req.Domain
	}
	
	// 检查目标URL是否变化，如果变化了需要重置缓存状态
	targetURLChanged := false
	if req.TargetURL != "" && req.TargetURL != site.TargetURL {
		targetURLChanged = true
		oldURL := site.TargetURL
		
		// 自动检测并补充协议
		targetURL := req.TargetURL
		if !strings.HasPrefix(targetURL, "http://") && !strings.HasPrefix(targetURL, "https://") {
			// 尝试检测最佳协议
			checkedURL := sh.detectProtocol(targetURL)
			if checkedURL != "" {
				targetURL = checkedURL
			}
			// 如果检测失败，保持原样不添加协议
		}
		site.TargetURL = targetURL
		// 重置缓存状态为待获取
		site.CacheStatus = "pending"
		site.CacheError = ""
		site.CacheUpdateAt = time.Now()
		
		sh.logger.Info("目标URL已变更，重置缓存状态",
			zap.Uint("site_id", site.ID),
			zap.String("old_url", oldURL),
			zap.String("new_url", req.TargetURL),
		)
	}
	// 更新分类ID（可以设置为null）
	site.CategoryID = req.CategoryID
	if req.CrawlDepth > 0 {
		site.CrawlDepth = req.CrawlDepth
	}
	site.EnableCache = req.EnableCache
	site.EnablePreload = req.EnablePreload
	site.DownloadExternalResources = req.DownloadExternalResources
	site.EnableHTTPSCheck = req.EnableHTTPSCheck
	site.EnableTraditionalConvert = req.EnableTraditionalConvert
	// UA判断三态控制
	site.UseGlobalUACheck = req.UseGlobalUACheck
	site.EnableUACheck = req.EnableUACheck
	site.AllowedUA = req.AllowedUA
	site.NonSpiderHTML = req.NonSpiderHTML
	
	// 调试日志：打印更新的UA设置
	sh.logger.Info("更新站点UA设置",
		zap.Uint("id", site.ID),
		zap.String("domain", site.Domain),
		zap.Bool("req_use_global_ua_check_is_nil", req.UseGlobalUACheck == nil),
		zap.Bool("req_use_global_ua_check_value", req.UseGlobalUACheck != nil && *req.UseGlobalUACheck),
		zap.Bool("req_enable_ua_check", req.EnableUACheck),
		zap.Bool("site_use_global_ua_check_is_nil", site.UseGlobalUACheck == nil),
		zap.Bool("site_use_global_ua_check_value", site.UseGlobalUACheck != nil && *site.UseGlobalUACheck),
		zap.Bool("site_enable_ua_check", site.EnableUACheck),
	)
	site.EnableSpiderBlock = req.EnableSpiderBlock
	site.UseGlobalSpiderUA = req.UseGlobalSpiderUA
	site.CustomSpiderUA = req.CustomSpiderUA
	site.SpiderBlock403Template = req.SpiderBlock403Template
	
	// 调试日志
	fmt.Printf("DEBUG UpdateSite: req.CustomSpiderUA='%s', site.CustomSpiderUA='%s'\n", req.CustomSpiderUA, site.CustomSpiderUA)
	if req.Status != "" {
		site.Status = req.Status
	}
	// 更新缓存配置
	site.UseGlobalCache = req.UseGlobalCache
	if req.CacheMaxSize > 0 {
		site.CacheMaxSize = int64(req.CacheMaxSize)
	}
	if req.CacheHomeTTL > 0 {
		site.CacheHomeTTL = req.CacheHomeTTL
	}
	if req.CacheOtherTTL > 0 {
		site.CacheOtherTTL = req.CacheOtherTTL
	}
	site.EnableRedisCache = req.EnableRedisCache
	
	// 更新Sitemap配置
	site.EnableSitemap = req.EnableSitemap
	if req.SitemapUpdateInterval > 0 {
		site.SitemapUpdateInterval = req.SitemapUpdateInterval
	}
	if req.SitemapChangefreq != "" {
		site.SitemapChangefreq = req.SitemapChangefreq
	}
	if req.SitemapPriority > 0 {
		site.SitemapPriority = req.SitemapPriority
	}
	if req.SitemapMaxUrls > 0 {
		site.SitemapMaxUrls = req.SitemapMaxUrls
	}
	
	// 更新域名跳转配置
	site.RedirectWWW = req.RedirectWWW
	
	// 更新企业名称配置
	site.EnableCompanyName = req.EnableCompanyName
	site.CompanyLibraryID = req.CompanyLibraryID
	// 如果提供了企业名称，使用提供的；否则保留原有的
	if req.CompanyName != "" {
		site.CompanyName = req.CompanyName
	}
	
	// 更新来源判断配置
	sh.logger.Info("更新来源判断配置",
		zap.Uint("site_id", site.ID),
		zap.Bool("req_enable_referer_check", req.EnableRefererCheck),
		zap.Bool("req_use_global_referer_check_nil", req.UseGlobalRefererCheck == nil),
		zap.Bool("req_use_global_referer_check_value", req.UseGlobalRefererCheck != nil && *req.UseGlobalRefererCheck),
		zap.String("req_allowed_referers", req.AllowedReferers),
		zap.Int("req_referer_block_code", req.RefererBlockCode),
	)
	site.UseGlobalRefererCheck = req.UseGlobalRefererCheck
	site.EnableRefererCheck = req.EnableRefererCheck
	site.AllowedReferers = req.AllowedReferers
	if req.RefererBlockCode > 0 {
		site.RefererBlockCode = req.RefererBlockCode
	}
	site.RefererBlockHTML = req.RefererBlockHTML

	// 更新统计设置配置
	site.UseGlobalAnalytics = req.UseGlobalAnalytics
	site.EnableAnalytics = req.EnableAnalytics

	// 更新注入配置
	if req.InjectConfig != nil {
		if site.InjectConfig == nil {
			site.InjectConfig = &model.InjectConfig{SiteID: site.ID}
		}
		site.InjectConfig.EnableKeyword = req.InjectConfig.EnableKeyword
		site.InjectConfig.EnableStructure = req.InjectConfig.EnableStructure
		site.InjectConfig.EnablePseudo = req.InjectConfig.EnablePseudo
		site.InjectConfig.FilterExternalLinks = req.InjectConfig.FilterExternalLinks
		site.InjectConfig.Keywords = req.InjectConfig.Keywords
		site.InjectConfig.TitleTemplate = req.InjectConfig.TitleTemplate
		site.InjectConfig.MetaTemplate = req.InjectConfig.MetaTemplate
		site.InjectConfig.HomeTitle = req.InjectConfig.HomeTitle
		site.InjectConfig.HomeDescription = req.InjectConfig.HomeDescription
		site.InjectConfig.HomeKeywords = req.InjectConfig.HomeKeywords
		site.InjectConfig.EnableUnicode = req.InjectConfig.EnableUnicode
		// Unicode转码细粒度控制
		site.InjectConfig.UnicodeScope = req.InjectConfig.UnicodeScope
		site.InjectConfig.EnableUnicodeTitle = req.InjectConfig.EnableUnicodeTitle
		site.InjectConfig.EnableUnicodeKeywords = req.InjectConfig.EnableUnicodeKeywords
		site.InjectConfig.EnableUnicodeDesc = req.InjectConfig.EnableUnicodeDesc
		site.InjectConfig.EnableRandomString = req.InjectConfig.EnableRandomString
		site.InjectConfig.RandomStringLength = req.InjectConfig.RandomStringLength
		// 拼音配置
		sh.logger.Info("更新拼音设置",
			zap.Bool("req_enable_pinyin", req.InjectConfig.EnablePinyin),
			zap.Bool("req_enable_special_chars", req.InjectConfig.EnablePinyinSpecialChars),
			zap.Float32("req_ratio", req.InjectConfig.PinyinSpecialCharsRatio),
			zap.Bool("old_enable_pinyin", site.InjectConfig.EnablePinyin),
			zap.Bool("old_enable_special_chars", site.InjectConfig.EnablePinyinSpecialChars),
			zap.Float32("old_ratio", site.InjectConfig.PinyinSpecialCharsRatio),
		)
		// 处理UseGlobalPinyin的三种状态：
		// nil = 使用全局设置
		// false = 独立关闭
		// true = 独立开启（注意：这里true表示独立开启，不是使用全局）
		site.InjectConfig.UseGlobalPinyin = req.InjectConfig.UseGlobalPinyin
		site.InjectConfig.EnablePinyin = req.InjectConfig.EnablePinyin
		site.InjectConfig.EnablePinyinSpecialChars = req.InjectConfig.EnablePinyinSpecialChars
		site.InjectConfig.PinyinSpecialCharsRatio = req.InjectConfig.PinyinSpecialCharsRatio
		site.InjectConfig.EnableHiddenHTML = req.InjectConfig.EnableHiddenHTML
		site.InjectConfig.HiddenHTMLLength = req.InjectConfig.HiddenHTMLLength
		site.InjectConfig.HiddenHTMLRandomID = req.InjectConfig.HiddenHTMLRandomID
		site.InjectConfig.HiddenHTMLPosition = req.InjectConfig.HiddenHTMLPosition
		site.InjectConfig.PseudoLibraryIDs = req.InjectConfig.PseudoLibraryIDs
		site.InjectConfig.KeywordLibraryIDs = req.InjectConfig.KeywordLibraryIDs
		site.InjectConfig.TitleKeywordLibraryIDs = req.InjectConfig.TitleKeywordLibraryIDs
		site.InjectConfig.MetaKeywordLibraryIDs = req.InjectConfig.MetaKeywordLibraryIDs
		site.InjectConfig.DescKeywordLibraryIDs = req.InjectConfig.DescKeywordLibraryIDs
		// 结构注入配置
		site.InjectConfig.StructureLibraryIDs = req.InjectConfig.StructureLibraryIDs
		site.InjectConfig.StructureMinPerPage = req.InjectConfig.StructureMinPerPage
		site.InjectConfig.StructureMaxPerPage = req.InjectConfig.StructureMaxPerPage
		// 关键词注入详细配置
		site.InjectConfig.KeywordInjectTitle = req.InjectConfig.KeywordInjectTitle
		site.InjectConfig.KeywordInjectMeta = req.InjectConfig.KeywordInjectMeta
		site.InjectConfig.KeywordInjectDesc = req.InjectConfig.KeywordInjectDesc
		site.InjectConfig.KeywordInjectBody = req.InjectConfig.KeywordInjectBody
		site.InjectConfig.KeywordInjectHidden = req.InjectConfig.KeywordInjectHidden
		site.InjectConfig.KeywordInjectH1 = req.InjectConfig.KeywordInjectH1
		site.InjectConfig.KeywordInjectH2 = req.InjectConfig.KeywordInjectH2
		site.InjectConfig.KeywordInjectAlt = req.InjectConfig.KeywordInjectAlt
		site.InjectConfig.KeywordMaxPerPage = req.InjectConfig.KeywordMaxPerPage
		site.InjectConfig.KeywordInjectRatio = req.InjectConfig.KeywordInjectRatio
		site.InjectConfig.KeywordMinWordCount = req.InjectConfig.KeywordMinWordCount
		site.InjectConfig.KeywordDensity = req.InjectConfig.KeywordDensity
		site.InjectConfig.KeywordTitleTemplate = req.InjectConfig.KeywordTitleTemplate
		site.InjectConfig.KeywordMetaTemplate = req.InjectConfig.KeywordMetaTemplate
		site.InjectConfig.KeywordDescTemplate = req.InjectConfig.KeywordDescTemplate
		// H1标签注入配置
		site.InjectConfig.EnableH1Tag = req.InjectConfig.EnableH1Tag
		site.InjectConfig.H1TagPosition = req.InjectConfig.H1TagPosition
		// 首页关键词注入配置
		site.InjectConfig.EnableHomeKeywordInject = req.InjectConfig.EnableHomeKeywordInject
		site.InjectConfig.HomeKeywordLibraryIDs = req.InjectConfig.HomeKeywordLibraryIDs
		site.InjectConfig.HomeKeywordInjectCount = req.InjectConfig.HomeKeywordInjectCount
		site.InjectConfig.EnableHomeKeywordUnicode = req.InjectConfig.EnableHomeKeywordUnicode
		// 统计代码
		site.InjectConfig.AnalyticsCode = req.InjectConfig.AnalyticsCode
	}

	if err := sh.siteService.UpdateSite(site); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	// 如果提供了子域名列表，更新子域名
	if req.AliasPrefixes != nil {
		if err := sh.siteService.UpdateSiteAliases(site.ID, req.AliasPrefixes); err != nil {
			sh.logger.Error("更新站点子域名失败", 
				zap.Uint("site_id", site.ID),
				zap.Error(err))
			// 不影响主体更新，只记录错误
		}
	}
	
	// 清理Redis中的站点配置缓存（同步执行，确保立即生效）
	if sh.optimizedMirrorHandler != nil {
		sh.optimizedMirrorHandler.ClearSiteConfigCache(site.Domain)
		sh.logger.Info("清理站点配置缓存",
			zap.String("domain", site.Domain))
	}
	
	// 如果目标URL发生了变化，清理所有缓存
	if targetURLChanged {
		// 重置缓存状态
		site.CacheStatus = "pending"
		site.CacheError = ""
		site.CacheUpdateAt = time.Now()
		
		// 清理文件缓存和Redis缓存（内存缓存已在上面清理）
		if sh.cacheService != nil {
			go func() {
				if deleted, err := sh.cacheService.ClearSiteCache(site.Domain); err != nil {
					sh.logger.Error("清理文件缓存失败",
						zap.String("domain", site.Domain),
						zap.Error(err))
				} else {
					sh.logger.Info("清理文件缓存成功",
						zap.String("domain", site.Domain),
						zap.Int("deleted_files", deleted))
				}
			}()
		}
		
		// 清理Redis缓存
		if sh.redisCache != nil {
			go func() {
				// 清理HTML缓存
				pattern := fmt.Sprintf("html:*%s*", site.Domain)
				if err := sh.redisCache.DeleteByPattern(pattern); err != nil {
					sh.logger.Error("清理Redis HTML缓存失败",
						zap.String("domain", site.Domain),
						zap.String("pattern", pattern),
						zap.Error(err))
				} else {
					sh.logger.Info("清理Redis HTML缓存",
						zap.String("domain", site.Domain),
						zap.String("pattern", pattern))
				}
				
				// 清理站点配置缓存 - 注意：Redis中的键是 site:{domain}
				configKey := fmt.Sprintf("site:%s", site.Domain)
				if err := sh.redisCache.Delete(configKey); err != nil {
					sh.logger.Error("清理Redis站点配置失败",
						zap.String("domain", site.Domain),
						zap.String("key", configKey),
						zap.Error(err))
				} else {
					sh.logger.Info("清理Redis站点配置成功",
						zap.String("domain", site.Domain),
						zap.String("key", configKey))
				}
			}()
		}
		
		sh.logger.Info("目标URL变更：重置缓存状态并清理所有缓存",
			zap.Uint("site_id", site.ID),
			zap.String("domain", site.Domain),
			zap.String("new_target_url", site.TargetURL),
		)
		
		// 再次保存以更新缓存状态
		if err := sh.siteService.UpdateSite(site); err != nil {
			sh.logger.Error("更新缓存状态失败", zap.Error(err))
		}
	}

	// 清除站点列表缓存
	sh.clearSiteListCache()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
		"message": "站点更新成功",
	})
}

// DeleteSite 删除站点
func (sh *SiteHandler) DeleteSite(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}

	// 获取站点信息以便清理缓存
	site, _ := sh.siteService.GetSite(uint(id))
	
	if err := sh.siteService.DeleteSite(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 清理所有缓存（文件缓存、Redis缓存、站点配置缓存）
	if site != nil {
		sh.clearDomainAllCache(site.Domain)
		
		// 也清理子域名的缓存
		if site.Aliases != nil && len(site.Aliases) > 0 {
			for _, alias := range site.Aliases {
				sh.clearDomainAllCache(alias.AliasDomain)
			}
		}
	}
	
	// 清除站点列表缓存
	sh.clearSiteListCache()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "站点删除成功",
	})
}

// BatchDeleteSites 批量删除站点
func (sh *SiteHandler) BatchDeleteSites(c *gin.Context) {
	var req struct {
		IDs []uint `json:"ids" binding:"required,min=1"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请选择要删除的站点",
		})
		return
	}

	// 获取所有要删除的站点信息（包括子域名）
	var sitesToClear []*model.Site
	for _, id := range req.IDs {
		if site, err := sh.siteService.GetSite(id); err == nil && site != nil {
			sitesToClear = append(sitesToClear, site)
		}
	}

	// 批量删除站点
	deletedCount, err := sh.siteService.BatchDeleteSites(req.IDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 异步清理所有缓存
	if len(sitesToClear) > 0 {
		go func() {
			for _, site := range sitesToClear {
				// 清理主域名缓存
				sh.clearDomainAllCache(site.Domain)
				
				// 清理子域名缓存
				if site.Aliases != nil && len(site.Aliases) > 0 {
					for _, alias := range site.Aliases {
						sh.clearDomainAllCache(alias.AliasDomain)
					}
				}
			}
		}()
	}
	
	// 清除站点列表缓存
	sh.clearSiteListCache()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("成功删除 %d 个站点", deletedCount),
		"data": gin.H{
			"deleted_count": deletedCount,
		},
	})
}

// TriggerCrawl 触发爬取
func (sh *SiteHandler) TriggerCrawl(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}

	job, err := sh.siteService.TriggerCrawl(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    job,
		"message": "爬取任务已创建",
	})
}

// GetSiteStats 获取站点统计
func (sh *SiteHandler) GetSiteStats(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}

	stats, err := sh.siteService.GetSiteStats(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// BatchCreateSitesWithAliases 批量创建站点（支持子域名格式）
func (sh *SiteHandler) BatchCreateSitesWithAliases(c *gin.Context) {
	var req BatchCreateSitesWithAliasesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	successCount := 0
	failedCount := 0
	var errors []string

	for _, line := range req.Lines {
		// 解析格式：子域名前缀,主域名|目标URL|站点名称|描述|关键词
		siteData, aliasPrefixes, err := sh.parseBatchLine(line)
		if err != nil {
			failedCount++
			errors = append(errors, line+": "+err.Error())
			continue
		}

		// 应用请求中的配置到站点
		if req.CategoryID != nil {
			siteData.CategoryID = req.CategoryID
		}
		if req.CrawlDepth > 0 {
			siteData.CrawlDepth = req.CrawlDepth
		}
		if req.Status != "" {
			siteData.Status = req.Status
		}
		siteData.EnableCache = req.EnableCache
		// CacheTTL 在 model 中没有对应字段，跳过
		if req.MaxCacheSize > 0 {
			siteData.CacheMaxSize = int64(req.MaxCacheSize)
		}
		if req.CacheHomeTTL > 0 {
			siteData.CacheHomeTTL = req.CacheHomeTTL
		}
		if req.CacheOtherTTL > 0 {
			siteData.CacheOtherTTL = req.CacheOtherTTL
		}
		siteData.EnableRedisCache = req.EnableRedisCache
		if req.UseGlobalCache != nil {
			siteData.UseGlobalCache = *req.UseGlobalCache
		}
		siteData.EnablePreload = req.EnablePreload
		siteData.DownloadExternalResources = req.DownloadExternalResources
		siteData.EnableTraditionalConvert = req.EnableTraditionalConvert
		siteData.EnableHTTPSCheck = req.EnableHTTPSCheck
		siteData.EnableAnalytics = req.EnableAnalytics
		// AnalyticsCode 在 InjectConfig 中处理
		siteData.UseGlobalAnalytics = req.UseGlobalAnalytics
		siteData.EnableSitemap = req.EnableSitemap
		siteData.SitemapUpdateInterval = req.SitemapUpdateInterval
		siteData.SitemapChangefreq = req.SitemapChangefreq
		siteData.SitemapPriority = req.SitemapPriority
		siteData.SitemapMaxUrls = req.SitemapMaxUrls
		siteData.EnableCompanyName = req.EnableCompanyName
		if req.CompanyLibraryID != nil {
			siteData.CompanyLibraryID = *req.CompanyLibraryID
		}
		siteData.UseGlobalUACheck = req.UseGlobalUACheck
		siteData.EnableUACheck = req.EnableUACheck
		siteData.AllowedUA = req.AllowedUA
		siteData.NonSpiderHTML = req.NonSpiderHTML
		siteData.EnableSpiderBlock = req.EnableSpiderBlock
		siteData.UseGlobalSpiderUA = req.UseGlobalSpiderUA
		siteData.CustomSpiderUA = req.CustomSpiderUA
		siteData.SpiderBlock403Template = req.SpiderBlock403Template
		siteData.UseGlobalRefererCheck = req.UseGlobalRefererCheck
		siteData.EnableRefererCheck = req.EnableRefererCheck
		// 处理允许的来源字段（支持两个字段名）
		if req.AllowedReferer != "" {
			siteData.AllowedReferers = req.AllowedReferer
		} else if req.AllowedReferers != "" {
			siteData.AllowedReferers = req.AllowedReferers
		}
		if req.RefererBlockCode > 0 {
			siteData.RefererBlockCode = req.RefererBlockCode
		} else {
			siteData.RefererBlockCode = 403 // 默认403
		}
		if req.RefererBlockHTML != "" {
			siteData.RefererBlockHTML = req.RefererBlockHTML
		} else if req.NonRefererHTML != "" {
			siteData.RefererBlockHTML = req.NonRefererHTML
		}
		siteData.RedirectWWW = req.RedirectWWW
		
		// 如果有注入配置，则合并
		if req.InjectConfig != nil {
			if siteData.InjectConfig == nil {
				// 如果站点没有内联配置，直接使用请求的配置
				siteData.InjectConfig = &model.InjectConfig{
					EnableKeyword:             req.InjectConfig.EnableKeyword,
					EnableStructure:           req.InjectConfig.EnableStructure,
					EnablePseudo:              req.InjectConfig.EnablePseudo,
					FilterExternalLinks:       req.InjectConfig.FilterExternalLinks,
					HomeTitle:                 req.InjectConfig.HomeTitle,
					HomeDescription:           req.InjectConfig.HomeDescription,
					HomeKeywords:              req.InjectConfig.HomeKeywords,
					EnableUnicode:             req.InjectConfig.EnableUnicode,
					UnicodeScope:              req.InjectConfig.UnicodeScope,
					EnableUnicodeTitle:        req.InjectConfig.EnableUnicodeTitle,
					EnableUnicodeKeywords:     req.InjectConfig.EnableUnicodeKeywords,
					EnableUnicodeDesc:         req.InjectConfig.EnableUnicodeDesc,
					EnableRandomString:        req.InjectConfig.EnableRandomString,
					RandomStringLength:        req.InjectConfig.RandomStringLength,
					RandomStringPrefix:        req.InjectConfig.RandomStringPrefix,
					EnablePinyin:              req.InjectConfig.EnablePinyin,
					EnablePinyinSpecialChars:  req.InjectConfig.EnablePinyinSpecialChars,
					PinyinSpecialCharsRatio:   req.InjectConfig.PinyinSpecialCharsRatio,
					PinyinSpecialChars:        req.InjectConfig.PinyinSpecialChars,
					KeywordInjectTitle:        req.InjectConfig.KeywordInjectTitle,
					KeywordInjectMeta:         req.InjectConfig.KeywordInjectMeta,
					KeywordInjectDesc:         req.InjectConfig.KeywordInjectDesc,
					KeywordInjectBody:         req.InjectConfig.KeywordInjectBody,
					KeywordInjectHidden:       req.InjectConfig.KeywordInjectHidden,
					KeywordMaxPerPage:         req.InjectConfig.KeywordMaxPerPage,
					KeywordDensity:            req.InjectConfig.KeywordDensity,
					KeywordLibraryIDs:         req.InjectConfig.KeywordLibraryIDs,
					PseudoLibraryIDs:          req.InjectConfig.PseudoLibraryIDs,
					EnableHiddenHTML:          req.InjectConfig.EnableHiddenHTML,
					HiddenHTMLLength:          req.InjectConfig.HiddenHTMLLength,
					HiddenHTMLRandomID:        req.InjectConfig.HiddenHTMLRandomID,
					HiddenHTMLPosition:        req.InjectConfig.HiddenHTMLPosition,
					// 补充缺失的字段
					StructureMinPerPage:       req.InjectConfig.StructureMinPerPage,
					StructureMaxPerPage:       req.InjectConfig.StructureMaxPerPage,
					EnableH1Tag:               req.InjectConfig.EnableH1Tag,
					H1TagPosition:             req.InjectConfig.H1TagPosition,
					EnableHomeKeywordInject:   req.InjectConfig.EnableHomeKeywordInject,
					HomeKeywordInjectCount:    req.InjectConfig.HomeKeywordInjectCount,
					EnableHomeKeywordUnicode:  req.InjectConfig.EnableHomeKeywordUnicode,
					HomeKeywordLibraryIDs:     req.InjectConfig.HomeKeywordLibraryIDs,
					KeywordInjectH1:           req.InjectConfig.KeywordInjectH1,
					KeywordInjectH2:           req.InjectConfig.KeywordInjectH2,
					KeywordInjectAlt:          req.InjectConfig.KeywordInjectAlt,
					KeywordInjectRatio:        req.InjectConfig.KeywordInjectRatio,
					KeywordMinWordCount:       req.InjectConfig.KeywordMinWordCount,
					KeywordTitleTemplate:      req.InjectConfig.KeywordTitleTemplate,
					KeywordMetaTemplate:       req.InjectConfig.KeywordMetaTemplate,
					KeywordDescTemplate:       req.InjectConfig.KeywordDescTemplate,
					AnalyticsCode:             func() string {
						if req.AnalyticsCode != "" {
							return req.AnalyticsCode
						}
						return req.InjectConfig.AnalyticsCode
					}(),
					StructureLibraryIDs:       req.InjectConfig.StructureLibraryIDs,
					TitleKeywordLibraryIDs:    req.InjectConfig.TitleKeywordLibraryIDs,
					MetaKeywordLibraryIDs:     req.InjectConfig.MetaKeywordLibraryIDs,
					DescKeywordLibraryIDs:     req.InjectConfig.DescKeywordLibraryIDs,
					BodyKeywordLibraryIDs:     req.InjectConfig.BodyKeywordLibraryIDs,
				}
				// 处理UseGlobalPinyin指针字段
				if req.InjectConfig.UseGlobalPinyin != nil {
					useGlobal := *req.InjectConfig.UseGlobalPinyin
					siteData.InjectConfig.UseGlobalPinyin = &useGlobal
				}
			} else {
				// 如果站点有内联配置（从批量数据中解析的标题/描述/关键词），需要合并所有其他配置
				// 保留站点已有的HomeTitle, HomeDescription, HomeKeywords，但应用所有其他配置
				existingTitle := siteData.InjectConfig.HomeTitle
				existingDesc := siteData.InjectConfig.HomeDescription
				existingKeywords := siteData.InjectConfig.HomeKeywords
				
				// 复制所有请求配置
				siteData.InjectConfig.EnableKeyword = req.InjectConfig.EnableKeyword
				siteData.InjectConfig.EnableStructure = req.InjectConfig.EnableStructure
				siteData.InjectConfig.EnablePseudo = req.InjectConfig.EnablePseudo
				siteData.InjectConfig.FilterExternalLinks = req.InjectConfig.FilterExternalLinks
				siteData.InjectConfig.EnableUnicode = req.InjectConfig.EnableUnicode
				siteData.InjectConfig.UnicodeScope = req.InjectConfig.UnicodeScope
				siteData.InjectConfig.EnableUnicodeTitle = req.InjectConfig.EnableUnicodeTitle
				siteData.InjectConfig.EnableUnicodeKeywords = req.InjectConfig.EnableUnicodeKeywords
				siteData.InjectConfig.EnableUnicodeDesc = req.InjectConfig.EnableUnicodeDesc
				siteData.InjectConfig.EnableRandomString = req.InjectConfig.EnableRandomString
				siteData.InjectConfig.RandomStringLength = req.InjectConfig.RandomStringLength
				siteData.InjectConfig.RandomStringPrefix = req.InjectConfig.RandomStringPrefix
				siteData.InjectConfig.EnablePinyin = req.InjectConfig.EnablePinyin
				siteData.InjectConfig.EnablePinyinSpecialChars = req.InjectConfig.EnablePinyinSpecialChars
				siteData.InjectConfig.PinyinSpecialCharsRatio = req.InjectConfig.PinyinSpecialCharsRatio
				siteData.InjectConfig.PinyinSpecialChars = req.InjectConfig.PinyinSpecialChars
				siteData.InjectConfig.KeywordInjectTitle = req.InjectConfig.KeywordInjectTitle
				siteData.InjectConfig.KeywordInjectMeta = req.InjectConfig.KeywordInjectMeta
				siteData.InjectConfig.KeywordInjectDesc = req.InjectConfig.KeywordInjectDesc
				siteData.InjectConfig.KeywordInjectBody = req.InjectConfig.KeywordInjectBody
				siteData.InjectConfig.KeywordInjectHidden = req.InjectConfig.KeywordInjectHidden
				siteData.InjectConfig.KeywordMaxPerPage = req.InjectConfig.KeywordMaxPerPage
				siteData.InjectConfig.KeywordDensity = req.InjectConfig.KeywordDensity
				siteData.InjectConfig.KeywordLibraryIDs = req.InjectConfig.KeywordLibraryIDs
				siteData.InjectConfig.PseudoLibraryIDs = req.InjectConfig.PseudoLibraryIDs
				siteData.InjectConfig.EnableHiddenHTML = req.InjectConfig.EnableHiddenHTML
				siteData.InjectConfig.HiddenHTMLLength = req.InjectConfig.HiddenHTMLLength
				siteData.InjectConfig.HiddenHTMLRandomID = req.InjectConfig.HiddenHTMLRandomID
				siteData.InjectConfig.HiddenHTMLPosition = req.InjectConfig.HiddenHTMLPosition
				
				// 补充缺失的字段
				siteData.InjectConfig.StructureMinPerPage = req.InjectConfig.StructureMinPerPage
				siteData.InjectConfig.StructureMaxPerPage = req.InjectConfig.StructureMaxPerPage
				siteData.InjectConfig.EnableH1Tag = req.InjectConfig.EnableH1Tag
				siteData.InjectConfig.H1TagPosition = req.InjectConfig.H1TagPosition
				siteData.InjectConfig.EnableHomeKeywordInject = req.InjectConfig.EnableHomeKeywordInject
				siteData.InjectConfig.HomeKeywordInjectCount = req.InjectConfig.HomeKeywordInjectCount
				siteData.InjectConfig.EnableHomeKeywordUnicode = req.InjectConfig.EnableHomeKeywordUnicode
				siteData.InjectConfig.HomeKeywordLibraryIDs = req.InjectConfig.HomeKeywordLibraryIDs
				siteData.InjectConfig.KeywordInjectH1 = req.InjectConfig.KeywordInjectH1
				siteData.InjectConfig.KeywordInjectH2 = req.InjectConfig.KeywordInjectH2
				siteData.InjectConfig.KeywordInjectAlt = req.InjectConfig.KeywordInjectAlt
				siteData.InjectConfig.KeywordInjectRatio = req.InjectConfig.KeywordInjectRatio
				siteData.InjectConfig.KeywordMinWordCount = req.InjectConfig.KeywordMinWordCount
				siteData.InjectConfig.KeywordTitleTemplate = req.InjectConfig.KeywordTitleTemplate
				siteData.InjectConfig.KeywordMetaTemplate = req.InjectConfig.KeywordMetaTemplate
				siteData.InjectConfig.KeywordDescTemplate = req.InjectConfig.KeywordDescTemplate
				// 优先使用请求中的 AnalyticsCode
				if req.AnalyticsCode != "" {
					siteData.InjectConfig.AnalyticsCode = req.AnalyticsCode
				} else {
					siteData.InjectConfig.AnalyticsCode = req.InjectConfig.AnalyticsCode
				}
				siteData.InjectConfig.StructureLibraryIDs = req.InjectConfig.StructureLibraryIDs
				siteData.InjectConfig.TitleKeywordLibraryIDs = req.InjectConfig.TitleKeywordLibraryIDs
				siteData.InjectConfig.MetaKeywordLibraryIDs = req.InjectConfig.MetaKeywordLibraryIDs
				siteData.InjectConfig.DescKeywordLibraryIDs = req.InjectConfig.DescKeywordLibraryIDs
				siteData.InjectConfig.BodyKeywordLibraryIDs = req.InjectConfig.BodyKeywordLibraryIDs
				
				// 处理UseGlobalPinyin指针字段
				if req.InjectConfig.UseGlobalPinyin != nil {
					useGlobal := *req.InjectConfig.UseGlobalPinyin
					siteData.InjectConfig.UseGlobalPinyin = &useGlobal
				}
				
				// 恢复内联定义的值（优先级更高）
				if existingTitle != "" {
					siteData.InjectConfig.HomeTitle = existingTitle
				} else if req.InjectConfig.HomeTitle != "" {
					siteData.InjectConfig.HomeTitle = req.InjectConfig.HomeTitle
				}
				
				if existingDesc != "" {
					siteData.InjectConfig.HomeDescription = existingDesc
				} else if req.InjectConfig.HomeDescription != "" {
					siteData.InjectConfig.HomeDescription = req.InjectConfig.HomeDescription
				}
				
				if existingKeywords != "" {
					siteData.InjectConfig.HomeKeywords = existingKeywords
				} else if req.InjectConfig.HomeKeywords != "" {
					siteData.InjectConfig.HomeKeywords = req.InjectConfig.HomeKeywords
				}
			}
		}

		// 创建站点
		if err := sh.siteService.CreateSiteWithAliases(siteData, aliasPrefixes); err != nil {
			failedCount++
			errors = append(errors, siteData.Domain+": "+err.Error())
			sh.logger.Error("批量创建站点失败", zap.String("domain", siteData.Domain), zap.Error(err))
		} else {
			successCount++
			sh.logger.Info("批量创建站点成功", zap.String("domain", siteData.Domain))
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"success_count": successCount,
			"failed_count":  failedCount,
			"errors":        errors,
		},
		"message": "批量创建完成",
	})
}

// BatchCreateSites 批量创建站点（旧版接口，保持兼容）
func (sh *SiteHandler) BatchCreateSites(c *gin.Context) {
	var req BatchCreateSitesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	successCount := 0
	failedCount := 0
	var errors []string

	for _, siteReq := range req.Sites {
		// 直接使用用户提供的URL，不进行协议检测
		// 协议检测将在用户访问时进行
		targetURL := siteReq.TargetURL
		
		site := &model.Site{
			Domain:                    siteReq.Domain,
			TargetURL:                 targetURL,
			CategoryID:                siteReq.CategoryID,  // 添加分类ID
			CrawlDepth:                siteReq.CrawlDepth,
			EnablePreload:             siteReq.EnablePreload,
			DownloadExternalResources: siteReq.DownloadExternalResources,
			EnableTraditionalConvert:  siteReq.EnableTraditionalConvert,
			EnableHTTPSCheck:          siteReq.EnableHTTPSCheck,
			EnableAnalytics:           siteReq.EnableAnalytics,
			UseGlobalUACheck:          siteReq.UseGlobalUACheck,  // UA检查设置
			EnableUACheck:             siteReq.EnableUACheck,
			AllowedUA:                 siteReq.AllowedUA,
			NonSpiderHTML:             siteReq.NonSpiderHTML,
			EnableSpiderBlock:         siteReq.EnableSpiderBlock,
			UseGlobalSpiderUA:         siteReq.UseGlobalSpiderUA,
			CustomSpiderUA:            siteReq.CustomSpiderUA,
			SpiderBlock403Template:    siteReq.SpiderBlock403Template,
			UseGlobalCache:            siteReq.UseGlobalCache,
			CacheMaxSize:              int64(siteReq.CacheMaxSize),
			CacheHomeTTL:              siteReq.CacheHomeTTL,
			CacheOtherTTL:             siteReq.CacheOtherTTL,
			EnableRedisCache:          siteReq.EnableCache,
			Status:                    siteReq.Status,
			// Sitemap配置
			EnableSitemap:             siteReq.EnableSitemap,
			SitemapUpdateInterval:     siteReq.SitemapUpdateInterval,
			SitemapChangefreq:         siteReq.SitemapChangefreq,
			SitemapPriority:           siteReq.SitemapPriority,
			SitemapMaxUrls:            siteReq.SitemapMaxUrls,
			// 域名跳转配置
			RedirectWWW:               siteReq.RedirectWWW,
			// 企业名称配置
			EnableCompanyName:         siteReq.EnableCompanyName,
			CompanyLibraryID:          siteReq.CompanyLibraryID,
			CompanyName:               siteReq.CompanyName,
			// 来源判断配置
			UseGlobalRefererCheck:     siteReq.UseGlobalRefererCheck,
			EnableRefererCheck:        siteReq.EnableRefererCheck,
			AllowedReferers:           siteReq.AllowedReferers,
			RefererBlockCode:          siteReq.RefererBlockCode,
			RefererBlockHTML:          siteReq.RefererBlockHTML,
		}

		// 创建注入配置
		if siteReq.InjectConfig != nil {
			site.InjectConfig = &model.InjectConfig{
				EnableKeyword:      siteReq.InjectConfig.EnableKeyword,
				EnableStructure:    siteReq.InjectConfig.EnableStructure,
				EnablePseudo:       siteReq.InjectConfig.EnablePseudo,
				FilterExternalLinks: siteReq.InjectConfig.FilterExternalLinks,
				Keywords:           siteReq.InjectConfig.Keywords,
				TitleTemplate:      siteReq.InjectConfig.TitleTemplate,
				MetaTemplate:       siteReq.InjectConfig.MetaTemplate,
				HomeTitle:          siteReq.InjectConfig.HomeTitle,
				HomeDescription:    siteReq.InjectConfig.HomeDescription,
				HomeKeywords:       siteReq.InjectConfig.HomeKeywords,
				EnableUnicode:      siteReq.InjectConfig.EnableUnicode,
				// Unicode转码细粒度控制
				UnicodeScope:           siteReq.InjectConfig.UnicodeScope,
				EnableUnicodeTitle:     siteReq.InjectConfig.EnableUnicodeTitle,
				EnableUnicodeKeywords:  siteReq.InjectConfig.EnableUnicodeKeywords,
				EnableUnicodeDesc:      siteReq.InjectConfig.EnableUnicodeDesc,
				EnableRandomString: siteReq.InjectConfig.EnableRandomString,
				RandomStringLength: siteReq.InjectConfig.RandomStringLength,
				// 拼音设置
				UseGlobalPinyin:         siteReq.InjectConfig.UseGlobalPinyin,
				EnablePinyin:            siteReq.InjectConfig.EnablePinyin,
				EnablePinyinSpecialChars: siteReq.InjectConfig.EnablePinyinSpecialChars,
				PinyinSpecialCharsRatio: siteReq.InjectConfig.PinyinSpecialCharsRatio,
				// H1标签设置
				EnableH1Tag:     siteReq.InjectConfig.EnableH1Tag,
				H1TagPosition:   siteReq.InjectConfig.H1TagPosition,
				// 首页关键词注入设置
				EnableHomeKeywordInject:     siteReq.InjectConfig.EnableHomeKeywordInject,
				HomeKeywordLibraryIDs:       siteReq.InjectConfig.HomeKeywordLibraryIDs,
				HomeKeywordInjectCount:      siteReq.InjectConfig.HomeKeywordInjectCount,
				EnableHomeKeywordUnicode:    siteReq.InjectConfig.EnableHomeKeywordUnicode,
				EnableHiddenHTML:   siteReq.InjectConfig.EnableHiddenHTML,
				HiddenHTMLLength:   siteReq.InjectConfig.HiddenHTMLLength,
				HiddenHTMLRandomID: siteReq.InjectConfig.HiddenHTMLRandomID,
				HiddenHTMLPosition: siteReq.InjectConfig.HiddenHTMLPosition,
				PseudoLibraryIDs:   siteReq.InjectConfig.PseudoLibraryIDs,
				KeywordLibraryIDs:  siteReq.InjectConfig.KeywordLibraryIDs,
				TitleKeywordLibraryIDs: siteReq.InjectConfig.TitleKeywordLibraryIDs,
				MetaKeywordLibraryIDs:  siteReq.InjectConfig.MetaKeywordLibraryIDs,
				DescKeywordLibraryIDs:  siteReq.InjectConfig.DescKeywordLibraryIDs,
				// 关键词注入详细配置
				KeywordInjectTitle:      siteReq.InjectConfig.KeywordInjectTitle,
				KeywordInjectMeta:       siteReq.InjectConfig.KeywordInjectMeta,
				KeywordInjectDesc:       siteReq.InjectConfig.KeywordInjectDesc,
				KeywordInjectBody:       siteReq.InjectConfig.KeywordInjectBody,
				KeywordInjectHidden:     siteReq.InjectConfig.KeywordInjectHidden,
				KeywordInjectH1:         siteReq.InjectConfig.KeywordInjectH1,
				KeywordInjectH2:         siteReq.InjectConfig.KeywordInjectH2,
				KeywordInjectAlt:        siteReq.InjectConfig.KeywordInjectAlt,
				KeywordMaxPerPage:       siteReq.InjectConfig.KeywordMaxPerPage,
				KeywordInjectRatio:      siteReq.InjectConfig.KeywordInjectRatio,
				KeywordMinWordCount:     siteReq.InjectConfig.KeywordMinWordCount,
				KeywordDensity:          siteReq.InjectConfig.KeywordDensity,
				KeywordTitleTemplate:    siteReq.InjectConfig.KeywordTitleTemplate,
				KeywordMetaTemplate:     siteReq.InjectConfig.KeywordMetaTemplate,
				KeywordDescTemplate:     siteReq.InjectConfig.KeywordDescTemplate,
				// 统计代码
				AnalyticsCode:           siteReq.InjectConfig.AnalyticsCode,
			}
		}

		if err := sh.siteService.CreateSite(site); err != nil {
			failedCount++
			errors = append(errors, site.Domain+": "+err.Error())
			sh.logger.Error("批量创建站点失败", zap.String("domain", site.Domain), zap.Error(err))
		} else {
			successCount++
			sh.logger.Info("批量创建站点成功", zap.String("domain", site.Domain))
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"success_count": successCount,
			"failed_count":  failedCount,
			"errors":        errors,
		},
		"message": "批量创建完成",
	})
}

// 请求结构体
type CreateSiteRequest struct {
	Domain        string              `json:"domain" binding:"required"`
	TargetURL     string              `json:"target_url" binding:"required"`
	CategoryID    *uint               `json:"category_id"`
	CrawlDepth    int                 `json:"crawl_depth"`
	EnableCache   bool                `json:"enable_cache"`
	EnablePreload bool                `json:"enable_preload"`
	DownloadExternalResources bool   `json:"download_external_resources"`
	EnableHTTPSCheck          bool   `json:"enable_https_check"`
	EnableTraditionalConvert  bool   `json:"enable_traditional_convert"`
	UseGlobalUACheck          *bool  `json:"use_global_ua_check"`
	EnableUACheck             bool   `json:"enable_ua_check"`
	AllowedUA                 string  `json:"allowed_ua"`
	NonSpiderHTML             string  `json:"non_spider_html"`
	EnableSpiderBlock         bool   `json:"enable_spider_block"`
	UseGlobalSpiderUA         bool   `json:"use_global_spider_ua"`
	CustomSpiderUA            string  `json:"custom_spider_ua"`
	SpiderBlock403Template    string  `json:"spider_block_403_template"`
	// 缓存配置
	UseGlobalCache            bool    `json:"use_global_cache"`
	CacheMaxSize              int     `json:"cache_max_size"`
	CacheHomeTTL              int     `json:"cache_home_ttl"`
	CacheOtherTTL             int     `json:"cache_other_ttl"`
	EnableRedisCache          bool    `json:"enable_redis_cache"`
	InjectConfig  *InjectConfigRequest `json:"inject_config"`
	// Sitemap配置
	EnableSitemap             bool    `json:"enable_sitemap"`
	SitemapUpdateInterval     int     `json:"sitemap_update_interval"`
	SitemapChangefreq         string  `json:"sitemap_changefreq"`
	SitemapPriority           float32 `json:"sitemap_priority"`
	SitemapMaxUrls            int     `json:"sitemap_max_urls"`
	// 域名跳转配置
	RedirectWWW    *bool               `json:"redirect_www"`
	// 企业名称配置
	EnableCompanyName         bool    `json:"enable_company_name"`
	CompanyLibraryID          uint    `json:"company_library_id"`
	CompanyName               string  `json:"company_name"`
	// 来源判断配置
	UseGlobalRefererCheck     *bool   `json:"use_global_referer_check"`
	EnableRefererCheck        bool    `json:"enable_referer_check"`
	AllowedReferers           string  `json:"allowed_referers"`
	RefererBlockCode          int     `json:"referer_block_code"`
	RefererBlockHTML          string  `json:"referer_block_html"`
	// 子域名前缀列表
	AliasPrefixes             []string `json:"alias_prefixes"`
}

type UpdateSiteRequest struct {
	Domain        string              `json:"domain"`
	TargetURL     string              `json:"target_url"`
	CategoryID    *uint               `json:"category_id"`
	CrawlDepth    int                 `json:"crawl_depth"`
	EnableCache   bool                `json:"enable_cache"`
	EnablePreload bool                `json:"enable_preload"`
	DownloadExternalResources bool   `json:"download_external_resources"`
	EnableHTTPSCheck          bool   `json:"enable_https_check"`
	EnableTraditionalConvert  bool   `json:"enable_traditional_convert"`
	UseGlobalUACheck          *bool  `json:"use_global_ua_check"`
	EnableUACheck             bool   `json:"enable_ua_check"`
	AllowedUA                 string  `json:"allowed_ua"`
	NonSpiderHTML             string  `json:"non_spider_html"`
	EnableSpiderBlock         bool   `json:"enable_spider_block"`
	UseGlobalSpiderUA         bool   `json:"use_global_spider_ua"`
	CustomSpiderUA            string  `json:"custom_spider_ua"`
	SpiderBlock403Template    string  `json:"spider_block_403_template"`
	Status        string              `json:"status"`
	// 缓存配置
	UseGlobalCache            bool    `json:"use_global_cache"`
	CacheMaxSize              int     `json:"cache_max_size"`
	CacheHomeTTL              int     `json:"cache_home_ttl"`
	CacheOtherTTL             int     `json:"cache_other_ttl"`
	EnableRedisCache          bool    `json:"enable_redis_cache"`
	// 统计设置配置
	UseGlobalAnalytics        *bool   `json:"use_global_analytics"`
	EnableAnalytics           bool    `json:"enable_analytics"`
	InjectConfig  *InjectConfigRequest `json:"inject_config"`
	// Sitemap配置
	EnableSitemap             bool    `json:"enable_sitemap"`
	SitemapUpdateInterval     int     `json:"sitemap_update_interval"`
	SitemapChangefreq         string  `json:"sitemap_changefreq"`
	SitemapPriority           float32 `json:"sitemap_priority"`
	SitemapMaxUrls            int     `json:"sitemap_max_urls"`
	// 域名跳转配置
	RedirectWWW    *bool               `json:"redirect_www"`
	// 企业名称配置
	EnableCompanyName         bool    `json:"enable_company_name"`
	CompanyLibraryID          uint    `json:"company_library_id"`
	CompanyName               string  `json:"company_name"`
	// 来源判断配置
	UseGlobalRefererCheck     *bool   `json:"use_global_referer_check"`
	EnableRefererCheck        bool    `json:"enable_referer_check"`
	AllowedReferers           string  `json:"allowed_referers"`
	RefererBlockCode          int     `json:"referer_block_code"`
	RefererBlockHTML          string  `json:"referer_block_html"`
	// 子域名前缀列表
	AliasPrefixes             []string `json:"alias_prefixes"`
}

type BatchCreateSitesRequest struct {
	Sites []BatchSiteRequest `json:"sites" binding:"required,min=1"`
}

type BatchSiteRequest struct {
	Domain                    string               `json:"domain" binding:"required"`
	TargetURL                 string               `json:"target_url" binding:"required,url"`
	CategoryID                *uint                `json:"category_id"`  // 添加分类ID
	CrawlDepth                int                  `json:"crawl_depth"`
	Status                    string               `json:"status"`
	EnableCache               bool                 `json:"enable_cache"`
	EnablePreload             bool                 `json:"enable_preload"`
	DownloadExternalResources bool                 `json:"download_external_resources"`
	EnableTraditionalConvert  bool                 `json:"enable_traditional_convert"`
	EnableHTTPSCheck          bool                 `json:"enable_https_check"`
	EnableAnalytics           bool                 `json:"enable_analytics"`
	UseGlobalUACheck          *bool                `json:"use_global_ua_check"`  // 添加UA全局检查
	EnableUACheck             bool                 `json:"enable_ua_check"`
	AllowedUA                 string               `json:"allowed_ua"`
	NonSpiderHTML             string               `json:"non_spider_html"`
	EnableSpiderBlock         bool                 `json:"enable_spider_block"`
	UseGlobalSpiderUA         bool                 `json:"use_global_spider_ua"`
	CustomSpiderUA            string               `json:"custom_spider_ua"`
	SpiderBlock403Template    string               `json:"spider_block_403_template"`  // 添加403模板
	UseGlobalCache            bool                 `json:"use_global_cache"`
	CacheMaxSize              int                  `json:"cache_max_size"`
	CacheHomeTTL              int                  `json:"cache_home_ttl"`
	CacheOtherTTL             int                  `json:"cache_other_ttl"`
	InjectConfig              *InjectConfigRequest `json:"inject_config"`
	// Sitemap配置
	EnableSitemap             bool    `json:"enable_sitemap"`
	SitemapUpdateInterval     int     `json:"sitemap_update_interval"`
	SitemapChangefreq         string  `json:"sitemap_changefreq"`
	SitemapPriority           float32 `json:"sitemap_priority"`
	SitemapMaxUrls            int     `json:"sitemap_max_urls"`
	// 域名跳转配置
	RedirectWWW               *bool    `json:"redirect_www"`
	// 企业名称配置
	EnableCompanyName         bool    `json:"enable_company_name"`
	CompanyLibraryID          uint    `json:"company_library_id"`
	CompanyName               string  `json:"company_name"`
	// 来源判断配置
	UseGlobalRefererCheck     *bool   `json:"use_global_referer_check"`
	EnableRefererCheck        bool    `json:"enable_referer_check"`
	AllowedReferers           string  `json:"allowed_referers"`
	RefererBlockCode          int     `json:"referer_block_code"`
	RefererBlockHTML          string  `json:"referer_block_html"`
	// 子域名前缀列表
	AliasPrefixes             []string `json:"alias_prefixes"`
}

type InjectConfigRequest struct {
	EnableKeyword   bool     `json:"enable_keyword"`
	EnableStructure bool     `json:"enable_structure"`
	EnablePseudo    bool     `json:"enable_pseudo"`
	FilterExternalLinks bool `json:"filter_external_links"`
	Keywords        []string `json:"keywords"`
	TitleTemplate   string   `json:"title_template"`
	MetaTemplate    string   `json:"meta_template"`
	HomeTitle       string   `json:"home_title"`
	HomeDescription string   `json:"home_description"`
	HomeKeywords    string   `json:"home_keywords"`
	EnableUnicode   bool     `json:"enable_unicode"`
	// Unicode转码细粒度控制
	UnicodeScope           string    `json:"unicode_scope"`
	EnableUnicodeTitle     bool      `json:"enable_unicode_title"`
	EnableUnicodeKeywords  bool      `json:"enable_unicode_keywords"`
	EnableUnicodeDesc      bool      `json:"enable_unicode_desc"`
	EnableRandomString bool  `json:"enable_random_string"`
	RandomStringLength int   `json:"random_string_length"`
	// 拼音配置
	UseGlobalPinyin           *bool   `json:"use_global_pinyin"`
	EnablePinyin              bool    `json:"enable_pinyin"`
	EnablePinyinSpecialChars  bool    `json:"enable_pinyin_special_chars"`
	PinyinSpecialCharsRatio   float32 `json:"pinyin_special_chars_ratio"`
	EnableHiddenHTML   bool  `json:"enable_hidden_html"`
	HiddenHTMLLength   int   `json:"hidden_html_length"`
	HiddenHTMLRandomID bool  `json:"hidden_html_random_id"`
	HiddenHTMLPosition string `json:"hidden_html_position"`
	PseudoLibraryIDs   []uint `json:"pseudo_library_ids"`
	KeywordLibraryIDs  []uint `json:"keyword_library_ids"`
	// 独立的关键词库选择
	TitleKeywordLibraryIDs []uint `json:"title_keyword_library_ids"`
	MetaKeywordLibraryIDs  []uint `json:"meta_keyword_library_ids"`
	DescKeywordLibraryIDs  []uint `json:"desc_keyword_library_ids"`
	// 结构注入配置
	StructureLibraryIDs []uint `json:"structure_library_ids"`
	StructureMinPerPage int    `json:"structure_min_per_page"`
	StructureMaxPerPage int    `json:"structure_max_per_page"`
	// 关键词注入详细配置
	KeywordInjectTitle      bool    `json:"keyword_inject_title"`
	KeywordInjectMeta       bool    `json:"keyword_inject_meta"`
	KeywordInjectDesc       bool    `json:"keyword_inject_desc"`
	KeywordInjectBody       bool    `json:"keyword_inject_body"`
	KeywordInjectHidden     bool    `json:"keyword_inject_hidden"`
	KeywordInjectH1         bool    `json:"keyword_inject_h1"`
	KeywordInjectH2         bool    `json:"keyword_inject_h2"`
	KeywordInjectAlt        bool    `json:"keyword_inject_alt"`
	KeywordMaxPerPage       int     `json:"keyword_max_per_page"`
	KeywordInjectRatio      float32 `json:"keyword_inject_ratio"`
	KeywordMinWordCount     int     `json:"keyword_min_word_count"`
	KeywordDensity          float32 `json:"keyword_density"`
	// 关键词模板配置
	KeywordTitleTemplate    string  `json:"keyword_title_template"`
	KeywordMetaTemplate     string  `json:"keyword_meta_template"`
	KeywordDescTemplate     string  `json:"keyword_desc_template"`
	// H1标签注入配置
	EnableH1Tag     bool   `json:"enable_h1_tag"`
	H1TagPosition   string `json:"h1_tag_position"`
	// 首页关键词注入配置
	EnableHomeKeywordInject  bool   `json:"enable_home_keyword_inject"`
	HomeKeywordLibraryIDs    []uint `json:"home_keyword_library_ids"`
	HomeKeywordInjectCount   int    `json:"home_keyword_inject_count"`
	EnableHomeKeywordUnicode bool   `json:"enable_home_keyword_unicode"`
	// 统计代码
	AnalyticsCode   string `json:"analytics_code"`
}

// BatchUpdateSitesRequest 批量更新站点请求
type BatchUpdateSitesRequest struct {
	SiteIDs  []uint                 `json:"site_ids" binding:"required,min=1"`
	Settings map[string]interface{} `json:"settings" binding:"required"`
}

// BatchUpdateSites 批量更新站点
func (sh *SiteHandler) BatchUpdateSites(c *gin.Context) {
	var req BatchUpdateSitesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		sh.logger.Error("批量更新站点请求解析失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	if len(req.SiteIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "至少需要选择一个站点",
		})
		return
	}

	if len(req.Settings) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "至少需要设置一个配置项",
		})
		return
	}

	// 批量更新
	updatedCount, err := sh.siteService.BatchUpdateSites(req.SiteIDs, req.Settings)
	if err != nil {
		sh.logger.Error("批量更新站点失败", 
			zap.Error(err),
			zap.Uints("site_ids", req.SiteIDs),
			zap.Any("settings", req.Settings))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "批量更新失败: " + err.Error(),
		})
		return
	}

	// 清理所有更新站点的Redis缓存，确保配置立即生效
	if sh.optimizedMirrorHandler != nil {
		for _, siteID := range req.SiteIDs {
			// 获取站点信息以获取域名
			site, err := sh.siteService.GetSite(siteID)
			if err == nil && site != nil {
				sh.optimizedMirrorHandler.ClearSiteConfigCache(site.Domain)
				sh.logger.Info("批量更新：清理站点配置缓存",
					zap.Uint("site_id", siteID),
					zap.String("domain", site.Domain))
			}
		}
	}

	sh.logger.Info("批量更新站点成功", 
		zap.Uints("site_ids", req.SiteIDs),
		zap.Int("updated_count", updatedCount),
		zap.Any("settings", req.Settings))

	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"message":       fmt.Sprintf("成功更新 %d 个站点", updatedCount),
		"updated_count": updatedCount,
	})
}

// CleanupOrphanedData 清理孤立数据
func (sh *SiteHandler) CleanupOrphanedData(c *gin.Context) {
	domain := c.Query("domain")
	if domain == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名参数不能为空",
		})
		return
	}

	// 检查站点是否真的不存在
	existing, err := sh.siteService.GetSiteByDomain(domain)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "检查站点失败: " + err.Error(),
		})
		return
	}

	if existing != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "站点仍然存在，无法清理",
		})
		return
	}

	// 执行清理（这里可以扩展实际的清理逻辑）
	sh.logger.Info("手动清理孤立数据", zap.String("domain", domain))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "清理完成",
	})
}

// DetectOrphanedData 检测孤立数据
func (sh *SiteHandler) DetectOrphanedData(c *gin.Context) {
	orphanedData, err := sh.siteService.DetectOrphanedData()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "检测脏数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    orphanedData,
	})
}

// CleanupAllOrphanedData 批量清理所有孤立数据
func (sh *SiteHandler) CleanupAllOrphanedData(c *gin.Context) {
	count, err := sh.siteService.CleanupAllOrphanedData()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "批量清理失败: " + err.Error(),
		})
		return
	}

	sh.logger.Info("批量清理孤立数据完成", zap.Int("count", count))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("成功清理 %d 条脏数据", count),
		"count":   count,
	})
}

// BatchReplaceSites 批量替换站点域名和目标URL
func (sh *SiteHandler) BatchReplaceSites(c *gin.Context) {
	var req struct {
		Replacements []struct {
			// 用于通过域名查找并替换目标站地址
			Domain       string `json:"domain"`
			NewTargetURL string `json:"new_target_url"`
			
			// 保留原有字段以兼容旧版本
			OldDomain    string `json:"old_domain"`
			NewDomain    string `json:"new_domain"`
			OldTargetURL string `json:"old_target_url"`
		} `json:"replacements" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "参数错误: " + err.Error(),
		})
		return
	}

	results := []gin.H{}
	successCount := 0
	failedCount := 0

	for _, replacement := range req.Replacements {
		// 确定查找方式
		var site *model.Site
		var err error
		
		// 添加调试日志
		c.Set("debug_domain", replacement.Domain)
		c.Set("debug_new_target", replacement.NewTargetURL)
		
		// 新方式：通过域名查找站点并替换目标站地址
		if replacement.Domain != "" && replacement.NewTargetURL != "" {
			site, err = sh.siteService.GetSiteByDomain(replacement.Domain)
			if err != nil || site == nil {
				results = append(results, gin.H{
					"domain":    replacement.Domain,
					"old_value": "-",
					"new_value": replacement.NewTargetURL,
					"type":      "目标站地址",
					"status":    "failed",
					"message":   "未找到域名: " + replacement.Domain,
				})
				failedCount++
				continue
			}
			
			// 更新目标站地址
			oldTargetURL := site.TargetURL
			site.TargetURL = replacement.NewTargetURL
			
			// 重置缓存状态为待获取
			site.CacheStatus = "pending"
			site.CacheError = ""
			site.CacheUpdateAt = time.Now()
			
			// 清理所有缓存（文件缓存和Redis缓存）
			sh.clearDomainAllCache(replacement.Domain)
			
			sh.logger.Info("批量替换：重置缓存状态并清理缓存",
				zap.String("domain", replacement.Domain),
				zap.String("old_target_url", oldTargetURL),
				zap.String("new_target_url", replacement.NewTargetURL),
				zap.String("cache_status", site.CacheStatus),
				zap.Time("cache_update_at", site.CacheUpdateAt),
			)
			
			// 保存更新
			if err := sh.siteService.UpdateSite(site); err != nil {
				results = append(results, gin.H{
					"domain":    replacement.Domain,
					"old_value": oldTargetURL,
					"new_value": replacement.NewTargetURL,
					"type":      "目标站地址",
					"status":    "failed",
					"message":   "更新失败: " + err.Error(),
				})
				failedCount++
				continue
			}
			
			results = append(results, gin.H{
				"domain":    replacement.Domain,
				"old_value": oldTargetURL,
				"new_value": replacement.NewTargetURL,
				"type":      "目标站地址",
				"status":    "success",
				"message":   "成功更新目标站地址",
				"cache_status": site.CacheStatus,  // 添加缓存状态到返回结果
			})
			successCount++
			continue
		} else if replacement.OldDomain != "" {
			// 兼容旧方式：原始的域名替换逻辑
			site, err = sh.siteService.GetSiteByDomain(replacement.OldDomain)
			if err != nil || site == nil {
				results = append(results, gin.H{
					"old_value": replacement.OldDomain,
					"new_value": replacement.NewDomain,
					"type":      "域名",
					"status":    "failed",
					"message":   "未找到域名: " + replacement.OldDomain,
				})
				failedCount++
				continue
			}
		} else if replacement.OldTargetURL != "" {
			// 如果没有提供域名，尝试通过目标URL查找
			sites, _, err := sh.siteService.GetSites(1, 1000, "", 0, "", "id", "desc")
			if err != nil {
				results = append(results, gin.H{
					"old_value": replacement.OldTargetURL,
					"new_value": replacement.NewTargetURL,
					"type":      "目标站",
					"status":    "failed",
					"message":   "查询失败: " + err.Error(),
				})
				failedCount++
				continue
			}
			
			// 查找匹配的站点
			for _, s := range sites {
				if s.TargetURL == replacement.OldTargetURL {
					site = s
					break
				}
			}
			
			if site == nil {
				results = append(results, gin.H{
					"old_value": replacement.OldTargetURL,
					"new_value": replacement.NewTargetURL,
					"type":      "目标站",
					"status":    "failed",
					"message":   "未找到目标站: " + replacement.OldTargetURL,
				})
				failedCount++
				continue
			}
		} else {
			results = append(results, gin.H{
				"old_value": "-",
				"new_value": "-",
				"type":      "未知",
				"status":    "failed",
				"message":   "必须提供域名或目标站地址",
			})
			failedCount++
			continue
		}

		// 记录原始信息
		originalDomain := site.Domain
		originalTargetURL := site.TargetURL
		updateCount := 0
		message := ""
		resultType := ""

		// 更新域名
		if replacement.NewDomain != "" && replacement.NewDomain != site.Domain {
			// 检查新域名是否已存在
			existingSite, _ := sh.siteService.GetSiteByDomain(replacement.NewDomain)
			if existingSite != nil && existingSite.ID != site.ID {
				results = append(results, gin.H{
					"old_value": originalDomain,
					"new_value": replacement.NewDomain,
					"type":      "域名",
					"status":    "failed",
					"message":   "新域名已存在",
				})
				failedCount++
				continue
			}
			site.Domain = replacement.NewDomain
			updateCount++
			message += fmt.Sprintf("域名: %s → %s", originalDomain, replacement.NewDomain)
			resultType = "域名"
		}

		// 更新目标URL
		if replacement.NewTargetURL != "" && replacement.NewTargetURL != site.TargetURL {
			oldTargetURL := site.TargetURL
			site.TargetURL = replacement.NewTargetURL
			// 重置缓存状态
			site.CacheStatus = "pending"
			site.CacheError = ""
			site.CacheUpdateAt = time.Now()
			
			sh.logger.Info("批量替换（旧方式）：重置缓存状态",
				zap.String("domain", site.Domain),
				zap.String("old_target_url", oldTargetURL),
				zap.String("new_target_url", replacement.NewTargetURL),
				zap.String("cache_status", site.CacheStatus),
				zap.Time("cache_update_at", site.CacheUpdateAt),
			)
			
			updateCount++
			if message != "" {
				message += "; "
			}
			message += fmt.Sprintf("目标站: %s → %s", originalTargetURL, replacement.NewTargetURL)
			if resultType == "" {
				resultType = "目标站"
			} else {
				resultType = "域名+目标站"
			}
		}

		// 如果没有任何更新
		if updateCount == 0 {
			oldValue := originalDomain
			newValue := replacement.NewDomain
			if replacement.OldTargetURL != "" {
				oldValue = replacement.OldTargetURL
				newValue = replacement.NewTargetURL
			}
			results = append(results, gin.H{
				"old_value": oldValue,
				"new_value": newValue,
				"type":      resultType,
				"status":    "skipped",
				"message":   "新值与原值相同",
			})
			continue
		}

		// 执行更新
		if err := sh.siteService.UpdateSite(site); err != nil {
			results = append(results, gin.H{
				"old_value": originalDomain,
				"new_value": replacement.NewDomain,
				"type":      resultType,
				"status":    "failed",
				"message":   "更新失败: " + err.Error(),
			})
			failedCount++
			continue
		}

		// 清理缓存
		if replacement.NewDomain != "" && replacement.NewDomain != originalDomain && sh.cacheService != nil {
			go sh.cacheService.ClearSiteCache(originalDomain)
		}
		if replacement.NewTargetURL != "" && replacement.NewTargetURL != originalTargetURL && sh.cacheService != nil {
			go sh.cacheService.ClearSiteCache(site.Domain)
		}

		// 添加成功结果
		oldValue := originalDomain
		newValue := site.Domain
		if resultType == "目标站" {
			oldValue = originalTargetURL
			newValue = site.TargetURL
		}
		
		results = append(results, gin.H{
			"old_value": oldValue,
			"new_value": newValue,
			"type":      resultType,
			"status":    "success",
			"message":   message,
			"cache_status": site.CacheStatus,  // 添加缓存状态到返回结果
		})
		successCount++

		sh.logger.Info("替换站点成功",
			zap.Uint("site_id", site.ID),
			zap.String("message", message),
		)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("替换完成：成功 %d 条，失败 %d 条", successCount, failedCount),
		"results": results,
		"summary": gin.H{
			"total":   len(req.Replacements),
			"success": successCount,
			"failed":  failedCount,
		},
	})
}

// BatchCreateSitesWithAliasesRequest 批量创建站点请求（支持子域名）
type BatchCreateSitesWithAliasesRequest struct {
	Lines                     []string             `json:"lines" binding:"required,min=1"`
	CategoryID                *uint                `json:"category_id"`
	CrawlDepth                int                  `json:"crawl_depth"`
	Status                    string               `json:"status"`
	EnableCache               bool                 `json:"enable_cache"`
	CacheTTL                  int                  `json:"cache_ttl"`
	MaxCacheSize              int                  `json:"max_cache_size"`
	CacheHomeTTL              int                  `json:"cache_home_ttl"`
	CacheOtherTTL             int                  `json:"cache_other_ttl"`
	EnableRedisCache          bool                 `json:"enable_redis_cache"`
	UseGlobalCache            *bool                `json:"use_global_cache"`
	EnablePreload             bool                 `json:"enable_preload"`
	DownloadExternalResources bool                 `json:"download_external_resources"`
	EnableTraditionalConvert  bool                 `json:"enable_traditional_convert"`
	EnableHTTPSCheck          bool                 `json:"enable_https_check"`
	EnableAnalytics           bool                 `json:"enable_analytics"`
	AnalyticsCode             string               `json:"analytics_code"`
	UseGlobalAnalytics        *bool                `json:"use_global_analytics"`
	EnableSitemap             bool                 `json:"enable_sitemap"`
	SitemapUpdateInterval     int                  `json:"sitemap_update_interval"`
	SitemapChangefreq         string               `json:"sitemap_changefreq"`
	SitemapPriority           float32              `json:"sitemap_priority"`
	SitemapMaxUrls            int                  `json:"sitemap_max_urls"`
	EnableCompanyName         bool                 `json:"enable_company_name"`
	CompanyLibraryID          *uint                `json:"company_library_id"`
	UseGlobalUACheck          *bool                `json:"use_global_ua_check"`
	EnableUACheck             bool                 `json:"enable_ua_check"`
	AllowedUA                 string               `json:"allowed_ua"`
	NonSpiderHTML             string               `json:"non_spider_html"`
	EnableSpiderBlock         bool                 `json:"enable_spider_block"`
	UseGlobalSpiderUA         bool                 `json:"use_global_spider_ua"`
	CustomSpiderUA            string               `json:"custom_spider_ua"`
	SpiderBlock403Template    string               `json:"spider_block_403_template"`
	UseGlobalRefererCheck     *bool                `json:"use_global_referer_check"`
	EnableRefererCheck        bool                 `json:"enable_referer_check"`
	AllowedReferer            string               `json:"allowed_referer"`
	AllowedReferers           string               `json:"allowed_referers"`
	RefererBlockCode          int                  `json:"referer_block_code"`
	RefererBlockHTML          string               `json:"referer_block_html"`
	NonRefererHTML            string               `json:"non_referer_html"`
	RedirectWWW               *bool                `json:"redirect_www"`
	InjectConfig              *model.InjectConfig  `json:"inject_config"`
}

// parseBatchLine 解析批量添加行
// 格式: wap,m,@,www,example.com|||https://www.example.com|||示例网站|||描述|||关键词
func (sh *SiteHandler) parseBatchLine(line string) (*model.Site, []string, error) {
	// 分割主要部分 - 使用 ||| 作为分隔符避免与URL中的 | 冲突
	parts := strings.Split(line, "|||")
	if len(parts) < 2 {
		return nil, nil, fmt.Errorf("格式错误：至少需要域名和目标URL（使用 ||| 分隔）")
	}
	
	// 解析域名部分
	domainParts := strings.Split(parts[0], ",")
	if len(domainParts) < 1 {
		return nil, nil, fmt.Errorf("域名格式错误")
	}
	
	// 最后一个是主域名
	mainDomain := strings.TrimSpace(domainParts[len(domainParts)-1])
	// 前面的都是子域名前缀
	var aliasPrefixes []string
	if len(domainParts) > 1 {
		aliasPrefixes = domainParts[:len(domainParts)-1]
		// 清理前缀
		for i := range aliasPrefixes {
			aliasPrefixes[i] = strings.TrimSpace(aliasPrefixes[i])
		}
	}
	
	// 直接使用用户提供的URL，不进行协议检测
	// 协议检测将在用户访问时进行
	targetURL := strings.TrimSpace(parts[1])
	
	// 创建站点对象
	site := &model.Site{
		Domain:    mainDomain,
		TargetURL: targetURL,
		Status:    "active",
		EnableCache: true,
		CrawlDepth: 1,
	}
	
	// 可选字段处理
	if len(parts) > 2 && parts[2] != "" {
		// 站点标题
		if site.InjectConfig == nil {
			site.InjectConfig = &model.InjectConfig{}
		}
		title := strings.TrimSpace(parts[2])
		site.InjectConfig.HomeTitle = title
		
		// 如果没有提供描述，用标题填充
		if len(parts) > 3 && parts[3] != "" {
			site.InjectConfig.HomeDescription = strings.TrimSpace(parts[3])
		} else {
			// 用标题作为描述
			site.InjectConfig.HomeDescription = title
		}
		
		// 如果没有提供关键词，用标题填充
		if len(parts) > 4 && parts[4] != "" {
			site.InjectConfig.HomeKeywords = strings.TrimSpace(parts[4])
		} else {
			// 用标题作为关键词
			site.InjectConfig.HomeKeywords = title
		}
	} else if len(parts) > 3 && parts[3] != "" {
		// 如果没有标题但有描述（不太可能的情况）
		if site.InjectConfig == nil {
			site.InjectConfig = &model.InjectConfig{}
		}
		site.InjectConfig.HomeDescription = strings.TrimSpace(parts[3])
		
		if len(parts) > 4 && parts[4] != "" {
			site.InjectConfig.HomeKeywords = strings.TrimSpace(parts[4])
		}
	} else if len(parts) > 4 && parts[4] != "" {
		// 如果没有标题和描述但有关键词（不太可能的情况）
		if site.InjectConfig == nil {
			site.InjectConfig = &model.InjectConfig{}
		}
		site.InjectConfig.HomeKeywords = strings.TrimSpace(parts[4])
	}
	
	return site, aliasPrefixes, nil
}

// CheckProtocol 检测URL的协议是否可访问
func (sh *SiteHandler) CheckProtocol(c *gin.Context) {
	var req struct {
		URL string `json:"url" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "参数错误: " + err.Error(),
		})
		return
	}

	// 创建HTTP客户端，设置短超时
	client := &http.Client{
		Timeout: 3 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 允许最多3次重定向
			if len(via) >= 3 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 尝试HEAD请求（更快）
	req2, err := http.NewRequest("HEAD", req.URL, nil)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success":    true,
			"accessible": false,
			"error":      "无效的URL格式",
		})
		return
	}

	// 设置User-Agent
	req2.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	resp, err := client.Do(req2)
	if err != nil {
		// 如果HEAD失败，尝试GET请求
		req3, _ := http.NewRequest("GET", req.URL, nil)
		req3.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
		
		resp, err = client.Do(req3)
		if err != nil {
			c.JSON(http.StatusOK, gin.H{
				"success":    true,
				"accessible": false,
				"error":      err.Error(),
			})
			return
		}
	}
	defer resp.Body.Close()

	// 检查状态码
	accessible := resp.StatusCode >= 200 && resp.StatusCode < 400

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"accessible":  accessible,
		"status_code": resp.StatusCode,
		"protocol":    strings.Split(req.URL, "://")[0],
	})
}

// clearDomainAllCache 清理指定域名的所有缓存（文件缓存、Redis缓存）
func (sh *SiteHandler) clearDomainAllCache(domain string) {
	sh.logger.Info("开始清理域名所有缓存",
		zap.String("domain", domain))
	
	// 清理Redis中的站点配置缓存（同步执行，确保立即生效）
	if sh.optimizedMirrorHandler != nil {
		sh.optimizedMirrorHandler.ClearSiteConfigCache(domain)
		sh.logger.Info("清理站点配置缓存成功",
			zap.String("domain", domain))
	}
	
	// 清理文件缓存
	if sh.cacheService != nil {
		go func() {
			if deleted, err := sh.cacheService.ClearSiteCache(domain); err != nil {
				sh.logger.Error("清理文件缓存失败",
					zap.String("domain", domain),
					zap.Error(err))
			} else {
				sh.logger.Info("清理文件缓存成功",
					zap.String("domain", domain),
					zap.Int("deleted_files", deleted))
			}
		}()
	}
	
	// 清理Redis缓存
	if sh.redisCache != nil {
		go func() {
			// 清理HTML缓存
			pattern := fmt.Sprintf("html:*%s*", domain)
			if err := sh.redisCache.DeleteByPattern(pattern); err != nil {
				sh.logger.Error("清理Redis HTML缓存失败",
					zap.String("domain", domain),
					zap.String("pattern", pattern),
					zap.Error(err))
			} else {
				sh.logger.Info("清理Redis HTML缓存成功",
					zap.String("domain", domain),
					zap.String("pattern", pattern))
			}
			
			// 清理站点配置缓存
			sitePattern := fmt.Sprintf("site:%s", domain)
			if err := sh.redisCache.Del(sitePattern); err != nil {
				sh.logger.Error("清理Redis站点配置缓存失败",
					zap.String("domain", domain),
					zap.String("pattern", sitePattern),
					zap.Error(err))
			} else {
				sh.logger.Info("清理Redis站点配置缓存成功",
					zap.String("domain", domain))
			}
			
			// 清理404相关的缓存
			pattern404 := fmt.Sprintf("404*%s*", domain)
			if err := sh.redisCache.DeleteByPattern(pattern404); err != nil {
				sh.logger.Error("清理Redis 404缓存失败",
					zap.String("domain", domain),
					zap.String("pattern", pattern404),
					zap.Error(err))
			}
		}()
	}
	
	// 清理内存缓存（站点列表缓存）
	sh.clearSiteListCache()
}

