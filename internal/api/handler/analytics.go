package handler

import (
	"fmt"
	"net/http"
	"time"
	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AnalyticsHandler struct {
	logger           *zap.Logger
	analyticsService *service.AnalyticsService
}

func NewAnalyticsHandler(logger *zap.Logger, analyticsService *service.AnalyticsService) *AnalyticsHandler {
	return &AnalyticsHandler{
		logger:           logger,
		analyticsService: analyticsService,
	}
}

// GetAnalytics 获取统计设置
func (ah *AnalyticsHandler) GetAnalytics(c *gin.Context) {
	analytics, err := ah.analyticsService.GetAnalytics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 使用原生查询确保获取所有字段，包括auto_refresh
	var result struct {
		ID              uint      `json:"id"`
		Code            string    `json:"code"`
		Enabled         bool      `json:"enabled"`
		RefreshInterval int       `json:"refresh_interval"`
		AutoRefresh     bool      `json:"auto_refresh"`
		LastRefresh     time.Time `json:"last_refresh"`
		CreatedAt       time.Time `json:"created_at"`
		UpdatedAt       time.Time `json:"updated_at"`
	}
	
	if err := ah.analyticsService.GetDB().Raw("SELECT * FROM analytics WHERE id = ?", analytics.ID).Scan(&result).Error; err != nil {
		// 如果原生查询失败，回退到原来的方式
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    analytics,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// UpdateAnalytics 更新统计设置
func (ah *AnalyticsHandler) UpdateAnalytics(c *gin.Context) {
	var req UpdateAnalyticsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 添加调试日志
	ah.logger.Info("收到UpdateAnalytics请求",
		zap.String("code", req.Code),
		zap.Bool("enabled", req.Enabled),
		zap.Int("refresh_interval", req.RefreshInterval),
		zap.Bool("auto_refresh", req.AutoRefresh))

	if err := ah.analyticsService.UpdateAnalytics(req.Code, req.Enabled, req.RefreshInterval, req.AutoRefresh); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "统计设置更新成功",
	})
}

// RefreshAnalyticsJS 刷新所有站点的统计JS文件
func (ah *AnalyticsHandler) RefreshAnalyticsJS(c *gin.Context) {
	// 获取所有站点
	var sites []struct {
		Domain string
	}
	if err := ah.analyticsService.GetDB().Table("sites").Select("domain").Find(&sites).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取站点列表失败",
		})
		return
	}
	
	updatedCount := 0
	for _, site := range sites {
		if err := ah.analyticsService.GenerateAnalyticsJS(site.Domain); err != nil {
			ah.logger.Error("刷新统计JS失败", 
				zap.String("domain", site.Domain),
				zap.Error(err))
		} else {
			updatedCount++
		}
	}
	
	// 更新最后刷新时间
	if err := ah.analyticsService.UpdateLastRefresh(); err != nil {
		ah.logger.Error("更新最后刷新时间失败", zap.Error(err))
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"updated_count": updatedCount,
			"total_count":   len(sites),
		},
		"message": fmt.Sprintf("已刷新 %d 个站点的统计文件", updatedCount),
	})
}


// 请求结构体
type UpdateAnalyticsRequest struct {
	Code            string `json:"code"`
	Enabled         bool   `json:"enabled"`
	RefreshInterval int    `json:"refresh_interval"`
	AutoRefresh     bool   `json:"auto_refresh"`
}