package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"site-cluster/internal/service"
)

// StaticRefreshHandler 静态资源刷新处理器
type StaticRefreshHandler struct {
	refreshService *service.StaticRefreshService
	logger         *zap.Logger
}

// NewStaticRefreshHandler 创建静态资源刷新处理器
func NewStaticRefreshHandler(refreshService *service.StaticRefreshService, logger *zap.Logger) *StaticRefreshHandler {
	return &StaticRefreshHandler{
		refreshService: refreshService,
		logger:         logger,
	}
}

// RefreshSite 刷新单个站点
func (h *StaticRefreshHandler) RefreshSite(c *gin.Context) {
	siteIDStr := c.Param("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}

	status, err := h.refreshService.RefreshSiteStaticResources(uint(siteID))
	if err != nil {
		h.logger.Error("刷新站点静态资源失败",
			zap.Uint("site_id", uint(siteID)),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// RefreshBatch 批量刷新站点
func (h *StaticRefreshHandler) RefreshBatch(c *gin.Context) {
	var req struct {
		SiteIDs []uint `json:"site_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}

	if len(req.SiteIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请选择要刷新的站点",
		})
		return
	}

	// 异步执行批量刷新
	go func() {
		results := h.refreshService.RefreshMultipleSites(req.SiteIDs)
		h.logger.Info("批量刷新完成",
			zap.Int("total", len(req.SiteIDs)),
			zap.Int("results", len(results)))
	}()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量刷新任务已提交",
		"count":   len(req.SiteIDs),
	})
}

// RefreshAll 刷新所有站点
func (h *StaticRefreshHandler) RefreshAll(c *gin.Context) {
	// 异步执行全部刷新
	go func() {
		results, err := h.refreshService.RefreshAllSites()
		if err != nil {
			h.logger.Error("刷新所有站点失败", zap.Error(err))
			return
		}
		h.logger.Info("全部站点刷新完成",
			zap.Int("total", len(results)))
	}()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "全部刷新任务已提交",
	})
}

// GetStatus 获取刷新状态
func (h *StaticRefreshHandler) GetStatus(c *gin.Context) {
	// 如果提供了站点ID，返回单个站点状态
	if siteIDStr := c.Query("site_id"); siteIDStr != "" {
		siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "无效的站点ID",
			})
			return
		}

		status := h.refreshService.GetRefreshStatus(uint(siteID))
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    status,
		})
		return
	}

	// 返回所有站点状态
	statuses := h.refreshService.GetAllRefreshStatus()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    statuses,
	})
}