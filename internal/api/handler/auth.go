package handler

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"
	"strings"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"
	"site-cluster/internal/service"
	"site-cluster/internal/service/captcha"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AuthHandler struct {
	logger                *zap.Logger
	authService           *service.AuthService
	captchaService        *captcha.CaptchaService
	loginLogRepo          repository.LoginLogRepository
	systemSettingsService *service.SystemSettingsService
}

func NewAuthHandler(logger *zap.Logger, authService *service.AuthService, captchaService *captcha.CaptchaService, loginLogRepo repository.LoginLogRepository, systemSettingsService *service.SystemSettingsService) *AuthHandler {
	return &AuthHandler{
		logger:                logger,
		authService:           authService,
		captchaService:        captchaService,
		loginLogRepo:          loginLogRepo,
		systemSettingsService: systemSettingsService,
	}
}

// Login 登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}

	// 获取客户端IP和User-Agent
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 创建登录日志记录函数
	recordLoginLog := func(username, status, failReason string) {
		log := &model.LoginLog{
			Username:   username,
			IP:         clientIP,
			UserAgent:  userAgent,
			Status:     status,
			FailReason: failReason,
			LoginTime:  time.Now(),
		}
		if err := h.loginLogRepo.Create(log); err != nil {
			h.logger.Error("记录登录日志失败", zap.Error(err))
		}
	}

	// 检查IP最近失败次数（防暴力破解）
	failedCount, _ := h.loginLogRepo.GetRecentFailedAttempts(clientIP, 10*time.Minute)
	if failedCount >= 5 {
		recordLoginLog(req.Username, "failed", "IP被临时封禁（失败次数过多）")
		c.JSON(http.StatusTooManyRequests, gin.H{
			"success": false,
			"error":   "登录失败次数过多，请10分钟后再试",
		})
		return
	}

	// 验证验证码（如果启用）
	if h.systemSettingsService.IsCaptchaEnabled() {
		if req.CaptchaID == "" || req.CaptchaCode == "" {
			recordLoginLog(req.Username, "failed", "缺少验证码")
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "请输入验证码",
			})
			return
		}
		
		if !h.captchaService.Verify(req.CaptchaID, req.CaptchaCode) {
			recordLoginLog(req.Username, "failed", "验证码错误")
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "验证码错误或已过期",
			})
			return
		}
	}

	// 验证登录
	admin, session, err := h.authService.Login(req.Username, req.Password)
	if err != nil {
		h.logger.Error("登录失败", 
			zap.String("username", req.Username),
			zap.Error(err))
		recordLoginLog(req.Username, "failed", err.Error())
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("管理员登录成功", 
		zap.String("username", admin.Username),
		zap.Uint("admin_id", admin.ID))

	// 记录成功登录
	recordLoginLog(req.Username, "success", "")

	// 设置Cookie
	c.SetCookie(
		"admin_token",
		session.Token,
		3600*24, // 24小时
		"/",
		"",
		false,
		true,
	)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"admin": gin.H{
				"id":       admin.ID,
				"username": admin.Username,
				"nickname": admin.Nickname,
				"email":    admin.Email,
			},
			"token": session.Token,
		},
		"message": "登录成功",
	})
}

// GetCaptcha 获取验证码
func (h *AuthHandler) GetCaptcha(c *gin.Context) {
	id, _, imageBase64, err := h.captchaService.Generate()
	if err != nil {
		h.logger.Error("生成验证码失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "生成验证码失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"captcha_id": id,
			"image":      imageBase64,
		},
	})
}

// Logout 登出
func (h *AuthHandler) Logout(c *gin.Context) {
	token := h.getTokenFromRequest(c)
	if token != "" {
		if err := h.authService.Logout(token); err != nil {
			h.logger.Error("登出失败", zap.Error(err))
		}
	}

	// 清除Cookie
	c.SetCookie(
		"admin_token",
		"",
		-1,
		"/",
		"",
		false,
		true,
	)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "登出成功",
	})
}

// GetCurrentAdmin 获取当前登录管理员信息
func (h *AuthHandler) GetCurrentAdmin(c *gin.Context) {
	// 从上下文获取管理员信息（由中间件设置）
	adminInterface, exists := c.Get("admin")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "未登录",
		})
		return
	}

	// 支持gin.H和map[string]interface{}两种类型
	var admin interface{}
	switch v := adminInterface.(type) {
	case gin.H:
		admin = v
	case map[string]interface{}:
		admin = v
	default:
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "管理员数据格式错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    admin,
	})
}

// GetCaptchaConfig 获取验证码配置
func (h *AuthHandler) GetCaptchaConfig(c *gin.Context) {
	// 获取验证码配置
	enabled, length, expiry := h.systemSettingsService.GetCaptchaConfig()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"enabled": enabled,
			"length":  length,
			"expiry":  expiry,
		},
	})
}

// GetCSRFToken 获取CSRF Token
func (h *AuthHandler) GetCSRFToken(c *gin.Context) {
	// 获取session token
	sessionID, err := c.Cookie("admin_token")
	if err != nil || sessionID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "未登录",
		})
		return
	}
	
	// 生成随机token
	b := make([]byte, 32)
	if _, err := rand.Read(b); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "生成CSRF Token失败",
		})
		return
	}
	
	token := hex.EncodeToString(b)
	
	// 设置CSRF Token到Cookie（httponly=false以便JavaScript可以读取）
	c.SetCookie("csrf_token", token, 3600, "/", "", false, false)
	
	// 这里需要调用中间件的存储方法
	// 由于包依赖问题，我们通过Context传递
	c.Set("csrf_token", token)
	c.Set("session_id", sessionID)
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"token":   token,
	})
}

// ChangePassword 修改密码
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}

	// 从上下文获取管理员ID
	adminInterface, _ := c.Get("admin")
	var adminID uint
	switch v := adminInterface.(type) {
	case gin.H:
		if id, ok := v["id"].(float64); ok {
			adminID = uint(id)
		} else if id, ok := v["id"].(uint); ok {
			adminID = id
		}
	case map[string]interface{}:
		if id, ok := v["id"].(float64); ok {
			adminID = uint(id)
		} else if id, ok := v["id"].(uint); ok {
			adminID = id
		}
	}

	// 修改密码
	err := h.authService.ChangePassword(adminID, req.OldPassword, req.NewPassword)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码修改成功",
	})
}

// getTokenFromRequest 从请求中获取令牌
func (h *AuthHandler) getTokenFromRequest(c *gin.Context) string {
	// 优先从Cookie获取
	if token, err := c.Cookie("admin_token"); err == nil && token != "" {
		return token
	}

	// 从Header获取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
		return strings.TrimPrefix(authHeader, "Bearer ")
	}

	return ""
}

// 请求结构体
type LoginRequest struct {
	Username   string `json:"username" binding:"required"`
	Password   string `json:"password" binding:"required"`
	CaptchaID  string `json:"captcha_id"`  // 验证码ID（可选）
	CaptchaCode string `json:"captcha_code"`  // 验证码（可选）
}

type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}