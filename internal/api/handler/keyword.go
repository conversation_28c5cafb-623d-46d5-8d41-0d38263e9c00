package handler

import (
	"net/http"
	"strconv"
	"strings"

	"site-cluster/internal/model"
	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type KeywordHandler struct {
	logger         *zap.Logger
	keywordService *service.KeywordService
}

func NewKeywordHandler(logger *zap.Logger, keywordService *service.KeywordService) *KeywordHandler {
	return &KeywordHandler{
		logger:         logger,
		keywordService: keywordService,
	}
}

// GetLibraries 获取关键词库列表
func (kh *KeywordHandler) GetLibraries(c *gin.Context) {
	page, _ := strconv.Atoi(c.Default<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.Default<PERSON>uery("page_size", "20"))
	
	libraries, _, err := kh.keywordService.GetLibraries(page, pageSize)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 直接返回数组，前端期望的是数组格式
	c.J<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    libraries,  // 直接返回libraries数组
	})
}

// CreateLibrary 创建关键词库
func (kh *KeywordHandler) CreateLibrary(c *gin.Context) {
	var req CreateLibraryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	library := &model.KeywordLibrary{
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
	}

	if err := kh.keywordService.CreateLibrary(library); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    library,
		"message": "关键词库创建成功",
	})
}

// UpdateLibrary 更新关键词库
func (kh *KeywordHandler) UpdateLibrary(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的关键词库ID",
		})
		return
	}

	var req UpdateLibraryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	library, err := kh.keywordService.GetLibrary(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "关键词库不存在",
		})
		return
	}

	if req.Name != "" {
		library.Name = req.Name
	}
	if req.Description != "" {
		library.Description = req.Description
	}

	if err := kh.keywordService.UpdateLibrary(library); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    library,
		"message": "关键词库更新成功",
	})
}

// DeleteLibrary 删除关键词库
func (kh *KeywordHandler) DeleteLibrary(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的关键词库ID",
		})
		return
	}

	if err := kh.keywordService.DeleteLibrary(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "关键词库删除成功",
	})
}

// GetKeywords 获取关键词列表
func (kh *KeywordHandler) GetKeywords(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的关键词库ID",
		})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))  // 使用limit而不是page_size
	search := c.Query("search")

	keywords, total, err := kh.keywordService.GetKeywords(uint(libraryID), page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"items":  keywords,  // 使用items而不是keywords
			"total":  total,
			"page":   page,
			"limit":  limit,     // 使用limit而不是page_size
		},
	})
}

// AddKeyword 添加关键词
func (kh *KeywordHandler) AddKeyword(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的关键词库ID",
		})
		return
	}

	var req AddKeywordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	keyword := &model.Keyword{
		LibraryID: uint(libraryID),
		Keyword:   req.Keyword,
		Weight:    req.Weight,
	}

	if err := kh.keywordService.AddKeyword(keyword); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    keyword,
		"message": "关键词添加成功",
	})
}

// UpdateKeyword 更新关键词
func (kh *KeywordHandler) UpdateKeyword(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的关键词ID",
		})
		return
	}

	var req UpdateKeywordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	keyword, err := kh.keywordService.GetKeyword(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "关键词不存在",
		})
		return
	}

	if req.Keyword != "" {
		keyword.Keyword = req.Keyword
	}
	if req.Weight > 0 {
		keyword.Weight = req.Weight
	}

	if err := kh.keywordService.UpdateKeyword(keyword); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    keyword,
		"message": "关键词更新成功",
	})
}

// DeleteKeyword 删除关键词
func (kh *KeywordHandler) DeleteKeyword(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的关键词ID",
		})
		return
	}

	if err := kh.keywordService.DeleteKeyword(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "关键词删除成功",
	})
}

// ImportKeywords 批量导入关键词
func (kh *KeywordHandler) ImportKeywords(c *gin.Context) {
	var req struct {
		LibraryID uint     `json:"library_id" binding:"required"`
		Keywords  []string `json:"keywords" binding:"required"`  // 修改为数组类型
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 去重处理
	uniqueKeywords := make([]string, 0)
	seen := make(map[string]bool)
	for _, kw := range req.Keywords {
		kw = strings.TrimSpace(kw)
		if kw != "" && !seen[kw] {
			seen[kw] = true
			uniqueKeywords = append(uniqueKeywords, kw)
		}
	}
	
	// 批量导入
	success, failed, err := kh.keywordService.BatchImportKeywords(req.LibraryID, uniqueKeywords)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"success": success,
			"failed":  failed,
			"total":   len(uniqueKeywords),
			"duplicate": len(req.Keywords) - len(uniqueKeywords),  // 记录去重数量
		},
		"message": "关键词导入完成",
	})
}

// ExportKeywords 导出关键词
func (kh *KeywordHandler) ExportKeywords(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的关键词库ID",
		})
		return
	}

	// 获取所有关键词
	keywords, err := kh.keywordService.GetAllKeywords(uint(libraryID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 生成文本内容
	var content strings.Builder
	for _, keyword := range keywords {
		content.WriteString(keyword.Keyword)
		content.WriteString("\n")
	}

	// 设置响应头
	c.Header("Content-Type", "text/plain")
	c.Header("Content-Disposition", "attachment; filename=keywords.txt")
	
	// 返回文件内容
	c.String(http.StatusOK, content.String())
}

// BatchAddKeywords 批量添加关键词
func (kh *KeywordHandler) BatchAddKeywords(c *gin.Context) {
	var req BatchAddKeywordsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	success, failed, err := kh.keywordService.BatchAddKeywords(req.LibraryID, req.Keywords, req.Weight)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"success_count": success,
			"failed_count":  failed,
		},
		"message": "批量添加关键词完成",
	})
}

// GetKeywordStats 获取关键词统计
func (kh *KeywordHandler) GetKeywordStats(c *gin.Context) {
	stats, err := kh.keywordService.GetKeywordStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": stats,
	})
}

// 请求结构体
type CreateLibraryRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Type        string `json:"type" binding:"required,oneof=main synonym pseudo"`
}

type UpdateLibraryRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type AddKeywordRequest struct {
	Keyword string `json:"keyword" binding:"required"`
	Weight  int    `json:"weight"`
}

type UpdateKeywordRequest struct {
	Keyword string `json:"keyword"`
	Weight  int    `json:"weight"`
}

type BatchAddKeywordsRequest struct {
	LibraryID uint     `json:"library_id" binding:"required"`
	Keywords  []string `json:"keywords" binding:"required"`
	Weight    int      `json:"weight"`
}