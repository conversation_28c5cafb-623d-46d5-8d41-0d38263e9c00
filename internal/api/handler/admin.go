package handler

import (
	"net/http"
	"strconv"

	"site-cluster/internal/model"
	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

type AdminHandler struct {
	logger      *zap.Logger
	authService *service.AuthService
}

func NewAdminHandler(logger *zap.Logger, authService *service.AuthService) *AdminHandler {
	return &AdminHandler{
		logger:      logger,
		authService: authService,
	}
}

// GetAdmins 获取管理员列表
func (h *AdminHandler) GetAdmins(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("limit", "10"))

	admins, total, err := h.authService.GetAdmins(page, pageSize)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"admins": admins,
			"total":  total,
			"page":   page,
			"limit":  pageSize,
		},
	})
}

// GetAdmin 获取单个管理员
func (h *AdminHandler) GetAdmin(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的管理员ID",
		})
		return
	}

	admin, err := h.authService.GetAdminByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "管理员不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    admin,
	})
}

// CreateAdmin 创建管理员
func (h *AdminHandler) CreateAdmin(c *gin.Context) {
	var req CreateAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}

	// 密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "密码处理失败",
		})
		return
	}

	admin := &model.Admin{
		Username: req.Username,
		Password: string(hashedPassword),
		Nickname: req.Nickname,
		Email:    req.Email,
		Status:   req.Status,
	}

	if admin.Status == "" {
		admin.Status = "active"
	}

	if err := h.authService.CreateAdmin(admin); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 不返回密码
	admin.Password = ""

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    admin,
		"message": "管理员创建成功",
	})
}

// UpdateAdmin 更新管理员
func (h *AdminHandler) UpdateAdmin(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的管理员ID",
		})
		return
	}

	var req UpdateAdminRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}

	admin, err := h.authService.GetAdminByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "管理员不存在",
		})
		return
	}

	// 更新字段
	if req.Nickname != "" {
		admin.Nickname = req.Nickname
	}
	if req.Email != "" {
		admin.Email = req.Email
	}
	if req.Status != "" {
		admin.Status = req.Status
	}

	if err := h.authService.UpdateAdmin(admin); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 不返回密码
	admin.Password = ""

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    admin,
		"message": "管理员更新成功",
	})
}

// DeleteAdmin 删除管理员
func (h *AdminHandler) DeleteAdmin(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的管理员ID",
		})
		return
	}

	// 检查是否为系统管理员
	admin, err := h.authService.GetAdminByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "管理员不存在",
		})
		return
	}

	if admin.Username == "admin" {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error":   "不能删除系统管理员",
		})
		return
	}

	if err := h.authService.DeleteAdmin(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "管理员删除成功",
	})
}

// ChangeAdminPassword 修改管理员密码
func (h *AdminHandler) ChangeAdminPassword(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的管理员ID",
		})
		return
	}

	var req ChangeAdminPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}

	// 获取当前登录的管理员ID
	currentAdminID := c.GetUint("admin_id")
	
	// 只有自己或超级管理员可以修改密码
	currentAdmin, _ := h.authService.GetAdminByID(currentAdminID)
	if currentAdminID != uint(id) && currentAdmin.Username != "admin" {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error":   "无权修改其他管理员密码",
		})
		return
	}

	// 密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "密码处理失败",
		})
		return
	}

	if err := h.authService.UpdateAdminPassword(uint(id), string(hashedPassword)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "密码修改成功",
	})
}

// 请求结构体
type CreateAdminRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required,min=6"`
	Nickname string `json:"nickname"`
	Email    string `json:"email"`
	Status   string `json:"status"`
}

type UpdateAdminRequest struct {
	Nickname string `json:"nickname"`
	Email    string `json:"email"`
	Status   string `json:"status"`
}

type ChangeAdminPasswordRequest struct {
	Password string `json:"password" binding:"required,min=6"`
}