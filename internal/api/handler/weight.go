package handler

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"
	"site-cluster/internal/service"
	"site-cluster/internal/service/weight"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// WeightHandler 权重监测处理器
type WeightHandler struct {
	logger         *zap.Logger
	repo           repository.WeightRepository
	monitorService *weight.MonitorService
	redisCache     *service.RedisCacheService
}

// NewWeightHandler 创建权重监测处理器
func NewWeightHandler(logger *zap.Logger, repo repository.WeightRepository, monitorService *weight.MonitorService) *WeightHandler {
	return &WeightHandler{
		logger:         logger,
		repo:           repo,
		monitorService: monitorService,
	}
}

// SetRedisCache 设置Redis缓存服务
func (h *WeightHandler) SetRedisCache(redisCache *service.RedisCacheService) {
	h.redisCache = redisCache
}

// GetConfig 获取权重监测配置
func (h *WeightHandler) GetConfig(c *gin.Context) {
	config, err := h.repo.GetWeightConfig()
	if err != nil {
		h.logger.Error("获取权重监测配置失败", zap.Error(err))
		// 返回默认配置
		config = &model.WeightMonitorConfig{
			Enabled:       false,
			CheckInterval: 60,
			BatchSize:     5,
			BatchDelay:    5,
			CycleWait:     24,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// UpdateConfig 更新权重监测配置
func (h *WeightHandler) UpdateConfig(c *gin.Context) {
	var config model.WeightMonitorConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "参数错误: " + err.Error(),
		})
		return
	}

	// 验证参数
	if config.CheckInterval < 10 || config.CheckInterval > 1440 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "检查间隔必须在10-1440分钟之间",
		})
		return
	}

	if config.BatchSize < 1 || config.BatchSize > 20 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "批次大小必须在1-20之间",
		})
		return
	}

	// 更新配置
	if err := h.monitorService.UpdateConfig(&config); err != nil {
		h.logger.Error("更新权重监测配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "更新配置失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配置更新成功",
	})
}

// TestAPI 测试API密钥
func (h *WeightHandler) TestAPI(c *gin.Context) {
	var req struct {
		APIKey string `json:"api_key"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "参数错误",
		})
		return
	}

	if req.APIKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "API密钥不能为空",
		})
		return
	}

	// 测试API密钥
	api := weight.NewAizhanAPI(req.APIKey)
	if err := api.ValidateAPIKey(); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "API密钥验证成功",
	})
}

// ManualCheck 手动触发权重检查
func (h *WeightHandler) ManualCheck(c *gin.Context) {
	var req struct {
		Domains []string `json:"domains"`
	}

	// 可选的域名列表
	c.ShouldBindJSON(&req)

	// 触发检查
	if err := h.monitorService.ManualCheck(req.Domains); err != nil {
		h.logger.Error("手动触发权重检查失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "权重检查已触发",
	})
}

// GetWeightList 获取权重列表
func (h *WeightHandler) GetWeightList(c *gin.Context) {
	// 获取所有域名的最新权重
	stats, err := h.repo.GetAllDomainsLatestWeight()
	if err != nil {
		h.logger.Error("获取权重列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取数据失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// GetWeightHistory 获取权重历史记录
func (h *WeightHandler) GetWeightHistory(c *gin.Context) {
	domain := c.Query("domain")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	var startTime, endTime time.Time
	var err error

	if startDate != "" {
		startTime, err = time.Parse("2006-01-02", startDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "开始日期格式错误",
			})
			return
		}
	}

	if endDate != "" {
		endTime, err = time.Parse("2006-01-02", endDate)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "结束日期格式错误",
			})
			return
		}
		// 包含结束日期的全天
		endTime = endTime.Add(24 * time.Hour)
	}

	// 如果没有指定时间范围，默认获取最近30天
	if startTime.IsZero() && endTime.IsZero() {
		endTime = time.Now()
		startTime = endTime.AddDate(0, 0, -30)
	}

	// 如果没有指定域名，获取所有域名的历史
	var history []*model.WeightHistory
	if domain == "" {
		// 获取所有域名的历史记录
		domains := []string{} // 空数组表示所有域名
		history, err = h.repo.GetWeightHistoryByDateRange(startTime.Format("2006-01-02"), endTime.Format("2006-01-02"), domains)
	} else {
		history, err = h.repo.GetWeightHistory(domain, startTime, endTime)
	}
	
	if err != nil {
		h.logger.Error("获取权重历史失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取数据失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    history,
	})
}

// GetWeightTrend 获取权重趋势
func (h *WeightHandler) GetWeightTrend(c *gin.Context) {
	domain := c.Query("domain")
	daysStr := c.DefaultQuery("days", "30")

	if domain == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名不能为空",
		})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "天数必须在1-365之间",
		})
		return
	}

	trends, err := h.repo.GetWeightTrend(domain, days)
	if err != nil {
		h.logger.Error("获取权重趋势失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取数据失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    trends,
	})
}

// GetDomainStats 获取域名统计信息
func (h *WeightHandler) GetDomainStats(c *gin.Context) {
	domain := c.Param("domain")
	if domain == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名不能为空",
		})
		return
	}

	// 获取统计信息
	stats, err := h.repo.GetDomainWeightStats(domain)
	if err != nil {
		h.logger.Error("获取域名统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取数据失败",
		})
		return
	}

	// 获取最近30天的历史记录
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -30)
	history, _ := h.repo.GetWeightHistory(domain, startTime, endTime)

	// 获取趋势数据
	trends, _ := h.repo.GetWeightTrend(domain, 30)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"stats":   stats,
			"history": history,
			"trends":  trends,
		},
	})
}

// ExportWeightData 导出权重数据
func (h *WeightHandler) ExportWeightData(c *gin.Context) {
	// format := c.DefaultQuery("format", "csv")
	// startDate := c.Query("start_date")
	// endDate := c.Query("end_date")

	// TODO: 实现数据导出功能
	// 支持CSV和Excel格式

	c.JSON(http.StatusOK, gin.H{
		"success": false,
		"error":   "导出功能开发中",
	})
}

// GetWeightStats 获取权重站点统计
func (h *WeightHandler) GetWeightStats(c *gin.Context) {
	// 缓存键
	cacheKey := "weight:stats"
	
	// 尝试从缓存获取
	if h.redisCache != nil {
		if cachedData, found := h.redisCache.Get(cacheKey); found && cachedData != "" {
			// 直接返回缓存的JSON响应
			c.Header("X-Cache", "HIT")
			c.Data(http.StatusOK, "application/json", []byte(cachedData))
			return
		}
	}
	
	// 使用优化的批量查询方法
	totalDomains, weightedCount, err := h.repo.GetWeightStats()
	if err != nil {
		h.logger.Error("获取权重统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取权重统计失败",
		})
		return
	}
	
	// 构建响应
	response := gin.H{
		"success": true,
		"data": gin.H{
			"total_domains": totalDomains,
			"weighted_count": weightedCount,
		},
	}
	
	// 缓存响应（2分钟TTL，权重数据不会频繁变化）
	if h.redisCache != nil {
		go func() {
			if jsonData, err := json.Marshal(response); err == nil {
				if err := h.redisCache.Set(cacheKey, string(jsonData), 2*time.Minute); err != nil {
					h.logger.Warn("缓存权重统计失败", zap.Error(err))
				}
			}
		}()
	}
	
	c.Header("X-Cache", "MISS")
	c.JSON(http.StatusOK, response)
}

// DeleteDomainWeightData 删除指定域名的权重数据
func (h *WeightHandler) DeleteDomainWeightData(c *gin.Context) {
	domain := c.Param("domain")
	if domain == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名参数不能为空",
		})
		return
	}
	
	// 删除域名的权重历史数据
	err := h.repo.DeleteDomainWeightData(domain)
	if err != nil {
		h.logger.Error("删除域名权重数据失败", 
			zap.String("domain", domain),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "删除域名权重数据失败",
		})
		return
	}
	
	h.logger.Info("成功删除域名权重数据", zap.String("domain", domain))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "域名权重数据已删除",
	})
}

// ClearWeightData 清空所有权重数据
func (h *WeightHandler) ClearWeightData(c *gin.Context) {
	// 清空权重历史数据
	err := h.repo.ClearAllWeightData()
	if err != nil {
		h.logger.Error("清空权重数据失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清空权重数据失败",
		})
		return
	}
	
	h.logger.Info("成功清空所有权重数据")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "权重数据已清空",
	})
}