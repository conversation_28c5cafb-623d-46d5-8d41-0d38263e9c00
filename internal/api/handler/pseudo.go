package handler

import (
	"net/http"
	"strconv"

	"site-cluster/internal/model"
	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PseudoHandler struct {
	logger        *zap.Logger
	pseudoService *service.PseudoService
}

func NewPseudoHandler(logger *zap.Logger, pseudoService *service.PseudoService) *PseudoHandler {
	return &PseudoHandler{
		logger:        logger,
		pseudoService: pseudoService,
	}
}

// GetLibraries 获取伪原创词库列表
func (ph *PseudoHandler) GetLibraries(c *gin.Context) {
	libraries, err := ph.pseudoService.GetLibraries()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.J<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    libraries,
	})
}

// GetLibrary 获取单个词库详情
func (ph *PseudoHandler) GetLibrary(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词库ID",
		})
		return
	}

	library, err := ph.pseudoService.GetLibrary(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "词库不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    library,
	})
}

// CreateLibrary 创建词库
func (ph *PseudoHandler) CreateLibrary(c *gin.Context) {
	var req CreatePseudoLibraryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	library := &model.PseudoLibrary{
		Name:        req.Name,
		Description: req.Description,
	}

	if err := ph.pseudoService.CreateLibrary(library); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    library,
		"message": "词库创建成功",
	})
}

// UpdateLibrary 更新词库
func (ph *PseudoHandler) UpdateLibrary(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词库ID",
		})
		return
	}

	var req UpdatePseudoLibraryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	library, err := ph.pseudoService.GetLibrary(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "词库不存在",
		})
		return
	}

	library.Name = req.Name
	library.Description = req.Description

	if err := ph.pseudoService.UpdateLibrary(library); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    library,
		"message": "词库更新成功",
	})
}

// DeleteLibrary 删除词库
func (ph *PseudoHandler) DeleteLibrary(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词库ID",
		})
		return
	}

	if err := ph.pseudoService.DeleteLibrary(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "词库删除成功",
	})
}

// GetWords 获取词库的词条列表
func (ph *PseudoHandler) GetWords(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词库ID",
		})
		return
	}

	// 获取分页参数
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}
	
	limit := 20
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}
	
	// 获取搜索关键词
	search := c.Query("search")

	words, total, err := ph.pseudoService.GetWordsPaginated(uint(libraryID), page, limit, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    words,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// CreateWord 创建词条
func (ph *PseudoHandler) CreateWord(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词库ID",
		})
		return
	}

	var req CreateWordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	word := &model.PseudoWord{
		LibraryID: uint(libraryID),
		Original:  req.Original,
		Synonyms:  req.Synonyms,
	}

	if err := ph.pseudoService.CreateWord(word); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    word,
		"message": "词条创建成功",
	})
}

// ImportWords 批量导入词条
func (ph *PseudoHandler) ImportWords(c *gin.Context) {
	libraryID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词库ID",
		})
		return
	}

	var req ImportWordsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	count, err := ph.pseudoService.ImportWords(uint(libraryID), req.Content)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "导入成功",
		"data": gin.H{
			"imported_count": count,
		},
	})
}

// GetWord 获取单个词条详情
func (ph *PseudoHandler) GetWord(c *gin.Context) {
	wordID, err := strconv.ParseUint(c.Param("wordId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词条ID",
		})
		return
	}

	word, err := ph.pseudoService.GetWord(uint(wordID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "词条不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    word,
	})
}

// UpdateWord 更新词条
func (ph *PseudoHandler) UpdateWord(c *gin.Context) {
	wordID, err := strconv.ParseUint(c.Param("wordId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词条ID",
		})
		return
	}

	var req UpdateWordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	word, err := ph.pseudoService.GetWord(uint(wordID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "词条不存在",
		})
		return
	}

	word.Original = req.Original
	word.Synonyms = req.Synonyms

	if err := ph.pseudoService.UpdateWord(word); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    word,
		"message": "词条更新成功",
	})
}

// DeleteWord 删除词条
func (ph *PseudoHandler) DeleteWord(c *gin.Context) {
	wordID, err := strconv.ParseUint(c.Param("wordId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的词条ID",
		})
		return
	}

	if err := ph.pseudoService.DeleteWord(uint(wordID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "词条删除成功",
	})
}

// 请求结构体
type CreatePseudoLibraryRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

type UpdatePseudoLibraryRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

type CreateWordRequest struct {
	Original string   `json:"original" binding:"required"`
	Synonyms []string `json:"synonyms" binding:"required"`
}

type UpdateWordRequest struct {
	Original string   `json:"original" binding:"required"`
	Synonyms []string `json:"synonyms" binding:"required"`
}

type ImportWordsRequest struct {
	Content string `json:"content" binding:"required"`
}