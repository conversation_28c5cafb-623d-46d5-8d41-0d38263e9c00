package handler

import (
	"net/http"
	"strconv"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/repository"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// LoginLogHandler 登录日志处理器
type LoginLogHandler struct {
	logger       *zap.Logger
	loginLogRepo repository.LoginLogRepository
}

// NewLoginLogHandler 创建登录日志处理器
func NewLoginLogHandler(logger *zap.Logger, loginLogRepo repository.LoginLogRepository) *LoginLogHandler {
	return &LoginLogHandler{
		logger:       logger,
		loginLogRepo: loginLogRepo,
	}
}

// GetLoginLogs 获取登录日志列表
func (h *LoginLogHandler) GetLoginLogs(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>fault<PERSON>y("page_size", "20"))
	username := c.Query("username")
	startDate := c.Que<PERSON>("start_date")
	endDate := c.Query("end_date")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	var logs []*model.LoginLog
	var total int64
	var err error

	// 根据是否有日期范围查询
	if startDate != "" && endDate != "" {
		start, err1 := time.Parse("2006-01-02", startDate)
		end, err2 := time.Parse("2006-01-02", endDate)
		if err1 == nil && err2 == nil {
			// 结束日期加一天，包含整天
			end = end.Add(24 * time.Hour)
			logs, total, err = h.loginLogRepo.GetByDateRange(username, start, end, page, pageSize)
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "日期格式错误",
			})
			return
		}
	} else {
		logs, total, err = h.loginLogRepo.GetList(username, page, pageSize)
	}

	if err != nil {
		h.logger.Error("获取登录日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取登录日志失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list":  logs,
			"total": total,
			"page":  page,
			"page_size": pageSize,
		},
	})
}

// GetLoginStats 获取登录统计
func (h *LoginLogHandler) GetLoginStats(c *gin.Context) {
	// 获取最近7天的登录统计
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -7)

	// 获取所有登录记录
	logs, _, err := h.loginLogRepo.GetByDateRange("", startDate, endDate, 1, 10000)
	if err != nil {
		h.logger.Error("获取登录统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取登录统计失败",
		})
		return
	}

	// 统计数据
	stats := make(map[string]interface{})
	dailyStats := make(map[string]map[string]int)
	userStats := make(map[string]int)
	var successCount, failedCount int

	for _, log := range logs {
		// 按日期统计
		date := log.LoginTime.Format("2006-01-02")
		if _, exists := dailyStats[date]; !exists {
			dailyStats[date] = map[string]int{"success": 0, "failed": 0}
		}
		dailyStats[date][log.Status]++

		// 按用户统计
		userStats[log.Username]++

		// 总体统计
		if log.Status == "success" {
			successCount++
		} else {
			failedCount++
		}
	}

	stats["daily_stats"] = dailyStats
	stats["user_stats"] = userStats
	stats["total_success"] = successCount
	stats["total_failed"] = failedCount

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// CleanOldLogs 清理旧日志
func (h *LoginLogHandler) CleanOldLogs(c *gin.Context) {
	var req struct {
		Days int `json:"days" binding:"required,min=7"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "参数错误：保留天数最少为7天",
		})
		return
	}

	deletedCount, err := h.loginLogRepo.CleanOldLogs(req.Days)
	if err != nil {
		h.logger.Error("清理登录日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清理登录日志失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "清理成功",
		"deleted": deletedCount,
	})
}

// CleanAllLogs 清空所有日志
func (h *LoginLogHandler) CleanAllLogs(c *gin.Context) {
	deletedCount, err := h.loginLogRepo.CleanAllLogs()
	if err != nil {
		h.logger.Error("清空登录日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清空登录日志失败",
		})
		return
	}

	h.logger.Info("清空所有登录日志", zap.Int64("deleted", deletedCount))
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "已清空所有登录日志",
		"deleted": deletedCount,
	})
}