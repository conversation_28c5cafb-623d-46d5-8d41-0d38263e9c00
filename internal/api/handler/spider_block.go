package handler

import (
	"net/http"
	"site-cluster/internal/model"
	"site-cluster/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SpiderBlockHandler struct {
	logger             *zap.Logger
	spiderBlockService *service.SpiderBlockService
	uaStatsService     *service.UAStatsService
}

func NewSpiderBlockHandler(logger *zap.Logger, spiderBlockService *service.SpiderBlockService) *SpiderBlockHandler {
	return &SpiderBlockHandler{
		logger:             logger,
		spiderBlockService: spiderBlockService,
	}
}

// SetUAStatsService 设置UA统计服务
func (sbh *SpiderBlockHandler) SetUAStatsService(service *service.UAStatsService) {
	sbh.uaStatsService = service
}

// GetRules 获取所有蜘蛛屏蔽规则（支持分页和排序）
func (sbh *SpiderBlockHandler) GetRules(c *gin.Context) {
	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "10")) // 默认10条
	
	// 获取排序参数
	sortBy := c.DefaultQuery("sort_by", "id")
	sortOrder := c.DefaultQuery("sort_order", "desc") // 默认降序
	
	// 获取搜索参数
	search := c.Query("search")
	
	// 分页获取规则
	rules, total, err := sbh.spiderBlockService.GetRulesWithPagination(page, pageSize, sortBy, sortOrder, search)
	if err != nil {
		sbh.logger.Error("获取蜘蛛规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取规则失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"rules":     rules,
			"total":     total,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetRule 获取单个规则
func (sbh *SpiderBlockHandler) GetRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的规则ID",
		})
		return
	}

	rule, err := sbh.spiderBlockService.GetRule(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "规则不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rule,
	})
}

// CreateRule 创建新规则
func (sbh *SpiderBlockHandler) CreateRule(c *gin.Context) {
	var req model.SpiderBlock
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求数据格式错误",
		})
		return
	}
	
	// 记录API调用日志
	sbh.logger.Warn("!!!API调用: 创建Spider Block规则!!!", 
		zap.String("user_agent", req.UserAgent),
		zap.String("description", req.Description),
		zap.String("request_path", c.Request.URL.Path),
		zap.String("request_method", c.Request.Method),
		zap.String("client_ip", c.ClientIP()),
		zap.String("referer", c.GetHeader("Referer")))

	// 设置默认值
	if req.ReturnCode == 0 {
		req.ReturnCode = 403
	}
	if req.UserAgent == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "用户代理特征不能为空",
		})
		return
	}

	if err := sbh.spiderBlockService.CreateRule(&req); err != nil {
		sbh.logger.Error("创建蜘蛛规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "创建规则失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "规则创建成功",
		"data": gin.H{
			"id":          req.ID,
			"user_agent":  req.UserAgent,
			"description": req.Description,
		},
	})
}

// UpdateRule 更新规则
func (sbh *SpiderBlockHandler) UpdateRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的规则ID",
		})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求数据格式错误",
		})
		return
	}

	if err := sbh.spiderBlockService.UpdateRuleByID(uint(id), req); err != nil {
		sbh.logger.Error("更新蜘蛛规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "更新规则失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "规则更新成功",
	})
}

// DeleteRule 删除规则
func (sbh *SpiderBlockHandler) DeleteRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的规则ID",
		})
		return
	}

	if err := sbh.spiderBlockService.DeleteRule(uint(id)); err != nil {
		sbh.logger.Error("删除蜘蛛规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "删除规则失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "规则删除成功",
	})
}

// ToggleRule 切换规则启用状态
func (sbh *SpiderBlockHandler) ToggleRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的规则ID",
		})
		return
	}

	if err := sbh.spiderBlockService.ToggleRule(uint(id)); err != nil {
		sbh.logger.Error("切换蜘蛛规则状态失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "切换状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "状态切换成功",
	})
}

// ResetHitCount 重置命中计数
func (sbh *SpiderBlockHandler) ResetHitCount(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的规则ID",
		})
		return
	}

	if err := sbh.spiderBlockService.ResetHitCount(uint(id)); err != nil {
		sbh.logger.Error("重置命中计数失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "重置计数失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "计数重置成功",
	})
}

// ResetAllHitCounts 重置所有命中计数
func (sbh *SpiderBlockHandler) ResetAllHitCounts(c *gin.Context) {
	if err := sbh.spiderBlockService.ResetAllHitCounts(); err != nil {
		sbh.logger.Error("重置所有命中计数失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "重置计数失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "所有计数已重置",
	})
}

// BatchDelete 批量删除规则
func (sbh *SpiderBlockHandler) BatchDelete(c *gin.Context) {
	var req struct {
		IDs []uint `json:"ids" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}
	
	if len(req.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请选择要删除的规则",
		})
		return
	}
	
	// 批量删除
	successCount := 0
	for _, id := range req.IDs {
		if err := sbh.spiderBlockService.DeleteRule(id); err != nil {
			sbh.logger.Error("删除规则失败", zap.Uint("id", id), zap.Error(err))
			continue
		}
		successCount++
	}
	
	if successCount == 0 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "批量删除失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量删除成功",
		"data": gin.H{
			"total":   len(req.IDs),
			"success": successCount,
			"failed":  len(req.IDs) - successCount,
		},
	})
}

// BatchToggle 批量启用/禁用规则
func (sbh *SpiderBlockHandler) BatchToggle(c *gin.Context) {
	var req struct {
		IDs     []uint `json:"ids" binding:"required"`
		Enabled bool   `json:"enabled"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}
	
	if len(req.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请选择要操作的规则",
		})
		return
	}
	
	// 批量更新状态
	successCount := 0
	for _, id := range req.IDs {
		// 获取规则
		rule, err := sbh.spiderBlockService.GetRuleByID(id)
		if err != nil {
			sbh.logger.Error("获取规则失败", zap.Uint("id", id), zap.Error(err))
			continue
		}
		
		// 更新状态
		rule.Enabled = req.Enabled
		if err := sbh.spiderBlockService.UpdateRule(rule); err != nil {
			sbh.logger.Error("更新规则状态失败", zap.Uint("id", id), zap.Error(err))
			continue
		}
		successCount++
	}
	
	if successCount == 0 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "批量操作失败",
		})
		return
	}
	
	action := "启用"
	if !req.Enabled {
		action = "禁用"
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量" + action + "成功",
		"data": gin.H{
			"total":   len(req.IDs),
			"success": successCount,
			"failed":  len(req.IDs) - successCount,
		},
	})
}

// GetStats 获取统计数据
func (sbh *SpiderBlockHandler) GetStats(c *gin.Context) {
	period := c.DefaultQuery("period", "7days")
	domain := c.Query("domain")
	
	statsService := sbh.spiderBlockService.GetStatsService()
	if statsService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "统计服务未初始化",
		})
		return
	}
	
	data, err := statsService.GetChartData(period, domain)
	if err != nil {
		sbh.logger.Error("获取统计数据失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取统计失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

// ClearAllRules 清空所有屏蔽规则
func (sbh *SpiderBlockHandler) ClearAllRules(c *gin.Context) {
	if err := sbh.spiderBlockService.ClearAllRules(); err != nil {
		sbh.logger.Error("清空所有规则失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清空规则失败",
		})
		return
	}

	sbh.logger.Info("已清空所有蜘蛛屏蔽规则")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "所有规则已清空",
	})
}

// ClearStatsData 清空统计图表数据
func (sbh *SpiderBlockHandler) ClearStatsData(c *gin.Context) {
	statsService := sbh.spiderBlockService.GetStatsService()
	if statsService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "统计服务未初始化",
		})
		return
	}
	
	if err := statsService.ClearStats(); err != nil {
		sbh.logger.Error("清空统计数据失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清空统计数据失败",
		})
		return
	}

	sbh.logger.Info("已清空所有蜘蛛统计数据")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "统计数据已清空",
	})
}

// GetUAStats 获取UA统计数据
func (sbh *SpiderBlockHandler) GetUAStats(c *gin.Context) {
	if sbh.uaStatsService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "UA统计服务未初始化",
		})
		return
	}
	
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10")) // 默认改为10条
	sortBy := c.DefaultQuery("sort_by", "hit_count")
	statType := c.Query("stat_type") // browser, os, device, spider
	filterType := c.Query("filter_type") // real_user, crawler, bot, unknown
	deviceType := c.Query("device_type") // PC, Mobile, Tablet, Bot
	searchQuery := c.Query("search") // 搜索关键词
	
	// 确保分页参数有效
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10 // 默认改为10条
	}
	if pageSize > 100 {
		pageSize = 100
	}
	
	// 如果指定了统计类型，返回分组统计
	if statType != "" {
		stats, err := sbh.uaStatsService.GetStatsByType(statType)
		if err != nil {
			sbh.logger.Error("获取UA分组统计失败", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "获取统计失败",
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    stats,
		})
		return
	}
	
	// 获取分页的UA列表（带筛选）
	stats, total, err := sbh.uaStatsService.GetStatsWithFilter(page, pageSize, sortBy, filterType, deviceType, searchQuery)
	if err != nil {
		sbh.logger.Error("获取UA统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取统计失败",
		})
		return
	}
	
	// 获取总体统计
	totalStats, _ := sbh.uaStatsService.GetTotalStats()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"list":        stats,
			"total":       total,
			"page":        page,
			"page_size":   pageSize,
			"total_stats": totalStats,
		},
	})
}

// GetUADetail 获取特定UA的详细信息
func (sbh *SpiderBlockHandler) GetUADetail(c *gin.Context) {
	if sbh.uaStatsService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "UA统计服务未初始化",
		})
		return
	}
	
	hash := c.Param("hash")
	if hash == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "UA哈希不能为空",
		})
		return
	}
	
	ua, err := sbh.uaStatsService.GetUADetail(hash)
	if err != nil {
		sbh.logger.Error("获取UA详情失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取UA详情失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    ua,
	})
}

// FlushUAStats 刷新UA统计缓存到数据库
func (sbh *SpiderBlockHandler) FlushUAStats(c *gin.Context) {
	if sbh.uaStatsService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "UA统计服务未初始化",
		})
		return
	}
	
	sbh.uaStatsService.FlushCache()
	sbh.logger.Info("UA统计缓存已手动刷新")
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "缓存已刷新到数据库",
	})
}

// ClearUAStats 清空UA统计数据
func (sbh *SpiderBlockHandler) ClearUAStats(c *gin.Context) {
	if sbh.uaStatsService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "UA统计服务未初始化",
		})
		return
	}
	
	if err := sbh.uaStatsService.ClearStats(); err != nil {
		sbh.logger.Error("清空UA统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清空统计失败",
		})
		return
	}
	
	sbh.logger.Info("已清空所有UA统计数据")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "UA统计数据已清空",
	})
}
