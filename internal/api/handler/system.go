package handler

import (
	"site-cluster/internal/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SystemHandler 系统监控处理器
type SystemHandler struct {
	logger            *zap.Logger
	performanceMonitor *service.PerformanceMonitor
	workerPool        interface{}
	redisCache        *service.RedisCacheService
	fileCache         *service.FileCacheService
	siteService       *service.SiteService
}

// NewSystemHandler 创建系统监控处理器
func NewSystemHandler(
	logger *zap.Logger,
	performanceMonitor *service.PerformanceMonitor,
	redisCache *service.RedisCacheService,
	fileCache *service.FileCacheService,
	siteService *service.SiteService,
) *SystemHandler {
	return &SystemHandler{
		logger:            logger,
		performanceMonitor: performanceMonitor,
		redisCache:        redisCache,
		fileCache:         fileCache,
		siteService:       siteService,
	}
}

// SetWorkerPool 设置工作池
func (h *SystemHandler) SetWorkerPool(pool interface{}) {
	h.workerPool = pool
}

// GetMetrics 获取系统指标
func (h *SystemHandler) GetMetrics(c *gin.Context) {
	// 获取性能指标
	perfMetrics := h.performanceMonitor.GetMetrics()
	
	// 获取健康状态
	healthStatus := h.performanceMonitor.GetHealthStatus()
	health := gin.H{
		"healthy":  true,
		"warnings": []string{},
	}
	if healthData, ok := healthStatus["health"].(map[string]interface{}); ok {
		health = gin.H{
			"healthy":  healthData["healthy"],
			"warnings": healthData["warnings"],
		}
	}
	
	// 获取缓存统计
	var redisInfo map[string]interface{}
	if h.redisCache != nil {
		stats, err := h.redisCache.GetCacheStats()
		if err == nil {
			redisInfo = make(map[string]interface{})
			for k, v := range stats {
				redisInfo[k] = v
			}
		}
	}
	
	// 获取文件缓存统计 - 已禁用，避免全盘扫描影响性能
	// var fileCacheStats map[string]interface{}
	// if h.fileCache != nil {
	// 	stats, _ := h.fileCache.GetStats()
	// 	fileCacheStats = stats
	// }
	var fileCacheStats map[string]interface{}
	
	// 获取站点统计
	var siteStats map[string]interface{}
	if h.siteService != nil {
		activeSites, _ := h.siteService.GetActiveSites()
		totalSites, _ := h.siteService.GetTotalSites()
		siteStats = map[string]interface{}{
			"active_sites": len(activeSites),
			"total_sites":  totalSites,
		}
	}
	
	// 获取工作池指标
	var workerPoolMetrics map[string]interface{}
	if h.workerPool != nil {
		if pool, ok := h.workerPool.(interface{ GetMetrics() map[string]interface{} }); ok {
			workerPoolMetrics = pool.GetMetrics()
		}
	}
	
	// 计算综合指标
	var errorRate float64
	if perfMetrics.RequestCount > 0 {
		errorRate = float64(perfMetrics.ErrorCount) / float64(perfMetrics.RequestCount)
	}
	
	// 组装响应
	response := gin.H{
		"success": true,
		"data": gin.H{
			// 基础指标
			"concurrent_requests": perfMetrics.ConcurrentReqs,
			"queue_size":         perfMetrics.QueueSize,
			"cache_hit_rate":     perfMetrics.CacheHitRate,
			"avg_response_time":  perfMetrics.AvgLatency,
			"error_rate":         errorRate,
			"active_workers":     0,
			
			// 健康状态  
			"health": health,
			
			// 系统资源
			"cpu_usage":       perfMetrics.CPUUsage,
			"memory_usage":    perfMetrics.MemoryUsage,
			"goroutine_count": perfMetrics.GoroutineCount,
			"db_connections":  perfMetrics.DBConnections,
			"memory_mb":       perfMetrics.MemoryUsage / 1024 / 1024,
			
			// 请求统计
			"total_requests":   perfMetrics.RequestCount,
			"success_requests": perfMetrics.RequestCount - perfMetrics.ErrorCount,
			"failed_requests":  perfMetrics.ErrorCount,
			"p95_latency":      perfMetrics.P95Latency,
			
			// 缓存统计
			"redis_hits":       int64(0),
			"file_cache_size":  int64(0),
			"write_queue_size": 0,
			"expired_count":    int64(0),
			
			// 站点统计
			"active_sites":   0,
			"total_sites":    0,
			"crawling_sites": 0,
			"failed_sites":   0,
			
			// 带宽统计
			"upload_speed":    perfMetrics.UploadSpeed,
			"download_speed":  perfMetrics.DownloadSpeed,
			"total_upload":    perfMetrics.TotalUpload,
			"total_download":  perfMetrics.TotalDownload,
		},
	}
	
	// 填充Redis统计
	if redisInfo != nil {
		if hits, ok := redisInfo["keyspace_hits"].(int64); ok {
			response["data"].(gin.H)["redis_hits"] = hits
		}
	}
	
	// 填充文件缓存统计
	if fileCacheStats != nil {
		if size, ok := fileCacheStats["total_size"].(int64); ok {
			response["data"].(gin.H)["file_cache_size"] = size
		}
	}
	
	// 填充站点统计
	if siteStats != nil {
		if active, ok := siteStats["active_sites"].(int); ok {
			response["data"].(gin.H)["active_sites"] = active
		}
		if total, ok := siteStats["total_sites"].(int); ok {
			response["data"].(gin.H)["total_sites"] = total
		}
	}
	
	// 填充工作池统计
	if workerPoolMetrics != nil {
		if workers, ok := workerPoolMetrics["workers"].(int32); ok {
			response["data"].(gin.H)["active_workers"] = workers
		}
		if queueSize, ok := workerPoolMetrics["queue_size"].(int); ok {
			response["data"].(gin.H)["queue_size"] = queueSize
		}
		if writeQueue, ok := workerPoolMetrics["write_queue_size"].(int); ok {
			response["data"].(gin.H)["write_queue_size"] = writeQueue
		}
	}
	
	c.JSON(200, response)
}

// ExportMetrics 导出指标
func (h *SystemHandler) ExportMetrics(c *gin.Context) {
	metrics := h.performanceMonitor.GetMetrics()
	healthStatus := h.performanceMonitor.GetHealthStatus()
	
	exportData := gin.H{
		"metrics":     metrics,
		"health":      healthStatus,
		"exported_at": metrics.UpdatedAt,
	}
	
	c.Header("Content-Disposition", "attachment; filename=metrics.json")
	c.JSON(200, exportData)
}