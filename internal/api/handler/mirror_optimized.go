package handler

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"site-cluster/internal/model"
	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	// "github.com/patrickmn/go-cache" // 已移除内存缓存
	"go.uber.org/zap"
)

type OptimizedMirrorHandler struct {
	logger             *zap.Logger
	siteService        *service.SiteService
	fileCacheService   *service.FileCacheService
	redisCacheService  *service.RedisCacheService
	mirrorHandler      *MirrorHandler
	spiderStatsService *service.SpiderStatsService
	sitemapService     *service.SitemapService
	resourceLimiter    *service.ResourceLimiter // 替代 workerPool
	uaStatsService     *service.UAStatsService    // UA统计服务
	// siteConfigCache    *cache.Cache               // 站点配置内存缓存 - 已移除
}

// NewOptimizedMirrorHandler 创建优化的镜像处理器
func NewOptimizedMirrorHandler(
	logger *zap.Logger,
	siteService *service.SiteService,
	fileCacheService *service.FileCacheService,
	redisCacheService *service.RedisCacheService,
	mirrorHandler *MirrorHandler,
	spiderStatsService *service.SpiderStatsService,
	sitemapService *service.SitemapService,
	resourceLimiter *service.ResourceLimiter,
) *OptimizedMirrorHandler {
	return &OptimizedMirrorHandler{
		logger:            logger,
		siteService:       siteService,
		fileCacheService:  fileCacheService,
		redisCacheService: redisCacheService,
		mirrorHandler:     mirrorHandler,
		spiderStatsService: spiderStatsService,
		sitemapService:    sitemapService,
		resourceLimiter:   resourceLimiter,
		// siteConfigCache:   cache.New(5*time.Minute, 10*time.Minute), // 已移除内存缓存
	}
}

// SetUAStatsService 设置UA统计服务
func (h *OptimizedMirrorHandler) SetUAStatsService(service *service.UAStatsService) {
	h.uaStatsService = service
}

// HandleMirrorRequest 处理镜像请求（带Redis缓存）
func (h *OptimizedMirrorHandler) HandleMirrorRequest(c *gin.Context) {
	// 设置路由总超时
	if h.resourceLimiter != nil {
		ctx, cancel := h.resourceLimiter.WithRouteTimeout(c.Request.Context())
		defer cancel()
		c.Request = c.Request.WithContext(ctx)
	}
	
	userAgent := c.GetHeader("User-Agent")
	
	host := c.Request.Host
	path := c.Request.URL.Path
	
	// 去掉端口号
	if colonIndex := strings.IndexByte(host, ':'); colonIndex != -1 {
		host = host[:colonIndex]
	}
	
	
	// 获取站点配置以检查站点级别的蜘蛛屏蔽设置（使用缓存）
	site, err := h.getSiteWithCache(host)
	if err != nil {
		h.logger.Error("获取站点配置失败",
			zap.String("host", host),
			zap.Error(err))
	}
	
	// 如果站点不存在，返回自定义404页面
	if site == nil {
		// 获取系统设置中的自定义404页面
		if h.mirrorHandler.systemSettingsService != nil {
			settings, _ := h.mirrorHandler.systemSettingsService.GetSystemSettings()
			if settings != nil && settings.SiteNotFoundHTML != "" {
				c.Data(http.StatusNotFound, "text/html; charset=utf-8", []byte(settings.SiteNotFoundHTML))
				return
			}
		}
		
		// 默认的404页面
		defaultHTML := `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>站点未配置</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; margin-bottom: 20px; }
        p { color: #666; line-height: 1.6; }
        .error-code { font-size: 72px; color: #e74c3c; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-code">404</div>
        <h1>站点未配置</h1>
        <p>您访问的站点尚未配置或不存在。</p>
        <p>请联系管理员进行配置。</p>
    </div>
</body>
</html>`
		c.Data(http.StatusNotFound, "text/html; charset=utf-8", []byte(defaultHTML))
		return
	}
	
	// 记录爬虫统计（异步）- 在屏蔽之前记录
	if h.spiderStatsService != nil {
		// 异步记录爬虫统计（不阻塞主流程）
		go func(siteID uint, domain string, ua string) {
			defer func() {
				if r := recover(); r != nil {
					h.logger.Error("记录爬虫统计失败", zap.Any("error", r))
				}
			}()
			h.spiderStatsService.RecordSpiderVisitWithSiteID(siteID, domain, ua)
		}(site.ID, host, userAgent)
	}
	
	// 记录UA统计（异步）
	if h.uaStatsService != nil {
		go func(ua string, ip string, path string) {
			defer func() {
				if r := recover(); r != nil {
					h.logger.Error("记录UA统计失败", zap.Any("error", r))
				}
			}()
			// 根据路径判断资源类型
			resourceType := "html"
			pathLower := strings.ToLower(path)
			if strings.HasSuffix(pathLower, ".css") {
				resourceType = "css"
			} else if strings.HasSuffix(pathLower, ".js") {
				resourceType = "js"
			} else if strings.HasSuffix(pathLower, ".jpg") || strings.HasSuffix(pathLower, ".jpeg") ||
				strings.HasSuffix(pathLower, ".png") || strings.HasSuffix(pathLower, ".gif") ||
				strings.HasSuffix(pathLower, ".webp") || strings.HasSuffix(pathLower, ".ico") ||
				strings.HasSuffix(pathLower, ".svg") {
				resourceType = "image"
			} else if strings.HasSuffix(pathLower, ".woff") || strings.HasSuffix(pathLower, ".woff2") ||
				strings.HasSuffix(pathLower, ".ttf") || strings.HasSuffix(pathLower, ".eot") {
				resourceType = "font"
			}
			
			h.uaStatsService.RecordUA(ua, ip, resourceType)
		}(userAgent, c.ClientIP(), c.Request.URL.Path)
	}
	
	// 检查是否需要 @ 跳转到 www
	if h.shouldRedirectToWWW(host, site) {
		// 构建跳转 URL
		wwwHost := "www." + host
		redirectURL := "http://" + wwwHost + c.Request.URL.String()
		if c.Request.TLS != nil || c.GetHeader("X-Forwarded-Proto") == "https" {
			redirectURL = "https://" + wwwHost + c.Request.URL.String()
		}
		
		h.logger.Info("执行 @ 到 www 跳转",
			zap.String("from", host),
			zap.String("to", wwwHost))
		
		c.Redirect(http.StatusMovedPermanently, redirectURL)
		return
	}
	
	// 检查蜘蛛屏蔽（站点级别和全局级别）
	shouldBlock := false
	var template403 string
	
	if site != nil && site.EnableSpiderBlock {
		// 站点启用了蜘蛛屏蔽
		shouldBlock = true
		template403 = site.SpiderBlock403Template
	} else if h.mirrorHandler.systemSettingsService != nil {
		// 检查全局设置
		settings, _ := h.mirrorHandler.systemSettingsService.GetSystemSettings()
		if settings != nil && settings.EnableGlobalSpiderBlock {
			shouldBlock = true
			template403 = settings.SpiderBlock403Template
		}
	}
	
	if shouldBlock && h.mirrorHandler.spiderBlockService != nil {
		blocked, statusCode := h.mirrorHandler.spiderBlockService.CheckUserAgentWithDomain(userAgent, host)
		if blocked {
			// 如果没有返回码，默认使用403
			if statusCode == 0 {
				statusCode = http.StatusForbidden
			}
			// 返回自定义模板，使用配置的状态码
			h.renderBlockTemplate(c, userAgent, template403, statusCode)
			return
		}
	}
	
	// 检查是否已经被缓存为404
	if h.mirrorHandler != nil && h.mirrorHandler.cache404Service != nil && h.mirrorHandler.cache404Service.Is404Cached(host, path) {
		h.logger.Info("页面已被缓存为404",
			zap.String("host", host),
			zap.String("path", path))
		
		// 返回自定义404页面
		html404 := ""
		if h.mirrorHandler.systemSettingsService != nil {
			html404 = h.mirrorHandler.systemSettingsService.GetDefault404HTML()
		}
		if html404 == "" {
			html404 = "<h1>404 Not Found</h1>"
		}
		
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.Header("X-Cache", "404-CACHED")
		c.String(http.StatusNotFound, html404)
		return
	}
	

	// 生成缓存键（包含目标站URL，确保目标站变化时缓存失效）
	cacheKey := h.generateCacheKeyWithTarget(host, c.Request.URL.String(), site.TargetURL)
	
	// 1. 先检查Redis缓存（热点数据）
	if h.redisCacheService != nil {
		if html, found := h.redisCacheService.GetHTML(cacheKey); found {
			c.Header("X-Cache", "HIT-REDIS")
			c.Header("X-Target-URL", site.TargetURL)  // 添加目标站信息到响应头
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.String(http.StatusOK, html)
			
			// 异步更新访问统计
			go func() {
				if h.resourceLimiter != nil {
					redisCtx, redisCancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
					defer redisCancel()
					
					if err := h.resourceLimiter.AcquireRedis(redisCtx); err == nil {
						defer h.resourceLimiter.ReleaseRedis()
						h.redisCacheService.IncrementCounter(host + ":hits")
					}
				} else {
					h.redisCacheService.IncrementCounter(host + ":hits")
				}
			}()
			return
		}
	}

	// 2. 此时站点配置一定存在（前面已经检查过）
	
	// 获取站点的有效设置（考虑全局默认值）
	effectiveSite := h.siteService.GetEffectiveSiteSettings(site)
	
	// 来源判断逻辑 - 在UA判断之前
	if h.mirrorHandler != nil {
		blocked, statusCode, blockHTML := h.mirrorHandler.checkReferer(c, site)
		if blocked {
			h.logger.Info("来源判断：访问被拒绝",
				zap.String("host", host),
				zap.String("referer", c.GetHeader("Referer")),
				zap.Int("status_code", statusCode))
			
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.String(statusCode, blockHTML)
			return
		}
	}
	
	// UA判断逻辑 - 支持三态控制
	needUACheck := false
	allowedUA := ""
	nonSpiderHTML := ""
	
	// 判断是否使用全局设置
	// UseGlobalUACheck: true表示使用全局，false表示站点独立控制
	useGlobal := site.UseGlobalUACheck == nil || (site.UseGlobalUACheck != nil && *site.UseGlobalUACheck)
	
	if !useGlobal {
		// 站点独立控制UA判断
		if site.EnableUACheck {
			needUACheck = true
			allowedUA = site.AllowedUA
			nonSpiderHTML = site.NonSpiderHTML
		}
	} else {
		// 使用全局设置
		if h.mirrorHandler.systemSettingsService != nil && h.mirrorHandler.systemSettingsService.IsUACheckEnabled() {
			settings, _ := h.mirrorHandler.systemSettingsService.GetSystemSettings()
			if settings != nil {
				needUACheck = true
				// 使用全局设置时，始终使用全局的UA规则和模板
				allowedUA = settings.DefaultAllowedUA
				nonSpiderHTML = settings.DefaultNonSpiderHTML
			}
		}
	}
	
	// 执行UA检查
	if needUACheck && allowedUA != "" {
		if !h.mirrorHandler.systemSettingsService.CheckUserAgent(userAgent, allowedUA) {
			h.logger.Info("非允许的UA访问，显示自定义页面", 
				zap.String("host", host),
				zap.String("user_agent", userAgent),
				zap.Bool("site_level", site.EnableUACheck))
			
			// 如果没有自定义HTML，使用默认的
			if nonSpiderHTML == "" && h.mirrorHandler.systemSettingsService != nil {
				nonSpiderHTML = h.mirrorHandler.systemSettingsService.GetDefaultNonSpiderHTML()
			}
			
			// 替换标签后返回非爬虫HTML
			processedHTML := h.mirrorHandler.systemSettingsService.ReplaceNonSpiderHTMLTags(nonSpiderHTML, site)
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.String(http.StatusOK, processedHTML)
			return
		}
	}

	// 检查是否是统计JS文件请求（格式：/xxxxxxxxxxxx.js）
	if matched, _ := regexp.MatchString(`^/[a-f0-9]{12}\.js$`, path); matched {
		// 使用站点的主域名（site.Domain）来查找统计JS文件
		// 无论是通过主域名还是子域名访问，都从主域名目录获取文件
		jsPath := filepath.Join("./cache", site.Domain, path[1:]) // 去掉路径开头的/
		
		// 检查文件是否存在，如果不存在则生成
		if _, err := os.Stat(jsPath); os.IsNotExist(err) {
			// 判断是否使用分类独立统计
			if site.Category != nil && site.Category.UseIndependentAnalytics {
				// 使用分类独立统计
				h.mirrorHandler.analyticsService.GenerateCategoryAnalyticsJS(site.Domain, site.Category.AnalyticsCode)
			} else {
				// 使用全局统计
				h.mirrorHandler.analyticsService.GenerateAnalyticsJS(site.Domain)
			}
			
			// 再次检查文件是否存在
			if _, err := os.Stat(jsPath); os.IsNotExist(err) {
				// 如果还是不存在，返回空的JS文件（避免影响页面加载）
				c.String(http.StatusOK, "// Analytics script")
				return
			}
		}
		
		c.File(jsPath)
		return
	}

	// 检查并补充协议
	baseURL := effectiveSite.TargetURL
	
	
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		// 如果没有协议，尝试检测最佳协议
		if h.mirrorHandler != nil {
			detectedURL := h.mirrorHandler.detectProtocol(baseURL)
			if detectedURL != "" {
				baseURL = detectedURL
				// 更新站点的目标URL以避免重复检测
				go func() {
					if h.siteService != nil {
						site.TargetURL = detectedURL
						h.siteService.UpdateSite(site)
					}
				}()
			} else {
				// 检测失败，默认使用https
				baseURL = "https://" + baseURL
			}
		} else {
			// 默认使用https
			baseURL = "https://" + baseURL
		}
	}
	
	targetURL := baseURL + path
	if c.Request.URL.RawQuery != "" {
		targetURL += "?" + c.Request.URL.RawQuery
	}

	// 3. 检查文件缓存
	// 根据URL判断是否为首页，使用不同的缓存时间
	maxAge := time.Duration(effectiveSite.CacheOtherTTL) * time.Minute
	if maxAge <= 0 {
		maxAge = 24 * time.Hour // 默认24小时
	}
	
	// 判断是否为首页（只有根路径、index.html或index.php且无查询参数才是首页）
	parsedURL, _ := url.Parse(targetURL)
	if (parsedURL.Path == "" || parsedURL.Path == "/" || 
		parsedURL.Path == "/index.html" || parsedURL.Path == "/index.php") && 
		parsedURL.RawQuery == "" {
		homeMaxAge := time.Duration(effectiveSite.CacheHomeTTL) * time.Minute
		if homeMaxAge > 0 {
			maxAge = homeMaxAge
		} else {
			maxAge = 30 * time.Minute // 首页默认30分钟
		}
	}
	
	content, found := h.fileCacheService.GetContent(effectiveSite.Domain, targetURL, maxAge)
	if found {
		
		// 如果是首页缓存命中，更新缓存状态为成功
		if (parsedURL.Path == "" || parsedURL.Path == "/" || parsedURL.Path == "/index.html") && 
			h.mirrorHandler.siteService != nil && effectiveSite != nil {
			// 只有当前状态不是success时才更新，避免频繁更新数据库
			if effectiveSite.CacheStatus != "success" {
				if updateErr := h.mirrorHandler.siteService.UpdateCacheStatus(effectiveSite.ID, "success", ""); updateErr != nil {
					h.logger.Error("更新站点缓存状态失败", zap.Error(updateErr))
				}
			}
		}
		
		// 异步写入Redis（仅缓存首页，且需要启用）
		if h.redisCacheService != nil && effectiveSite.EnableRedisCache && strings.Contains(content.ContentType, "text/html") {
			// 只缓存首页到Redis
			if parsedURL.Path == "" || parsedURL.Path == "/" || parsedURL.Path == "/index.html" {
				// 使用站点配置的缓存时间的1/4作为Redis缓存时间
				redisCacheDuration := maxAge / 4
				if redisCacheDuration < time.Minute {
					redisCacheDuration = time.Minute // 最少1分钟
				}
				if redisCacheDuration > 30*time.Minute {
					redisCacheDuration = 30 * time.Minute // 首页最多缓存30分钟
				}
				// 异步写入Redis缓存
				go func() {
					if h.resourceLimiter != nil {
						redisCtx, redisCancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
						defer redisCancel()
						
						if err := h.resourceLimiter.AcquireRedis(redisCtx); err == nil {
							defer h.resourceLimiter.ReleaseRedis()
							h.redisCacheService.SetHTML(cacheKey, string(content.Data), redisCacheDuration)
						}
					} else {
						h.redisCacheService.SetHTML(cacheKey, string(content.Data), redisCacheDuration)
					}
				}()
			}
		}
		
		h.serveContent(c, content, "HIT-FILE")
		return
	}

	// 4. 缓存未命中，使用分布式锁防止缓存击穿
	if h.redisCacheService != nil {
		lockKey := "lock:" + cacheKey
		locked := h.tryLock(lockKey, 30*time.Second)
		if !locked {
			// 等待其他请求完成
			time.Sleep(100 * time.Millisecond)
			// 重试从缓存获取
			if html, found := h.redisCacheService.GetHTML(cacheKey); found {
				c.Header("X-Cache", "HIT-REDIS-RETRY")
				c.Header("Content-Type", "text/html; charset=utf-8")
				c.String(http.StatusOK, html)
				return
			}
		}
		defer h.unlock(lockKey)
	}

	// 5. 从源站获取内容
	content, err = h.mirrorHandler.fetchFromTarget(targetURL, site)
	if err != nil {
		h.logger.Error("获取目标页面失败",
			zap.String("host", host),
			zap.String("target_url", targetURL),
			zap.Error(err))
		
		// 只有在首页缓存不存在的情况下才更新为失败状态
		// 如果有首页缓存存在，保持原状态不变，因为用户仍然可以访问缓存内容
		if h.mirrorHandler.siteService != nil && site != nil {
			// 检查首页缓存文件是否存在
			homeCacheFile := filepath.Join("./cache", site.Domain, "index.html")
			if _, err := os.Stat(homeCacheFile); os.IsNotExist(err) {
				// 首页缓存不存在，更新为失败状态
				if updateErr := h.mirrorHandler.siteService.UpdateCacheStatus(site.ID, "failed", "无法获取源站内容且无首页缓存"); updateErr != nil {
					h.logger.Error("更新站点缓存状态失败", zap.Error(updateErr))
				}
			} else {
				// 首页缓存存在，记录警告但不更新状态为失败
				h.logger.Warn("获取源站失败但首页缓存存在，保持缓存状态不变", 
					zap.String("domain", site.Domain),
					zap.Error(err))
			}
		}
		
		// 如果是404错误，缓存并返回自定义404页面
		if strings.Contains(err.Error(), "404") {
			// 缓存404状态
			if h.mirrorHandler.cache404Service != nil {
				parsedURL, _ := url.Parse(targetURL)
				h.mirrorHandler.cache404Service.Cache404Status(host, parsedURL.Path)
				h.logger.Info("已缓存404状态",
					zap.String("host", host),
					zap.String("path", parsedURL.Path))
			} else {
				h.logger.Warn("cache404Service is nil, cannot cache 404 status")
			}
			
			// 获取404页面HTML
			html404 := ""
			if h.mirrorHandler.systemSettingsService != nil {
				html404 = h.mirrorHandler.systemSettingsService.GetDefault404HTML()
			}
			if html404 == "" {
				html404 = "<h1>404 Not Found</h1>"
			}
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.String(http.StatusNotFound, html404)
			return
		}
		
		c.String(http.StatusBadGateway, "获取目标页面失败")
		return
	}

	// 更新站点缓存状态为成功
	if h.mirrorHandler.siteService != nil && site != nil {
		if updateErr := h.mirrorHandler.siteService.UpdateCacheStatus(site.ID, "success", ""); updateErr != nil {
			h.logger.Error("更新站点缓存状态失败", zap.Error(updateErr))
		}
	}
	
	// 6. 处理HTML内容
	if strings.Contains(content.ContentType, "text/html") {
		var processErr error
		// 构建请求路径用于判断是否为首页
		requestPath := c.Request.URL.Path
		if c.Request.URL.RawQuery != "" {
			requestPath = requestPath + "?" + c.Request.URL.RawQuery
		}
		processedData, _, processErr := h.mirrorHandler.processHTMLContent(content.Data, targetURL, requestPath, site)
		if processErr != nil {
			h.logger.Error("处理HTML内容失败", zap.Error(processErr))
		} else {
			content.Data = processedData
			// 处理后的HTML内容始终是UTF-8编码
			content.ContentType = "text/html; charset=utf-8"
			// 清除源站的响应头，避免编码问题
			content.Headers = make(map[string]string)
		}
	}

	// 7. 必须先同步保存到缓存，然后从缓存读取返回给用户
	// 这样确保用户永远不会直接接收源站响应
	
	// 保存到文件缓存（同步执行）
	if h.resourceLimiter != nil {
		fileCtx, fileCancel := context.WithTimeout(context.Background(), 1000*time.Millisecond)
		defer fileCancel()
		
		if err := h.resourceLimiter.AcquireFile(fileCtx); err != nil {
			h.logger.Warn("文件操作限流", zap.Error(err))
			c.String(http.StatusServiceUnavailable, "服务繁忙")
			return
		}
		defer h.resourceLimiter.ReleaseFile()
	}
	
	// 保存处理后的内容到缓存
	if err := h.fileCacheService.SaveContent(effectiveSite.Domain, content); err != nil {
		h.logger.Error("保存文件缓存失败", zap.Error(err))
		// 即使保存失败，也返回处理后的内容（但不包含源站响应头）
		h.serveProcessedContent(c, content, "MISS-NOSAVE")
		return
	}
	
	// 8. 从缓存重新读取内容返回给用户
	// 这确保用户获得的是经过处理并缓存的内容
	// 使用较短的缓存时间来确保获取最新处理的内容
	cachedContent, found := h.fileCacheService.GetContent(effectiveSite.Domain, targetURL, 24*time.Hour)
	if found && cachedContent != nil {
		h.serveContent(c, cachedContent, "MISS-CACHED")
	} else {
		// 如果读取失败，返回处理后的内容
		h.serveProcessedContent(c, content, "MISS-DIRECT")
	}
	
	// 9. 异步执行其他操作
	go func() {
		// 如果是HTML页面，添加到sitemap
		if h.sitemapService != nil && strings.Contains(content.ContentType, "text/html") {
			urlPath := c.Request.URL.Path
			if c.Request.URL.RawQuery != "" {
				urlPath = urlPath + "?" + c.Request.URL.RawQuery
			}
			h.sitemapService.AddURL(site.ID, site.Domain, urlPath)
		}
		
		// 保存到Redis（仅缓存首页）
		if h.redisCacheService != nil && effectiveSite.EnableRedisCache && strings.Contains(content.ContentType, "text/html") {
			parsedURL, _ := url.Parse(targetURL)
			if (parsedURL.Path == "" || parsedURL.Path == "/" || 
				parsedURL.Path == "/index.html" || parsedURL.Path == "/index.php") && 
				parsedURL.RawQuery == "" {
				redisCacheDuration := 5 * time.Minute
				if effectiveSite.CacheHomeTTL > 0 {
					redisCacheDuration = time.Duration(effectiveSite.CacheHomeTTL/4) * time.Minute
					if redisCacheDuration < time.Minute {
						redisCacheDuration = time.Minute
					}
					if redisCacheDuration > 30*time.Minute {
						redisCacheDuration = 30 * time.Minute
					}
				}
				if err := h.redisCacheService.SetHTML(cacheKey, string(content.Data), redisCacheDuration); err != nil {
					h.logger.Error("保存Redis缓存失败", zap.Error(err))
				}
			}
		}
		
		// 预加载下一层
		if effectiveSite.EnablePreload && strings.Contains(content.ContentType, "text/html") {
			if h.resourceLimiter != nil {
				crawlerCtx, crawlerCancel := context.WithTimeout(context.Background(), 5000*time.Millisecond)
				defer crawlerCancel()
				if err := h.resourceLimiter.AcquireCrawler(crawlerCtx); err == nil {
					defer h.resourceLimiter.ReleaseCrawler()
					h.mirrorHandler.preloadNextLevel(content, effectiveSite)
				}
			} else {
				h.mirrorHandler.preloadNextLevel(content, effectiveSite)
			}
		}
	}()
}

// getSiteWithCache 从缓存获取站点配置
func (h *OptimizedMirrorHandler) getSiteWithCache(domain string) (*model.Site, error) {
	// 1. 先从数据库获取站点基本信息（包含ID）
	site, err := h.siteService.GetSiteByDomain(domain)
	if err != nil {
		return nil, err
	}
	
	if site == nil {
		return nil, nil
	}
	
	// 2. 使用站点ID作为缓存键，尝试从Redis获取完整配置
	if h.redisCacheService != nil {
		if configStr, found := h.redisCacheService.GetSiteConfigByID(site.ID); found {
			var cachedSite model.Site
			if err := json.Unmarshal([]byte(configStr), &cachedSite); err == nil {
				// 如果有拼音配置，同时缓存拼音配置
				if cachedSite.InjectConfig != nil && 
					(cachedSite.InjectConfig.EnablePinyin || cachedSite.InjectConfig.EnablePinyinSpecialChars) {
					h.cachePinyinConfig(domain, cachedSite.InjectConfig)
				}
				return &cachedSite, nil
			}
		}
		
		// 3. 缓存未命中，使用数据库数据并写入缓存
		// 异步写入Redis缓存（使用站点ID作为键）
		if data, err := json.Marshal(site); err == nil {
			go h.redisCacheService.SetSiteConfigByID(site.ID, string(data))
		}
		
		// 如果有拼音配置，同时缓存拼音配置
		if site.InjectConfig != nil && 
			(site.InjectConfig.EnablePinyin || site.InjectConfig.EnablePinyinSpecialChars) {
			go h.cachePinyinConfig(domain, site.InjectConfig)
		}
	}

	return site, nil
}

// generateCacheKey 生成缓存键（旧版本，保留用于兼容）
func (h *OptimizedMirrorHandler) generateCacheKey(domain, url string) string {
	hash := md5.New()
	hash.Write([]byte(domain + ":" + url))
	return hex.EncodeToString(hash.Sum(nil))
}

// generateCacheKeyWithTarget 生成包含目标站的缓存键（确保目标站变化时缓存失效）
// 返回的是MD5值，调用方需要自己添加前缀
func (h *OptimizedMirrorHandler) generateCacheKeyWithTarget(domain, url, targetURL string) string {
	hash := md5.New()
	hash.Write([]byte(domain + ":" + url + ":" + targetURL))
	return hex.EncodeToString(hash.Sum(nil))
}

// tryLock 尝试获取分布式锁
func (h *OptimizedMirrorHandler) tryLock(key string, ttl time.Duration) bool {
	if h.redisCacheService == nil {
		return true
	}
	
	locked, err := h.redisCacheService.SetNX(key, "1", ttl)
	if err != nil {
		h.logger.Error("获取锁失败", zap.Error(err))
		return false
	}
	return locked
}

// unlock 释放锁
func (h *OptimizedMirrorHandler) unlock(key string) {
	if h.redisCacheService != nil {
		h.redisCacheService.Del(key)
	}
}

// ClearSiteConfigCache 清理站点配置的缓存（已移除内存缓存，改为清理Redis缓存）
func (h *OptimizedMirrorHandler) ClearSiteConfigCache(domain string) {
	// 内存缓存已移除，改为清理Redis缓存
	if h.redisCacheService != nil {
		// 清理站点配置 - 重要：Redis中的键是 site:{domain}，不是 site:config:{domain}
		configKey := fmt.Sprintf("site:%s", domain)
		if err := h.redisCacheService.Delete(configKey); err != nil {
			h.logger.Error("清理Redis站点配置失败",
				zap.String("domain", domain),
				zap.String("key", configKey),
				zap.Error(err))
		} else {
			h.logger.Info("清理Redis站点配置缓存成功",
				zap.String("domain", domain),
				zap.String("key", configKey))
		}
	}
}

// serveProcessedContent 返回处理后的内容（不包含源站响应头）
func (h *OptimizedMirrorHandler) serveProcessedContent(c *gin.Context, content *model.CachedContent, cacheStatus string) {
	// 设置缓存头
	c.Header("X-Cache", cacheStatus)
	c.Header("X-Cache-Time", content.CachedAt.Format(time.RFC3339))
	
	// 不设置源站的响应头，只设置必要的头部
	
	// 如果是HTML内容，插入sitemap链接
	data := content.Data
	if strings.Contains(content.ContentType, "text/html") {
		// 检查是否存在sitemap文件
		host := c.Request.Host
		if colonIndex := strings.IndexByte(host, ':'); colonIndex != -1 {
			host = host[:colonIndex]
		}
		sitemapPath := filepath.Join("./cache", host, "sitemap.xml")
		if _, err := os.Stat(sitemapPath); err == nil {
			// sitemap文件存在，插入链接
			bodyEndRegex := regexp.MustCompile(`(?i)(</body>)`)
			sitemapLink := `<div style="text-align:center;margin:20px 0;font-size:12px;"><a href="/sitemap.xml" style="color:#666;">Sitemap</a></div>`
			data = bodyEndRegex.ReplaceAll(data, []byte(sitemapLink+"\n${1}"))
		}
	}
	
	// 确保HTML内容有正确的charset
	contentType := content.ContentType
	if strings.Contains(contentType, "text/html") && !strings.Contains(contentType, "charset") {
		contentType = "text/html; charset=utf-8"
	}
	
	// 返回内容
	c.Data(http.StatusOK, contentType, data)
}

// serveContent 返回内容
func (h *OptimizedMirrorHandler) serveContent(c *gin.Context, content *model.CachedContent, cacheStatus string) {
	// 设置缓存头
	c.Header("X-Cache", cacheStatus)
	c.Header("X-Cache-Time", content.CachedAt.Format(time.RFC3339))
	
	// 设置原始响应头，但排除可能影响编码的头部
	for k, v := range content.Headers {
		lowerKey := strings.ToLower(k)
		// 过滤掉会影响编码和内容的头部
		if !strings.HasPrefix(lowerKey, "content-encoding") && 
		   !strings.HasPrefix(lowerKey, "content-type") &&
		   !strings.HasPrefix(lowerKey, "content-length") {
			c.Header(k, v)
		}
	}
	
	// 如果是HTML内容，插入sitemap链接
	data := content.Data
	if strings.Contains(content.ContentType, "text/html") {
		// 检查是否存在sitemap文件
		host := c.Request.Host
		if colonIndex := strings.IndexByte(host, ':'); colonIndex != -1 {
			host = host[:colonIndex]
		}
		sitemapPath := filepath.Join("./cache", host, "sitemap.xml")
		if _, err := os.Stat(sitemapPath); err == nil {
			// sitemap文件存在，插入链接
			bodyEndRegex := regexp.MustCompile(`(?i)(</body>)`)
			sitemapLink := `<div style="text-align:center;margin:20px 0;font-size:12px;"><a href="/sitemap.xml" style="color:#666;">Sitemap</a></div>`
			data = bodyEndRegex.ReplaceAll(data, []byte(sitemapLink+"\n${1}"))
		}
	}
	
	// 确保HTML内容有正确的charset
	contentType := content.ContentType
	if strings.Contains(contentType, "text/html") && !strings.Contains(contentType, "charset") {
		contentType = "text/html; charset=utf-8"
	}
	
	// 返回内容
	c.Data(http.StatusOK, contentType, data)
}

// HandleResourceRequest 处理静态资源请求（带缓存）
func (h *OptimizedMirrorHandler) HandleResourceRequest(c *gin.Context) {
	host := c.Request.Host
	resourceURL := c.Query("url")
	
	if resourceURL == "" {
		c.Status(http.StatusBadRequest)
		return
	}

	// 生成缓存键
	cacheKey := h.generateCacheKey(host, resourceURL)
	
	// 检查Redis缓存（小文件）
	if h.redisCacheService != nil {
		if data, found := h.redisCacheService.GetHTML(cacheKey); found {
			contentType := http.DetectContentType([]byte(data))
			c.Header("X-Cache", "HIT-REDIS")
			c.Data(http.StatusOK, contentType, []byte(data))
			return
		}
	}

	// 检查文件缓存
	data, contentType, found := h.fileCacheService.GetResource(host, resourceURL)
	if found {
		// 小文件写入Redis
		if len(data) < 1024*1024 && h.redisCacheService != nil { // 1MB以下
			go h.redisCacheService.SetHTML(cacheKey, string(data), 1*time.Hour)
		}
		
		c.Header("X-Cache", "HIT-FILE")
		c.Data(http.StatusOK, contentType, data)
		return
	}

	// 从源站获取
	resp, err := http.Get(resourceURL)
	if err != nil {
		c.Status(http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	data, err = io.ReadAll(resp.Body)
	if err != nil {
		c.Status(http.StatusInternalServerError)
		return
	}

	// 异步保存缓存
	go func() {
		h.fileCacheService.SaveResource(host, resourceURL, data, resp.Header.Get("Content-Type"))
		
		// 小文件写入Redis
		if len(data) < 1024*1024 && h.redisCacheService != nil {
			h.redisCacheService.SetHTML(cacheKey, string(data), 1*time.Hour)
		}
	}()

	c.Header("X-Cache", "MISS")
	c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), data)
}

// renderBlockTemplate 渲染屏蔽模板（支持自定义状态码）
func (h *OptimizedMirrorHandler) renderBlockTemplate(c *gin.Context, userAgent string, template string, statusCode int) {
	if statusCode == 0 {
		statusCode = http.StatusForbidden // 默认403
	}
	
	// 获取状态码对应的标准文本
	statusText := http.StatusText(statusCode)
	if statusText == "" {
		statusText = fmt.Sprintf("Status %d", statusCode)
	}
	statusString := fmt.Sprintf("%d %s", statusCode, statusText)
	
	// 根据状态码选择默认的颜色和消息
	defaultColor := "#d32f2f"
	defaultMessage := "Access denied"
	
	switch statusCode {
	case http.StatusNotFound:
		defaultColor = "#ff9800"
		defaultMessage = "The requested resource was not found."
	case http.StatusGone:
		defaultColor = "#795548"
		defaultMessage = "The requested resource is no longer available."
	case http.StatusInternalServerError:
		defaultColor = "#f44336"
		defaultMessage = "The server encountered an internal error."
	case http.StatusServiceUnavailable:
		defaultColor = "#9c27b0"
		defaultMessage = "The service is temporarily unavailable."
	case http.StatusForbidden:
		defaultColor = "#d32f2f"
		defaultMessage = "Access to this resource is forbidden."
	case http.StatusUnauthorized:
		defaultColor = "#ff5722"
		defaultMessage = "Authorization is required to access this resource."
	}
	
	if template == "" {
		// 使用动态默认模板
		template = fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <title>%s</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f0f0f0;
        }
        h1 {
            color: %s;
            font-size: 48px;
        }
        .message {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .details {
            color: #666;
            margin-top: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="message">
        <h1>%s</h1>
        <p>%s</p>
        <div class="details">
            <p>User-Agent: {{.UserAgent}}</p>
            <p>Time: {{.Time}}</p>
        </div>
    </div>
</body>
</html>`, statusString, defaultColor, statusString, defaultMessage)
	} else {
		// 替换状态码相关的占位符
		// 支持以下占位符：
		// {{.StatusCode}} - 状态码数字 (如 404)
		// {{.StatusText}} - 状态码文本 (如 Not Found)
		// {{.Status}} - 完整状态 (如 404 Not Found)
		template = strings.ReplaceAll(template, "{{.StatusCode}}", fmt.Sprintf("%d", statusCode))
		template = strings.ReplaceAll(template, "{{.StatusText}}", statusText)
		template = strings.ReplaceAll(template, "{{.Status}}", statusString)
	}
	
	// 替换其他模板变量
	html := strings.ReplaceAll(template, "{{.UserAgent}}", userAgent)
	html = strings.ReplaceAll(html, "{{.Time}}", time.Now().Format("2006-01-02 15:04:05"))
	
	c.Header("Content-Type", "text/html; charset=utf-8")
	c.String(statusCode, html)
}

// render403Template 渲染403模板（保留以兼容旧代码）
func (h *OptimizedMirrorHandler) render403Template(c *gin.Context, userAgent string, template string) {
	h.renderBlockTemplate(c, userAgent, template, http.StatusForbidden)
}

// shouldRedirectToWWW 判断是否需要从 @ 跳转到 www
func (h *OptimizedMirrorHandler) shouldRedirectToWWW(host string, site *model.Site) bool {
	// 检查是否是根域名（不包含 www）
	if strings.HasPrefix(host, "www.") {
		return false // 已经是 www 子域名，不需要跳转
	}
	
	// 检查是否有其他子域名（如 blog.example.com）
	if strings.Count(host, ".") > 1 {
		return false // 有其他子域名，不跳转
	}
	
	// 检查站点级别设置
	if site != nil && site.RedirectWWW != nil {
		return *site.RedirectWWW
	}
	
	// 使用全局设置
	if h.mirrorHandler != nil && h.mirrorHandler.systemSettingsService != nil {
		settings, _ := h.mirrorHandler.systemSettingsService.GetSystemSettings()
		if settings != nil {
			return settings.GlobalRedirectWWW
		}
	}
	
	return false
}

// cachePinyinConfig 缓存拼音配置
func (h *OptimizedMirrorHandler) cachePinyinConfig(domain string, config *model.InjectConfig) {
	if h.redisCacheService == nil || config == nil {
		return
	}
	
	// 构造拼音配置
	pinyinConfig := map[string]interface{}{
		"enable_pinyin": config.EnablePinyin,
		"enable_pinyin_special_chars": config.EnablePinyinSpecialChars,
		"pinyin_special_chars_ratio": config.PinyinSpecialCharsRatio,
		"pinyin_special_chars": config.PinyinSpecialChars,
	}
	
	// 转换为JSON并缓存
	if data, err := json.Marshal(pinyinConfig); err == nil {
		key := fmt.Sprintf("site:%s:pinyin_config", domain)
		h.redisCacheService.Set(key, string(data), 1*time.Hour)
	}
}