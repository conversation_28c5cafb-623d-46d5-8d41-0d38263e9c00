package handler

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"
	
	"site-cluster/internal/model"
	"site-cluster/internal/service"
	
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SitemapHandler sitemap处理器
type SitemapHandler struct {
	logger         *zap.Logger
	sitemapService *service.SitemapService
	siteService    *service.SiteService
}

// NewSitemapHandler 创建sitemap处理器
func NewSitemapHandler(
	logger *zap.Logger,
	sitemapService *service.SitemapService,
	siteService *service.SiteService,
) *SitemapHandler {
	return &SitemapHandler{
		logger:         logger,
		sitemapService: sitemapService,
		siteService:    siteService,
	}
}

// GenerateSitemap 手动生成sitemap
func (h *SitemapHandler) GenerateSitemap(c *gin.Context) {
	siteIDStr := c.Param("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}
	
	// 获取站点信息
	site, err := h.siteService.GetSite(uint(siteID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "站点不存在",
		})
		return
	}
	
	// 生成sitemap
	if err := h.sitemapService.GenerateSitemap(site.ID, site.Domain); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "生成sitemap失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "sitemap生成成功",
	})
}

// ScanAndGenerate 扫描缓存目录并生成sitemap
func (h *SitemapHandler) ScanAndGenerate(c *gin.Context) {
	siteIDStr := c.Param("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}
	
	// 获取站点信息
	site, err := h.siteService.GetSite(uint(siteID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "站点不存在",
		})
		return
	}
	
	// 扫描缓存目录
	if err := h.sitemapService.ScanCacheDirectory(site.ID, site.Domain); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "扫描缓存目录失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "扫描并生成sitemap成功",
	})
}

// GetSitemapStats 获取sitemap统计信息
func (h *SitemapHandler) GetSitemapStats(c *gin.Context) {
	siteIDStr := c.Param("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}
	
	stats, err := h.sitemapService.GetSitemapStats(uint(siteID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取统计信息失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// CleanStaticResources 清理所有静态资源条目
func (h *SitemapHandler) CleanStaticResources(c *gin.Context) {
	err := h.sitemapService.CleanStaticResources()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清理失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "静态资源条目已清理",
	})
}

// RefreshAllSitemaps 批量刷新所有站点的sitemap
func (h *SitemapHandler) RefreshAllSitemaps(c *gin.Context) {
	// 获取所有启用了sitemap的站点
	sites, err := h.siteService.GetAllSites()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取站点列表失败",
		})
		return
	}
	
	successCount := 0
	failedCount := 0
	var errors []string
	
	for _, site := range sites {
		if site.EnableSitemap {
			// 生成sitemap
			err := h.sitemapService.GenerateSitemap(site.ID, site.Domain)
			if err != nil {
				failedCount++
				errors = append(errors, fmt.Sprintf("站点 %s: %v", site.Domain, err))
				h.logger.Error("生成sitemap失败", 
					zap.String("domain", site.Domain),
					zap.Error(err))
			} else {
				successCount++
				h.logger.Info("成功生成sitemap", 
					zap.String("domain", site.Domain))
			}
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("批量刷新完成：成功 %d 个，失败 %d 个", successCount, failedCount),
		"data": gin.H{
			"success_count": successCount,
			"failed_count":  failedCount,
			"errors":        errors,
		},
	})
}

// RefreshAllSitemapsWithProgress 批量刷新所有站点的sitemap（带SSE进度推送）
func (h *SitemapHandler) RefreshAllSitemapsWithProgress(c *gin.Context) {
	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	
	// 创建一个channel用于检测客户端断开
	clientGone := c.Request.Context().Done()
	
	// 获取所有站点
	sites, err := h.siteService.GetAllSites()
	if err != nil {
		fmt.Fprintf(c.Writer, "data: {\"error\": \"获取站点列表失败: %s\"}\n\n", err.Error())
		c.Writer.Flush()
		return
	}
	
	// 筛选启用了sitemap的站点
	var enabledSites []*model.Site
	for _, site := range sites {
		if site.EnableSitemap {
			enabledSites = append(enabledSites, site)
		}
	}
	
	totalSites := len(enabledSites)
	if totalSites == 0 {
		fmt.Fprintf(c.Writer, "data: {\"error\": \"没有启用站点地图的站点\"}\n\n")
		c.Writer.Flush()
		return
	}
	
	// 发送初始信息
	fmt.Fprintf(c.Writer, "data: {\"type\": \"start\", \"total\": %d}\n\n", totalSites)
	c.Writer.Flush()
	
	successCount := 0
	failedCount := 0
	var errors []string
	
	for i, site := range enabledSites {
		// 检查客户端是否断开
		select {
		case <-clientGone:
			h.logger.Info("客户端断开连接，停止刷新")
			return
		default:
		}
		
		// 发送进度信息
		progressData := fmt.Sprintf(`{"type": "progress", "current": %d, "total": %d, "domain": "%s"}`, 
			i+1, totalSites, site.Domain)
		fmt.Fprintf(c.Writer, "data: %s\n\n", progressData)
		c.Writer.Flush()
		
		// 生成sitemap
		err := h.sitemapService.GenerateSitemap(site.ID, site.Domain)
		if err != nil {
			failedCount++
			errorMsg := fmt.Sprintf("站点 %s: %v", site.Domain, err)
			errors = append(errors, errorMsg)
			
			// 发送错误信息
			errorData := fmt.Sprintf(`{"type": "error", "site": "%s", "error": "%s"}`, 
				site.Domain, err.Error())
			fmt.Fprintf(c.Writer, "data: %s\n\n", errorData)
			c.Writer.Flush()
			
			h.logger.Error("生成sitemap失败", 
				zap.String("domain", site.Domain),
				zap.Error(err))
		} else {
			successCount++
			
			// 发送成功信息  
			successData := fmt.Sprintf(`{"type": "success", "site": "%s"}`, site.Domain)
			fmt.Fprintf(c.Writer, "data: %s\n\n", successData)
			c.Writer.Flush()
			
			h.logger.Info("生成sitemap成功", 
				zap.String("domain", site.Domain))
		}
		
		// 添加小延迟，避免过快
		time.Sleep(100 * time.Millisecond)
	}
	
	// 发送完成信息
	completeData := fmt.Sprintf(`{"type": "complete", "success_count": %d, "failed_count": %d}`, 
		successCount, failedCount)
	fmt.Fprintf(c.Writer, "data: %s\n\n", completeData)
	c.Writer.Flush()
}

// ManualGenerateAll 手动生成所有启用站点的sitemap
func (h *SitemapHandler) ManualGenerateAll(c *gin.Context) {
	// 获取所有启用sitemap的站点
	sites, err := h.siteService.GetAllSites()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取站点列表失败",
		})
		return
	}
	
	successCount := 0
	failedCount := 0
	var errors []string
	
	for _, site := range sites {
		if site.EnableSitemap {
			// 强制生成sitemap，不检查更新间隔
			err := h.sitemapService.GenerateSitemap(site.ID, site.Domain)
			if err != nil {
				failedCount++
				errors = append(errors, fmt.Sprintf("站点 %s: %v", site.Domain, err))
				h.logger.Error("手动生成sitemap失败", 
					zap.String("domain", site.Domain),
					zap.Error(err))
			} else {
				successCount++
				h.logger.Info("手动生成sitemap成功", 
					zap.String("domain", site.Domain))
			}
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("手动生成完成：成功 %d 个，失败 %d 个", successCount, failedCount),
		"data": gin.H{
			"success_count": successCount,
			"failed_count":  failedCount,
			"errors":        errors,
		},
	})
}

// CleanupOldEntries 清理过期条目
func (h *SitemapHandler) CleanupOldEntries(c *gin.Context) {
	siteIDStr := c.Param("id")
	siteID, err := strconv.ParseUint(siteIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的站点ID",
		})
		return
	}
	
	// 获取天数参数，默认30天
	days := 30
	if daysStr := c.Query("days"); daysStr != "" {
		if d, err := strconv.Atoi(daysStr); err == nil {
			days = d
		}
	}
	
	if err := h.sitemapService.CleanupOldEntries(uint(siteID), days); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清理失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "清理成功",
	})
}

// ServeSitemap 提供sitemap.xml文件访问
func (h *SitemapHandler) ServeSitemap(c *gin.Context) {
	// 获取域名
	host := c.Request.Host
	// 去掉端口号
	if colonIndex := strings.IndexByte(host, ':'); colonIndex != -1 {
		host = host[:colonIndex]
	}
	
	// 查找对应的站点
	site, err := h.siteService.GetSiteByDomain(host)
	if err != nil || site == nil {
		c.String(http.StatusNotFound, "sitemap not found")
		return
	}
	
	// 检查是否启用sitemap
	if !site.EnableSitemap {
		c.String(http.StatusNotFound, "sitemap not enabled")
		return
	}
	
	// 确定协议
	protocol := "http"
	if c.Request.TLS != nil || c.GetHeader("X-Forwarded-Proto") == "https" {
		protocol = "https"
	}
	
	// 动态生成sitemap内容，使用当前访问的域名和协议
	sitemapContent, err := h.sitemapService.GenerateDynamicSitemap(site.ID, host, protocol)
	if err != nil {
		h.logger.Error("生成动态sitemap失败", zap.Error(err))
		// 如果动态生成失败，尝试返回静态文件
		sitemapPath := "./cache/" + site.Domain + "/sitemap.xml"
		if _, err := os.Stat(sitemapPath); err == nil {
			c.File(sitemapPath)
			return
		}
		c.String(http.StatusInternalServerError, "generate sitemap failed")
		return
	}
	
	// 返回动态生成的sitemap
	c.Data(http.StatusOK, "application/xml; charset=utf-8", []byte(sitemapContent))
}

// GetSitemapSettings 获取站点地图设置
func (h *SitemapHandler) GetSitemapSettings(c *gin.Context) {
	// 获取系统设置中的sitemap相关配置
	settings, err := h.sitemapService.GetSitemapSettings()
	if err != nil {
		h.logger.Error("获取sitemap设置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取设置失败",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"sitemap_refresh_interval":  settings.SitemapRefreshInterval,
			"last_sitemap_refresh_time": settings.LastSitemapRefreshTime,
		},
	})
}

// UpdateSitemapSettings 更新站点地图设置
func (h *SitemapHandler) UpdateSitemapSettings(c *gin.Context) {
	var req struct {
		SitemapRefreshInterval int `json:"sitemap_refresh_interval"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}
	
	// 验证刷新间隔
	if req.SitemapRefreshInterval < 60 || req.SitemapRefreshInterval > 10080 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "刷新间隔必须在60-10080分钟之间",
		})
		return
	}
	
	// 更新设置
	if err := h.sitemapService.UpdateSitemapSettings(req.SitemapRefreshInterval); err != nil {
		h.logger.Error("更新sitemap设置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "更新设置失败",
		})
		return
	}
	
	// 记录更新
	h.logger.Info("站点地图刷新间隔已更新", zap.Int("interval", req.SitemapRefreshInterval))
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "设置已更新",
	})
}

// ServeRobotsTxt 提供robots.txt文件访问（动态生成）
func (h *SitemapHandler) ServeRobotsTxt(c *gin.Context) {
	// 获取域名
	host := c.Request.Host
	// 去掉端口号
	if colonIndex := strings.IndexByte(host, ':'); colonIndex != -1 {
		host = host[:colonIndex]
	}
	
	// 查找对应的站点
	site, err := h.siteService.GetSiteByDomain(host)
	if err != nil || site == nil {
		// 如果找不到站点，返回默认的robots.txt
		defaultRobots := `User-agent: *
Allow: /
`
		c.Data(http.StatusOK, "text/plain; charset=utf-8", []byte(defaultRobots))
		return
	}
	
	// 确定协议
	protocol := "http"
	if c.Request.TLS != nil || c.GetHeader("X-Forwarded-Proto") == "https" {
		protocol = "https"
	}
	
	// 动态生成robots.txt内容，使用当前访问的域名和协议
	robotsContent, err := h.sitemapService.GenerateDynamicRobotsTxt(site.ID, host, protocol)
	if err != nil {
		h.logger.Error("生成动态robots.txt失败", zap.Error(err))
		// 如果动态生成失败，尝试返回静态文件
		robotsPath := "./cache/" + site.Domain + "/robots.txt"
		if _, err := os.Stat(robotsPath); err == nil {
			c.File(robotsPath)
			return
		}
		// 如果静态文件也不存在，返回基本的robots.txt
		basicRobots := fmt.Sprintf(`User-agent: *
Allow: /

# Sitemap
Sitemap: %s://%s/sitemap.xml
`, protocol, host)
		c.Data(http.StatusOK, "text/plain; charset=utf-8", []byte(basicRobots))
		return
	}
	
	// 返回动态生成的robots.txt
	c.Data(http.StatusOK, "text/plain; charset=utf-8", []byte(robotsContent))
}

// GetAllSitemapStats 获取所有站点地图统计
func (h *SitemapHandler) GetAllSitemapStats(c *gin.Context) {
	// 获取所有启用sitemap的站点数量
	sites, err := h.siteService.GetAllSites()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取站点列表失败",
		})
		return
	}
	
	enabledCount := 0
	pendingCount := 0
	
	// 收集所有启用sitemap的站点ID
	var enabledSiteIDs []uint
	for _, site := range sites {
		if site.EnableSitemap {
			enabledCount++
			
			// 检查是否需要刷新
			if site.SitemapUpdateInterval > 0 {
				// 计算是否超过更新间隔
				interval := time.Duration(site.SitemapUpdateInterval) * time.Minute
				if time.Since(site.SitemapLastUpdate) > interval {
					pendingCount++
				}
			}
			
			enabledSiteIDs = append(enabledSiteIDs, site.ID)
		}
	}
	
	// 批量获取所有站点的URL总数
	totalURLs, err := h.sitemapService.GetTotalURLCount(enabledSiteIDs)
	if err != nil {
		h.logger.Error("获取URL总数失败", zap.Error(err))
		totalURLs = 0
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"enabled_count": enabledCount,
			"total_urls":    totalURLs,
			"pending_count": pendingCount,
		},
	})
}

// ClearAllSitemapCache 清空所有站点地图缓存
func (h *SitemapHandler) ClearAllSitemapCache(c *gin.Context) {
	sites, err := h.siteService.GetAllSites()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取站点列表失败",
		})
		return
	}
	
	successCount := 0
	failCount := 0
	
	for _, site := range sites {
		if site.EnableSitemap {
			// 清空该站点的sitemap缓存
			sitemapPath := fmt.Sprintf("./cache/%s/sitemap.xml", site.Domain)
			if err := os.Remove(sitemapPath); err != nil {
				if !os.IsNotExist(err) {
					h.logger.Error("删除sitemap文件失败",
						zap.String("domain", site.Domain),
						zap.Error(err))
					failCount++
					continue
				}
			}
			
			// 清空数据库中的sitemap条目
			if _, err := h.sitemapService.ClearSitemapBySiteID(site.ID); err != nil {
				h.logger.Error("清空sitemap条目失败",
					zap.Uint("site_id", site.ID),
					zap.Error(err))
				failCount++
			} else {
				successCount++
			}
		}
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("清空完成，成功: %d, 失败: %d", successCount, failCount),
		"data": gin.H{
			"success_count": successCount,
			"failed_count":  failCount,
		},
	})
}