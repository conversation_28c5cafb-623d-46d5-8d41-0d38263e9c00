package handler

import (
	"net/http"
	"go.uber.org/zap"
	"github.com/gin-gonic/gin"
)

// ErrorCode 错误码
type ErrorCode string

const (
	// 通用错误码
	ErrCodeInternal      ErrorCode = "INTERNAL_ERROR"
	ErrCodeBadRequest    ErrorCode = "BAD_REQUEST"
	ErrCodeUnauthorized  ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden     ErrorCode = "FORBIDDEN"
	ErrCodeNotFound      ErrorCode = "NOT_FOUND"
	ErrCodeTimeout       ErrorCode = "TIMEOUT"
	ErrCodeRateLimit     ErrorCode = "RATE_LIMIT"
	
	// 业务错误码
	ErrCodeSiteNotFound  ErrorCode = "SITE_NOT_FOUND"
	ErrCodeCacheFailed   ErrorCode = "CACHE_FAILED"
	ErrCodeProxyFailed   ErrorCode = "PROXY_FAILED"
	ErrCodeInvalidConfig ErrorCode = "INVALID_CONFIG"
	ErrCodeDBError       ErrorCode = "DATABASE_ERROR"
	ErrCodeRedisError    ErrorCode = "REDIS_ERROR"
)

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Success bool      `json:"success"`
	Code    ErrorCode `json:"code"`
	Message string    `json:"message"`
}

// ErrorHandler 统一错误处理器
type ErrorHandler struct {
	logger *zap.Logger
	debug  bool // 是否为调试模式
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(logger *zap.Logger, debug bool) *ErrorHandler {
	return &ErrorHandler{
		logger: logger,
		debug:  debug,
	}
}

// HandleError 处理错误并返回统一格式的响应
func (h *ErrorHandler) HandleError(c *gin.Context, code ErrorCode, err error, statusCode int) {
	// 记录详细错误日志（内部使用）
	if err != nil {
		h.logger.Error("处理请求时发生错误",
			zap.String("code", string(code)),
			zap.Error(err),
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method),
			zap.String("ip", c.ClientIP()),
		)
	}

	// 构建响应消息（不包含敏感信息）
	message := h.getPublicMessage(code)
	
	// 如果是调试模式，可以包含更多信息
	if h.debug && err != nil {
		message = message + " (Debug: " + err.Error() + ")"
	}

	// 返回统一格式的错误响应
	c.JSON(statusCode, ErrorResponse{
		Success: false,
		Code:    code,
		Message: message,
	})
}

// getPublicMessage 获取对外显示的错误消息
func (h *ErrorHandler) getPublicMessage(code ErrorCode) string {
	messages := map[ErrorCode]string{
		ErrCodeInternal:      "服务器内部错误，请稍后重试",
		ErrCodeBadRequest:    "请求参数错误",
		ErrCodeUnauthorized:  "未授权访问",
		ErrCodeForbidden:     "禁止访问",
		ErrCodeNotFound:      "资源未找到",
		ErrCodeTimeout:       "请求超时",
		ErrCodeRateLimit:     "请求过于频繁，请稍后重试",
		ErrCodeSiteNotFound:  "站点未找到或已停用",
		ErrCodeCacheFailed:   "缓存处理失败",
		ErrCodeProxyFailed:   "代理请求失败",
		ErrCodeInvalidConfig: "配置无效",
		ErrCodeDBError:       "数据库操作失败",
		ErrCodeRedisError:    "缓存服务异常",
	}

	if msg, exists := messages[code]; exists {
		return msg
	}
	return "未知错误"
}

// HandleSuccess 处理成功响应
func (h *ErrorHandler) HandleSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

// HandleSuccessWithMessage 处理带消息的成功响应
func (h *ErrorHandler) HandleSuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": message,
		"data":    data,
	})
}

// SafeError 创建安全的错误（不包含敏感信息）
type SafeError struct {
	Code    ErrorCode
	Message string
}

func (e SafeError) Error() string {
	return e.Message
}

// NewSafeError 创建安全错误
func NewSafeError(code ErrorCode, message string) SafeError {
	return SafeError{
		Code:    code,
		Message: message,
	}
}