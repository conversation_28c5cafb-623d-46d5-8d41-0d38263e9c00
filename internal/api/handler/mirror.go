package handler

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"math/rand"
	"net"
	"net/http"
	"net/url"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"
	"unicode"

	"site-cluster/internal/injector"
	"site-cluster/internal/interfaces"
	"site-cluster/internal/model"
	"site-cluster/internal/service"
	"site-cluster/internal/utils"

	"github.com/PuerkitoBio/goquery"
	"github.com/gin-gonic/gin"
	"github.com/mozillazg/go-pinyin"
	"go.uber.org/zap"
)

type MirrorHandler struct {
	logger                *zap.Logger
	siteService           *service.SiteService
	cacheService          *service.FileCacheService
	crawler               interfaces.Crawler
	keywordInjector       *injector.KeywordInjector
	structInjector        *injector.StructureInjector
	pseudoProcessor       *injector.PseudoOriginalProcessor
	analyticsService      *service.AnalyticsService
	spiderBlockService    *service.SpiderBlockService
	systemSettingsService *service.SystemSettingsService
	spiderStatsService    *service.SpiderStatsService
	cache404Service       *service.Cache404Service
	pseudoService         *service.PseudoService
	sitemapService        *service.SitemapService
	htmlFixer             *service.HTMLFixer
	httpClient            *http.Client
	httpClientMutex       sync.RWMutex
}

func NewMirrorHandler(
	logger *zap.Logger,
	siteService *service.SiteService,
	cacheService *service.FileCacheService,
	crawler interfaces.Crawler,
	analyticsService *service.AnalyticsService,
	spiderBlockService *service.SpiderBlockService,
	systemSettingsService *service.SystemSettingsService,
	spiderStatsService *service.SpiderStatsService,
	cache404Service *service.Cache404Service,
	pseudoService *service.PseudoService,
	sitemapService *service.SitemapService,
) *MirrorHandler {
	return &MirrorHandler{
		logger:                logger,
		siteService:           siteService,
		cacheService:          cacheService,
		crawler:               crawler,
		spiderBlockService:    spiderBlockService,
		keywordInjector:       injector.NewKeywordInjector(logger),
		structInjector:        injector.NewStructureInjector(logger),
		pseudoProcessor:       injector.NewPseudoOriginalProcessor(logger),
		analyticsService:      analyticsService,
		systemSettingsService: systemSettingsService,
		spiderStatsService:    spiderStatsService,
		cache404Service:       cache404Service,
		pseudoService:         pseudoService,
		sitemapService:        sitemapService,
		htmlFixer:             service.NewHTMLFixer(logger),
	}
}

// checkAccessControl 检查访问控制（新的优先级逻辑：站点配置 > 全局配置）
// checkReferer 检查来源判断
func (h *MirrorHandler) checkReferer(c *gin.Context, site *model.Site) (blocked bool, statusCode int, blockHTML string) {
	referer := c.GetHeader("Referer")

	// 判断是否需要检查
	needCheck := false
	var allowedReferers string
	var blockCode int
	var blockHTMLContent string

	// 判断使用哪种配置

	if site.UseGlobalRefererCheck == nil {
		// null值，使用站点独立设置（独立模式优先）
		if site.EnableRefererCheck {
			needCheck = true
			allowedReferers = site.AllowedReferers
			blockCode = site.RefererBlockCode
			blockHTMLContent = site.RefererBlockHTML
		}
	} else if *site.UseGlobalRefererCheck {
		// true值，使用全局设置
		if h.systemSettingsService != nil {
			settings, _ := h.systemSettingsService.GetSystemSettings()
			if settings != nil && settings.EnableGlobalRefererCheck {
				needCheck = true
				allowedReferers = settings.GlobalAllowedReferers
				blockCode = settings.GlobalRefererBlockCode
				blockHTMLContent = settings.GlobalRefererBlockHTML
			}
		}
	} else {
		// false值，使用站点独立设置
		if site.EnableRefererCheck {
			needCheck = true
			allowedReferers = site.AllowedReferers
			blockCode = site.RefererBlockCode
			blockHTMLContent = site.RefererBlockHTML
		}
	}

	if !needCheck {
		return false, 0, ""
	}

	// 如果没有设置状态码，使用默认403
	if blockCode == 0 {
		blockCode = 403
	}

	// 如果没有设置HTML，使用默认内容
	if blockHTMLContent == "" {
		blockHTMLContent = "<h1>403 Forbidden</h1><p>Access denied</p>"
	}

	// 替换HTML中的变量
	blockHTMLContent = strings.ReplaceAll(blockHTMLContent, "{domain}", site.Domain)
	blockHTMLContent = strings.ReplaceAll(blockHTMLContent, "{referer}", referer)
	blockHTMLContent = strings.ReplaceAll(blockHTMLContent, "{ip}", c.ClientIP())
	blockHTMLContent = strings.ReplaceAll(blockHTMLContent, "{year}", fmt.Sprintf("%d", time.Now().Year()))
	blockHTMLContent = strings.ReplaceAll(blockHTMLContent, "{date}", time.Now().Format("2006-01-02"))

	// 如果允许列表为空，拒绝所有访问（包括直接访问）
	if allowedReferers == "" {
		return true, blockCode, blockHTMLContent
	}

	// 如果没有referer头，拒绝访问（必须有合法来源）
	if referer == "" {
		return true, blockCode, blockHTMLContent
	}

	// 解析referer获取域名
	refererURL, err := url.Parse(referer)
	if err != nil {
		return true, blockCode, blockHTMLContent
	}

	refererHost := refererURL.Host
	// 去掉端口号
	if colonIndex := strings.IndexByte(refererHost, ':'); colonIndex != -1 {
		refererHost = refererHost[:colonIndex]
	}

	// 检查是否在白名单中
	allowedList := strings.Split(allowedReferers, "|")
	for _, allowed := range allowedList {
		allowed = strings.TrimSpace(allowed)
		if allowed == "" {
			continue
		}

		// 支持通配符匹配（简单实现）
		if strings.HasPrefix(allowed, "*.") {
			// 匹配子域名
			suffix := allowed[1:] // 去掉 *
			if strings.HasSuffix(refererHost, suffix) {
				return false, 0, ""
			}
		} else if refererHost == allowed {
			// 精确匹配
			return false, 0, ""
		} else if refererHost == "www."+allowed || "www."+refererHost == allowed {
			// 支持www前缀的匹配（例如：google.com 匹配 www.google.com）
			return false, 0, ""
		}
	}

	// 不在白名单中，拒绝访问
	return true, blockCode, blockHTMLContent
}

func (h *MirrorHandler) checkAccessControl(userAgent string, site *model.Site) (blocked bool, statusCode int, blockHTML string) {
	// 1. 优先检查站点级别的爬虫屏蔽设置
	if site.EnableSpiderBlock {
		if site.UseGlobalSpiderUA {
			// 使用全局爬虫UA规则，但站点已启用屏蔽
			if h.systemSettingsService != nil && h.systemSettingsService.IsGlobalSpiderBlockEnabled() {
				if h.spiderBlockService != nil {
					blocked, returnCode := h.spiderBlockService.CheckUserAgent(userAgent)
					if blocked {
						// 如果没有返回码，默认使用403
						if returnCode == 0 {
							returnCode = http.StatusForbidden
						}
						// 优先使用站点模板，否则使用全局模板
						if site.SpiderBlock403Template != "" {
							return true, returnCode, site.SpiderBlock403Template
						}
						if h.systemSettingsService != nil {
							return true, returnCode, h.systemSettingsService.GetSpiderBlock403Template()
						}
						return true, returnCode, "<h1>403 Forbidden</h1><p>访问被拒绝</p>"
					}
				}
			}
		} else {
			// 使用站点自定义爬虫UA规则
			if site.CustomSpiderUA != "" && h.systemSettingsService != nil {
				isAllowed := h.systemSettingsService.CheckUserAgent(userAgent, site.CustomSpiderUA)
				if !isAllowed {
					// 站点自定义规则默认返回403
					if site.SpiderBlock403Template != "" {
						return true, http.StatusForbidden, site.SpiderBlock403Template
					}
					return true, http.StatusForbidden, "<h1>403 Forbidden</h1><p>访问被拒绝</p>"
				}
			}
		}
	} else {
		// 2. 站点未启用屏蔽，检查全局级别的爬虫屏蔽
		if h.systemSettingsService != nil && h.systemSettingsService.IsGlobalSpiderBlockEnabled() {
			if h.spiderBlockService != nil {
				blocked, returnCode := h.spiderBlockService.CheckUserAgent(userAgent)
				if blocked {
					// 如果没有返回码，默认使用403
					if returnCode == 0 {
						returnCode = http.StatusForbidden
					}
					// 使用全局模板
					return true, returnCode, h.systemSettingsService.GetSpiderBlock403Template()
				}
			}
		}
	}

	// 未被屏蔽
	return false, 0, ""
}

// HandleMirrorRequest 处理镜像请求
func (h *MirrorHandler) HandleMirrorRequest(c *gin.Context) {
	// 获取请求的域名和User-Agent
	host := c.Request.Host
	path := c.Request.URL.Path
	userAgent := c.GetHeader("User-Agent")

	// 去掉端口号
	if colonIndex := strings.IndexByte(host, ':'); colonIndex != -1 {
		host = host[:colonIndex]
	}

	// 先获取站点配置以确定访问控制优先级
	site, err := h.siteService.GetSiteByDomain(host)
	if err != nil || site == nil {
		h.logger.Warn("站点未配置",
			zap.String("host", host),
			zap.Error(err))
		c.String(http.StatusNotFound, "站点未配置: "+host)
		return
	}

	// 来源判断检查（Referer Check）
	if blocked, statusCode, blockHTML := h.checkReferer(c, site); blocked {
		h.logger.Info("来源判断拒绝访问",
			zap.String("host", host),
			zap.String("referer", c.GetHeader("Referer")),
			zap.String("ip", c.ClientIP()),
			zap.Int("status_code", statusCode))
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(statusCode, blockHTML)
		return
	}

	// 访问控制检查（新的优先级逻辑：站点配置 > 全局配置）
	if blocked, statusCode, blockHTML := h.checkAccessControl(userAgent, site); blocked {
		h.logger.Info("访问被拒绝",
			zap.String("host", host),
			zap.String("user_agent", userAgent),
			zap.String("ip", c.ClientIP()),
			zap.Int("status_code", statusCode))
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(statusCode, blockHTML)
		return
	}

	// 检查是否已经被缓存为404
	if h.cache404Service != nil && h.cache404Service.Is404Cached(host, path) {
		h.logger.Info("页面已被缓存为404",
			zap.String("host", host),
			zap.String("path", path))

		// 返回自定义404页面
		html404 := ""
		if h.systemSettingsService != nil {
			html404 = h.systemSettingsService.GetDefault404HTML()
		}
		if html404 == "" {
			html404 = "<h1>404 Not Found</h1>"
		}

		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(http.StatusNotFound, html404)
		return
	}

	// 检查是否是统计JS文件请求（格式：/xxxxxxxxxxxx.js）
	if matched, _ := regexp.MatchString(`^/[a-f0-9]{12}\.js$`, path); matched {
		// 返回统计JS文件（从域名对应的缓存目录中读取）
		jsPath := filepath.Join("./cache", host, path[1:]) // 去掉路径开头的/
		c.File(jsPath)
		return
	}

	// robots.txt和sitemap.xml已在router中处理，这里不再处理

	h.logger.Info("处理镜像请求",
		zap.String("host", host),
		zap.String("path", path))

	// UA判断逻辑 - 支持站点级别和全局设置
	if h.systemSettingsService != nil {
		shouldBlockUA := false
		blockHTML := ""

		// 检查UA判断设置（独立于爬虫屏蔽）
		// 1. 首先检查站点是否使用全局UA设置
		if site.UseGlobalUACheck == nil || *site.UseGlobalUACheck {
			// 使用全局UA判断设置
			if h.systemSettingsService.IsUACheckEnabled() {
				settings, _ := h.systemSettingsService.GetSystemSettings()
				allowedUA := ""
				if settings != nil {
					allowedUA = settings.DefaultAllowedUA
				}
				isAllowed := h.systemSettingsService.CheckUserAgent(userAgent, allowedUA)
				if !isAllowed {
					shouldBlockUA = true
					// 优先使用站点自定义的非爬虫HTML，否则使用全局设置
					if site.NonSpiderHTML != "" {
						blockHTML = site.NonSpiderHTML
					} else if settings != nil {
						blockHTML = settings.DefaultNonSpiderHTML
					}
				}
			}
		} else {
			// 使用站点独立的UA判断设置
			if site.EnableUACheck {
				isAllowed := h.systemSettingsService.CheckUserAgent(userAgent, site.AllowedUA)
				if !isAllowed {
					shouldBlockUA = true
					if site.NonSpiderHTML != "" {
						blockHTML = site.NonSpiderHTML
					} else {
						// 如果站点没有设置，使用全局默认值
						blockHTML = h.systemSettingsService.GetDefaultNonSpiderHTML()
					}
				}
			}
		}

		// 如果需要屏蔽UA，显示自定义HTML
		if shouldBlockUA {
			h.logger.Info("UA被屏蔽，显示自定义页面",
				zap.String("host", host),
				zap.String("user_agent", userAgent),
				zap.Bool("use_global", site.UseGlobalUACheck == nil || *site.UseGlobalUACheck),
				zap.Bool("site_ua_check", site.EnableUACheck))

			// 使用默认HTML如果没有自定义的
			if blockHTML == "" {
				blockHTML = h.systemSettingsService.GetDefaultNonSpiderHTML()
			}

			c.Header("Content-Type", "text/html; charset=utf-8")
			c.String(http.StatusOK, blockHTML)
			return
		}
	}

	// 处理目录访问 - 自动识别首页文件
	var content *model.CachedContent
	var found bool

	if path == "" || path == "/" || strings.HasSuffix(path, "/") {
		// 定义常见的首页文件列表
		indexFiles := []string{
			"index.html",
			"index.htm",
			"index.php",
			"index.jsp",
			"index.asp",
			"index.aspx",
			"index.shtml",
			"default.html",
			"default.htm",
			"default.php",
			"default.asp",
			"default.aspx",
			"home.html",
			"home.htm",
		}

		// 优先从缓存中查找首页文件
		for _, indexFile := range indexFiles {
			testPath := path
			if !strings.HasSuffix(testPath, "/") && testPath != "" {
				testPath += "/"
			}
			testPath += indexFile

			// 构建测试URL
			testURL := site.TargetURL + testPath
			if c.Request.URL.RawQuery != "" {
				testURL += "?" + c.Request.URL.RawQuery
			}

			// 检查缓存中是否存在该文件
			content, found = h.cacheService.GetContent(site.Domain, testURL, 24*time.Hour)
			if found {
				h.logger.Info("找到缓存的首页文件",
					zap.String("domain", site.Domain),
					zap.String("index_file", indexFile),
					zap.String("path", testPath))
				path = testPath // 更新路径为找到的首页文件
				break
			}
		}

		// 如果缓存中没有找到，则使用默认的index.html
		if !found {
			if !strings.HasSuffix(path, "/") && path != "" {
				path += "/"
			}
			path += "index.html"
			h.logger.Info("缓存中未找到首页文件，使用默认index.html",
				zap.String("domain", site.Domain),
				zap.String("path", path))
		}
	}

	// 构建目标URL
	targetURL := site.TargetURL + path
	if c.Request.URL.RawQuery != "" {
		targetURL += "?" + c.Request.URL.RawQuery
	}

	// 如果之前没有从缓存找到内容，则需要查询缓存
	if !found || content == nil {
		// 检查缓存
		content, found = h.cacheService.GetContent(site.Domain, targetURL, 24*time.Hour)
	}

	// 如果缓存中仍然没有，需要从目标站点获取
	if !found || content == nil {
		// 缓存中没有，需要从目标站点获取
		h.logger.Info("缓存未命中，从目标站点获取",
			zap.String("domain", site.Domain),
			zap.String("url", targetURL))

		// 从目标站点获取内容
		content, err = h.fetchFromTarget(targetURL, site)
		if err != nil {
			h.logger.Error("获取目标页面失败", zap.Error(err))

			// 如果是404错误，返回自定义404页面
			if strings.Contains(err.Error(), "404") {
				html404 := ""
				if h.systemSettingsService != nil {
					html404 = h.systemSettingsService.GetDefault404HTML()
				}
				if html404 == "" {
					html404 = "<h1>404 Not Found</h1>"
				}
				c.Header("Content-Type", "text/html; charset=utf-8")
				c.String(http.StatusNotFound, html404)
				return
			}

			c.String(http.StatusBadGateway, "获取目标页面失败")
			return
		}

		// 如果是HTML内容且没有经过Colly处理，需要处理资源链接并进行注入
		if strings.Contains(content.ContentType, "text/html") && !h.isProcessedByColly(content) {
			// 处理资源本地化（传入请求路径用于判断是否为首页）
			requestPath := path
			if c.Request.URL.RawQuery != "" {
				requestPath = path + "?" + c.Request.URL.RawQuery
			}
			processedData, charset, err := h.processHTMLContent(content.Data, targetURL, requestPath, site)
			if err != nil {
				h.logger.Error("处理HTML内容失败", zap.Error(err))
			} else {
				// 调试：检查数据编码
				h.logger.Info("处理后的数据编码",
					zap.String("charset", charset),
					zap.Int("data_size", len(processedData)))
				content.Data = processedData
				// 始终更新Content-Type为UTF-8，因为我们已经将内容转换为UTF-8
				content.ContentType = "text/html; charset=utf-8"
			}
		}

		// 保存到缓存
		if err := h.cacheService.SaveContent(site.Domain, content); err != nil {
			h.logger.Error("保存缓存失败", zap.Error(err))
		}

		// 如果启用预加载，异步预缓存下一层链接
		if site.EnablePreload && strings.Contains(content.ContentType, "text/html") {
			go h.preloadNextLevel(content, site)
		}
	} else {
		h.logger.Info("从缓存提供内容",
			zap.String("domain", site.Domain),
			zap.String("url", targetURL))
	}

	// 处理内容类型
	contentType := content.ContentType
	if contentType == "" {
		contentType = "text/html; charset=utf-8"
	}

	// 设置响应头
	c.Header("Content-Type", contentType)

	// 对HTML内容禁用缓存，避免编码问题
	if strings.Contains(contentType, "text/html") {
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
		// 明确告诉浏览器使用UTF-8编码
		c.Header("Content-Type", "text/html; charset=utf-8")
	} else {
		// 其他资源可以缓存
		c.Header("Cache-Control", "public, max-age=3600")
	}

	if len(content.Headers) > 0 {
		for k, v := range content.Headers {
			// 跳过一些不应该传递的头
			if k == "Content-Length" || k == "Content-Encoding" || k == "Transfer-Encoding" ||
				k == "Content-Type" || k == "Cache-Control" {
				continue
			}
			c.Header(k, v)
		}
	}

	// 返回内容
	c.Data(http.StatusOK, contentType, content.Data)
}

// getOrCreateHTTPClient 获取或创建HTTP客户端
func (h *MirrorHandler) getOrCreateHTTPClient(timeout int) *http.Client {
	h.httpClientMutex.RLock()
	if h.httpClient != nil {
		h.httpClientMutex.RUnlock()
		return h.httpClient
	}
	h.httpClientMutex.RUnlock()

	h.httpClientMutex.Lock()
	defer h.httpClientMutex.Unlock()

	// 双重检查
	if h.httpClient != nil {
		return h.httpClient
	}

	// 从系统设置中获取HTTP客户端配置
	var settings *model.SystemSettings
	if h.systemSettingsService != nil {
		settings, _ = h.systemSettingsService.GetSystemSettings()
	}
	if settings == nil {
		settings = &model.SystemSettings{}
	}

	// 使用系统设置的值，如果为0则使用默认值
	maxIdleConns := 200       // 默认值，适合200站点
	maxIdleConnsPerHost := 50 // 默认值
	maxConnsPerHost := 100    // 默认值
	idleConnTimeout := 90     // 默认90秒

	if settings.HTTPMaxIdleConns > 0 {
		maxIdleConns = settings.HTTPMaxIdleConns
	}
	if settings.HTTPMaxIdleConnsPerHost > 0 {
		maxIdleConnsPerHost = settings.HTTPMaxIdleConnsPerHost
	}
	if settings.HTTPMaxConnsPerHost > 0 {
		maxConnsPerHost = settings.HTTPMaxConnsPerHost
	}
	if settings.HTTPIdleConnTimeout > 0 {
		idleConnTimeout = settings.HTTPIdleConnTimeout
	}

	// 创建全局共享的HTTP客户端
	h.httpClient = &http.Client{
		Timeout: time.Duration(timeout) * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   time.Duration(timeout) * time.Second, // 使用配置的超时时间
				KeepAlive: 30 * time.Second,
				DualStack: false,
			}).DialContext,
			MaxIdleConns:          maxIdleConns,
			MaxIdleConnsPerHost:   maxIdleConnsPerHost,
			MaxConnsPerHost:       maxConnsPerHost,
			IdleConnTimeout:       time.Duration(idleConnTimeout) * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			DisableCompression:    false,
			DisableKeepAlives:     false,
		},
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 10 {
				return fmt.Errorf("too many redirects")
			}
			return nil
		},
	}

	return h.httpClient
}

// detectProtocol 检测域名的最佳协议
func (h *MirrorHandler) detectProtocol(domain string) string {
	// 移除可能的路径部分，只保留域名
	if idx := strings.Index(domain, "/"); idx != -1 {
		domain = domain[:idx]
	}
	
	// 先尝试HTTPS
	httpsURL := "https://" + domain
	client := &http.Client{
		Timeout: 5 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 允许重定向，但限制次数
			if len(via) >= 3 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}
	
	resp, err := client.Head(httpsURL)
	if err == nil && resp.StatusCode < 500 {
		resp.Body.Close()
		h.logger.Info("协议检测：HTTPS可用", zap.String("domain", domain))
		return httpsURL
	}
	if resp != nil {
		resp.Body.Close()
	}
	
	// HTTPS失败，尝试HTTP
	httpURL := "http://" + domain
	resp, err = client.Head(httpURL)
	if err == nil && resp.StatusCode < 500 {
		resp.Body.Close()
		h.logger.Info("协议检测：HTTP可用", zap.String("domain", domain))
		return httpURL
	}
	if resp != nil {
		resp.Body.Close()
	}
	
	h.logger.Warn("协议检测：无法检测到可用协议", zap.String("domain", domain))
	// 两个都失败，返回空字符串，让调用者使用默认值
	return ""
}

// fetchFromTarget 从目标站点获取内容
func (h *MirrorHandler) fetchFromTarget(targetURL string, site *model.Site) (*model.CachedContent, error) {
	// 直接使用HTTP客户端获取内容
	return h.fetchWithHTTPClient(targetURL, site)
}

// fetchWithHTTPClient 使用HTTP客户端获取内容（备用方案）
func (h *MirrorHandler) fetchWithHTTPClient(targetURL string, site *model.Site) (*model.CachedContent, error) {
	h.logger.Info("开始获取目标页面",
		zap.String("url", targetURL),
		zap.String("domain", site.Domain))

	// 创建带超时的context
	// 使用系统配置的代理请求超时
	timeout := 60
	if h.systemSettingsService != nil {
		timeout = h.systemSettingsService.GetProxyRequestTimeout()
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
	defer cancel()

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		h.logger.Error("创建请求失败", zap.Error(err))
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	// 使用随机的User-Agent
	userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
	if h.systemSettingsService != nil {
		userAgent = h.systemSettingsService.GetRandomUserAgent()
	}
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	// 不设置 Accept-Encoding，让 Go 的 http 客户端自动处理压缩

	// 使用共享的HTTP客户端池
	// 对于镜像代理请求，使用 ProxyRequestTimeout
	clientTimeout := 60
	if h.systemSettingsService != nil {
		clientTimeout = h.systemSettingsService.GetProxyRequestTimeout()
	}

	// 使用缓存的客户端或创建新的
	client := h.getOrCreateHTTPClient(clientTimeout)

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		h.logger.Error("发送请求失败", zap.String("url", targetURL), zap.Error(err))
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 移除INFO日志，减少资源占用
	// h.logger.Info("收到响应",
	// 	zap.String("url", targetURL),
	// 	zap.Int("status", resp.StatusCode))

	// 检查是否为404响应
	if resp.StatusCode == http.StatusNotFound {
		h.logger.Warn("目标页面返回404",
			zap.String("url", targetURL),
			zap.String("domain", site.Domain))

		// 缓存404状态
		if h.cache404Service != nil {
			parsedURL, _ := url.Parse(targetURL)
			h.cache404Service.Cache404Status(site.Domain, parsedURL.Path)
		}

		// 返回404错误
		return nil, fmt.Errorf("目标页面返回404")
	}

	// 自动处理压缩 - Go的http客户端会自动处理gzip
	// 读取响应体 - 保持原始编码，不做任何转换
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}
	

	// 获取内容类型
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "text/html"
	}

	// 保存响应头
	headers := make(map[string]string)
	for k, v := range resp.Header {
		if len(v) > 0 {
			headers[k] = v[0]
		}
	}

	// 根据URL判断是否为首页，设置不同的缓存时间
	cacheDuration := time.Duration(site.CacheOtherTTL) * time.Minute
	if cacheDuration <= 0 {
		cacheDuration = 24 * time.Hour // 默认24小时
	}

	// 判断是否为首页（只有根路径、index.html或index.php且无查询参数才是首页）
	parsedURL, _ := url.Parse(targetURL)
	if (parsedURL.Path == "" || parsedURL.Path == "/" ||
		parsedURL.Path == "/index.html" || parsedURL.Path == "/index.php") &&
		parsedURL.RawQuery == "" {
		homeCacheDuration := time.Duration(site.CacheHomeTTL) * time.Minute
		if homeCacheDuration > 0 {
			cacheDuration = homeCacheDuration
		} else {
			cacheDuration = 30 * time.Minute // 首页默认30分钟
		}
	}

	return &model.CachedContent{
		URL:         targetURL,
		ContentType: contentType,
		Data:        body,
		Headers:     headers,
		CachedAt:    time.Now(),
		ExpiresAt:   time.Now().Add(cacheDuration),
	}, nil
}

// processHTMLContent 处理HTML内容，下载资源并替换链接
// pageURL: 目标站点的完整URL（用于资源下载）
// requestPath: 用户请求的路径（用于判断是否为首页）
func (h *MirrorHandler) processHTMLContent(htmlData []byte, pageURL string, requestPath string, site *model.Site) ([]byte, string, error) {
	
	// 检测并转换编码为UTF-8（用于goquery解析）
	utf8Data, originalCharset, err := utils.DetectAndConvertToUTF8(htmlData, "text/html")
	if err != nil {
		h.logger.Warn("编码转换失败，使用原始数据", zap.Error(err))
		utf8Data = htmlData
		originalCharset = "utf-8"
	}

	h.logger.Info("检测到编码",
		zap.String("original_charset", originalCharset),
		zap.Int("original_size", len(htmlData)),
		zap.Int("utf8_size", len(utf8Data)))

	// 解析HTML（goquery需要UTF-8编码）
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(utf8Data))
	if err != nil {
		return htmlData, originalCharset, fmt.Errorf("解析HTML失败: %w", err)
	}

	// 解析基础URL（用于资源处理）
	baseURL, err := url.Parse(pageURL)
	if err != nil {
		return htmlData, originalCharset, fmt.Errorf("解析URL失败: %w", err)
	}

	// 处理所有资源链接
	h.processResourceLinks(doc, baseURL, site)

	// 如果启用了内容注入
	if site.InjectConfig != nil {
		// 解析请求URL来判断是否为首页
		requestURL, _ := url.Parse(requestPath)
		// 判断是否为首页（只有根路径或index文件且无查询参数才是首页）
		isHomePage := false
		if requestURL != nil {
			path := requestURL.Path
			// 只有这些路径且没有查询参数才是首页
			isHomePage = (path == "/" || path == "" ||
				path == "/index.html" || path == "/index.htm" ||
				path == "/index.php" || path == "/index.asp" ||
				path == "/index.aspx" || path == "/index.jsp") &&
				requestURL.RawQuery == ""
		}

		// 判断是否需要应用Unicode转码
		shouldApplyUnicode := false
		if site.InjectConfig.UnicodeScope == "all" ||
			(site.InjectConfig.UnicodeScope == "homepage" && isHomePage) ||
			(site.InjectConfig.UnicodeScope == "" && site.InjectConfig.EnableUnicode && isHomePage) { // 兼容旧版本
			shouldApplyUnicode = true
		}

		// 处理首页标题、描述和关键词
		if isHomePage && site.InjectConfig != nil {
			h.logger.Info("处理首页内容",
				zap.String("domain", site.Domain),
				zap.String("HomeTitle", site.InjectConfig.HomeTitle),
				zap.Bool("EnableUnicode", site.InjectConfig.EnableUnicode),
				zap.String("UnicodeScope", site.InjectConfig.UnicodeScope),
				zap.Bool("EnableUnicodeTitle", site.InjectConfig.EnableUnicodeTitle),
				zap.Bool("shouldApplyUnicode", shouldApplyUnicode),
				zap.Bool("EnableCompanyName", site.EnableCompanyName),
				zap.String("CompanyName", site.CompanyName))

			// 如果设置了首页标题，自动替换页面中的title
			if site.InjectConfig.HomeTitle != "" {
				// 先删除所有现有的title标签，避免重复
				doc.Find("title").Remove()
				// 在head开头添加新的title标签（Unicode编码将在最后处理）
				doc.Find("head").PrependHtml(fmt.Sprintf(`<title>%s</title>`, site.InjectConfig.HomeTitle))
			}

			// 如果设置了首页描述，自动替换或添加meta description
			if site.InjectConfig.HomeDescription != "" {
				// 查找meta description（支持大小写）
				metaDesc := doc.Find("meta[name='description'], meta[name='Description']")
				if metaDesc.Length() > 0 {
					// 找到了，替换内容（Unicode编码将在最后处理）
					metaDesc.SetAttr("content", site.InjectConfig.HomeDescription)
				} else {
					// 没找到，插入到head中合适的位置
					// 先找到所有的meta标签
					lastMeta := doc.Find("head meta").Last()
					if lastMeta.Length() > 0 {
						// 在最后一个meta标签后面插入
						lastMeta.AfterHtml(fmt.Sprintf(`<meta name="description" content="%s"/>`, site.InjectConfig.HomeDescription))
					} else {
						// 如果没有meta标签，插入到title后面
						titleTag := doc.Find("head title")
						if titleTag.Length() > 0 {
							titleTag.AfterHtml(fmt.Sprintf(`<meta name="description" content="%s"/>`, site.InjectConfig.HomeDescription))
						} else {
							// 如果连title都没有，直接插入到head开头
							doc.Find("head").PrependHtml(fmt.Sprintf(`<meta name="description" content="%s"/>`, site.InjectConfig.HomeDescription))
						}
					}
				}
			}

			// 如果设置了首页关键词，自动替换或添加meta keywords
			if site.InjectConfig.HomeKeywords != "" {
				// 查找meta keywords（支持大小写）
				metaKeywords := doc.Find("meta[name='keywords'], meta[name='Keywords']")
				if metaKeywords.Length() > 0 {
					// 找到了，替换内容（Unicode编码将在最后处理）
					metaKeywords.SetAttr("content", site.InjectConfig.HomeKeywords)
				} else {
					// 没找到，插入到description后面或合适位置
					metaDesc := doc.Find("meta[name='description'], meta[name='Description']")
					if metaDesc.Length() > 0 {
						// 在description后面插入
						metaDesc.AfterHtml(fmt.Sprintf(`<meta name="keywords" content="%s"/>`, site.InjectConfig.HomeKeywords))
					} else {
						// 如果没有description，找最后一个meta标签
						lastMeta := doc.Find("head meta").Last()
						if lastMeta.Length() > 0 {
							// 在最后一个meta标签后面插入
							lastMeta.AfterHtml(fmt.Sprintf(`<meta name="keywords" content="%s"/>`, site.InjectConfig.HomeKeywords))
						} else {
							// 如果没有meta标签，插入到title后面
							titleTag := doc.Find("head title")
							if titleTag.Length() > 0 {
								titleTag.AfterHtml(fmt.Sprintf(`<meta name="keywords" content="%s"/>`, site.InjectConfig.HomeKeywords))
							} else {
								// 如果连title都没有，直接插入到head开头
								doc.Find("head").PrependHtml(fmt.Sprintf(`<meta name="keywords" content="%s"/>`, site.InjectConfig.HomeKeywords))
							}
						}
					}
				}
			}

			// 企业名称注入移到简繁转换之后处理
		}

		// 全站的Unicode编码将在最后的applyUnicodeEncoding函数中处理

		// 随机字符串注入 - 必须在其他处理之前进行
		if site.InjectConfig.EnableRandomString {
			h.injectRandomStrings(doc, site.InjectConfig)
		}

		// 将整个文档转换为HTML字符串
		// 使用自定义函数来保持完整的HTML结构
		htmlStr := h.renderFullHTML(doc)

		// 第一步：伪原创 - 仅在非首页时执行
		if site.InjectConfig.EnablePseudo && !isHomePage && len(site.InjectConfig.PseudoLibraryIDs) > 0 {
			// 获取伪原创词库的词条
			pseudoWords, err := h.pseudoService.GetWordsByLibraryIDs(site.InjectConfig.PseudoLibraryIDs)
			if err != nil {
				h.logger.Error("获取伪原创词条失败", zap.Error(err))
			} else if len(pseudoWords) > 0 {
				// 转换为Keyword格式供处理器使用
				keywords := make([]model.Keyword, len(pseudoWords))
				for i, word := range pseudoWords {
					keywords[i] = model.Keyword{
						Keyword:  word.Original,
						Synonyms: word.Synonyms,
					}
				}
				h.logger.Info("应用伪原创处理",
					zap.String("domain", site.Domain),
					zap.Int("word_count", len(keywords)))
				htmlStr, _ = h.pseudoProcessor.ProcessContent(htmlStr, site.InjectConfig, keywords)
			}
		}

		// 第二步：注入统计代码
		htmlStr = h.injectAnalytics(htmlStr, site)

		// 第三步：简繁转换 - 在伪原创和统计代码之后进行
		if site.EnableTraditionalConvert {
			// 检测是否为简体中文内容
			if utils.IsSimplifiedChinese(htmlStr) {
				// 收集需要排除的关键词
				var excludeKeywords []string
				if site.InjectConfig != nil && site.InjectConfig.EnableKeyword {
					// 从配置中获取所有关键词
					excludeKeywords = append(excludeKeywords, site.InjectConfig.Keywords...)
				}

				// 使用带排除功能的转换函数
				htmlStr = utils.ConvertHTMLToTraditionalWithExclusions(htmlStr, excludeKeywords, isHomePage)
			}
		}

		// 第四步：首页关键词注入 - 在简繁转换之后进行，避免被转换影响
		if isHomePage && site.InjectConfig.EnableHomeKeywordInject {
			h.logger.Info("注入首页关键词（在简繁转换之后）",
				zap.String("domain", site.Domain),
				zap.Int("inject_count", site.InjectConfig.HomeKeywordInjectCount),
				zap.Any("library_ids", site.InjectConfig.HomeKeywordLibraryIDs),
				zap.Bool("enable_html_encode", site.InjectConfig.EnableHomeKeywordUnicode))
			htmlStr = h.injectHomeKeywords(htmlStr, site)
		}

		// 第五步：H1标签注入 - 仅在首页进行，在首页关键词之后
		if isHomePage && site.InjectConfig.EnableH1Tag {
			h.logger.Info("注入H1标签",
				zap.String("domain", site.Domain),
				zap.String("home_title", site.InjectConfig.HomeTitle),
				zap.Bool("enable_unicode", site.InjectConfig.EnableUnicode),
				zap.Bool("enable_unicode_title", site.InjectConfig.EnableUnicodeTitle),
				zap.String("unicode_scope", site.InjectConfig.UnicodeScope),
				zap.String("position", site.InjectConfig.H1TagPosition))
			htmlStr = h.injectH1Tag(htmlStr, site)
		}

		// 第六步：隐藏HTML注入
		if site.InjectConfig.EnableHiddenHTML {
			htmlStr = h.injectHiddenHTML(htmlStr, site.InjectConfig)
		}

		// 第七步：企业名称处理 - 已改为通过全局UA模板的{company}标签处理
		// 不再通过script标签直接修改title，而是依赖全局UA模板中的{company}标签
		if isHomePage && site.EnableCompanyName && site.CompanyName != "" {
			h.logger.Info("企业名称已启用，将通过全局UA模板的{company}标签处理",
				zap.String("domain", site.Domain),
				zap.String("companyName", site.CompanyName),
				zap.Bool("isHomePage", isHomePage))
		}

		// 第八步：拼音标注 - 在简繁转换之后处理，避免被转换破坏
		// 判断是否启用拼音（全局或站点设置）
		enablePinyin := false
		var pinyinConfig *model.InjectConfig

		if site.InjectConfig != nil {
			// 判断是否使用全局设置（UseGlobalPinyin == nil 表示使用全局）
			if site.InjectConfig.UseGlobalPinyin == nil {
				// 使用全局设置
				if h.systemSettingsService != nil {
					settings, _ := h.systemSettingsService.GetSystemSettings()
					if settings != nil {
						enablePinyin = settings.EnableGlobalPinyin
						// 如果全局启用拼音，构造配置
						if enablePinyin {
							// 创建临时配置，使用全局的所有拼音设置
							tempConfig := model.InjectConfig{
								SiteID: site.InjectConfig.SiteID,
								// 拼音相关设置全部从全局获取
								EnablePinyin:             true,
								EnablePinyinSpecialChars: true, // 启用特殊字符
								PinyinSpecialChars:       settings.PinyinSpecialChars,
								PinyinSpecialCharsRatio:  settings.PinyinSpecialCharsRatio,
								// 其他配置保持站点原有设置
								EnableKeyword:       site.InjectConfig.EnableKeyword,
								EnableStructure:     site.InjectConfig.EnableStructure,
								EnablePseudo:        site.InjectConfig.EnablePseudo,
								FilterExternalLinks: site.InjectConfig.FilterExternalLinks,
							}
							pinyinConfig = &tempConfig

							h.logger.Info("使用全局拼音设置",
								zap.String("domain", site.Domain),
								zap.Bool("enable_global_pinyin", settings.EnableGlobalPinyin),
								zap.String("special_chars", settings.PinyinSpecialChars),
								zap.Float32("ratio", settings.PinyinSpecialCharsRatio))
						}
					}
				}
			} else {
				// 使用站点独立设置
				// UseGlobalPinyin == true 表示独立开启
				// UseGlobalPinyin == false 表示独立关闭
				enablePinyin = *site.InjectConfig.UseGlobalPinyin
				if enablePinyin {
					pinyinConfig = site.InjectConfig
					h.logger.Info("使用站点独立拼音设置",
						zap.String("domain", site.Domain),
						zap.Bool("enable_pinyin", enablePinyin),
						zap.Bool("enable_special_chars", site.InjectConfig.EnablePinyinSpecialChars),
						zap.Float32("ratio", site.InjectConfig.PinyinSpecialCharsRatio))
				}
			}
		}

		// 需要重新解析HTML为DOM来处理拼音
		if enablePinyin && pinyinConfig != nil {
			doc2, err := goquery.NewDocumentFromReader(strings.NewReader(htmlStr))
			if err == nil {
				h.logger.Info("准备执行拼音注入",
					zap.String("domain", site.Domain),
					zap.Bool("enable_pinyin", enablePinyin),
					zap.Bool("config_not_nil", pinyinConfig != nil))
				h.injectPinyin(doc2, pinyinConfig)
				// 重新渲染完整的HTML
				htmlStr = h.renderFullHTML(doc2)
				h.logger.Info("拼音注入完成")
			} else {
				h.logger.Error("解析HTML失败，跳过拼音注入", zap.Error(err))
			}
		} else {
			h.logger.Info("跳过拼音注入",
				zap.String("domain", site.Domain),
				zap.Bool("enable_pinyin", enablePinyin),
				zap.Bool("config_not_nil", pinyinConfig != nil))
		}

		// 第九步：结构注入 - 放在拼音处理之后，避免被拼音转换
		// 仅在非首页时执行
		if site.InjectConfig.EnableStructure && !isHomePage {
			// 如果配置了结构注入专用词库，加载关键词
			if len(site.InjectConfig.StructureLibraryIDs) > 0 {
				// 创建一个临时的config副本，避免影响其他处理
				configCopy := *site.InjectConfig
				configCopy.KeywordsByLibrary = make(map[uint][]string)

				// 加载结构注入词库的关键词
				for _, libID := range site.InjectConfig.StructureLibraryIDs {
					libKeywords, err := h.siteService.GetKeywordsByLibraryID(libID)
					if err != nil {
						h.logger.Error("获取结构注入词库关键词失败",
							zap.Uint("library_id", libID),
							zap.Error(err))
						continue
					}
					// 转换为字符串数组
					keywordStrings := make([]string, len(libKeywords))
					for i, kw := range libKeywords {
						keywordStrings[i] = kw.Keyword
					}
					configCopy.KeywordsByLibrary[libID] = keywordStrings
				}

				h.logger.Info("加载结构注入词库",
					zap.String("domain", site.Domain),
					zap.Any("library_ids", site.InjectConfig.StructureLibraryIDs),
					zap.Int("total_keywords", len(configCopy.KeywordsByLibrary)))

				htmlStr, _ = h.structInjector.InjectStructures(htmlStr, &configCopy)
			} else {
				// 没有配置专用词库，使用通用关键词
				htmlStr, _ = h.structInjector.InjectStructures(htmlStr, site.InjectConfig)
			}
		}

		// 第十步：SEO关键词注入和模板替换 - 在所有文本转换之后执行，避免被拼音和简繁转换影响
		if site.InjectConfig.EnableKeyword && !isHomePage {
			// 注意：InjectKeywords 已经会处理标题、Meta和描述的关键词注入
			// 所以不需要再调用 applyKeywordTemplates
			// 直接使用 keywordInjector 进行所有关键词注入
			htmlStr, _ = h.keywordInjector.InjectKeywords(htmlStr, site.InjectConfig)
		}

		// 最后处理Unicode编码 - 在所有HTML处理完成后进行
		if shouldApplyUnicode {
			htmlStr = h.applyUnicodeEncoding(htmlStr, site.InjectConfig, isHomePage)
		}

		// 不转换编码，保持UTF-8，但更新charset声明
		htmlStr = utils.UpdateHTMLCharset(htmlStr, "utf-8")

		finalData := []byte(htmlStr)
		return finalData, "utf-8", nil
	}

	// 返回处理后的HTML，使用renderFullHTML确保包含完整的DOCTYPE和html标签
	htmlStr := h.renderFullHTML(doc)
	
	// 注入统计代码
	htmlBytes := []byte(h.injectAnalytics(htmlStr, site))

	// 如果需要Unicode编码，在最后处理title标签中的中文
	if site.InjectConfig != nil &&
		(site.InjectConfig.UnicodeScope == "all" ||
			(site.InjectConfig.UnicodeScope == "homepage" && (baseURL.Path == "/" || baseURL.Path == ""))) &&
		site.InjectConfig.EnableUnicodeTitle {
		// 使用正则表达式匹配title标签内容
		titleRegex := regexp.MustCompile(`<title>([^<]+)</title>`)
		htmlBytes = titleRegex.ReplaceAllFunc(htmlBytes, func(match []byte) []byte {
			// 提取title内容
			matches := titleRegex.FindSubmatch(match)
			if len(matches) > 1 {
				titleContent := string(matches[1])
				// 编码为HTML实体
				encodedTitle := h.toUnicodeEntities(titleContent)
				return []byte(fmt.Sprintf("<title>%s</title>", encodedTitle))
			}
			return match
		})
	}

	// 在页面底部添加sitemap链接（如果启用）
	bodyEndRegex := regexp.MustCompile(`(?i)(</body>)`)
	sitemapLink := `<div style="text-align:center;margin:20px 0;font-size:12px;"><a href="/sitemap.xml" style="color:#666;">Sitemap</a></div>`
	htmlBytes = bodyEndRegex.ReplaceAll(htmlBytes, []byte(sitemapLink+"\n${1}"))

	// 记录到 sitemap（内容注入完成后）
	if h.sitemapService != nil && site.EnableSitemap {
		go func() {
			// 对于动态URL，需要包含查询参数
			urlPath := baseURL.Path
			if baseURL.RawQuery != "" {
				urlPath = urlPath + "?" + baseURL.RawQuery
			}
			if err := h.sitemapService.AddURL(site.ID, site.Domain, urlPath); err != nil {
				h.logger.Error("记录sitemap条目失败",
					zap.String("domain", site.Domain),
					zap.String("url", urlPath),
					zap.Error(err))
			}
		}()
	}

	// 不转换编码，保持UTF-8，但更新charset声明
	htmlStr = utils.UpdateHTMLCharset(string(htmlBytes), "utf-8")
	
	// 使用 golang.org/x/net/html 修复HTML，确保标签正确闭合
	// 这必须在最后进行，避免被其他字符串操作破坏
	if h.htmlFixer != nil {
		h.logger.Info("开始修复HTML", zap.Int("处理后长度", len(htmlStr)))
		fixedHTML, err := h.htmlFixer.Fix(htmlStr)
		if err == nil {
			htmlStr = fixedHTML
			h.logger.Info("HTML修复成功", zap.Int("修复后长度", len(htmlStr)))
		} else {
			h.logger.Error("HTML修复失败，使用原始HTML", zap.Error(err))
		}
	} else {
		h.logger.Warn("HTMLFixer未初始化")
	}
	
	htmlBytes = []byte(htmlStr)
	

	return htmlBytes, "utf-8", nil
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// processResourceLinks 处理资源链接
func (h *MirrorHandler) processResourceLinks(doc *goquery.Document, baseURL *url.URL, site *model.Site) {
	// 处理img标签
	doc.Find("img").Each(func(i int, s *goquery.Selection) {
		if src, exists := s.Attr("src"); exists {
			newSrc := h.processResourceURL(src, baseURL, site, "images")
			s.SetAttr("src", newSrc)
		}
	})

	// 处理link标签（CSS）
	doc.Find("link[rel='stylesheet']").Each(func(i int, s *goquery.Selection) {
		if href, exists := s.Attr("href"); exists {
			newHref := h.processResourceURL(href, baseURL, site, "css")
			s.SetAttr("href", newHref)
		}
	})

	// 处理script标签
	doc.Find("script[src]").Each(func(i int, s *goquery.Selection) {
		if src, exists := s.Attr("src"); exists {
			newSrc := h.processResourceURL(src, baseURL, site, "js")
			s.SetAttr("src", newSrc)
		}
	})

	// 处理style标签中的url()
	doc.Find("style").Each(func(i int, s *goquery.Selection) {
		styleContent := s.Text()
		newStyle := h.processStyleURLs(styleContent, baseURL, site)
		s.SetText(newStyle)
	})

	// 处理内联style属性中的url()
	doc.Find("[style]").Each(func(i int, s *goquery.Selection) {
		if style, exists := s.Attr("style"); exists {
			newStyle := h.processStyleURLs(style, baseURL, site)
			s.SetAttr("style", newStyle)
		}
	})

	// 处理a标签 - 确保所有链接都使用相对路径
	targetURL, _ := url.Parse(site.TargetURL)
	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		if href, exists := s.Attr("href"); exists {
			// 解析链接
			linkURL, err := url.Parse(href)
			if err != nil {
				return
			}

			// 检查是否是目标站的绝对URL
			if linkURL.Host != "" && targetURL != nil && linkURL.Host == targetURL.Host {
				// 这是目标站自身的链接，转换为相对路径
				newHref := linkURL.Path
				if linkURL.RawQuery != "" {
					newHref += "?" + linkURL.RawQuery
				}
				if linkURL.Fragment != "" {
					newHref += "#" + linkURL.Fragment
				}
				s.SetAttr("href", newHref)
				return
			}

			// 转换为绝对URL
			absoluteURL := baseURL.ResolveReference(linkURL)

			// 处理协议相对URL（//example.com/path）
			if href != "" && strings.HasPrefix(href, "//") {
				// 如果是协议相对URL且指向同一域名，转换为相对路径
				if strings.HasPrefix(href, "//"+baseURL.Host) {
					newHref := strings.TrimPrefix(href, "//"+baseURL.Host)
					if newHref == "" {
						newHref = "/"
					}
					s.SetAttr("href", newHref)
					return
				}
			}

			// 如果是同域名的链接，转换为相对路径
			if absoluteURL.Host == baseURL.Host {
				// 使用相对路径，避免协议问题
				newHref := absoluteURL.Path
				if absoluteURL.RawQuery != "" {
					newHref += "?" + absoluteURL.RawQuery
				}
				if absoluteURL.Fragment != "" {
					newHref += "#" + absoluteURL.Fragment
				}
				s.SetAttr("href", newHref)
			} else if site.InjectConfig != nil && site.InjectConfig.FilterExternalLinks {
				// 如果配置了过滤外链，则移除
				s.Remove()
			}
			// 否则保持原样（外部链接）
		}
	})

	// 处理form标签的action属性
	doc.Find("form[action]").Each(func(i int, s *goquery.Selection) {
		if action, exists := s.Attr("action"); exists {
			// 解析action URL
			actionURL, err := url.Parse(action)
			if err != nil {
				return
			}

			// 转换为绝对URL
			absoluteURL := baseURL.ResolveReference(actionURL)

			// 如果是同域名，使用相对路径
			if absoluteURL.Host == baseURL.Host {
				newAction := absoluteURL.Path
				if absoluteURL.RawQuery != "" {
					newAction += "?" + absoluteURL.RawQuery
				}
				s.SetAttr("action", newAction)
			}
		}
	})
}

// processResourceURL 处理资源URL
func (h *MirrorHandler) processResourceURL(resourceURL string, baseURL *url.URL, site *model.Site, resourceType string) string {
	// 获取目标站点的URL用于判断
	targetURL, _ := url.Parse(site.TargetURL)

	// 处理协议相对URL
	if strings.HasPrefix(resourceURL, "//") {
		// 如果是协议相对URL且指向同一域名，转换为相对路径
		if strings.HasPrefix(resourceURL, "//"+baseURL.Host) {
			resourceURL = strings.TrimPrefix(resourceURL, "//"+baseURL.Host)
			if resourceURL == "" {
				resourceURL = "/"
			}
		} else {
			// 外部协议相对URL，使用HTTP协议
			resourceURL = "http:" + resourceURL
		}
	}

	// 解析资源URL
	parsedURL, err := url.Parse(resourceURL)
	if err != nil {
		return resourceURL
	}

	// 如果资源URL是目标站的绝对URL（如 https://www.gd-wy.com/public/static/...）
	// 需要转换为相对路径并下载
	if parsedURL.Host != "" && targetURL != nil && parsedURL.Host == targetURL.Host {
		// 这是目标站自身的资源，需要转换为相对路径
		localPath := parsedURL.Path
		if localPath == "" || localPath == "/" {
			localPath = "/index.html"
		}

		// 异步下载资源
		go h.downloadResource(parsedURL.String(), site)

		return localPath
	}

	// 转换为绝对URL
	absoluteURL := baseURL.ResolveReference(parsedURL)

	// 如果是外部资源，根据配置决定是否下载
	if h.isExternalResource(absoluteURL, baseURL) && !site.DownloadExternalResources {
		return absoluteURL.String() // 保持原始URL
	}

	// 异步下载资源
	go h.downloadResource(absoluteURL.String(), site)

	// 返回相对于缓存目录的路径
	// 保持原始的路径结构
	localPath := absoluteURL.Path
	if localPath == "" || localPath == "/" {
		localPath = "/index.html"
	}

	return localPath
}

// processStyleURLs 处理CSS中的URL
func (h *MirrorHandler) processStyleURLs(style string, baseURL *url.URL, site *model.Site) string {
	// 匹配CSS中的url()
	urlRegex := regexp.MustCompile(`url\(['"]?([^'")\s]+)['"]?\)`)

	return urlRegex.ReplaceAllStringFunc(style, func(match string) string {
		// 提取URL
		matches := urlRegex.FindStringSubmatch(match)
		if len(matches) < 2 {
			return match
		}

		oldURL := matches[1]
		newURL := h.processResourceURL(oldURL, baseURL, site, "assets")
		return fmt.Sprintf("url('%s')", newURL)
	})
}

// downloadResource 下载资源文件
func (h *MirrorHandler) downloadResource(resourceURL string, site *model.Site) {
	// 创建HTTP请求
	req, err := http.NewRequest("GET", resourceURL, nil)
	if err != nil {
		h.logger.Error("创建资源请求失败", zap.String("url", resourceURL), zap.Error(err))
		return
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Referer", site.TargetURL)

	// 发送请求
	// 使用系统配置的代理请求超时
	resourceTimeout := 60
	if h.systemSettingsService != nil {
		resourceTimeout = h.systemSettingsService.GetProxyRequestTimeout()
	}
	client := &http.Client{Timeout: time.Duration(resourceTimeout) * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		h.logger.Error("下载资源失败", zap.String("url", resourceURL), zap.Error(err))
		return
	}
	defer resp.Body.Close()

	// 读取内容
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		h.logger.Error("读取资源失败", zap.String("url", resourceURL), zap.Error(err))
		return
	}

	// 保存到缓存
	content := &model.CachedContent{
		URL:         resourceURL,
		ContentType: resp.Header.Get("Content-Type"),
		Data:        data,
		Headers:     make(map[string]string),
		CachedAt:    time.Now(),
		ExpiresAt:   time.Now().Add(7 * 24 * time.Hour), // 资源缓存7天
	}

	// 使用缓存服务保存
	if err := h.cacheService.SaveContent(site.Domain, content); err != nil {
		h.logger.Error("保存资源缓存失败", zap.String("url", resourceURL), zap.Error(err))
	}
}

// preloadNextLevel 预加载下一层链接
func (h *MirrorHandler) preloadNextLevel(content *model.CachedContent, site *model.Site) {
	// 只处理HTML内容
	if !strings.Contains(content.ContentType, "text/html") {
		return
	}

	// 初始化预加载深度（使用缓存深度作为预加载深度）
	maxDepth := site.CrawlDepth
	if maxDepth <= 0 {
		maxDepth = 2 // 默认深度为2
	}

	// 开始递归预加载
	h.logger.Info("开始预加载",
		zap.String("domain", site.Domain),
		zap.String("start_url", content.URL),
		zap.Int("max_depth", maxDepth))

	// 使用map记录已访问的URL，避免重复
	visited := make(map[string]bool)
	visited[content.URL] = true
	visitedMutex := &sync.RWMutex{}

	// 开始递归预加载
	h.recursivePreload(content.URL, site, 1, maxDepth, visited, visitedMutex)
}

// recursivePreload 递归预加载页面
func (h *MirrorHandler) recursivePreload(pageURL string, site *model.Site, currentDepth, maxDepth int, visited map[string]bool, visitedMutex *sync.RWMutex) {
	// 检查深度限制
	if currentDepth > maxDepth {
		return
	}

	// 获取页面内容（可能从缓存或远程获取）
	var content *model.CachedContent
	cachedContent, found := h.cacheService.GetContent(site.Domain, pageURL, 24*time.Hour)

	if !found {
		// 需要从远程获取
		fetchedContent, err := h.fetchFromTarget(pageURL, site)
		if err != nil {
			h.logger.Error("预加载获取内容失败",
				zap.String("url", pageURL),
				zap.Int("depth", currentDepth),
				zap.Error(err))
			return
		}

		// 处理HTML内容
		if strings.Contains(fetchedContent.ContentType, "text/html") {
			// 从pageURL中提取路径作为请求路径
			parsedURL, _ := url.Parse(pageURL)
			requestPath := ""
			if parsedURL != nil {
				requestPath = parsedURL.Path
				if parsedURL.RawQuery != "" {
					requestPath = requestPath + "?" + parsedURL.RawQuery
				}
			}
			processedData, charset, _ := h.processHTMLContent(fetchedContent.Data, pageURL, requestPath, site)
			fetchedContent.Data = processedData
			if charset != "" && charset != "utf-8" {
				fetchedContent.ContentType = fmt.Sprintf("text/html; charset=%s", charset)
			}
		}

		// 保存缓存
		if err := h.cacheService.SaveContent(site.Domain, fetchedContent); err != nil {
			h.logger.Error("保存预加载缓存失败",
				zap.String("url", pageURL),
				zap.Int("depth", currentDepth),
				zap.Error(err))
		} else {

			// 预加载的HTML页面也添加到sitemap
			if h.sitemapService != nil && site.EnableSitemap && strings.Contains(fetchedContent.ContentType, "text/html") {
				if parsedURL, err := url.Parse(pageURL); err == nil {
					// 异步记录sitemap，避免阻塞预加载
					go func() {
						// 对于动态URL，需要包含查询参数
						urlPath := parsedURL.Path
						if parsedURL.RawQuery != "" {
							urlPath = urlPath + "?" + parsedURL.RawQuery
						}
						if err := h.sitemapService.AddURL(site.ID, site.Domain, urlPath); err != nil {
							h.logger.Error("预加载页面记录sitemap失败",
								zap.String("domain", site.Domain),
								zap.String("url", urlPath),
								zap.Int("depth", currentDepth),
								zap.Error(err))
						}
					}()
				}
			}
		}

		content = fetchedContent
	} else {
		// 使用已存在的缓存内容，不重新获取避免覆盖
		content = cachedContent
	}

	// 只处理HTML内容
	if !strings.Contains(content.ContentType, "text/html") {
		return
	}

	// 解析HTML获取链接
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(content.Data))
	if err != nil {
		h.logger.Error("解析HTML失败", zap.Error(err))
		return
	}

	// 解析基础URL
	baseURL, err := url.Parse(pageURL)
	if err != nil {
		h.logger.Error("解析URL失败", zap.Error(err))
		return
	}

	// 收集所有内部链接
	var links []string
	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		if href, exists := s.Attr("href"); exists {
			// 解析链接
			linkURL, err := url.Parse(href)
			if err != nil {
				return
			}

			// 转换为绝对URL
			absoluteURL := baseURL.ResolveReference(linkURL)

			// 只预加载同域名的链接，且未访问过的
			if absoluteURL.Host == baseURL.Host {
				urlStr := absoluteURL.String()

				// 并发安全地检查和标记访问
				visitedMutex.Lock()
				if !visited[urlStr] {
					visited[urlStr] = true
					links = append(links, urlStr)
				}
				visitedMutex.Unlock()
			}
		}
	})

	// 限制每层预加载数量（根据深度递减）
	maxPreloadPerLevel := 20 / currentDepth // 深度越深，预加载越少
	if maxPreloadPerLevel < 5 {
		maxPreloadPerLevel = 5
	}
	if len(links) > maxPreloadPerLevel {
		links = links[:maxPreloadPerLevel]
	}

	h.logger.Info("预加载当前层链接",
		zap.String("page", pageURL),
		zap.Int("depth", currentDepth),
		zap.Int("links_count", len(links)))

	// 异步预加载下一层链接
	for _, link := range links {
		go func(url string, depth int) {
			// 递归预加载下一层
			h.recursivePreload(url, site, depth+1, maxDepth, visited, visitedMutex)
		}(link, currentDepth)
	}
}

// 辅助方法

// isExternalLink 判断是否为外部链接
func (h *MirrorHandler) isExternalLink(href string, baseURL *url.URL) bool {
	linkURL, err := url.Parse(href)
	if err != nil {
		return false
	}

	absoluteURL := baseURL.ResolveReference(linkURL)
	return absoluteURL.Host != "" && absoluteURL.Host != baseURL.Host
}

// isExternalResource 判断是否为外部资源
func (h *MirrorHandler) isExternalResource(resourceURL, baseURL *url.URL) bool {
	return resourceURL.Host != "" && resourceURL.Host != baseURL.Host
}

// isProcessedByColly 检查内容是否已经被Colly处理过
func (h *MirrorHandler) isProcessedByColly(content *model.CachedContent) bool {
	// 检查是否有Colly处理标记
	if processed, ok := content.Headers["X-Processed-By"]; ok {
		return processed == "colly"
	}
	return false
}

// toUnicodeEntities 将中文字符转换为HTML实体编码（十六进制格式）
func (h *MirrorHandler) toUnicodeEntities(text string) string {
	var result strings.Builder
	for _, r := range text {
		// 检查是否为非ASCII字符
		if r > 127 {
			// 转换为 &#x十六进制; 格式（HTML实体编码标准格式）
			result.WriteString(fmt.Sprintf("&#x%04X;", r))
		} else {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// applyKeywordTemplates 应用关键词模板替换标题、关键词和描述
func (h *MirrorHandler) applyKeywordTemplates(htmlStr string, config *model.InjectConfig) string {
	// 为不同位置分别获取关键词
	titleKeywords := []string{}
	metaKeywords := []string{}
	descKeywords := []string{}

	// 获取标题关键词
	if len(config.TitleKeywordLibraryIDs) > 0 {
		for _, libID := range config.TitleKeywordLibraryIDs {
			libKeywords, err := h.siteService.GetKeywordsByLibraryID(libID)
			if err == nil {
				for _, kw := range libKeywords {
					titleKeywords = append(titleKeywords, kw.Keyword)
				}
			}
		}
	} else if len(config.KeywordLibraryIDs) > 0 {
		// 向后兼容：如果没有设置独立的标题关键词库，使用通用关键词库
		for _, libID := range config.KeywordLibraryIDs {
			libKeywords, err := h.siteService.GetKeywordsByLibraryID(libID)
			if err == nil {
				for _, kw := range libKeywords {
					titleKeywords = append(titleKeywords, kw.Keyword)
				}
			}
		}
	}

	// 获取Meta关键词
	if len(config.MetaKeywordLibraryIDs) > 0 {
		for _, libID := range config.MetaKeywordLibraryIDs {
			libKeywords, err := h.siteService.GetKeywordsByLibraryID(libID)
			if err == nil {
				for _, kw := range libKeywords {
					metaKeywords = append(metaKeywords, kw.Keyword)
				}
			}
		}
	} else if len(config.KeywordLibraryIDs) > 0 {
		// 向后兼容
		for _, libID := range config.KeywordLibraryIDs {
			libKeywords, err := h.siteService.GetKeywordsByLibraryID(libID)
			if err == nil {
				for _, kw := range libKeywords {
					metaKeywords = append(metaKeywords, kw.Keyword)
				}
			}
		}
	}

	// 获取描述关键词
	if len(config.DescKeywordLibraryIDs) > 0 {
		for _, libID := range config.DescKeywordLibraryIDs {
			libKeywords, err := h.siteService.GetKeywordsByLibraryID(libID)
			if err == nil {
				for _, kw := range libKeywords {
					descKeywords = append(descKeywords, kw.Keyword)
				}
			}
		}
	} else if len(config.KeywordLibraryIDs) > 0 {
		// 向后兼容
		for _, libID := range config.KeywordLibraryIDs {
			libKeywords, err := h.siteService.GetKeywordsByLibraryID(libID)
			if err == nil {
				for _, kw := range libKeywords {
					descKeywords = append(descKeywords, kw.Keyword)
				}
			}
		}
	}

	// 不再预先打乱关键词顺序，让 replaceTemplate 函数随机选择

	// 始终删除多余的title标签（保留第一个）
	titleRegex := regexp.MustCompile(`<title>([^<]*)</title>`)
	titleMatches := titleRegex.FindAllStringIndex(htmlStr, -1)
	if len(titleMatches) > 1 {
		// 从后往前删除，避免索引变化
		for i := len(titleMatches) - 1; i > 0; i-- {
			htmlStr = htmlStr[:titleMatches[i][0]] + htmlStr[titleMatches[i][1]:]
		}
	}

	// 替换标题 - 需要同时满足模板存在和注入开关开启
	if config.KeywordTitleTemplate != "" && config.KeywordInjectTitle && len(titleKeywords) > 0 {
		// 处理第一个title标签
		processed := false
		htmlStr = titleRegex.ReplaceAllStringFunc(htmlStr, func(match string) string {
			if !processed {
				processed = true
				matches := titleRegex.FindStringSubmatch(match)
				if len(matches) > 1 {
					originalTitle := matches[1]
					newTitle := h.replaceTemplate(config.KeywordTitleTemplate, titleKeywords, originalTitle)
					return fmt.Sprintf("<title>%s</title>", newTitle)
				}
			}
			return match
		})
	}

	// 始终删除多余的meta keywords标签（保留第一个）
	keywordsRegex := regexp.MustCompile(`<meta\s+name=["']?[Kk]eywords["']?\s+content=["']([^"']*)["'][^>]*>`)
	keywordsMatches := keywordsRegex.FindAllStringIndex(htmlStr, -1)
	if len(keywordsMatches) > 1 {
		for i := len(keywordsMatches) - 1; i > 0; i-- {
			htmlStr = htmlStr[:keywordsMatches[i][0]] + htmlStr[keywordsMatches[i][1]:]
		}
	}

	// 替换meta keywords - 需要同时满足模板存在和注入开关开启
	if config.KeywordMetaTemplate != "" && config.KeywordInjectMeta && len(metaKeywords) > 0 {
		htmlStr = keywordsRegex.ReplaceAllStringFunc(htmlStr, func(match string) string {
			matches := keywordsRegex.FindStringSubmatch(match)
			originalKeywords := ""
			if len(matches) > 1 {
				originalKeywords = matches[1]
			}
			newKeywords := h.replaceTemplate(config.KeywordMetaTemplate, metaKeywords, originalKeywords)
			return fmt.Sprintf(`<meta name="keywords" content="%s"/>`, newKeywords)
		})

		// 如果没有找到keywords标签，在第一个title后面添加
		if !keywordsRegex.MatchString(htmlStr) {
			newKeywords := h.replaceTemplate(config.KeywordMetaTemplate, metaKeywords, "")
			titleRegex := regexp.MustCompile(`(</title>)`)
			// 只替换第一个</title>
			if loc := titleRegex.FindStringIndex(htmlStr); loc != nil {
				htmlStr = htmlStr[:loc[0]] + "</title>\n<meta name=\"keywords\" content=\"" + newKeywords + "\"/>" + htmlStr[loc[1]:]
			}
		}
	}

	// 始终删除多余的meta description标签（保留第一个）
	descRegex := regexp.MustCompile(`<meta\s+name=["']?[Dd]escription["']?\s+content=["']([^"']*)["'][^>]*>`)
	descMatches := descRegex.FindAllStringIndex(htmlStr, -1)
	if len(descMatches) > 1 {
		for i := len(descMatches) - 1; i > 0; i-- {
			htmlStr = htmlStr[:descMatches[i][0]] + htmlStr[descMatches[i][1]:]
		}
	}

	// 替换meta description - 需要同时满足模板存在和注入开关开启
	if config.KeywordDescTemplate != "" && config.KeywordInjectDesc && len(descKeywords) > 0 {
		htmlStr = descRegex.ReplaceAllStringFunc(htmlStr, func(match string) string {
			matches := descRegex.FindStringSubmatch(match)
			originalDesc := ""
			if len(matches) > 1 {
				originalDesc = matches[1]
			}
			newDesc := h.replaceTemplate(config.KeywordDescTemplate, descKeywords, originalDesc)
			return fmt.Sprintf(`<meta name="description" content="%s"/>`, newDesc)
		})

		// 如果没有找到description标签，在keywords后面添加
		if !descRegex.MatchString(htmlStr) {
			newDesc := h.replaceTemplate(config.KeywordDescTemplate, descKeywords, "")
			keywordsRegex := regexp.MustCompile(`(<meta name="keywords"[^>]*>)`)
			// 只在第一个keywords后添加
			if loc := keywordsRegex.FindStringIndex(htmlStr); loc != nil {
				htmlStr = htmlStr[:loc[1]] + "\n<meta name=\"description\" content=\"" + newDesc + "\"/>" + htmlStr[loc[1]:]
			} else {
				// 如果也没有keywords，就在第一个title后面添加
				titleRegex := regexp.MustCompile(`(</title>)`)
				if loc := titleRegex.FindStringIndex(htmlStr); loc != nil {
					htmlStr = htmlStr[:loc[0]] + "</title>\n<meta name=\"description\" content=\"" + newDesc + "\"/>" + htmlStr[loc[1]:]
				}
			}
		}
	}

	return htmlStr
}

// replaceTemplate 替换模板中的变量
func (h *MirrorHandler) replaceTemplate(template string, keywords []string, original string) string {
	result := template

	// 替换 {original}
	result = strings.Replace(result, "{original}", original, -1)

	// 创建随机数生成器
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 替换 {keyword1} 到 {keyword10}
	// 每个占位符都随机选择一个关键词
	for i := 1; i <= 10; i++ {
		placeholder := fmt.Sprintf("{keyword%d}", i)
		if strings.Contains(result, placeholder) {
			if len(keywords) > 0 {
				// 随机选择一个关键词
				randomIndex := r.Intn(len(keywords))
				result = strings.Replace(result, placeholder, keywords[randomIndex], -1)
			}
		}
	}

	return result
}

// applyUnicodeEncoding 在HTML字符串中应用Unicode编码
func (h *MirrorHandler) applyUnicodeEncoding(htmlStr string, config *model.InjectConfig, isHomePage bool) string {
	// 保存编码后的title内容，供H1标签使用
	var encodedTitle string

	// 处理title标签
	if config.EnableUnicodeTitle || config.EnableUnicode {
		// 匹配title标签内容
		titleRegex := regexp.MustCompile(`<title>([^<]*)</title>`)
		htmlStr = titleRegex.ReplaceAllStringFunc(htmlStr, func(match string) string {
			// 提取标题内容
			matches := titleRegex.FindStringSubmatch(match)
			if len(matches) > 1 {
				// 对标题内容进行编码
				encodedTitle = h.toUnicodeEntities(matches[1])
				return fmt.Sprintf("<title>%s</title>", encodedTitle)
			}
			return match
		})
	}

	// 处理meta description
	if config.EnableUnicodeDesc || config.EnableUnicode {
		// 匹配meta description标签（支持两种格式：name在前或content在前）
		descRegex1 := regexp.MustCompile(`<meta\s+name=["']?[Dd]escription["']?\s+content=["']([^"']*)["'][^>]*>`)
		descRegex2 := regexp.MustCompile(`<meta\s+content=["']([^"']*)["']\s+name=["']?[Dd]escription["'][^>]*>`)

		// 处理第一种格式
		htmlStr = descRegex1.ReplaceAllStringFunc(htmlStr, func(match string) string {
			matches := descRegex1.FindStringSubmatch(match)
			if len(matches) > 1 {
				encodedContent := h.toUnicodeEntities(matches[1])
				return strings.Replace(match, matches[1], encodedContent, 1)
			}
			return match
		})

		// 处理第二种格式
		htmlStr = descRegex2.ReplaceAllStringFunc(htmlStr, func(match string) string {
			matches := descRegex2.FindStringSubmatch(match)
			if len(matches) > 1 {
				encodedContent := h.toUnicodeEntities(matches[1])
				return strings.Replace(match, matches[1], encodedContent, 1)
			}
			return match
		})
	}

	// 处理meta keywords
	if config.EnableUnicodeKeywords || config.EnableUnicode {
		// 匹配meta keywords标签（支持两种格式：name在前或content在前）
		keywordsRegex1 := regexp.MustCompile(`<meta\s+name=["']?[Kk]eywords["']?\s+content=["']([^"']*)["'][^>]*>`)
		keywordsRegex2 := regexp.MustCompile(`<meta\s+content=["']([^"']*)["']\s+name=["']?[Kk]eywords["'][^>]*>`)

		// 处理第一种格式
		htmlStr = keywordsRegex1.ReplaceAllStringFunc(htmlStr, func(match string) string {
			matches := keywordsRegex1.FindStringSubmatch(match)
			if len(matches) > 1 {
				encodedContent := h.toUnicodeEntities(matches[1])
				return strings.Replace(match, matches[1], encodedContent, 1)
			}
			return match
		})

		// 处理第二种格式
		htmlStr = keywordsRegex2.ReplaceAllStringFunc(htmlStr, func(match string) string {
			matches := keywordsRegex2.FindStringSubmatch(match)
			if len(matches) > 1 {
				encodedContent := h.toUnicodeEntities(matches[1])
				return strings.Replace(match, matches[1], encodedContent, 1)
			}
			return match
		})
	}

	// 处理H1标签（仅在首页，使用与title标签相同的编码内容）
	if isHomePage && config.EnableH1Tag && encodedTitle != "" {
		// H1标签注入的是HomeTitle，应该与title标签使用相同的编码内容
		// 匹配H1标签中的链接文本
		h1Regex := regexp.MustCompile(`<h1><a[^>]*>([^<]*)</a></h1>`)
		htmlStr = h1Regex.ReplaceAllStringFunc(htmlStr, func(match string) string {
			matches := h1Regex.FindStringSubmatch(match)
			if len(matches) > 1 {
				// 直接使用已编码的title内容替换H1内容
				return strings.Replace(match, matches[1], encodedTitle, 1)
			}
			return match
		})

		h.logger.Info("H1标签使用已编码的title内容",
			zap.String("encoded_title", encodedTitle),
			zap.Bool("enable_unicode", config.EnableUnicode),
			zap.Bool("enable_unicode_title", config.EnableUnicodeTitle))
	}

	return htmlStr
}

// injectRandomStrings 只为class属性注入随机字符串（不修改ID以避免破坏CSS选择器）
func (h *MirrorHandler) injectRandomStrings(doc *goquery.Document, config *model.InjectConfig) {
	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	// 为每个页面生成一个唯一的随机字符串
	randomStr := h.generateRandomString(config.RandomStringLength)

	// 只处理class属性，不修改ID以避免破坏CSS和JavaScript
	doc.Find("[class]").Each(func(i int, s *goquery.Selection) {
		if class, exists := s.Attr("class"); exists && class != "" {
			// 在原有class后面添加随机字符串，确保有空格分隔
			newClass := strings.TrimSpace(class) + " " + randomStr
			s.SetAttr("class", newClass)
		}
	})
}

// generateRandomString 生成指定长度的随机字符串
func (h *MirrorHandler) generateRandomString(length int) string {
	if length <= 0 {
		length = 4
	}

	// 定义字符集（小写字母和数字）
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"

	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}

	return string(result)
}

// injectPinyin 为body内的中文文本添加拼音标注
func (h *MirrorHandler) injectPinyin(doc *goquery.Document, config *model.InjectConfig) {
	if config != nil {
		h.logger.Info("开始注入拼音",
			zap.Bool("enable_pinyin", config.EnablePinyin),
			zap.Bool("enable_special_chars", config.EnablePinyinSpecialChars),
			zap.String("special_chars", config.PinyinSpecialChars),
			zap.Float32("ratio", config.PinyinSpecialCharsRatio))
	}

	// 只处理body内的文本节点
	doc.Find("body *").Each(func(i int, s *goquery.Selection) {
		// 跳过script、style、a标签和包含href属性的元素
		if s.Is("script") || s.Is("style") || s.Is("a") {
			return
		}

		// 跳过标记为不处理拼音的元素（如首页关键词div）
		if noPinyin, exists := s.Attr("data-no-pinyin"); exists && noPinyin == "true" {
			return
		}

		// 检查是否在首页关键词注入的元素内部
		// 只检查直接父元素链，避免误判
		parent := s.Parent()
		for parent.Length() > 0 && !parent.Is("body") {
			// 检查是否有data-no-pinyin属性
			if noPinyin, exists := parent.Attr("data-no-pinyin"); exists && noPinyin == "true" {
				return
			}
			// 检查是否是marquee元素（首页关键词注入的特征）
			if parent.Is("marquee") {
				if onmouseover, exists := parent.Attr("onmouseover"); exists && strings.Contains(onmouseover, "this.scrollAmount=0") {
					return
				}
			}
			parent = parent.Parent()
		}

		// 跳过包含链接的父元素
		if s.Find("a").Length() > 0 {
			return
		}

		// 处理元素的文本内容
		h.processTextNodes(s, config)
	})
}

// processTextNodes 处理元素的文本节点
func (h *MirrorHandler) processTextNodes(s *goquery.Selection, config *model.InjectConfig) {
	// 获取原始HTML
	html, err := s.Html()
	if err != nil {
		return
	}

	// 如果没有中文，直接返回
	if !h.containsChinese(html) {
		return
	}

	// 处理文本内容，保留HTML标签
	processedHTML := h.processMixedContent(html, config)

	// 设置处理后的内容
	s.SetHtml(processedHTML)
}

// processMixedContent 处理包含HTML标签的混合内容
func (h *MirrorHandler) processMixedContent(content string, config *model.InjectConfig) string {
	// 正则表达式匹配HTML标签
	tagRegex := regexp.MustCompile(`<[^>]+>`)

	// 找到所有标签的位置
	tags := tagRegex.FindAllStringIndex(content, -1)

	var result strings.Builder
	lastEnd := 0

	// 处理标签之间的文本
	for _, tagPos := range tags {
		// 处理标签前的文本
		if tagPos[0] > lastEnd {
			text := content[lastEnd:tagPos[0]]
			result.WriteString(h.addPinyinToText(text, config))
		}

		// 保留原始标签
		result.WriteString(content[tagPos[0]:tagPos[1]])
		lastEnd = tagPos[1]
	}

	// 处理最后一个标签后的文本
	if lastEnd < len(content) {
		text := content[lastEnd:]
		result.WriteString(h.addPinyinToText(text, config))
	}

	return result.String()
}

// addPinyinToText 为纯文本添加拼音（随机选择部分字符）
func (h *MirrorHandler) addPinyinToText(text string, config *model.InjectConfig) string {
	if text == "" || !h.containsChinese(text) {
		return text
	}


	var result strings.Builder

	// 将文本按字符处理
	runes := []rune(text)
	i := 0

	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	// 获取特殊字符配置
	var specialChars []string
	var insertRatio float32 = 0.3
	var enableSpecialChars bool

	if config != nil {
		// 使用传入配置中的设置
		enableSpecialChars = config.EnablePinyinSpecialChars
		if config.PinyinSpecialCharsRatio > 0 {
			insertRatio = config.PinyinSpecialCharsRatio
		}

		// 优先使用config中的特殊字符列表
		if config.PinyinSpecialChars != "" {
			specialChars = strings.Split(config.PinyinSpecialChars, "|")
		} else if h.systemSettingsService != nil {
			// 如果config中没有，从全局设置获取
			settings, _ := h.systemSettingsService.GetSystemSettings()
			if settings != nil && settings.PinyinSpecialChars != "" {
				specialChars = strings.Split(settings.PinyinSpecialChars, "|")
			}
		}
	}

	for i < len(runes) {
		char := runes[i]

		// 检查是否是中文字符
		if h.isChineseChar(char) {
			// 收集连续的中文字符
			chineseChars := []rune{char}
			j := i + 1

			// 继续收集连续的中文字符
			for j < len(runes) && h.isChineseChar(runes[j]) {
				chineseChars = append(chineseChars, runes[j])
				j++
			}

			// 转换为拼音
			chineseStr := string(chineseChars)
			pinyinResult := pinyin.Pinyin(chineseStr, pinyin.NewArgs())

			// 逐个字符添加拼音标注
			for k, ch := range chineseChars {
				result.WriteRune(ch)
				// 随机决定是否添加拼音（使用配置的概率）
				if k < len(pinyinResult) && len(pinyinResult[k]) > 0 && rand.Float32() < 0.3 {
					// 添加拼音标注
					result.WriteString("(")
					result.WriteString(pinyinResult[k][0])
					result.WriteString(")")

					// 如果启用了特殊字符插入，并且有特殊字符列表
					if enableSpecialChars && len(specialChars) > 0 {
						// 根据比例决定是否插入特殊字符
						if rand.Float32() < insertRatio {
							// 随机选择一个特殊字符
							specialChar := specialChars[rand.Intn(len(specialChars))]
							// 随机决定插入位置（前面或后面）
							if rand.Float32() < 0.5 {
								// 插入到拼音前面，需要回退并重新构建
								// 这里简化处理，直接添加到后面
								result.WriteString(specialChar)
							} else {
								// 插入到拼音后面
								result.WriteString(specialChar)
							}
						}
					}
				}
			}

			i = j
		} else {
			// 非中文字符直接输出
			result.WriteRune(char)
			i++
		}
	}

	return result.String()
}

// containsChinese 检查字符串是否包含中文
func (h *MirrorHandler) containsChinese(text string) bool {
	for _, r := range text {
		if h.isChineseChar(r) {
			return true
		}
	}
	return false
}

// isChineseChar 判断是否为中文字符
func (h *MirrorHandler) isChineseChar(r rune) bool {
	return unicode.Is(unicode.Han, r)
}

// injectH1Tag 注入H1标签到首页
func (h *MirrorHandler) injectH1Tag(html string, site *model.Site) string {
	config := site.InjectConfig
	if config == nil || !config.EnableH1Tag || config.HomeTitle == "" {
		h.logger.Warn("H1标签注入条件不满足",
			zap.Bool("config_nil", config == nil),
			zap.Bool("enable_h1", config != nil && config.EnableH1Tag),
			zap.String("home_title", config.HomeTitle))
		return html
	}

	// 获取标题内容（不进行编码，让它在 applyUnicodeEncoding 中统一处理）
	titleText := config.HomeTitle

	// 调试：打印当前配置值
	h.logger.Info("H1标签配置",
		zap.String("home_title", config.HomeTitle),
		zap.Bool("enable_unicode", config.EnableUnicode),
		zap.String("unicode_scope", config.UnicodeScope),
		zap.Bool("enable_unicode_title", config.EnableUnicodeTitle),
		zap.String("h1_tag_position", config.H1TagPosition))

	// 构建H1标签，使用站点域名作为链接
	// Unicode编码将在 applyUnicodeEncoding 函数中统一处理，与title标签保持一致
	h1Tag := fmt.Sprintf(`<h1><a href="https://%s">%s</a></h1>`, site.Domain, titleText)

	// 创建正则表达式来匹配body标签
	bodyStartRegex := regexp.MustCompile(`(?i)(<body[^>]*>)`)
	bodyEndRegex := regexp.MustCompile(`(?i)(</body>)`)

	// 根据配置的位置注入H1标签
	switch config.H1TagPosition {
	case "top":
		// 只在顶部注入
		html = bodyStartRegex.ReplaceAllString(html, "${1}\n"+h1Tag)

	case "bottom":
		// 只在底部注入
		html = bodyEndRegex.ReplaceAllString(html, h1Tag+"\n${1}")

	default: // "both"
		// 在顶部和底部都注入
		html = bodyStartRegex.ReplaceAllString(html, "${1}\n"+h1Tag)
		html = bodyEndRegex.ReplaceAllString(html, h1Tag+"\n${1}")
	}

	return html
}

// injectHiddenHTML 注入隐藏HTML到body标签
func (h *MirrorHandler) injectHiddenHTML(html string, config *model.InjectConfig) string {
	// 创建隐藏HTML生成器
	generator := utils.NewHiddenHTMLGenerator()

	// 创建正则表达式来匹配body标签（包含任意属性）
	bodyStartRegex := regexp.MustCompile(`(?i)(<body[^>]*>)`)
	bodyEndRegex := regexp.MustCompile(`(?i)(</body>)`)

	// 根据配置生成隐藏HTML
	switch config.HiddenHTMLPosition {
	case "top":
		// 只在顶部注入（前后添加换行，与其他内容分开）
		hiddenHTML := generator.GenerateHiddenHTML(config.HiddenHTMLLength, config.HiddenHTMLRandomID)
		// 在<body>标签后注入
		html = bodyStartRegex.ReplaceAllString(html, "${1}\n"+hiddenHTML+"\n")

	case "bottom":
		// 只在底部注入（前后添加换行，与其他内容分开）
		hiddenHTML := generator.GenerateHiddenHTML(config.HiddenHTMLLength, config.HiddenHTMLRandomID)
		// 在</body>标签前注入
		html = bodyEndRegex.ReplaceAllString(html, "\n"+hiddenHTML+"\n${1}")

	default: // "both"
		// 在顶部和底部都注入（前后添加换行，与其他内容分开）
		topHTML, bottomHTML := generator.GenerateHiddenHTMLSeparate(config.HiddenHTMLLength, config.HiddenHTMLRandomID)
		// 在<body>标签后注入顶部内容
		html = bodyStartRegex.ReplaceAllString(html, "${1}\n"+topHTML+"\n")
		// 在</body>标签前注入底部内容
		html = bodyEndRegex.ReplaceAllString(html, "\n"+bottomHTML+"\n${1}")
	}

	return html
}

// injectAnalytics 注入统计代码到</head>标签前
func (h *MirrorHandler) injectAnalytics(html string, site *model.Site) string {
	// 统计代码优先级逻辑：
	// 1. 如果站点明确设置为不使用统计代码（use_global_analytics为false），则不注入
	// 2. 如果站点有分类，且分类启用了独立统计，使用分类的统计代码生成JS文件
	// 3. 否则使用全局统计代码生成JS文件
	
	// 优先级1：站点明确禁用统计
	if site.UseGlobalAnalytics != nil && !*site.UseGlobalAnalytics {
		return html
	}

	// 优先级2：检查分类是否有独立统计代码
	if site.Category != nil && site.Category.UseIndependentAnalytics {
		// 使用分类的独立统计代码生成JS文件
		if err := h.analyticsService.GenerateCategoryAnalyticsJS(site.Domain, site.Category.AnalyticsCode); err != nil {
			h.logger.Error("生成分类独立统计JS文件失败", zap.Error(err))
		}
	} else {
		// 优先级3：使用全局统计设置生成JS文件
		if err := h.analyticsService.GenerateAnalyticsJS(site.Domain); err != nil {
			h.logger.Error("生成全局统计JS文件失败", zap.Error(err))
		}
	}

	// 获取统一的JS引用标签（不管是分类还是全局，都使用相同的JS文件名）
	analyticsScript := h.analyticsService.GetAnalyticsScript(site.Domain)

	if analyticsScript == "" {
		return html
	}

	// 在</head>标签前注入统计代码引用
	headEndRegex := regexp.MustCompile(`(?i)(</head>)`)
	html = headEndRegex.ReplaceAllString(html, analyticsScript+"\n${1}")

	return html
}

// injectHomeKeywords 注入首页关键词
func (h *MirrorHandler) injectHomeKeywords(html string, site *model.Site) string {
	config := site.InjectConfig
	if config == nil || !config.EnableHomeKeywordInject || len(config.HomeKeywordLibraryIDs) == 0 || config.HomeKeywordInjectCount <= 0 {
		h.logger.Warn("首页关键词注入条件不满足",
			zap.Bool("config_nil", config == nil),
			zap.Bool("enable_inject", config != nil && config.EnableHomeKeywordInject),
			zap.Int("library_count", len(config.HomeKeywordLibraryIDs)),
			zap.Int("inject_count", config.HomeKeywordInjectCount))
		return html
	}

	// 从数据库获取随机关键词
	var allKeywords []string
	for _, libID := range config.HomeKeywordLibraryIDs {
		keywords, err := h.siteService.GetKeywordsByLibraryID(libID)
		if err != nil {
			h.logger.Error("获取首页关键词库失败",
				zap.Uint("library_id", libID),
				zap.Error(err))
			continue
		}

		// 将关键词添加到总列表
		for _, kw := range keywords {
			allKeywords = append(allKeywords, kw.Keyword)
		}
	}

	if len(allKeywords) == 0 {
		h.logger.Warn("没有找到可用的首页关键词", zap.String("domain", site.Domain))
		return html
	}

	// 随机抽取指定数量的关键词
	selectedKeywords := make([]string, 0, config.HomeKeywordInjectCount)

	// 如果关键词数量少于需要注入的数量，则重复使用
	for i := 0; i < config.HomeKeywordInjectCount; i++ {
		keyword := allKeywords[rand.Intn(len(allKeywords))]

		// 如果启用了HTML实体编码，进行编码
		if config.EnableHomeKeywordUnicode {
			encoded := h.encodeToHtmlEntity(keyword)
			if encoded != "" {
				keyword = encoded
			}
		}

		selectedKeywords = append(selectedKeywords, keyword)
	}

	// 生成隐藏的关键词HTML（每个关键词一行，添加标识跳过拼音处理）
	keywordHTML := `<table id="table1" height="15" cellspacing="0" cellpadding="0" width="90%" border="0" style="font-size: 12px; cursor: default; color: buttontext" class="` + generateRandomString(8) + `" data-no-pinyin="true"><caption><font color="#5AFF63"><marquee onmouseover="this.scrollAmount=0" onmouseout="this.scrollAmount=1" scrollamount="1" scrolldelay="1" direction="up" width="100%" height="3">`
	for i, keyword := range selectedKeywords {
		if i > 0 {
			keywordHTML += "\n"
		}
		keywordHTML += keyword
	}
	keywordHTML += `</marquee></font></caption></table>`

	// 确定插入位置
	bodyEndRegex := regexp.MustCompile(`(?i)(</body>)`)

	// 根据H1和隐藏HTML的开启状态决定位置（逻辑保持不变）
	if config.EnableH1Tag || config.EnableHiddenHTML {
		// 插入到H1或隐藏HTML之前的位置（实际插入到body结束前，由调用顺序保证）
		html = bodyEndRegex.ReplaceAllString(html, "\n"+keywordHTML+"\n${1}")
	} else {
		// 插入到页面最底部
		html = bodyEndRegex.ReplaceAllString(html, "\n"+keywordHTML+"\n${1}")
	}

	h.logger.Info("首页关键词注入完成",
		zap.String("domain", site.Domain),
		zap.Int("selected_count", len(selectedKeywords)),
		zap.Int("total_available", len(allKeywords)),
		zap.Bool("unicode_encoded", config.EnableHomeKeywordUnicode))

	return html
}

// renderFullHTML 渲染完整的HTML文档，保留DOCTYPE和所有标签
func (h *MirrorHandler) renderFullHTML(doc *goquery.Document) string {
	// goquery的Html()方法只返回内部HTML内容，不包含DOCTYPE和<html>标签
	// 我们需要使用goquery.OuterHtml来获取完整文档，或者手动重建
	
	// 获取内部HTML内容
	innerHTML, err := doc.Html()
	if err != nil {
		h.logger.Error("获取HTML内容失败", zap.Error(err))
		return ""
	}
	
	
	// 构建完整的HTML文档
	var fullHTML strings.Builder
	
	// 1. 添加DOCTYPE声明
	fullHTML.WriteString("<!DOCTYPE html>\n")
	
	// 2. 检查并添加<html>标签
	// 获取html元素的属性
	htmlElem := doc.Find("html")
	if htmlElem.Length() > 0 {
		// 如果有html元素，获取其属性
		lang, _ := htmlElem.Attr("lang")
		if lang != "" {
			fullHTML.WriteString(fmt.Sprintf("<html lang=\"%s\">\n", lang))
		} else {
			fullHTML.WriteString("<html>\n")
		}
	} else {
		// 默认添加html标签
		fullHTML.WriteString("<html>\n")
	}
	
	// 3. 添加head部分
	headHTML, _ := doc.Find("head").Html()
	if headHTML != "" {
		fullHTML.WriteString("<head>\n")
		fullHTML.WriteString(headHTML)
		fullHTML.WriteString("\n</head>\n")
	}
	
	// 4. 添加body部分
	bodyElem := doc.Find("body")
	if bodyElem.Length() > 0 {
		// 获取body的属性
		var bodyAttrs []string
		if class, exists := bodyElem.Attr("class"); exists {
			bodyAttrs = append(bodyAttrs, fmt.Sprintf(`class="%s"`, class))
		}
		if id, exists := bodyElem.Attr("id"); exists {
			bodyAttrs = append(bodyAttrs, fmt.Sprintf(`id="%s"`, id))
		}
		if style, exists := bodyElem.Attr("style"); exists {
			bodyAttrs = append(bodyAttrs, fmt.Sprintf(`style="%s"`, style))
		}
		
		if len(bodyAttrs) > 0 {
			fullHTML.WriteString(fmt.Sprintf("<body %s>\n", strings.Join(bodyAttrs, " ")))
		} else {
			fullHTML.WriteString("<body>\n")
		}
		
		// 添加body内容
		bodyHTML, _ := bodyElem.Html()
		fullHTML.WriteString(bodyHTML)
		fullHTML.WriteString("\n</body>\n")
	} else {
		// 如果没有明确的body标签，把所有非head内容当作body
		// 移除head内容后的剩余部分
		fullHTML.WriteString("<body>\n")
		fullHTML.WriteString(innerHTML)
		fullHTML.WriteString("\n</body>\n")
	}
	
	// 5. 关闭html标签
	fullHTML.WriteString("</html>")
	
	finalHTML := fullHTML.String()
	
	
	return finalHTML
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}

// encodeToUnicode Unicode编码函数
func (h *MirrorHandler) encodeToUnicode(text string) string {
	if text == "" {
		return ""
	}

	result := ""
	for _, r := range text {
		// 只对中文字符进行Unicode编码
		if r > 127 {
			result += fmt.Sprintf("\\u%04x", r)
		} else {
			result += string(r)
		}
	}
	return result
}

// encodeToHtmlEntity 将文本转换为HTML数字实体编码
func (h *MirrorHandler) encodeToHtmlEntity(text string) string {
	if text == "" {
		return ""
	}

	result := ""
	for _, r := range text {
		// 只对中文字符进行HTML实体编码
		if r > 127 {
			result += fmt.Sprintf("&#%d;", r)
		} else {
			result += string(r)
		}
	}
	return result
}
