package handler

import (
	"net"
	"net/http"
	"sync"
	"time"

	"site-cluster/internal/model"
	"go.uber.org/zap"
)

// HTTPClientManager 安全的HTTP客户端管理器
type HTTPClientManager struct {
	mu      sync.RWMutex
	clients map[int]*http.Client // key是超时时间（秒）
	logger  *zap.Logger
}

// NewHTTPClientManager 创建新的客户端管理器
func NewHTTPClientManager(logger *zap.Logger) *HTTPClientManager {
	return &HTTPClientManager{
		clients: make(map[int]*http.Client),
		logger:  logger,
	}
}

// GetClient 获取或创建HTTP客户端（线程安全）
func (m *HTTPClientManager) GetClient(timeout int, settings *model.SystemSettings) *http.Client {
	// 先尝试读锁获取
	m.mu.RLock()
	if client, exists := m.clients[timeout]; exists {
		m.mu.RUnlock()
		return client
	}
	m.mu.RUnlock()

	// 需要创建新客户端，使用写锁
	m.mu.Lock()
	defer m.mu.Unlock()

	// 双重检查，防止并发创建
	if client, exists := m.clients[timeout]; exists {
		return client
	}

	// 创建新客户端
	client := m.createClient(timeout, settings)
	m.clients[timeout] = client
	
	m.logger.Debug("创建新的HTTP客户端", 
		zap.Int("timeout", timeout),
		zap.Int("total_clients", len(m.clients)))
	
	return client
}

// createClient 创建HTTP客户端
func (m *HTTPClientManager) createClient(timeout int, settings *model.SystemSettings) *http.Client {
	// 默认配置
	maxIdleConns := 200
	maxIdleConnsPerHost := 50
	maxConnsPerHost := 100
	idleConnTimeout := 90

	// 如果有系统设置，使用系统设置的值
	if settings != nil {
		if settings.HTTPMaxIdleConns > 0 {
			maxIdleConns = settings.HTTPMaxIdleConns
		}
		if settings.HTTPMaxIdleConnsPerHost > 0 {
			maxIdleConnsPerHost = settings.HTTPMaxIdleConnsPerHost
		}
		if settings.HTTPMaxConnsPerHost > 0 {
			maxConnsPerHost = settings.HTTPMaxConnsPerHost
		}
		if settings.HTTPIdleConnTimeout > 0 {
			idleConnTimeout = settings.HTTPIdleConnTimeout
		}
	}

	return &http.Client{
		Timeout: time.Duration(timeout) * time.Second,
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   time.Duration(timeout) * time.Second,
				KeepAlive: 30 * time.Second,
				DualStack: false,
			}).DialContext,
			MaxIdleConns:          maxIdleConns,
			MaxIdleConnsPerHost:   maxIdleConnsPerHost,
			MaxConnsPerHost:       maxConnsPerHost,
			IdleConnTimeout:       time.Duration(idleConnTimeout) * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			DisableCompression:    false,
			DisableKeepAlives:     false,
		},
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 10 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}
}

// ClearClients 清理所有客户端（用于资源释放）
func (m *HTTPClientManager) ClearClients() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// 关闭所有客户端的空闲连接
	for _, client := range m.clients {
		if transport, ok := client.Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}
	}
	
	// 清空map
	m.clients = make(map[int]*http.Client)
	m.logger.Info("清理所有HTTP客户端")
}