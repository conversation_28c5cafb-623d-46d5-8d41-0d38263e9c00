package handler

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"
	
	"site-cluster/internal/model"
	"site-cluster/internal/service"
	
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SpiderStatsHandler 爬虫统计处理器
type SpiderStatsHandler struct {
	logger             *zap.Logger
	spiderStatsService *service.SpiderStatsService
	redisCache         *service.RedisCacheService
}

// NewSpiderStatsHandler 创建爬虫统计处理器
func NewSpiderStatsHandler(logger *zap.Logger, spiderStatsService *service.SpiderStatsService) *SpiderStatsHandler {
	return &SpiderStatsHandler{
		logger:             logger,
		spiderStatsService: spiderStatsService,
	}
}

// SetRedisCache 设置Redis缓存服务
func (h *SpiderStatsHandler) SetRedisCache(redisCache *service.RedisCacheService) {
	h.redisCache = redisCache
}

// clearSpiderCache 清除爬虫相关缓存
func (h *SpiderStatsHandler) clearSpiderCache() {
	if h.redisCache != nil {
		// 清除爬虫配置缓存
		h.redisCache.Delete("spider_configs:all")
		// 清除爬虫统计缓存（使用模式匹配删除）
		h.redisCache.DeleteByPattern("spider_stats:*")
		h.logger.Debug("已清除爬虫相关缓存")
	}
}

// GetSpiderStats 获取爬虫统计数据
func (h *SpiderStatsHandler) GetSpiderStats(c *gin.Context) {
	// 获取查询参数
	timeRange := c.DefaultQuery("range", "hour") // hour, day, week
	domain := c.Query("domain")
	siteIDStr := c.Query("site_id") // 新增：支持站点ID参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	sortBy := c.DefaultQuery("sort_by", "total") // 排序字段：domain, total, 或爬虫名称
	sortOrder := c.DefaultQuery("sort_order", "desc") // 排序方向：asc 或 desc
	
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}
	
	// 优先使用站点ID，其次使用域名
	var filterParam string
	if siteIDStr != "" {
		filterParam = siteIDStr // 使用站点ID
	} else {
		filterParam = domain // 向后兼容，使用域名
	}
	
	// 生成缓存键（包含排序参数）
	cacheKey := fmt.Sprintf("spider_stats:%s:%s:%s:%d:%d:%s:%s", 
		timeRange, domain, siteIDStr, page, pageSize, sortBy, sortOrder)
	
	// 尝试从Redis缓存获取
	if h.redisCache != nil {
		if cachedData, found := h.redisCache.Get(cacheKey); found && cachedData != "" {
			h.logger.Debug("爬虫统计缓存命中", zap.String("key", cacheKey))
			c.Header("X-Cache", "HIT")
			c.Header("Content-Type", "application/json; charset=utf-8")
			c.Data(http.StatusOK, "application/json; charset=utf-8", []byte(cachedData))
			return
		}
	}
	
	// 获取统计数据（传入排序参数）
	stats, err := h.spiderStatsService.GetSpiderStatsWithSort(timeRange, filterParam, page, pageSize, sortBy, sortOrder)
	if err != nil {
		h.logger.Error("获取爬虫统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取统计数据失败",
		})
		return
	}
	
	// 构建响应
	response := gin.H{
		"success": true,
		"data":    stats,
	}
	
	// 缓存响应（60秒）
	if h.redisCache != nil {
		if jsonData, err := json.Marshal(response); err == nil {
			h.redisCache.Set(cacheKey, string(jsonData), 60*time.Second)
			h.logger.Debug("爬虫统计已缓存", zap.String("key", cacheKey))
		}
	}
	
	c.Header("X-Cache", "MISS")
	c.JSON(http.StatusOK, response)
}

// GetSpiderConfigs 获取爬虫配置列表
func (h *SpiderStatsHandler) GetSpiderConfigs(c *gin.Context) {
	// 生成缓存键
	cacheKey := "spider_configs:all"
	
	// 尝试从Redis缓存获取（配置列表变化不频繁，缓存120秒）
	if h.redisCache != nil {
		if cachedData, found := h.redisCache.Get(cacheKey); found && cachedData != "" {
			h.logger.Debug("爬虫配置缓存命中", zap.String("key", cacheKey))
			c.Header("X-Cache", "HIT")
			c.Header("Content-Type", "application/json; charset=utf-8")
			c.Data(http.StatusOK, "application/json; charset=utf-8", []byte(cachedData))
			return
		}
	}
	
	configs, err := h.spiderStatsService.GetSpiderConfigs()
	if err != nil {
		h.logger.Error("获取爬虫配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取配置失败",
		})
		return
	}
	
	// 构建响应
	response := gin.H{
		"success": true,
		"data":    configs,
	}
	
	// 缓存响应（120秒）
	if h.redisCache != nil {
		if jsonData, err := json.Marshal(response); err == nil {
			h.redisCache.Set(cacheKey, string(jsonData), 120*time.Second)
			h.logger.Debug("爬虫配置已缓存", zap.String("key", cacheKey))
		}
	}
	
	c.Header("X-Cache", "MISS")
	c.JSON(http.StatusOK, response)
}

// CreateSpiderConfig 创建爬虫配置
func (h *SpiderStatsHandler) CreateSpiderConfig(c *gin.Context) {
	var config model.SpiderConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}
	
	// 验证必填字段
	if config.Name == "" || config.DisplayName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "名称和显示名称不能为空",
		})
		return
	}
	
	// 创建配置
	if err := h.spiderStatsService.SaveSpiderConfig(&config); err != nil {
		h.logger.Error("创建爬虫配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "创建配置失败",
		})
		return
	}
	
	// 清除缓存
	h.clearSpiderCache()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// UpdateSpiderConfig 更新爬虫配置
func (h *SpiderStatsHandler) UpdateSpiderConfig(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的ID",
		})
		return
	}
	
	var config model.SpiderConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}
	
	config.ID = uint(id)
	
	// 更新配置
	if err := h.spiderStatsService.SaveSpiderConfig(&config); err != nil {
		h.logger.Error("更新爬虫配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "更新配置失败",
		})
		return
	}
	
	// 清除缓存
	h.clearSpiderCache()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// DeleteSpiderConfig 删除爬虫配置
func (h *SpiderStatsHandler) DeleteSpiderConfig(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的ID",
		})
		return
	}
	
	// 删除配置
	if err := h.spiderStatsService.DeleteSpiderConfig(uint(id)); err != nil {
		h.logger.Error("删除爬虫配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "删除配置失败",
		})
		return
	}
	
	// 清除缓存
	h.clearSpiderCache()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// ToggleSpiderConfig 切换爬虫配置启用状态
func (h *SpiderStatsHandler) ToggleSpiderConfig(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的ID",
		})
		return
	}
	
	// 获取现有配置
	configs, err := h.spiderStatsService.GetSpiderConfigs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取配置失败",
		})
		return
	}
	
	// 查找并切换状态
	var config *model.SpiderConfig
	for i := range configs {
		if configs[i].ID == uint(id) {
			config = &configs[i]
			config.Enabled = !config.Enabled
			break
		}
	}
	
	if config == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "配置不存在",
		})
		return
	}
	
	// 保存更新
	if err := h.spiderStatsService.SaveSpiderConfig(config); err != nil {
		h.logger.Error("切换爬虫配置状态失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "更新状态失败",
		})
		return
	}
	
	// 清除缓存
	h.clearSpiderCache()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// ClearSpiderStats 清空爬虫统计数据
func (h *SpiderStatsHandler) ClearSpiderStats(c *gin.Context) {
	// 清空所有统计数据
	if err := h.spiderStatsService.ClearAllStats(); err != nil {
		h.logger.Error("清空爬虫统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "清空统计数据失败",
		})
		return
	}
	
	// 清除缓存
	h.clearSpiderCache()
	
	h.logger.Info("已清空所有爬虫统计数据")
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "统计数据已清空",
	})
}

// DeleteDomainStats 删除指定域名的统计数据
func (h *SpiderStatsHandler) DeleteDomainStats(c *gin.Context) {
	domain := c.Param("domain")
	if domain == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名不能为空",
		})
		return
	}
	
	// 删除域名统计数据
	if err := h.spiderStatsService.DeleteDomainStats(domain); err != nil {
		h.logger.Error("删除域名统计失败", zap.Error(err), zap.String("domain", domain))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "删除域名统计数据失败",
		})
		return
	}
	
	h.logger.Info("已删除域名统计数据", zap.String("domain", domain))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "域名统计数据已删除",
	})
}

// BatchDeleteDomainStats 批量删除域名的统计数据
func (h *SpiderStatsHandler) BatchDeleteDomainStats(c *gin.Context) {
	var req struct {
		Domains []string `json:"domains"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误",
		})
		return
	}
	
	if len(req.Domains) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "域名列表不能为空",
		})
		return
	}
	
	// 批量删除域名统计数据
	deletedCount := 0
	for _, domain := range req.Domains {
		if err := h.spiderStatsService.DeleteDomainStats(domain); err != nil {
			h.logger.Error("删除域名统计失败", zap.Error(err), zap.String("domain", domain))
			continue
		}
		deletedCount++
	}
	
	h.logger.Info("批量删除域名统计数据", zap.Int("total", len(req.Domains)), zap.Int("deleted", deletedCount))
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量删除成功",
		"deleted": deletedCount,
		"total":   len(req.Domains),
	})
}
