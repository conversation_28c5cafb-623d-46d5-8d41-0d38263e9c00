package handler

import (
	"fmt"
	"net/http"
	"runtime"
	"site-cluster/internal/service"
	"site-cluster/internal/service/captcha"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SystemSettingsHandler struct {
	logger                *zap.Logger
	systemSettingsService *service.SystemSettingsService
	captchaService        *captcha.CaptchaService
}

func NewSystemSettingsHandler(logger *zap.Logger, systemSettingsService *service.SystemSettingsService) *SystemSettingsHandler {
	return &SystemSettingsHandler{
		logger:                logger,
		systemSettingsService: systemSettingsService,
	}
}

// SetCaptchaService 设置验证码服务
func (h *SystemSettingsHandler) SetCaptchaService(captchaService *captcha.CaptchaService) {
	h.captchaService = captchaService
}

// GetSystemSettings 获取系统设置
func (h *SystemSettingsHandler) GetSystemSettings(c *gin.Context) {
	settings, err := h.systemSettingsService.GetSystemSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
	})
}

// UpdateSystemSettings 更新系统设置
func (h *SystemSettingsHandler) UpdateSystemSettings(c *gin.Context) {
	var req UpdateSystemSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 先获取当前设置
	currentSettings, err := h.systemSettingsService.GetSystemSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取当前设置失败",
		})
		return
	}
	
	params := service.UpdateSystemSettingsParams{
		// 如果请求中没有提供某个布尔字段，则保留当前值
		EnableGlobalUACheck:            getBoolValue(req.EnableGlobalUACheck, currentSettings.EnableGlobalUACheck),
		DefaultNonSpiderHTML:           req.DefaultNonSpiderHTML,
		DefaultAllowedUA:               req.DefaultAllowedUA,
		DefaultSpiderBlockUA:           req.DefaultSpiderBlockUA,
		UserAgentList:                  req.UserAgentList,
		DefaultCacheHomeTTL:            req.DefaultCacheHomeTTL,
		DefaultCacheOtherTTL:           req.DefaultCacheOtherTTL,
		DefaultEnableRedisCache:        getBoolValue(req.DefaultEnableRedisCache, currentSettings.DefaultEnableRedisCache),
		EnableGlobalSpiderBlock:        getBoolValue(req.EnableGlobalSpiderBlock, currentSettings.EnableGlobalSpiderBlock),
		SpiderBlock403Template:         req.SpiderBlock403Template,
		SiteNotFoundHTML:               req.SiteNotFoundHTML,
		Default404HTML:                 req.Default404HTML,
		Enable404Cache:                 getBoolValue(req.Enable404Cache, currentSettings.Enable404Cache),
		Cache404TTL:                    req.Cache404TTL,
		GlobalRedirectWWW:              getBoolValue(req.GlobalRedirectWWW, currentSettings.GlobalRedirectWWW),
		EnableUAStats:                  getBoolValue(req.EnableUAStats, currentSettings.EnableUAStats),
		
		// 拼音特殊字符配置
		EnableGlobalPinyin:      getBoolValue(req.EnableGlobalPinyin, currentSettings.EnableGlobalPinyin),
		PinyinSpecialChars:      req.PinyinSpecialChars,
		PinyinSpecialCharsRatio: req.PinyinSpecialCharsRatio,
		
		// Redis连接池配置
		RedisMaxPoolSize:        req.RedisMaxPoolSize,
		
		FileReadTimeout:         req.FileReadTimeout,
		FileWriteTimeout:        req.FileWriteTimeout,
		AsyncQueueSize:          req.AsyncQueueSize,
		AsyncWorkerCount:        req.AsyncWorkerCount,
		
		ProxyRequestTimeout:     req.ProxyRequestTimeout,
		
		// 性能配置
		DBMaxOpenConns:          req.DBMaxOpenConns,
		DBMaxIdleConns:          req.DBMaxIdleConns,
		DBConnMaxLifetime:       req.DBConnMaxLifetime,
		DBSlaveMaxOpenConns:     req.DBSlaveMaxOpenConns,
		DBSlaveMaxIdleConns:     req.DBSlaveMaxIdleConns,
		
		HTTPMaxIdleConns:        req.HTTPMaxIdleConns,
		HTTPMaxIdleConnsPerHost: req.HTTPMaxIdleConnsPerHost,
		HTTPMaxConnsPerHost:     req.HTTPMaxConnsPerHost,
		HTTPIdleConnTimeout:     req.HTTPIdleConnTimeout,
		
		CrawlerRateLimit:        req.CrawlerRateLimit,
		
		CacheLockTimeout:        req.CacheLockTimeout,
		CacheLockRetryInterval:  req.CacheLockRetryInterval,
		
		// 验证码配置
		EnableCaptcha:           getBoolValue(req.EnableCaptcha, currentSettings.EnableCaptcha),
		CaptchaLength:           req.CaptchaLength,
		CaptchaExpiry:           req.CaptchaExpiry,
		
		// CSRF配置
		EnableCSRF:              getBoolValue(req.EnableCSRF, currentSettings.EnableCSRF),
		
		// 日志配置
		LogEnabled:              getBoolValue(req.LogEnabled, currentSettings.LogEnabled),
		LogLevel:                req.LogLevel,
		LogStorage:              req.LogStorage,
		LogRetentionDays:        req.LogRetentionDays,
		LogMaxSize:              req.LogMaxSize,
		LogMaxBackups:           req.LogMaxBackups,
		LogAccessEnabled:        getBoolValue(req.LogAccessEnabled, currentSettings.LogAccessEnabled),
		LogErrorEnabled:         getBoolValue(req.LogErrorEnabled, currentSettings.LogErrorEnabled),
		
		// 站点地图配置
		SitemapRefreshInterval:   req.SitemapRefreshInterval,
		
		// 资源限流配置
		MaxDatabaseConn:         req.MaxDatabaseConn,
		MaxRedisConn:            req.MaxRedisConn,
		MaxHTTPRequests:         req.MaxHTTPRequests,
		MaxFileOps:              req.MaxFileOps,
		MaxCrawlerTasks:         req.MaxCrawlerTasks,
		
		// 统一超时配置
		RouteTimeout:            req.RouteTimeout,
		DatabaseQueryTimeout:    req.DatabaseQueryTimeout,
		RedisOpTimeout:          req.RedisOpTimeout,
		HTTPRequestTimeout:      req.HTTPRequestTimeout,
		FileOpTimeout:           req.FileOpTimeout,
		CrawlerTaskTimeout:      req.CrawlerTaskTimeout,
		
		// 来源判断配置
		EnableGlobalRefererCheck: getBoolValue(req.EnableGlobalRefererCheck, currentSettings.EnableGlobalRefererCheck),
		GlobalAllowedReferers:    req.GlobalAllowedReferers,
		GlobalRefererBlockCode:   req.GlobalRefererBlockCode,
		GlobalRefererBlockHTML:   req.GlobalRefererBlockHTML,
		
		// 工作池配置
		WorkerPoolMode:       req.WorkerPoolMode,
		WorkerPoolSize:       req.WorkerPoolSize,
		WorkerPoolMinSize:    req.WorkerPoolMinSize,
		WorkerPoolMaxSize:    req.WorkerPoolMaxSize,
		WorkerPoolScaleRatio: req.WorkerPoolScaleRatio,
	}
	
	if err := h.systemSettingsService.UpdateSystemSettings(params); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// 更新验证码服务的长度设置
	if h.captchaService != nil && params.CaptchaLength > 0 {
		h.captchaService.SetCaptchaLength(params.CaptchaLength)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "系统设置更新成功",
	})
}

// getBoolValue 辅助函数：如果指针为nil则返回默认值，否则返回指针指向的值
func getBoolValue(ptr *bool, defaultValue bool) bool {
	if ptr == nil {
		return defaultValue
	}
	return *ptr
}

// GetSystemSuggestions 获取系统建议
func (h *SystemSettingsHandler) GetSystemSuggestions(c *gin.Context) {
	suggestions := h.systemSettingsService.GetSystemSuggestions()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    suggestions,
	})
}

// GetSystemInfo 获取系统信息
func (h *SystemSettingsHandler) GetSystemInfo(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 获取数据库状态
	dbInfo := gin.H{
		"type":       "PostgreSQL",
		"version":    "14.0",
		"connected":  true,
		"size":       "128 MB",
		"table_count": 20,
		"stats": gin.H{
			"open_connections": 10,
			"in_use":          2,
			"idle":            8,
		},
		"active_queries": 0,
	}
	
	// 获取Redis状态  
	redisInfo := gin.H{
		"connected": true,
		"db_size":   1024,
		"stats": gin.H{
			"version":                    "7.0.0",
			"used_memory_human":         "32 MB",
			"hit_rate":                  "95%",
			"connected_clients":         5,
			"instantaneous_ops_per_sec": 100,
			"uptime_in_seconds":         86400,
		},
	}
	
	// 计算内存使用百分比
	memUsedMB := float64(m.Alloc) / 1024 / 1024
	memTotalMB := float64(m.Sys) / 1024 / 1024
	memPercent := (memUsedMB / memTotalMB) * 100
	
	// 格式化内存大小
	formatMemory := func(bytes uint64) string {
		mb := float64(bytes) / 1024 / 1024
		if mb < 1024 {
			return fmt.Sprintf("%.1f MB", mb)
		}
		return fmt.Sprintf("%.2f GB", mb/1024)
	}
	
	systemInfo := gin.H{
		// 基础信息
		"app_version": "1.0.2",
		"go_version":  runtime.Version(),
		"os":          runtime.GOOS,
		"arch":        runtime.GOARCH,
		"os_info":     runtime.GOOS + "/" + runtime.GOARCH,
		
		// CPU信息
		"cpu_cores":    runtime.NumCPU(),
		"num_cpu":      runtime.NumCPU(),
		"num_goroutine": runtime.NumGoroutine(),
		
		// 内存信息
		"memory": gin.H{
			"used":         formatMemory(m.Alloc),
			"total_system": formatMemory(m.Sys),
			"available":    formatMemory(m.Sys - m.Alloc),
			"percent":      fmt.Sprintf("%.1f%%", memPercent),
		},
		"total_memory":   m.Sys,
		"memory_gb":     float64(m.Sys) / 1024 / 1024 / 1024,
		"used_memory":   m.Alloc,
		"used_memory_mb": memUsedMB,
		
		// 磁盘信息（示例数据）
		"disk": gin.H{
			"used":    "50 GB",
			"total":   "100 GB",
			"free":    "50 GB",
			"percent": "50%",
		},
		
		// 运行时间
		"uptime": "7天12小时",
		
		// 网络状态
		"network": gin.H{
			"internet_connected": true,
		},
		
		// 其他信息
		"storage_type": "SSD",
		"goroutines":   runtime.NumGoroutine(),
		"gc_runs":      m.NumGC,
		
		// 数据库和Redis信息
		"database": dbInfo,
		"redis":    redisInfo,
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    systemInfo,
	})
}

// 请求结构体
type UpdateSystemSettingsRequest struct {
	EnableGlobalUACheck            *bool  `json:"enable_global_ua_check"`    // 全局UA判断开关 (使用指针以区分未设置)
	DefaultNonSpiderHTML           string `json:"default_non_spider_html"`
	DefaultAllowedUA               string `json:"default_allowed_ua"`
	DefaultSpiderBlockUA           string `json:"default_spider_block_ua"`
	UserAgentList                  string `json:"user_agent_list"`
	CrawlerConcurrency             int    `json:"crawler_concurrency"`
	CrawlerTimeout                 int    `json:"crawler_timeout"`
	DefaultCacheHomeTTL            int    `json:"default_cache_home_ttl"`
	DefaultCacheOtherTTL           int    `json:"default_cache_other_ttl"`
	DefaultEnableRedisCache        *bool  `json:"default_enable_redis_cache"`
	EnableGlobalSpiderBlock        *bool  `json:"enable_global_spider_block"` // 使用指针以区分未设置
	SpiderBlock403Template         string `json:"spider_block_403_template"`
	SiteNotFoundHTML               string `json:"site_not_found_html"`
	Default404HTML                 string `json:"default_404_html"`
	Enable404Cache                 *bool  `json:"enable_404_cache"`
	Cache404TTL                    int    `json:"cache_404_ttl"`
	GlobalRedirectWWW              *bool  `json:"global_redirect_www"`  // 全局 @ 跳转到 www
	EnableUAStats                  *bool  `json:"enable_ua_stats"`       // UA统计开关
	
	// 拼音配置
	EnableGlobalPinyin      *bool   `json:"enable_global_pinyin"`       // 全局启用拼音
	PinyinSpecialChars      string  `json:"pinyin_special_chars"`       // 特殊字符列表
	PinyinSpecialCharsRatio float32 `json:"pinyin_special_chars_ratio"` // 插入比例
	
	// 缓存超时配置
	RedisConnectTimeout     int `json:"redis_connect_timeout"`
	RedisReadTimeout        int `json:"redis_read_timeout"`
	RedisWriteTimeout       int `json:"redis_write_timeout"`
	RedisPoolTimeout        int `json:"redis_pool_timeout"`
	RedisMaxPoolSize        int `json:"redis_max_pool_size"`
	
	FileReadTimeout         int `json:"file_read_timeout"`
	FileWriteTimeout        int `json:"file_write_timeout"`
	AsyncQueueSize          int `json:"async_queue_size"`
	AsyncWorkerCount        int `json:"async_worker_count"`
	
	ProxyRequestTimeout     int `json:"proxy_request_timeout"`
	
	// 性能配置字段
	DBMaxOpenConns          int `json:"db_max_open_conns"`
	DBMaxIdleConns          int `json:"db_max_idle_conns"`
	DBConnMaxLifetime       int `json:"db_conn_max_lifetime"`
	DBSlaveMaxOpenConns     int `json:"db_slave_max_open_conns"`
	DBSlaveMaxIdleConns     int `json:"db_slave_max_idle_conns"`
	
	HTTPMaxIdleConns        int `json:"http_max_idle_conns"`
	HTTPMaxIdleConnsPerHost int `json:"http_max_idle_conns_per_host"`
	HTTPMaxConnsPerHost     int `json:"http_max_conns_per_host"`
	HTTPIdleConnTimeout     int `json:"http_idle_conn_timeout"`
	
	SchedulerMaxWorkers     int     `json:"scheduler_max_workers"`
	SchedulerQueueSize      int     `json:"scheduler_queue_size"`
	CrawlerRateLimit        float64 `json:"crawler_rate_limit"`
	
	CacheLockTimeout        int `json:"cache_lock_timeout"`
	CacheLockRetryInterval  int `json:"cache_lock_retry_interval"`
	
	// 验证码配置
	EnableCaptcha           *bool `json:"enable_captcha"`
	CaptchaLength           int   `json:"captcha_length"`
	CaptchaExpiry           int   `json:"captcha_expiry"`
	
	// CSRF配置
	EnableCSRF              *bool `json:"enable_csrf"`
	
	// 日志配置
	LogEnabled              *bool  `json:"log_enabled"`
	LogLevel                string `json:"log_level"`
	LogStorage              string `json:"log_storage"`
	LogRetentionDays        int    `json:"log_retention_days"`
	LogMaxSize              int    `json:"log_max_size"`
	LogMaxBackups           int    `json:"log_max_backups"`
	LogAccessEnabled        *bool  `json:"log_access_enabled"`
	LogErrorEnabled         *bool  `json:"log_error_enabled"`
	
	// 站点地图配置
	SitemapRefreshInterval   int   `json:"sitemap_refresh_interval"`
	
	// 资源限流配置
	MaxDatabaseConn         int `json:"max_database_conn"`
	MaxRedisConn            int `json:"max_redis_conn"`
	MaxHTTPRequests         int `json:"max_http_requests"`
	MaxFileOps              int `json:"max_file_ops"`
	MaxCrawlerTasks         int `json:"max_crawler_tasks"`
	
	// 统一超时配置
	RouteTimeout            int `json:"route_timeout"`
	DatabaseQueryTimeout    int `json:"database_query_timeout"`
	RedisOpTimeout          int `json:"redis_op_timeout"`
	HTTPRequestTimeout      int `json:"http_request_timeout"`
	FileOpTimeout           int `json:"file_op_timeout"`
	CrawlerTaskTimeout      int `json:"crawler_task_timeout"`
	
	// 来源判断配置
	EnableGlobalRefererCheck *bool  `json:"enable_global_referer_check"`
	GlobalAllowedReferers    string `json:"global_allowed_referers"`
	GlobalRefererBlockCode   int    `json:"global_referer_block_code"`
	GlobalRefererBlockHTML   string `json:"global_referer_block_html"`
	
	// 工作池配置
	WorkerPoolMode          string  `json:"worker_pool_mode"`
	WorkerPoolSize          int     `json:"worker_pool_size"`
	WorkerPoolMinSize       int     `json:"worker_pool_min_size"`
	WorkerPoolMaxSize       int     `json:"worker_pool_max_size"`
	WorkerPoolScaleRatio    float64 `json:"worker_pool_scale_ratio"`
}