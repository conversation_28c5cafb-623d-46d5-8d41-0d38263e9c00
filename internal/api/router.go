package api

import (
	"fmt"
	"runtime"
	"time"
	"site-cluster/internal/api/handler"
	"site-cluster/internal/api/middleware"
	"site-cluster/internal/repository"
	"site-cluster/internal/service"
	"site-cluster/internal/service/captcha"
	"site-cluster/internal/service/weight"
	"site-cluster/internal/interfaces"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"go.uber.org/zap"
)

type Router struct {
	engine           *gin.Engine
	logger           *zap.Logger
	siteHandler      *handler.SiteHandler
	keywordHandler   *handler.KeywordHandler
	cacheHandler     *handler.CacheHandler
	mirrorHandler    *handler.MirrorHandler
	optimizedMirrorHandler *handler.OptimizedMirrorHandler
	systemMonitor    *service.SystemMonitor
	authHandler      *handler.AuthHandler
	loginLogHandler  *handler.LoginLogHandler
	adminHandler     *handler.AdminHandler
	menuHandler      *handler.MenuHandler
	pseudoHandler    *handler.PseudoHandler
	analyticsHandler *handler.AnalyticsHandler
	spiderBlockHandler *handler.SpiderBlockHandler
	spiderStatsHandler *handler.SpiderStatsHandler
	systemHandler    *handler.SystemHandler
	systemSettingsHandler *handler.SystemSettingsHandler
	sitemapHandler   *handler.SitemapHandler
	companyLibraryHandler *handler.CompanyLibraryHandler
	siteCategoryHandler *handler.SiteCategoryHandler
	weightHandler    *handler.WeightHandler
	staticRefreshHandler *handler.StaticRefreshHandler
	authService      *service.AuthService
	redisCache       *service.RedisCacheService
	performanceMonitor *service.PerformanceMonitor
	weightMonitorService *weight.MonitorService
	siteService      *service.SiteService
	spiderStatsService *service.SpiderStatsService
	systemSettingsService *service.SystemSettingsService
	sitemapService   *service.SitemapService
	cache404Service  *service.Cache404Service
	analyticsService *service.AnalyticsService
	spiderBlockService *service.SpiderBlockService
	pseudoService    *service.PseudoService
	companyLibraryService *service.CompanyLibraryService
	siteCategoryService *service.SiteCategoryService
	captchaService   *captcha.CaptchaService
	loginLogRepo     repository.LoginLogRepository
	resourceLimiter  *service.ResourceLimiter
}

func NewRouter(
	logger *zap.Logger,
	siteService *service.SiteService,
	keywordService *service.KeywordService,
	cacheService *service.FileCacheService,
	crawler interfaces.Crawler,
	authService *service.AuthService,
	pseudoService *service.PseudoService,
	analyticsService *service.AnalyticsService,
	spiderBlockService *service.SpiderBlockService,
	spiderStatsService *service.SpiderStatsService,
	systemSettingsService *service.SystemSettingsService,
	companyLibraryService *service.CompanyLibraryService,
	siteCategoryService *service.SiteCategoryService,
) *Router {
	performanceMonitor := service.NewPerformanceMonitor(logger)
	
	// 初始化资源限流器（使用默认配置）
	resourceLimiter := service.NewResourceLimiter(logger, nil)
	
	// 初始化静态资源刷新服务
	// 获取数据库实例 (需要类型断言)
	var db *gorm.DB
	if dbInstance := siteService.GetDB(); dbInstance != nil {
		db = dbInstance.(*gorm.DB)
	}
	staticRefreshService := service.NewStaticRefreshService(db, logger, analyticsService)
	
	return &Router{
		engine:           gin.New(),
		logger:           logger,
		siteHandler:      handler.NewSiteHandler(logger, siteService, cacheService),
		keywordHandler:   handler.NewKeywordHandler(logger, keywordService),
		cacheHandler:     handler.NewCacheHandler(logger, cacheService),
		mirrorHandler:    handler.NewMirrorHandler(logger, siteService, cacheService, crawler, analyticsService, spiderBlockService, systemSettingsService, spiderStatsService, nil, pseudoService, nil),
		systemMonitor:    service.NewSystemMonitor(logger),
		authHandler:      handler.NewAuthHandler(logger, authService, nil, nil, systemSettingsService),
		adminHandler:     handler.NewAdminHandler(logger, authService),
		menuHandler:      handler.NewMenuHandler(),
		pseudoHandler:    handler.NewPseudoHandler(logger, pseudoService),
		analyticsHandler: handler.NewAnalyticsHandler(logger, analyticsService),
		spiderBlockHandler: handler.NewSpiderBlockHandler(logger, spiderBlockService),
		spiderStatsHandler: handler.NewSpiderStatsHandler(logger, spiderStatsService),
		systemHandler:    handler.NewSystemHandler(logger, performanceMonitor, nil, cacheService, siteService),
		systemSettingsHandler: handler.NewSystemSettingsHandler(logger, systemSettingsService),
		companyLibraryHandler: handler.NewCompanyLibraryHandler(logger, companyLibraryService),
		siteCategoryHandler: handler.NewSiteCategoryHandler(siteCategoryService, siteService, analyticsService),
		staticRefreshHandler: handler.NewStaticRefreshHandler(staticRefreshService, logger),
		authService:      authService,
		performanceMonitor: performanceMonitor,
		siteService:      siteService,
		spiderStatsService: spiderStatsService,
		systemSettingsService: systemSettingsService,
		analyticsService: analyticsService,
		spiderBlockService: spiderBlockService,
		pseudoService:    pseudoService,
		companyLibraryService: companyLibraryService,
		siteCategoryService: siteCategoryService,
		resourceLimiter:  resourceLimiter,
	}
}

// SetRedisCache 设置Redis缓存服务
func (r *Router) SetRedisCache(redisCache *service.RedisCacheService) {
	r.redisCache = redisCache
	// 更新系统处理器的Redis缓存
	if r.systemHandler != nil {
		r.systemHandler = handler.NewSystemHandler(r.logger, r.performanceMonitor, redisCache, 
			r.cacheHandler.GetCacheService(), r.siteHandler.GetSiteService())
	}
	// 设置SystemMonitor的Redis客户端
	if r.systemMonitor != nil && redisCache != nil {
		r.systemMonitor.SetRedisClient(redisCache.GetClient())
	}
	// 设置给SiteHandler（用于缓存站点列表）
	if r.siteHandler != nil {
		r.siteHandler.SetRedisCache(redisCache)
	}
	// 设置给SpiderStatsHandler（用于缓存爬虫统计）
	if r.spiderStatsHandler != nil {
		r.spiderStatsHandler.SetRedisCache(redisCache)
	}
	// 设置给WeightHandler（用于缓存权重统计）
	if r.weightHandler != nil {
		r.weightHandler.SetRedisCache(redisCache)
	}
	// 创建优化的镜像处理器
	if r.siteService != nil && r.cacheHandler != nil && r.mirrorHandler != nil {
		r.optimizedMirrorHandler = handler.NewOptimizedMirrorHandler(
			r.logger,
			r.siteService,
			r.cacheHandler.GetCacheService(),
			redisCache,
			r.mirrorHandler,
			r.spiderStatsService,
			r.sitemapService,
			r.resourceLimiter,
		)
		// 设置给SiteHandler，用于清理内存缓存
		if r.siteHandler != nil {
			r.siteHandler.SetOptimizedMirrorHandler(r.optimizedMirrorHandler)
		}
	}
}

// SetSitemapService 设置Sitemap服务
func (r *Router) SetSitemapService(sitemapService *service.SitemapService) {
	r.sitemapService = sitemapService
	// 创建sitemap处理器
	if r.siteService != nil {
		r.sitemapHandler = handler.NewSitemapHandler(r.logger, sitemapService, r.siteService)
	}
	// 设置SiteHandler的sitemap服务
	if r.siteHandler != nil {
		r.siteHandler.SetSitemapService(sitemapService)
	}
	// 设置CacheHandler的sitemap服务
	if r.cacheHandler != nil {
		r.cacheHandler.SetSitemapService(sitemapService)
	}
	// 重新创建mirrorHandler以包含sitemapService
	if r.siteService != nil && r.analyticsService != nil && r.spiderBlockService != nil &&
	   r.systemSettingsService != nil && r.spiderStatsService != nil && r.pseudoService != nil {
		r.mirrorHandler = handler.NewMirrorHandler(
			r.logger, 
			r.siteService, 
			r.cacheHandler.GetCacheService(), 
			nil, // crawler
			r.analyticsService, 
			r.spiderBlockService, 
			r.systemSettingsService, 
			r.spiderStatsService, 
			r.cache404Service, 
			r.pseudoService,
			sitemapService,
		)
	}
	// 如果优化镜像处理器已存在，更新它
	if r.optimizedMirrorHandler != nil && r.redisCache != nil {
		r.optimizedMirrorHandler = handler.NewOptimizedMirrorHandler(
			r.logger,
			r.siteService,
			r.cacheHandler.GetCacheService(),
			r.redisCache,
			r.mirrorHandler,
			r.spiderStatsService,
			sitemapService,
			r.resourceLimiter,
		)
		// 设置给SiteHandler，用于清理内存缓存
		if r.siteHandler != nil {
			r.siteHandler.SetOptimizedMirrorHandler(r.optimizedMirrorHandler)
		}
	}
}

// SetCache404Service 设置404缓存服务
func (r *Router) SetCache404Service(cache404Service *service.Cache404Service) {
	r.cache404Service = cache404Service
	// 设置给SiteHandler
	if r.siteHandler != nil {
		r.siteHandler.SetCache404Service(cache404Service)
	}
	// 设置给SiteService
	if r.siteService != nil {
		r.siteService.SetCache404Service(cache404Service)
	}
	// 设置给CacheHandler
	if r.cacheHandler != nil {
		r.cacheHandler.SetCache404Service(cache404Service)
		r.cacheHandler.SetSiteService(r.siteService)
	}
	// 更新镜像处理器
	if r.mirrorHandler != nil {
		r.mirrorHandler = handler.NewMirrorHandler(
			r.logger,
			r.siteService,
			r.cacheHandler.GetCacheService(),
			nil, // crawler
			r.analyticsService,
			r.spiderBlockService,
			r.systemSettingsService,
			r.spiderStatsService,
			cache404Service,
			r.pseudoService,
			r.sitemapService,
		)
		
		// 同时更新优化镜像处理器（它引用了mirrorHandler）
		if r.optimizedMirrorHandler != nil && r.redisCache != nil {
			r.optimizedMirrorHandler = handler.NewOptimizedMirrorHandler(
				r.logger,
				r.siteService,
				r.cacheHandler.GetCacheService(),
				r.redisCache,
				r.mirrorHandler,
				r.spiderStatsService,
				r.sitemapService,
				r.resourceLimiter,
			)
			// 设置给SiteHandler，用于清理内存缓存
			if r.siteHandler != nil {
				r.siteHandler.SetOptimizedMirrorHandler(r.optimizedMirrorHandler)
			}
		}
	}
}

// SetUAStatsService 设置UA统计服务
func (r *Router) SetUAStatsService(uaStatsService *service.UAStatsService) {
	// 设置给SpiderBlockHandler
	if r.spiderBlockHandler != nil {
		r.spiderBlockHandler.SetUAStatsService(uaStatsService)
	}
	// 设置给OptimizedMirrorHandler
	if r.optimizedMirrorHandler != nil {
		r.optimizedMirrorHandler.SetUAStatsService(uaStatsService)
	}
}

// SetDB 设置数据库连接
func (r *Router) SetDB(db interface{}) {
	// 设置SystemMonitor的数据库连接
	if r.systemMonitor != nil {
		if gormDB, ok := db.(*gorm.DB); ok {
			r.systemMonitor.SetDB(gormDB)
		}
	}
}

// setupAPIRoutes 配置所有API路由（公共方法，避免重复）
func (r *Router) setupAPIRoutes(engine *gin.Engine) {
	// API路由
	api := engine.Group("/api/v1")
	// api.Use(middleware.RateLimiter(100)) // 暂时注释，需要重构
	{
		// 认证相关
		auth := api.Group("/auth")
		{
			auth.GET("/captcha", r.authHandler.GetCaptcha)
			auth.GET("/captcha-config", r.authHandler.GetCaptchaConfig)  // 获取验证码配置（是否启用）
			auth.GET("/csrf-token", middleware.AuthMiddleware(r.logger, r.authService), r.authHandler.GetCSRFToken)
			auth.POST("/login", r.authHandler.Login)
			auth.POST("/logout", r.authHandler.Logout)
			auth.GET("/current", middleware.AuthMiddleware(r.logger, r.authService), r.authHandler.GetCurrentAdmin)
			auth.POST("/change-password", middleware.AuthMiddleware(r.logger, r.authService), r.authHandler.ChangePassword)
		}
		
		// 菜单管理
		menu := api.Group("/menu")
		menu.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			menu.GET("/groups", r.menuHandler.GetMenuGroups)
			menu.GET("/config", r.menuHandler.GetMenuConfig)
		}
		
		// 登录日志管理
		if r.loginLogHandler != nil {
			loginLogs := api.Group("/login-logs")
			loginLogs.Use(middleware.AuthMiddleware(r.logger, r.authService))
			{
				loginLogs.GET("", r.loginLogHandler.GetLoginLogs)
				loginLogs.GET("/stats", r.loginLogHandler.GetLoginStats)
				loginLogs.POST("/clean", r.loginLogHandler.CleanOldLogs)
				loginLogs.POST("/clean-all", r.loginLogHandler.CleanAllLogs)
			}
		}
		
		// 站点分类管理
		categories := api.Group("/site-categories")
		categories.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			categories.GET("", r.siteCategoryHandler.GetCategories)
			categories.GET("/:id", r.siteCategoryHandler.GetCategory)
			categories.POST("", r.siteCategoryHandler.CreateCategory)
			categories.PUT("/:id", r.siteCategoryHandler.UpdateCategory)
			categories.DELETE("/:id", r.siteCategoryHandler.DeleteCategory)
			categories.POST("/:id/refresh-analytics", r.siteCategoryHandler.RefreshCategoryAnalytics)
		}
		
		// 站点管理
		sites := api.Group("/sites")
		sites.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			sites.GET("", r.siteHandler.GetSites)
			sites.POST("/search", r.siteHandler.SearchSites)  // 新增批量搜索接口
			sites.GET("/:id", r.siteHandler.GetSite)
			sites.POST("", r.siteHandler.CreateSite)
			sites.POST("/batch", r.siteHandler.BatchCreateSites)
			sites.POST("/batch-with-aliases", r.siteHandler.BatchCreateSitesWithAliases)  // 新增支持子域名的批量添加
			sites.PUT("/:id", r.siteHandler.UpdateSite)
			sites.POST("/batch-update", r.siteHandler.BatchUpdateSites)
			sites.DELETE("/:id", r.siteHandler.DeleteSite)
			sites.DELETE("/batch", r.siteHandler.BatchDeleteSites)
			sites.POST("/batch-replace", r.siteHandler.BatchReplaceSites)      // 批量替换域名和目标站
			sites.POST("/check-protocol", r.siteHandler.CheckProtocol)         // 检测URL协议
			sites.GET("/orphaned-data", r.siteHandler.DetectOrphanedData)     // 检测孤立数据
			sites.POST("/cleanup-orphaned", r.siteHandler.CleanupOrphanedData) // 清理单个孤立数据
			sites.POST("/cleanup-all-orphaned", r.siteHandler.CleanupAllOrphanedData) // 批量清理所有孤立数据
			sites.POST("/:id/crawl", r.siteHandler.TriggerCrawl)
			sites.GET("/:id/stats", r.siteHandler.GetSiteStats)
			// Sitemap相关路由
			if r.sitemapHandler != nil {
				sites.POST("/:id/sitemap/generate", r.sitemapHandler.GenerateSitemap)
				sites.POST("/:id/sitemap/scan", r.sitemapHandler.ScanAndGenerate)
				sites.GET("/:id/sitemap/stats", r.sitemapHandler.GetSitemapStats)
				sites.DELETE("/:id/sitemap/cleanup", r.sitemapHandler.CleanupOldEntries)
			}
		}
		
		// 批量刷新所有sitemap（独立路由）
		if r.sitemapHandler != nil {
			api.POST("/sitemap/refresh-all", middleware.AuthMiddleware(r.logger, r.authService), r.sitemapHandler.RefreshAllSitemaps)
			api.GET("/sitemap/refresh-progress", middleware.AuthMiddleware(r.logger, r.authService), r.sitemapHandler.RefreshAllSitemapsWithProgress) // SSE进度接口
			api.POST("/sitemap/generate-all", middleware.AuthMiddleware(r.logger, r.authService), r.sitemapHandler.ManualGenerateAll)
			api.DELETE("/sitemap/clean-static", middleware.AuthMiddleware(r.logger, r.authService), r.sitemapHandler.CleanStaticResources)
			api.GET("/sitemap/stats", middleware.AuthMiddleware(r.logger, r.authService), r.sitemapHandler.GetAllSitemapStats)
			api.DELETE("/sitemap/clear-cache", middleware.AuthMiddleware(r.logger, r.authService), r.sitemapHandler.ClearAllSitemapCache)
			// 独立的sitemap设置接口
			api.GET("/sitemap/settings", middleware.AuthMiddleware(r.logger, r.authService), r.sitemapHandler.GetSitemapSettings)
			api.PUT("/sitemap/settings", middleware.AuthMiddleware(r.logger, r.authService), r.sitemapHandler.UpdateSitemapSettings)
		}

		// 关键词管理
		keywords := api.Group("/keywords")
		keywords.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			keywords.GET("/libraries", r.keywordHandler.GetLibraries)
			keywords.POST("/libraries", r.keywordHandler.CreateLibrary)
			keywords.PUT("/libraries/:id", r.keywordHandler.UpdateLibrary)
			keywords.DELETE("/libraries/:id", r.keywordHandler.DeleteLibrary)
			
			keywords.GET("/libraries/:id/keywords", r.keywordHandler.GetKeywords)
			keywords.POST("/libraries/:id/keywords", r.keywordHandler.AddKeyword)
			keywords.PUT("/keywords/:id", r.keywordHandler.UpdateKeyword)
			keywords.DELETE("/keywords/:id", r.keywordHandler.DeleteKeyword)
			
			keywords.POST("/import", r.keywordHandler.ImportKeywords)
			keywords.GET("/export/:id", r.keywordHandler.ExportKeywords)
		}

		// 缓存管理
		cache := api.Group("/cache")
		cache.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			cache.GET("/stats", r.cacheHandler.GetStats)
			cache.DELETE("/site/:domain", r.cacheHandler.ClearSiteCache)           // 兼容性接口（通过域名）
			cache.DELETE("/sites/:siteId", r.cacheHandler.ClearSiteCacheByID)      // 推荐接口（通过站点ID）
			cache.DELETE("/expired", r.cacheHandler.ClearExpiredCache)
			cache.GET("/size", r.cacheHandler.GetCacheSize)
		}

		// 系统信息
		system := api.Group("/system")
		system.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			system.GET("/info", r.systemSettingsHandler.GetSystemInfo)
			system.GET("/health", r.healthCheck)
			system.GET("/metrics", r.systemHandler.GetMetrics)
			system.GET("/metrics/export", r.systemHandler.ExportMetrics)
			system.GET("/settings", r.systemSettingsHandler.GetSystemSettings)
			system.PUT("/settings", r.systemSettingsHandler.UpdateSystemSettings)
			system.GET("/settings/suggestions", r.systemSettingsHandler.GetSystemSuggestions)
		}
		
		// 管理员管理
		admins := api.Group("/admins")
		admins.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			admins.GET("", r.adminHandler.GetAdmins)
			admins.GET("/:id", r.adminHandler.GetAdmin)
			admins.POST("", r.adminHandler.CreateAdmin)
			admins.PUT("/:id", r.adminHandler.UpdateAdmin)
			admins.DELETE("/:id", r.adminHandler.DeleteAdmin)
			admins.PUT("/:id/password", r.adminHandler.ChangeAdminPassword)
		}
		
		// 伪原创管理
		pseudo := api.Group("/pseudo")
		pseudo.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			pseudo.GET("/libraries", r.pseudoHandler.GetLibraries)
			pseudo.GET("/libraries/:id", r.pseudoHandler.GetLibrary)
			pseudo.POST("/libraries", r.pseudoHandler.CreateLibrary)
			pseudo.PUT("/libraries/:id", r.pseudoHandler.UpdateLibrary)
			pseudo.DELETE("/libraries/:id", r.pseudoHandler.DeleteLibrary)
			
			pseudo.GET("/libraries/:id/words", r.pseudoHandler.GetWords)
			pseudo.POST("/libraries/:id/words", r.pseudoHandler.CreateWord)
			pseudo.POST("/libraries/:id/import", r.pseudoHandler.ImportWords)
			pseudo.GET("/words/:wordId", r.pseudoHandler.GetWord)
			pseudo.PUT("/words/:wordId", r.pseudoHandler.UpdateWord)
			pseudo.DELETE("/words/:wordId", r.pseudoHandler.DeleteWord)
		}
		
		// 企业名称管理
		company := api.Group("/company")
		company.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			company.GET("/libraries", r.companyLibraryHandler.GetLibraries)
			company.GET("/libraries/:id", r.companyLibraryHandler.GetLibrary)
			company.POST("/libraries", r.companyLibraryHandler.CreateLibrary)
			company.PUT("/libraries/:id", r.companyLibraryHandler.UpdateLibrary)
			company.DELETE("/libraries/:id", r.companyLibraryHandler.DeleteLibrary)
			
			company.GET("/libraries/:id/names", r.companyLibraryHandler.GetCompanyNames)
			company.POST("/libraries/:id/names", r.companyLibraryHandler.AddCompanyName)
			company.POST("/libraries/:id/batch", r.companyLibraryHandler.BatchAddCompanyNames)
			company.PUT("/names/:nameId", r.companyLibraryHandler.UpdateCompanyName)
			company.DELETE("/names/:nameId", r.companyLibraryHandler.DeleteCompanyName)
		}
		
		// 统计管理
		analytics := api.Group("/analytics")
		analytics.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			analytics.GET("", r.analyticsHandler.GetAnalytics)
			analytics.PUT("", r.analyticsHandler.UpdateAnalytics)
			analytics.POST("/refresh", r.analyticsHandler.RefreshAnalyticsJS)
		}
		
		// 静态资源刷新管理
		staticRefresh := api.Group("/static-refresh")
		staticRefresh.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			staticRefresh.POST("/site/:id", r.staticRefreshHandler.RefreshSite)
			staticRefresh.POST("/batch", r.staticRefreshHandler.RefreshBatch)
			staticRefresh.POST("/all", r.staticRefreshHandler.RefreshAll)
			staticRefresh.GET("/status", r.staticRefreshHandler.GetStatus)
		}
		
		// 蜘蛛屏蔽管理
		spiderBlock := api.Group("/spider-block")
		spiderBlock.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			spiderBlock.GET("", r.spiderBlockHandler.GetRules)
			spiderBlock.GET("/:id", r.spiderBlockHandler.GetRule)
			spiderBlock.POST("", r.spiderBlockHandler.CreateRule)
			spiderBlock.PUT("/:id", r.spiderBlockHandler.UpdateRule)
			spiderBlock.DELETE("/:id", r.spiderBlockHandler.DeleteRule)
			spiderBlock.POST("/:id/toggle", r.spiderBlockHandler.ToggleRule)
			spiderBlock.POST("/:id/reset", r.spiderBlockHandler.ResetHitCount)
			spiderBlock.POST("/reset-all", r.spiderBlockHandler.ResetAllHitCounts)
			spiderBlock.DELETE("/clear", r.spiderBlockHandler.ClearAllRules)
			spiderBlock.GET("/stats", r.spiderBlockHandler.GetStats)
			spiderBlock.DELETE("/stats/clear", r.spiderBlockHandler.ClearStatsData)
			// 批量操作
			spiderBlock.DELETE("/batch", r.spiderBlockHandler.BatchDelete)
			spiderBlock.POST("/batch-delete", r.spiderBlockHandler.BatchDelete) // 兼容POST方式
			spiderBlock.PUT("/batch/toggle", r.spiderBlockHandler.BatchToggle)
			// UA统计相关
			spiderBlock.GET("/ua-stats", r.spiderBlockHandler.GetUAStats)
			spiderBlock.GET("/ua-stats/:hash", r.spiderBlockHandler.GetUADetail)
			spiderBlock.POST("/ua-stats/flush", r.spiderBlockHandler.FlushUAStats)
			spiderBlock.DELETE("/ua-stats", r.spiderBlockHandler.ClearUAStats)
		}
		
		// 爬虫统计管理
		spiderStats := api.Group("/spider-stats")
		spiderStats.Use(middleware.AuthMiddleware(r.logger, r.authService))
		{
			spiderStats.GET("", r.spiderStatsHandler.GetSpiderStats)
			spiderStats.DELETE("/clear", r.spiderStatsHandler.ClearSpiderStats)
			spiderStats.DELETE("/domains/:domain", r.spiderStatsHandler.DeleteDomainStats)
			spiderStats.DELETE("/domains/batch", r.spiderStatsHandler.BatchDeleteDomainStats)
			spiderStats.GET("/configs", r.spiderStatsHandler.GetSpiderConfigs)
			spiderStats.POST("/configs", r.spiderStatsHandler.CreateSpiderConfig)
			spiderStats.PUT("/configs/:id", r.spiderStatsHandler.UpdateSpiderConfig)
			spiderStats.DELETE("/configs/:id", r.spiderStatsHandler.DeleteSpiderConfig)
			spiderStats.POST("/configs/:id/toggle", r.spiderStatsHandler.ToggleSpiderConfig)
		}
		
		// 权重监测管理
		if r.weightHandler != nil {
			weightGroup := api.Group("/weight")
			weightGroup.Use(middleware.AuthMiddleware(r.logger, r.authService))
			{
				weightGroup.GET("/config", r.weightHandler.GetConfig)
				weightGroup.PUT("/config", r.weightHandler.UpdateConfig)
				weightGroup.POST("/test-api", r.weightHandler.TestAPI)
				weightGroup.POST("/check", r.weightHandler.ManualCheck)
				weightGroup.GET("/list", r.weightHandler.GetWeightList)
				weightGroup.GET("/stats", r.weightHandler.GetWeightStats)
				weightGroup.GET("/history", r.weightHandler.GetWeightHistory)
				weightGroup.GET("/trend", r.weightHandler.GetWeightTrend)
				weightGroup.GET("/domain/:domain", r.weightHandler.GetDomainStats)
				weightGroup.GET("/export", r.weightHandler.ExportWeightData)
				weightGroup.DELETE("/clear", r.weightHandler.ClearWeightData)
				weightGroup.DELETE("/domain/:domain", r.weightHandler.DeleteDomainWeightData)
			}
		}
	}
}

func (r *Router) Setup() *gin.Engine {
	// 中间件
	r.engine.Use(middleware.SecurityHeaders()) // 安全头
	r.engine.Use(middleware.Logger(r.logger))
	r.engine.Use(middleware.EnhancedRecovery(r.logger)) // 使用增强的panic恢复
	r.engine.Use(middleware.TimeoutMiddleware(60 * time.Second)) // 60秒超时

	// 静态文件
	r.engine.Static("/static", "./web/static")
	r.engine.Static("/cache", "./cache")  // 缓存的静态资源
	// 加载所有模板（包括子目录）
	r.engine.LoadHTMLFiles(
		// "web/templates/index.html",  // 前台首页模板，暂时注释
		// "web/templates/mirror.html",  // 文件不存在，暂时注释
		// "web/templates/404.html",      // 文件不存在，暂时注释
		"web/templates/spider-block.html",
		"web/templates/ua-stats.html",
	)
	
	// 登录页面
	r.engine.GET("/login", r.loginPage)

	// 管理界面路由
	admin := r.engine.Group("/admin")
	admin.Use(middleware.AuthMiddleware(r.logger, r.authService))
	{
		// 页面路由
		admin.GET("/", r.adminIndex)
		admin.GET("/batch-add", r.batchAddPage)
		admin.GET("/sites", r.sitesPage)
		admin.GET("/orphaned-data", r.orphanedDataPage)
		admin.GET("/batch-replace", r.batchReplacePage)
		admin.GET("/site-categories", r.siteCategoriesPage)
		admin.GET("/keywords", r.keywordsPage)
		admin.GET("/pseudo", r.pseudoPage)
		admin.GET("/company-libraries", r.companyLibrariesPage)
		admin.GET("/cache", r.cachePage)
		admin.GET("/admins", r.adminsPage)
		admin.GET("/settings", r.settingsPage)
		admin.GET("/settings/:tab", r.settingsPageWithTab)
		admin.GET("/settings/partial/:tab", r.settingsPartialPage)
		admin.GET("/spider-block", r.spiderBlockPage)
		admin.GET("/ua-stats", r.uaStatsPage)
		admin.GET("/monitor", r.monitorPage)
		admin.GET("/spider-stats", r.spiderStatsPage)
		admin.GET("/weight-monitor", r.weightMonitorPage)
		admin.GET("/login-logs", r.loginLogsPage)
	}

	// 使用公共方法配置API路由
	r.setupAPIRoutes(r.engine)

	// 前台路由 - 处理镜像请求
	r.engine.NoRoute(r.handleMirrorRequest)

	return r.engine
}

// 管理界面页面处理器
func (r *Router) adminIndex(c *gin.Context) {
	c.HTML(200, "dashboard.html", gin.H{
		"title": "控制台 - 站群管理系统",
	})
}

func (r *Router) batchAddPage(c *gin.Context) {
	c.HTML(200, "batch-add.html", gin.H{
		"title": "批量添加站点",
	})
}

func (r *Router) sitesPage(c *gin.Context) {
	c.HTML(200, "sites.html", gin.H{
		"title": "站点管理",
	})
}

func (r *Router) orphanedDataPage(c *gin.Context) {
	c.HTML(200, "orphaned-data.html", gin.H{
		"title": "脏数据管理",
	})
}

func (r *Router) batchReplacePage(c *gin.Context) {
	c.HTML(200, "batch-replace.html", gin.H{
		"title": "批量替换",
	})
}

func (r *Router) keywordsPage(c *gin.Context) {
	c.HTML(200, "keywords.html", gin.H{
		"title": "关键词管理",
	})
}

func (r *Router) companyLibrariesPage(c *gin.Context) {
	c.HTML(200, "company-libraries.html", gin.H{
		"title": "企业名称库管理",
	})
}

func (r *Router) siteCategoriesPage(c *gin.Context) {
	c.HTML(200, "site-categories.html", gin.H{
		"title": "站点分类管理",
	})
}

func (r *Router) cachePage(c *gin.Context) {
	c.HTML(200, "cache.html", gin.H{
		"title": "缓存管理",
	})
}

func (r *Router) settingsPage(c *gin.Context) {
	// 使用新的设置页面结构
	c.HTML(200, "main.html", gin.H{
		"title": "系统设置",
	})
}

func (r *Router) settingsPageWithTab(c *gin.Context) {
	tab := c.Param("tab")
	
	// 根据tab设置不同的active值
	active := "settings"
	if tab == "content" {
		active = "settings-content"
	}
	
	// 使用新的设置页面结构，传递tab参数
	c.HTML(200, "main.html", gin.H{
		"title": "系统设置",
		"tab":   tab,
		"active": active,
	})
}

// settingsPartialPage 处理设置页面的部分内容加载
func (r *Router) settingsPartialPage(c *gin.Context) {
	tab := c.Param("tab")
	
	// 验证tab参数
	validTabs := map[string]bool{
		"basic":         true,
		"request":       true,
		"ua":            true,
		"referer-check": true,  // 添加来源判断tab
		// "cache":       true,  // 缓存设置已移除，缓存现在永久有效
		"performance": true,
		"stats":       true,
		"sitemap":     true,  // 添加站点地图tab
		"system":      true,
		"error":       true,
		"weight":      true,
		"resource":    true,
		"content":     true,  // 添加内容优化tab
	}
	
	if !validTabs[tab] {
		c.String(404, "Invalid tab")
		return
	}
	
	// 返回对应的HTML片段
	// Gin使用文件的基础名称作为模板名
	templateName := fmt.Sprintf("%s.html", tab)
	c.HTML(200, templateName, gin.H{
		"tab": tab,
	})
}

func (r *Router) getSystemInfo(c *gin.Context) {
	systemInfo := r.systemMonitor.GetSystemInfo()
	c.JSON(200, gin.H{
		"success": true,
		"data": systemInfo,
	})
}

func (r *Router) healthCheck(c *gin.Context) {
	// 检查各个组件状态
	status := "healthy"
	issues := []string{}
	
	// 1. 检查数据库连接
	if r.siteService != nil {
		if _, _, err := r.siteService.GetSites(1, 1, "", 0, "", "id", "desc"); err != nil {
			status = "unhealthy"
			issues = append(issues, "database connection failed")
		}
	}
	
	// 2. 检查Redis连接
	if r.redisCache != nil {
		if err := r.redisCache.Ping(); err != nil {
			status = "degraded" // Redis失败不影响主功能
			issues = append(issues, "redis connection failed")
		}
	}
	
	// 3. 检查goroutine数量
	numGoroutines := runtime.NumGoroutine()
	if numGoroutines > 10000 {
		status = "unhealthy"
		issues = append(issues, fmt.Sprintf("too many goroutines: %d", numGoroutines))
	}
	
	// 4. 检查内存使用
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	allocMB := m.Alloc / 1024 / 1024
	if allocMB > 2048 { // 超过2GB
		status = "degraded"
		issues = append(issues, fmt.Sprintf("high memory usage: %dMB", allocMB))
	}
	
	code := 200
	if status == "unhealthy" {
		code = 503
	}
	
	c.JSON(code, gin.H{
		"status":      status,
		"time":        time.Now(),
		"goroutines":  numGoroutines,
		"memory_mb":   allocMB,
		"issues":      issues,
	})
}

func (r *Router) handleMirrorRequest(c *gin.Context) {
	if r.optimizedMirrorHandler != nil {
		r.optimizedMirrorHandler.HandleMirrorRequest(c)
	} else {
		r.mirrorHandler.HandleMirrorRequest(c)
	}
}

func (r *Router) loginPage(c *gin.Context) {
	c.HTML(200, "login.html", gin.H{
		"title": "登录",
	})
}

func (r *Router) adminsPage(c *gin.Context) {
	c.HTML(200, "admins.html", gin.H{
		"title": "管理员管理",
	})
}

func (r *Router) pseudoPage(c *gin.Context) {
	c.HTML(200, "pseudo.html", gin.H{
		"title": "伪原创管理",
	})
}

func (r *Router) spiderBlockPage(c *gin.Context) {
	c.HTML(200, "spider-block.html", gin.H{
		"title": "蜘蛛屏蔽管理",
	})
}

func (r *Router) uaStatsPage(c *gin.Context) {
	c.HTML(200, "ua-stats.html", gin.H{
		"title": "UA统计分析",
	})
}

func (r *Router) monitorPage(c *gin.Context) {
	c.HTML(200, "monitor.html", gin.H{
		"title": "系统监控",
	})
}

func (r *Router) spiderStatsPage(c *gin.Context) {
	c.HTML(200, "spider-stats.html", gin.H{
		"title": "爬虫统计",
	})
}

func (r *Router) weightMonitorPage(c *gin.Context) {
	c.HTML(200, "weight-monitor.html", gin.H{
		"title": "权重监测",
	})
}

func (r *Router) loginLogsPage(c *gin.Context) {
	c.HTML(200, "login-logs.html", gin.H{
		"title": "登录日志",
	})
}

// SetupSeparate 设置分离的前台和后台路由
func (r *Router) SetupSeparate() (*gin.Engine, *gin.Engine) {
	// 创建前台路由引擎
	webEngine := gin.New()
	webEngine.Use(middleware.Logger(r.logger))
	webEngine.Use(middleware.Metrics(r.performanceMonitor))
	webEngine.Use(middleware.EnhancedRecovery(r.logger)) // 使用增强的panic恢复
	webEngine.Use(middleware.TimeoutMiddleware(30 * time.Second)) // 30秒超时
	
	// 前台静态资源
	webEngine.Static("/cache", "./cache")  // 缓存的静态资源
	
	// 健康检查端点（不需要认证）
	webEngine.GET("/health", r.healthCheck)
	
	// 前台路由 - sitemap.xml和robots.txt路由
	if r.sitemapHandler != nil {
		webEngine.GET("/sitemap.xml", r.sitemapHandler.ServeSitemap)
		webEngine.GET("/robots.txt", r.sitemapHandler.ServeRobotsTxt)
	}
	
	// 前台路由 - 处理镜像请求
	webEngine.NoRoute(r.handleMirrorRequest)
	
	// 创建后台路由引擎
	adminEngine := gin.New()
	adminEngine.Use(middleware.SecurityHeaders()) // 安全头
	adminEngine.Use(middleware.Logger(r.logger))
	adminEngine.Use(middleware.Metrics(r.performanceMonitor))
	adminEngine.Use(middleware.EnhancedRecovery(r.logger)) // 使用增强的panic恢复
	adminEngine.Use(middleware.TimeoutMiddleware(60 * time.Second)) // 60秒超时
	adminEngine.Use(middleware.CSRFMiddleware(r.logger, r.systemSettingsService)) // CSRF保护 - 根据系统设置决定是否启用
	
	// 健康检查端点（不需要认证）
	adminEngine.GET("/health", r.healthCheck)
	
	// 后台静态文件
	adminEngine.Static("/static", "./web/static")
	// 加载所有后台模板（包括子目录）
	templates := []string{
		"web/templates/login.html",
		"web/templates/dashboard.html",
		"web/templates/sites.html",
		"web/templates/batch-add.html",
		"web/templates/orphaned-data.html",
		"web/templates/batch-replace.html",
		"web/templates/site-categories.html",
		"web/templates/keywords.html",
		"web/templates/pseudo.html",
		"web/templates/company-libraries.html",
		"web/templates/admins.html",
		"web/templates/login-logs.html",
		"web/templates/cache.html",
		"web/templates/monitor.html",
		"web/templates/spider-block.html",
		"web/templates/spider-stats.html",
		"web/templates/ua-stats.html",
		"web/templates/weight-monitor.html",
		// 设置页面
		"web/templates/admin/settings/main.html",
		"web/templates/admin/settings/basic.html",
		"web/templates/admin/settings/request.html",
		"web/templates/admin/settings/ua.html",
		"web/templates/admin/settings/content.html",
		"web/templates/admin/settings/performance.html",
		"web/templates/admin/settings/stats.html",
		"web/templates/admin/settings/sitemap.html",
		"web/templates/admin/settings/system.html",
		"web/templates/admin/settings/error.html",
		"web/templates/admin/settings/weight.html",
		"web/templates/admin/settings/resource.html",
		"web/templates/admin/settings/referer-check.html",
	}
	adminEngine.LoadHTMLFiles(templates...)
	
	// 登录页面
	adminEngine.GET("/login", r.loginPage)
	
	// 管理界面路由
	admin := adminEngine.Group("/admin")
	admin.Use(middleware.AuthMiddleware(r.logger, r.authService))
	{
		// 页面路由
		admin.GET("/", r.adminIndex)
		admin.GET("/batch-add", r.batchAddPage)
		admin.GET("/sites", r.sitesPage)
		admin.GET("/orphaned-data", r.orphanedDataPage)
		admin.GET("/batch-replace", r.batchReplacePage)
		admin.GET("/site-categories", r.siteCategoriesPage)
		admin.GET("/keywords", r.keywordsPage)
		admin.GET("/pseudo", r.pseudoPage)
		admin.GET("/company-libraries", r.companyLibrariesPage)
		admin.GET("/cache", r.cachePage)
		admin.GET("/admins", r.adminsPage)
		admin.GET("/settings", r.settingsPage)
		admin.GET("/settings/:tab", r.settingsPageWithTab)
		admin.GET("/settings/partial/:tab", r.settingsPartialPage)
		admin.GET("/spider-block", r.spiderBlockPage)
		admin.GET("/ua-stats", r.uaStatsPage)
		admin.GET("/monitor", r.monitorPage)
		admin.GET("/spider-stats", r.spiderStatsPage)
		admin.GET("/weight-monitor", r.weightMonitorPage)
		admin.GET("/login-logs", r.loginLogsPage)
	}
	
	// 使用公共方法配置API路由
	r.setupAPIRoutes(adminEngine)
	
	return webEngine, adminEngine
}

// SetWeightMonitorService 设置权重监测服务
func (r *Router) SetWeightMonitorService(monitorService *weight.MonitorService, weightRepo repository.WeightRepository) {
	r.weightMonitorService = monitorService
	r.weightHandler = handler.NewWeightHandler(r.logger, weightRepo, monitorService)
}

// SetLoginLogServices 设置登录日志相关服务
func (r *Router) SetLoginLogServices(captchaService *captcha.CaptchaService, loginLogRepo repository.LoginLogRepository) {
	r.captchaService = captchaService
	r.loginLogRepo = loginLogRepo
	// 重新创建authHandler，包含验证码和登录日志服务
	r.authHandler = handler.NewAuthHandler(r.logger, r.authService, captchaService, loginLogRepo, r.systemSettingsService)
	// 创建登录日志处理器
	r.loginLogHandler = handler.NewLoginLogHandler(r.logger, loginLogRepo)
	// 设置验证码服务到系统设置处理器
	if r.systemSettingsHandler != nil {
		r.systemSettingsHandler.SetCaptchaService(captchaService)
	}
}

// SetResourceLimiter 设置资源限流器
func (r *Router) SetResourceLimiter(resourceLimiter *service.ResourceLimiter) {
	r.resourceLimiter = resourceLimiter
	
	// 更新optimizedMirrorHandler
	if r.optimizedMirrorHandler != nil {
		r.optimizedMirrorHandler = handler.NewOptimizedMirrorHandler(
			r.logger,
			r.siteService,
			r.cacheHandler.GetCacheService(),
			r.redisCache,
			r.mirrorHandler,
			r.spiderStatsService,
			r.sitemapService,
			resourceLimiter,
		)
		// 设置给SiteHandler，用于清理内存缓存
		if r.siteHandler != nil {
			r.siteHandler.SetOptimizedMirrorHandler(r.optimizedMirrorHandler)
		}
	}
}

// GetPerformanceMonitor 获取性能监控器
func (r *Router) GetPerformanceMonitor() *service.PerformanceMonitor {
	return r.performanceMonitor
}