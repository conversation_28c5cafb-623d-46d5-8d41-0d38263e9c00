package middleware

import (
	"crypto/rand"
	"encoding/base64"
	"net/http"
	"strings"
	"sync"
	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var (
	csrfTokens = sync.Map{} // 存储CSRF Token
)

// generateCSRFToken 生成CSRF Token
func generateCSRFToken() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// CSRFMiddleware CSRF保护中间件 - 简化版
func CSRFMiddleware(logger *zap.Logger, systemSettingsService *service.SystemSettingsService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从数据库获取系统设置
		systemSettings, err := systemSettingsService.GetSystemSettings()
		if err != nil {
			logger.Error("获取系统设置失败", zap.Error(err))
			// 如果获取失败，默认不启用CSRF
			c.Next()
			return
		}
		
		// 检查是否禁用CSRF保护
		if !systemSettings.EnableCSRF {
			logger.Debug("CSRF保护已禁用")
			c.Next()
			return
		}
		
		path := c.Request.URL.Path
		
		// 跳过不需要CSRF保护的路径
		skipPaths := []string{
			"/api/v1/auth/login",
			"/api/v1/auth/logout",
			"/api/v1/auth/captcha",
			"/api/v1/auth/csrf-token",
		}
		
		for _, skipPath := range skipPaths {
			if path == skipPath {
				c.Next()
				return
			}
		}
		
		// 只对API路径进行CSRF保护
		if !strings.HasPrefix(path, "/api/") {
			c.Next()
			return
		}
		
		// GET和HEAD请求不需要验证
		if c.Request.Method == "GET" || c.Request.Method == "HEAD" || c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// 非GET请求验证Token
		// 获取请求中的CSRF Token
		requestToken := c.GetHeader("X-CSRF-Token")
		if requestToken == "" {
			requestToken = c.PostForm("csrf_token")
		}
		
		// 获取Cookie中的CSRF Token
		cookieToken, _ := c.Cookie("csrf_token")
		
		// 简单验证：请求token和cookie token相同
		if requestToken == "" || cookieToken == "" || requestToken != cookieToken {
			logger.Warn("CSRF token验证失败",
				zap.String("path", path),
				zap.String("method", c.Request.Method),
				zap.String("request_token", requestToken),
				zap.String("cookie_token", cookieToken),
				zap.Bool("request_empty", requestToken == ""),
				zap.Bool("cookie_empty", cookieToken == ""))
			
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "CSRF验证失败",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// SetCSRFToken 设置CSRF Token到存储中
func SetCSRFToken(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从context获取token和session ID
		token, tokenExists := c.Get("csrf_token")
		sessionID, sessionExists := c.Get("session_id")
		
		if tokenExists && sessionExists {
			if tokenStr, ok := token.(string); ok {
				if sessionStr, ok := sessionID.(string); ok {
					// 存储到内存中
					csrfTokens.Store(sessionStr, tokenStr)
					logger.Debug("CSRF token已存储",
						zap.String("session", sessionStr),
						zap.String("token", tokenStr[:8]+"..."))
				}
			}
		}
		
		c.Next()
	}
}

// CleanupCSRFTokens 清理过期的CSRF Token（应该定期调用）
func CleanupCSRFTokens() {
	// 这里可以实现更复杂的清理逻辑
	// 比如记录Token的创建时间，清理超过一定时间的Token
}