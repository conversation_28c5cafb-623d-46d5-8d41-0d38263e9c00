package middleware

import (
	"bytes"
	"io"
	"site-cluster/internal/service"
	"time"

	"github.com/gin-gonic/gin"
)

// ResponseWriter 自定义响应写入器，用于记录响应大小
type ResponseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
	size int64
}

func (w *ResponseWriter) Write(b []byte) (int, error) {
	w.size += int64(len(b))
	return w.ResponseWriter.Write(b)
}

func (w *ResponseWriter) WriteString(s string) (int, error) {
	w.size += int64(len(s))
	return w.ResponseWriter.WriteString(s)
}

// Metrics 性能监控中间件
func Metrics(monitor *service.PerformanceMonitor) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录并发数增加
		monitor.UpdateConcurrency(1)
		defer monitor.UpdateConcurrency(-1)

		// 记录请求开始时间
		start := time.Now()
		
		// 记录请求体大小（上传）
		var requestSize int64
		if c.Request.Body != nil {
			body, _ := io.ReadAll(c.Request.Body)
			requestSize = int64(len(body))
			c.Request.Body = io.NopCloser(bytes.NewReader(body))
		}
		
		// 包装响应写入器以记录响应大小
		rw := &ResponseWriter{
			ResponseWriter: c.Writer,
			body:          bytes.NewBuffer(nil),
		}
		c.Writer = rw

		// 处理请求
		c.Next()

		// 记录请求完成
		latency := time.Since(start)
		isError := c.Writer.Status() >= 400
		monitor.RecordRequest(latency, isError)
		
		// 记录带宽使用
		monitor.RecordBandwidth(uint64(requestSize), uint64(rw.size))

		// 记录缓存命中（如果有缓存标记）
		if cacheHit, exists := c.Get("cache_hit"); exists {
			monitor.RecordCacheHit(cacheHit.(bool))
		}
	}
}