package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Logger 日志中间件
func Logger(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 记录日志
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		// 根据状态码选择日志级别 - 移除不必要的错误日志打印
		switch {
		case statusCode >= 500:
			// 只记录严重错误，不记录常规的500错误
			if c.Errors.String() != "" && c.Errors.String() != "[]" {
				logger.Error("HTTP请求",
					zap.String("ip", clientIP),
					zap.String("method", method),
					zap.String("path", path),
					zap.Int("status", statusCode),
					zap.Duration("latency", latency),
					zap.String("error", c.Errors.String()),
				)
			}
		case statusCode >= 400:
			// 不记录404等常见错误，减少日志噪音
			if statusCode != 404 && statusCode != 401 && statusCode != 403 {
				logger.Warn("HTTP请求",
					zap.String("ip", clientIP),
					zap.String("method", method),
					zap.String("path", path),
					zap.Int("status", statusCode),
					zap.Duration("latency", latency),
				)
			}
		default:
			// 默认不记录Info级别的请求日志，减少资源占用
			// 如需调试可以取消注释
			// logger.Info("HTTP请求",
			// 	zap.String("ip", clientIP),
			// 	zap.String("method", method),
			// 	zap.String("path", path),
			// 	zap.Int("status", statusCode),
			// 	zap.Duration("latency", latency),
			// )
		}
	}
}