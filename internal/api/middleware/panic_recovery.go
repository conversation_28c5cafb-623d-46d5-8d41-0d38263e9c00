package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// EnhancedRecovery 增强的panic恢复中间件
func EnhancedRecovery(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 记录panic堆栈
				stack := debug.Stack()
				logger.Error("Panic recovered",
					zap.Any("error", err),
					zap.String("stack", string(stack)),
					zap.String("path", c.Request.URL.Path),
					zap.String("method", c.Request.Method),
					zap.String("ip", c.ClientIP()),
					zap.Time("time", time.Now()),
				)
				
				// 终止请求上下文
				c.Abort()
				
				// 返回错误响应
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "服务器内部错误",
					"message": fmt.Sprintf("%v", err),
					"time": time.Now().Unix(),
				})
			}
		}()
		
		// 继续处理请求
		c.Next()
	}
}

// TimeoutMiddleware 请求超时中间件
func TimeoutMiddleware(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 创建带超时的context
		ctx, cancel := c.Request.Context(), func() {}
		if timeout > 0 {
			ctx, cancel = c.Request.Context(), func() {}
			defer cancel()
		}
		
		// 更新请求context
		c.Request = c.Request.WithContext(ctx)
		
		// 设置响应超时
		c.Writer.Header().Set("X-Timeout", timeout.String())
		
		c.Next()
	}
}