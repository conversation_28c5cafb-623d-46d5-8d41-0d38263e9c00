package middleware

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
)

// RateLimiter 限流器接口
type RateLimiter interface {
	Allow(key string) bool
	GetLimiter(key string) *rate.Limiter
}

// GlobalRateLimiter 全局限流器
type GlobalRateLimiter struct {
	logger      *zap.Logger
	limiter     *rate.Limiter
	maxRequests int
}

// SiteRateLimiter 站点级别限流器
type SiteRateLimiter struct {
	logger      *zap.Logger
	limiters    map[string]*rate.Limiter
	mu          sync.RWMutex
	maxRequests int // 每分钟最大请求数
	burst       int // 突发请求数
}

// NewGlobalRateLimiter 创建全局限流器
func NewGlobalRateLimiter(logger *zap.Logger, maxRequestsPerMinute int) *GlobalRateLimiter {
	if maxRequestsPerMinute <= 0 {
		maxRequestsPerMinute = 1000 // 默认每分钟1000个请求
	}
	
	// 计算每秒请求数
	rps := float64(maxRequestsPerMinute) / 60.0
	
	return &GlobalRateLimiter{
		logger:      logger,
		limiter:     rate.NewLimiter(rate.Limit(rps), maxRequestsPerMinute/10), // burst为1/10
		maxRequests: maxRequestsPerMinute,
	}
}

// Allow 检查是否允许请求
func (g *GlobalRateLimiter) Allow(key string) bool {
	return g.limiter.Allow()
}

// GetLimiter 获取限流器
func (g *GlobalRateLimiter) GetLimiter(key string) *rate.Limiter {
	return g.limiter
}

// NewSiteRateLimiter 创建站点级别限流器
func NewSiteRateLimiter(logger *zap.Logger, maxRequestsPerMinute int) *SiteRateLimiter {
	if maxRequestsPerMinute <= 0 {
		maxRequestsPerMinute = 100 // 默认每个站点每分钟100个请求
	}
	
	return &SiteRateLimiter{
		logger:      logger,
		limiters:    make(map[string]*rate.Limiter),
		maxRequests: maxRequestsPerMinute,
		burst:       maxRequestsPerMinute / 10,
	}
}

// Allow 检查是否允许请求
func (s *SiteRateLimiter) Allow(key string) bool {
	limiter := s.GetLimiter(key)
	return limiter.Allow()
}

// GetLimiter 获取或创建站点限流器
func (s *SiteRateLimiter) GetLimiter(key string) *rate.Limiter {
	s.mu.RLock()
	limiter, exists := s.limiters[key]
	s.mu.RUnlock()
	
	if exists {
		return limiter
	}
	
	// 创建新的限流器
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// 双重检查
	if limiter, exists := s.limiters[key]; exists {
		return limiter
	}
	
	// 计算每秒请求数
	rps := float64(s.maxRequests) / 60.0
	limiter = rate.NewLimiter(rate.Limit(rps), s.burst)
	s.limiters[key] = limiter
	
	// 定期清理不活跃的限流器
	if len(s.limiters) > 1000 {
		s.cleanup()
	}
	
	return limiter
}

// cleanup 清理不活跃的限流器
func (s *SiteRateLimiter) cleanup() {
	// 简单的清理策略：删除超过100个的旧限流器
	if len(s.limiters) > 100 {
		count := 0
		for key := range s.limiters {
			delete(s.limiters, key)
			count++
			if count > 50 {
				break
			}
		}
		s.logger.Info("清理限流器", zap.Int("cleaned", count))
	}
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(limiter RateLimiter) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 使用IP或域名作为限流键
		key := c.ClientIP()
		if host := c.Request.Host; host != "" {
			key = host
		}
		
		if !limiter.Allow(key) {
			c.Header("X-RateLimit-Limit", "exceeded")
			c.Header("Retry-After", "60")
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "请求过于频繁，请稍后再试",
				"code":  http.StatusTooManyRequests,
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// IPRateLimitMiddleware IP级别限流中间件
func IPRateLimitMiddleware(logger *zap.Logger, maxRequestsPerMinute int) gin.HandlerFunc {
	ipLimiters := make(map[string]*rate.Limiter)
	mu := sync.RWMutex{}
	
	if maxRequestsPerMinute <= 0 {
		maxRequestsPerMinute = 60 // 默认每个IP每分钟60个请求
	}
	
	rps := float64(maxRequestsPerMinute) / 60.0
	burst := maxRequestsPerMinute / 10
	if burst < 1 {
		burst = 1
	}
	
	return func(c *gin.Context) {
		ip := c.ClientIP()
		
		// 获取或创建限流器
		mu.RLock()
		limiter, exists := ipLimiters[ip]
		mu.RUnlock()
		
		if !exists {
			mu.Lock()
			// 双重检查，防止其他goroutine已经创建
			if limiter, exists = ipLimiters[ip]; !exists {
				limiter = rate.NewLimiter(rate.Limit(rps), burst)
				ipLimiters[ip] = limiter
				
				// 简单清理
				if len(ipLimiters) > 10000 {
					// 清理一半
					count := 0
					for k := range ipLimiters {
						delete(ipLimiters, k)
						count++
						if count > 5000 {
							break
						}
					}
				}
			}
			mu.Unlock()
		}
		
		if !limiter.Allow() {
			logger.Warn("IP限流触发", 
				zap.String("ip", ip),
				zap.String("path", c.Request.URL.Path))
			
			c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", maxRequestsPerMinute))
			c.Header("Retry-After", "60")
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "请求过于频繁，请稍后再试",
				"code":  http.StatusTooManyRequests,
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// DomainRateLimitMiddleware 域名级别限流中间件
func DomainRateLimitMiddleware(logger *zap.Logger, limiter *SiteRateLimiter) gin.HandlerFunc {
	return func(c *gin.Context) {
		domain := c.Request.Host
		if domain == "" {
			c.Next()
			return
		}
		
		if !limiter.Allow(domain) {
			logger.Warn("域名限流触发",
				zap.String("domain", domain),
				zap.String("path", c.Request.URL.Path))
			
			c.Header("X-RateLimit-Domain", domain)
			c.Header("Retry-After", "60")
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "站点请求过于频繁",
				"code":  http.StatusTooManyRequests,
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// AdaptiveRateLimiter 自适应限流器
type AdaptiveRateLimiter struct {
	logger        *zap.Logger
	baseLimiter   *rate.Limiter
	currentLoad   float64
	maxLoad       float64
	mu            sync.RWMutex
	lastAdjust    time.Time
	adjustPeriod  time.Duration
}

// NewAdaptiveRateLimiter 创建自适应限流器
func NewAdaptiveRateLimiter(logger *zap.Logger, baseRPS float64) *AdaptiveRateLimiter {
	return &AdaptiveRateLimiter{
		logger:       logger,
		baseLimiter:  rate.NewLimiter(rate.Limit(baseRPS), int(baseRPS*10)),
		maxLoad:      0.8, // CPU/内存使用率超过80%时降低限流
		adjustPeriod: 10 * time.Second,
		lastAdjust:   time.Now(),
	}
}

// Allow 检查是否允许请求
func (a *AdaptiveRateLimiter) Allow(key string) bool {
	a.adjustLimit()
	return a.baseLimiter.Allow()
}

// adjustLimit 动态调整限流
func (a *AdaptiveRateLimiter) adjustLimit() {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	// 限制调整频率
	if time.Since(a.lastAdjust) < a.adjustPeriod {
		return
	}
	
	// 根据系统负载调整
	if a.currentLoad > a.maxLoad {
		// 降低限流
		newLimit := a.baseLimiter.Limit() * 0.8
		a.baseLimiter.SetLimit(rate.Limit(newLimit))
		a.logger.Info("降低限流阈值", zap.Float64("new_limit", float64(newLimit)))
	} else if a.currentLoad < a.maxLoad*0.5 {
		// 提高限流
		newLimit := a.baseLimiter.Limit() * 1.2
		a.baseLimiter.SetLimit(rate.Limit(newLimit))
		a.logger.Info("提高限流阈值", zap.Float64("new_limit", float64(newLimit)))
	}
	
	a.lastAdjust = time.Now()
}

// UpdateLoad 更新系统负载
func (a *AdaptiveRateLimiter) UpdateLoad(load float64) {
	a.mu.Lock()
	a.currentLoad = load
	a.mu.Unlock()
}

// GetLimiter 获取限流器
func (a *AdaptiveRateLimiter) GetLimiter(key string) *rate.Limiter {
	return a.baseLimiter
}