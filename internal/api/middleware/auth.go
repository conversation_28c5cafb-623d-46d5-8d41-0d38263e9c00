package middleware

import (
	"net/http"
	"strings"

	"site-cluster/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(logger *zap.Logger, authService *service.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取令牌
		token := getTokenFromRequest(c)
		if token == "" {
			handleUnauthorized(c, "未登录")
			return
		}

		// 验证会话
		admin, err := authService.VerifySession(token)
		if err != nil {
			logger.Debug("会话验证失败", 
				zap.String("token", token),
				zap.Error(err))
			handleUnauthorized(c, err.Error())
			return
		}

		// 将管理员信息存入上下文
		c.Set("admin", gin.H{
			"id":       admin.ID,
			"username": admin.Username,
			"nickname": admin.Nickname,
			"email":    admin.Email,
		})
		c.Set("admin_id", admin.ID)

		c.Next()
	}
}

// handleUnauthorized 处理未授权的请求
func handleUnauthorized(c *gin.Context, message string) {
	// 判断是否是API请求
	if strings.HasPrefix(c.Request.URL.Path, "/api/") {
		// API请求返回JSON
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   message,
		})
		c.Abort()
	} else {
		// 页面请求重定向到登录页
		c.Redirect(http.StatusFound, "/login")
		c.Abort()
	}
}

// getTokenFromRequest 从请求中获取令牌
func getTokenFromRequest(c *gin.Context) string {
	// 优先从Cookie获取
	if token, err := c.Cookie("admin_token"); err == nil && token != "" {
		return token
	}

	// 从Header获取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
		return strings.TrimPrefix(authHeader, "Bearer ")
	}

	return ""
}