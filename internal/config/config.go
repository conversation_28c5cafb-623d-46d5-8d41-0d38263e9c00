package config

import (
	"os"
	"strconv"
	"time"
)

type Config struct {
	Port           int
	Mode           string
	MaxWorkers     int
	CacheDuration  time.Duration
	RateLimit      float64
	RateBurst      int
	ContentTimeout time.Duration
	CacheDir       string
	TemplateDir    string
}

func Load() *Config {
	cfg := &Config{
		Port:           getEnvAsInt("PORT", 8080),
		Mode:           getEnv("MODE", "release"),
		MaxWorkers:     getEnvAsInt("MAX_WORKERS", 1000),
		CacheDuration:  time.Duration(getEnvAsInt("CACHE_DURATION_MIN", 60)) * time.Minute,
		RateLimit:      getEnvAsFloat("RATE_LIMIT", 1000),
		RateBurst:      getEnvAsInt("RATE_BURST", 2000),
		ContentTimeout: time.Duration(getEnvAsInt("CONTENT_TIMEOUT_SEC", 30)) * time.Second,
		CacheDir:       getEnv("CACHE_DIR", "./cache"),
		TemplateDir:    getEnv("TEMPLATE_DIR", "./templates"),
	}

	return cfg
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	valueStr := os.Getenv(key)
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

func getEnvAsFloat(key string, defaultValue float64) float64 {
	valueStr := os.Getenv(key)
	if value, err := strconv.ParseFloat(valueStr, 64); err == nil {
		return value
	}
	return defaultValue
}