package utils

import (
	"fmt"
	"math/rand"
	"strings"
	"time"
)

// 零宽字符集合
var zeroWidthChars = []rune{
	'\u2060', // 词连接符
	'\u2064', // 不可见加号
	'\u200C', // 零宽非连接符
	'\u200D', // 零宽连接符
	'\u2062', // 不可见乘号
	'\u2063', // 不可见分隔符
}

// HTML标签集合
var htmlTags = []string{
	"tr", "td", "th", "thead", "tbody", "table",
	"option", "select", "optgroup",
	"legend", "fieldset", "form",
	"abbr", "acronym", "address",
	"b", "big", "bdo", "blockquote",
	"button", "center", "cite", "code",
	"dd", "del", "dfn", "dir", "dl", "dt",
	"em", "font", "i", "ins", "kbd",
	"label", "li", "noframes", "ol",
	"p", "pre", "q", "s", "samp",
	"small", "span", "strike", "strong",
	"style", "sub", "sup", "tt",
	"u", "ul", "var",
}

// HiddenHTMLGenerator 隐藏HTML生成器
type HiddenHTMLGenerator struct {
	rand *rand.Rand
}

// NewHiddenHTMLGenerator 创建新的隐藏HTML生成器
func NewHiddenHTMLGenerator() *HiddenHTMLGenerator {
	return &HiddenHTMLGenerator{
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// generateRandomID 生成随机ID
func (g *HiddenHTMLGenerator) generateRandomID(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[g.rand.Intn(len(charset))]
	}
	return string(b)
}

// generateZeroWidthString 生成零宽字符串
func (g *HiddenHTMLGenerator) generateZeroWidthString(minLen, maxLen int) string {
	length := minLen + g.rand.Intn(maxLen-minLen+1)
	var result strings.Builder
	
	// 固定前缀模式
	result.WriteRune('\u2060') // 开始标记
	for i := 0; i < 8; i++ {
		result.WriteRune('\u2064')
	}
	result.WriteRune('\u200C')
	result.WriteRune('\u2060')
	
	// 随机部分
	for i := 11; i < length; i++ {
		result.WriteRune(zeroWidthChars[g.rand.Intn(len(zeroWidthChars))])
	}
	
	return result.String()
}

// generateRandomElement 生成随机HTML元素
func (g *HiddenHTMLGenerator) generateRandomElement(useRandomID bool, fixedID string) string {
	tag := htmlTags[g.rand.Intn(len(htmlTags))]
	content := g.generateZeroWidthString(15, 25)
	
	id := fixedID
	if useRandomID {
		id = g.generateRandomID(5)
	}
	
	// 随机选择元素类型
	switch g.rand.Intn(4) {
	case 0: // 自闭合元素
		return fmt.Sprintf(`<%s id="%s">%s</%s>`, tag, id, content, tag)
	case 1: // 嵌套元素
		innerTag := htmlTags[g.rand.Intn(len(htmlTags))]
		return fmt.Sprintf(`<%s id="%s"><%s id="%s">%s</%s></%s>`, 
			tag, id, innerTag, id, content, innerTag, tag)
	case 2: // 只有内容
		return fmt.Sprintf(`<%s>%s</%s>`, tag, content, tag)
	default: // 带属性的元素
		return fmt.Sprintf(`<%s id="%s">%s</%s>`, tag, id, content, tag)
	}
}

// GenerateHiddenHTML 生成隐藏HTML代码
func (g *HiddenHTMLGenerator) GenerateHiddenHTML(elementCount int, useRandomID bool) string {
	var elements []string
	fixedID := ""
	
	if !useRandomID {
		fixedID = g.generateRandomID(5)
	}
	
	// 生成指定数量的元素
	for i := 0; i < elementCount; i++ {
		elements = append(elements, g.generateRandomElement(useRandomID, fixedID))
	}
	
	// 打乱元素顺序
	g.rand.Shuffle(len(elements), func(i, j int) {
		elements[i], elements[j] = elements[j], elements[i]
	})
	
	// 构建完整的隐藏div（不添加换行，保持代码紧凑）
	var result strings.Builder
	result.WriteString(`<div style="position:fixed;left:-9000px;top:-9000px;">`)
	
	for _, elem := range elements {
		result.WriteString(elem)
		// 不添加换行，保持代码紧凑
	}
	
	result.WriteString(`</div>`)
	
	return result.String()
}

// GenerateHiddenHTMLSeparate 分别生成顶部和底部的隐藏HTML
func (g *HiddenHTMLGenerator) GenerateHiddenHTMLSeparate(elementCount int, useRandomID bool) (top, bottom string) {
	// 顶部和底部各生成一半的元素
	topCount := elementCount / 2
	bottomCount := elementCount - topCount
	
	// 生成顶部HTML
	topGen := NewHiddenHTMLGenerator()
	top = topGen.GenerateHiddenHTML(topCount, useRandomID)
	
	// 生成底部HTML
	bottomGen := NewHiddenHTMLGenerator()
	bottom = bottomGen.GenerateHiddenHTML(bottomCount, useRandomID)
	
	return top, bottom
}