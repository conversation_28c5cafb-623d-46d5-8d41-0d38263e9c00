package utils

import (
	"fmt"
	"net/http"
	"strings"
	"time"
)

// RenderBlockTemplate 渲染屏蔽模板，支持状态码占位符
// 支持的占位符：
// {{.StatusCode}} - 状态码数字 (如 404)
// {{.StatusText}} - 状态码文本 (如 Not Found)
// {{.Status}} - 完整状态 (如 404 Not Found)
// {{.UserAgent}} - 用户代理
// {{.Time}} - 当前时间
func RenderBlockTemplate(template string, statusCode int, userAgent string) string {
	if statusCode == 0 {
		statusCode = http.StatusForbidden // 默认403
	}
	
	// 获取状态码对应的标准文本
	statusText := http.StatusText(statusCode)
	if statusText == "" {
		statusText = fmt.Sprintf("Status %d", statusCode)
	}
	statusString := fmt.Sprintf("%d %s", statusCode, statusText)
	
	// 根据状态码选择默认的颜色和消息
	defaultColor := "#d32f2f"
	defaultMessage := "Access denied"
	
	switch statusCode {
	case http.StatusNotFound:
		defaultColor = "#ff9800"
		defaultMessage = "The requested resource was not found."
	case http.StatusGone:
		defaultColor = "#795548"
		defaultMessage = "The requested resource is no longer available."
	case http.StatusInternalServerError:
		defaultColor = "#f44336"
		defaultMessage = "The server encountered an internal error."
	case http.StatusServiceUnavailable:
		defaultColor = "#9c27b0"
		defaultMessage = "The service is temporarily unavailable."
	case http.StatusForbidden:
		defaultColor = "#d32f2f"
		defaultMessage = "Access to this resource is forbidden."
	case http.StatusUnauthorized:
		defaultColor = "#ff5722"
		defaultMessage = "Authorization is required to access this resource."
	case http.StatusTooManyRequests:
		defaultColor = "#2196f3"
		defaultMessage = "Too many requests. Please try again later."
	}
	
	if template == "" {
		// 使用动态默认模板
		template = fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <title>%s</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f0f0f0;
        }
        h1 {
            color: %s;
            font-size: 48px;
        }
        .message {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .details {
            color: #666;
            margin-top: 20px;
            font-size: 14px;
        }
        p {
            font-size: 18px;
            color: #333;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="message">
        <h1>%s</h1>
        <p>%s</p>
        <div class="details">
            <p>User-Agent: {{.UserAgent}}</p>
            <p>Time: {{.Time}}</p>
        </div>
    </div>
</body>
</html>`, statusString, defaultColor, statusString, defaultMessage)
	} else {
		// 替换状态码相关的占位符
		template = strings.ReplaceAll(template, "{{.StatusCode}}", fmt.Sprintf("%d", statusCode))
		template = strings.ReplaceAll(template, "{{.StatusText}}", statusText)
		template = strings.ReplaceAll(template, "{{.Status}}", statusString)
	}
	
	// 替换其他模板变量
	html := strings.ReplaceAll(template, "{{.UserAgent}}", userAgent)
	html = strings.ReplaceAll(html, "{{.Time}}", time.Now().Format("2006-01-02 15:04:05"))
	
	return html
}