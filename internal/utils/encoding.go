package utils

import (
	"bytes"
	"io"
	"strings"
	"golang.org/x/net/html/charset"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/encoding/traditionalchinese"
	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/encoding/korean"
	"golang.org/x/text/transform"
)

// DetectAndConvertToUTF8 检测HTML编码并转换为UTF-8
func DetectAndConvertToUTF8(data []byte, contentType string) ([]byte, string, error) {
	// 尝试从Content-Type中提取charset
	originalCharset := extractCharset(contentType)
	
	// 如果Content-Type中没有charset，尝试从HTML中检测
	if originalCharset == "" {
		// 检查HTML meta标签
		originalCharset = detectCharsetFromHTML(data)
	}
	
	// 如果还是没有检测到，使用charset包自动检测
	if originalCharset == "" {
		_, name, _ := charset.DetermineEncoding(data, contentType)
		if name != "" {
			originalCharset = name
		}
	}
	
	// 如果已经是UTF-8或未检测到编码，直接返回
	if originalCharset == "" || strings.ToLower(originalCharset) == "utf-8" || strings.ToLower(originalCharset) == "utf8" {
		return data, "utf-8", nil
	}
	
	// 转换编码 - 支持各种常见编码
	var enc encoding.Encoding
	charsetName := strings.ToLower(strings.TrimSpace(originalCharset))
	
	switch charsetName {
	// 简体中文编码
	case "gbk", "cp936", "ms936", "windows-936":
		enc = simplifiedchinese.GBK
	case "gb18030", "gb-18030":
		enc = simplifiedchinese.GB18030
	case "gb2312", "gb-2312", "euc-cn", "euccn":
		enc = simplifiedchinese.GBK // 使用GBK来处理GB2312，兼容性更好
	// 繁体中文编码
	case "big5", "big-5", "cp950", "windows-950":
		enc = traditionalchinese.Big5
	// 日文编码
	case "shift_jis", "shift-jis", "sjis", "cp932", "windows-932":
		enc = japanese.ShiftJIS
	case "euc-jp", "eucjp":
		enc = japanese.EUCJP
	case "iso-2022-jp", "iso2022jp":
		enc = japanese.ISO2022JP
	// 韩文编码
	case "euc-kr", "euckr", "cp949", "windows-949":
		enc = korean.EUCKR
	// Windows编码
	case "windows-1252", "cp1252", "iso-8859-1", "latin1":
		// 这些通常可以直接作为UTF-8处理，或使用charset.Lookup
		enc, _ = charset.Lookup(originalCharset)
	default:
		// 尝试通过charset包获取编码
		enc, _ = charset.Lookup(originalCharset)
	}
	
	if enc == nil {
		// 无法识别编码，返回原始数据
		return data, originalCharset, nil
	}
	
	// 转换为UTF-8
	reader := transform.NewReader(bytes.NewReader(data), enc.NewDecoder())
	utf8Data, err := io.ReadAll(reader)
	if err != nil {
		// 转换失败，返回原始数据
		return data, originalCharset, err
	}
	
	return utf8Data, originalCharset, nil
}

// ConvertFromUTF8 将UTF-8转换回原始编码
func ConvertFromUTF8(data []byte, targetCharset string) ([]byte, error) {
	if targetCharset == "" || strings.ToLower(targetCharset) == "utf-8" || strings.ToLower(targetCharset) == "utf8" {
		return data, nil
	}
	
	var enc encoding.Encoding
	charsetName := strings.ToLower(strings.TrimSpace(targetCharset))
	
	switch charsetName {
	// 简体中文编码
	case "gbk", "cp936", "ms936", "windows-936":
		enc = simplifiedchinese.GBK
	case "gb18030", "gb-18030":
		enc = simplifiedchinese.GB18030
	case "gb2312", "gb-2312", "euc-cn", "euccn":
		enc = simplifiedchinese.GBK
	// 繁体中文编码
	case "big5", "big-5", "cp950", "windows-950":
		enc = traditionalchinese.Big5
	// 日文编码
	case "shift_jis", "shift-jis", "sjis", "cp932", "windows-932":
		enc = japanese.ShiftJIS
	case "euc-jp", "eucjp":
		enc = japanese.EUCJP
	// 韩文编码
	case "euc-kr", "euckr", "cp949", "windows-949":
		enc = korean.EUCKR
	default:
		enc, _ = charset.Lookup(targetCharset)
	}
	
	if enc == nil {
		return data, nil
	}
	
	reader := transform.NewReader(bytes.NewReader(data), enc.NewEncoder())
	return io.ReadAll(reader)
}

// extractCharset 从Content-Type中提取charset
func extractCharset(contentType string) string {
	if contentType == "" {
		return ""
	}
	
	// 查找charset=
	index := strings.Index(strings.ToLower(contentType), "charset=")
	if index == -1 {
		return ""
	}
	
	// 提取charset值
	charset := contentType[index+8:]
	// 去除可能的引号
	charset = strings.Trim(charset, `"'`)
	// 去除分号后的内容
	if idx := strings.IndexAny(charset, "; \t\r\n"); idx != -1 {
		charset = charset[:idx]
	}
	
	return strings.TrimSpace(charset)
}

// detectCharsetFromHTML 从HTML内容中检测charset
func detectCharsetFromHTML(data []byte) string {
	// 只检查前2048字节，有些网站的meta标签比较靠后
	sample := data
	if len(sample) > 2048 {
		sample = sample[:2048]
	}
	
	content := string(sample)
	content = strings.ToLower(content)
	
	// 查找各种格式的charset声明
	// 1. <meta charset="...">
	if idx := strings.Index(content, `charset="`); idx != -1 {
		start := idx + 9
		if end := strings.Index(content[start:], `"`); end != -1 {
			return content[start : start+end]
		}
	}
	
	// 查找 <meta charset='...'>
	if idx := strings.Index(content, `charset='`); idx != -1 {
		start := idx + 9
		if end := strings.Index(content[start:], `'`); end != -1 {
			return content[start : start+end]
		}
	}
	
	// 查找 <meta http-equiv="Content-Type" content="...; charset=...">
	if idx := strings.Index(content, `content-type`); idx != -1 {
		// 查找content属性
		if cidx := strings.Index(content[idx:], `content="`); cidx != -1 {
			contentStart := idx + cidx + 9
			if cend := strings.Index(content[contentStart:], `"`); cend != -1 {
				contentValue := content[contentStart : contentStart+cend]
				return extractCharset(contentValue)
			}
		}
	}
	
	return ""
}

// UpdateHTMLCharset 更新HTML中的charset声明
func UpdateHTMLCharset(html string, newCharset string) string {
	// 替换 <meta charset="...">
	html = replaceCharsetMeta(html, newCharset)
	
	// 替换 <meta http-equiv="Content-Type" content="...; charset=...">
	html = replaceContentTypeMeta(html, newCharset)
	
	// 如果没有找到任何charset声明，在<head>后插入一个
	lower := strings.ToLower(html)
	if !strings.Contains(lower, `charset="utf-8"`) && !strings.Contains(lower, `charset=utf-8`) {
		// 在<head>标签后插入UTF-8声明
		headIdx := strings.Index(lower, "<head")
		if headIdx != -1 {
			// 找到<head>标签的结束位置
			headEndIdx := strings.Index(html[headIdx:], ">")
			if headEndIdx != -1 {
				insertPos := headIdx + headEndIdx + 1
				metaTag := `<meta charset="utf-8">`
				html = html[:insertPos] + "\n" + metaTag + "\n" + html[insertPos:]
			}
		}
	}
	
	return html
}

func replaceCharsetMeta(html string, newCharset string) string {
	// 使用正则表达式替换更安全，但这里用简单的字符串处理
	lower := strings.ToLower(html)
	if idx := strings.Index(lower, `<meta charset="`); idx != -1 {
		start := idx
		if end := strings.Index(html[start:], `>`); end != -1 {
			oldMeta := html[start : start+end+1]
			newMeta := `<meta charset="` + newCharset + `">`
			html = strings.Replace(html, oldMeta, newMeta, 1)
		}
	}
	return html
}

func replaceContentTypeMeta(html string, newCharset string) string {
	lower := strings.ToLower(html)
	if idx := strings.Index(lower, `http-equiv="content-type"`); idx != -1 {
		// 找到整个meta标签
		metaStart := strings.LastIndex(lower[:idx], `<meta`)
		if metaStart != -1 {
			metaEnd := strings.Index(html[metaStart:], `>`)
			if metaEnd != -1 {
				oldMeta := html[metaStart : metaStart+metaEnd+1]
				// 构建新的meta标签
				newMeta := `<meta http-equiv="Content-Type" content="text/html; charset=` + newCharset + `">`
				html = strings.Replace(html, oldMeta, newMeta, 1)
			}
		}
	}
	return html
}