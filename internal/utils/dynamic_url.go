package utils

import (
	"crypto/md5"
	"fmt"
	"net/url"
	"path"
	"regexp"
	"sort"
	"strings"
)

// NormalizeDynamicURL 将动态URL转换为合法的缓存文件路径
// 例如: /index.php?m=content&c=index&a=lists&catid=39
// 转换为: /index.php/m_content/c_index/a_lists/catid_39/index.html
func NormalizeDynamicURL(urlPath string) string {
	// 解析URL
	u, err := url.Parse(urlPath)
	if err != nil {
		return urlPath
	}

	// 如果没有查询参数，返回原路径
	if u.RawQuery == "" {
		return urlPath
	}

	// 获取基础路径
	basePath := u.Path
	if basePath == "" || basePath == "/" {
		basePath = "/index"
	}

	// 解析查询参数
	params := u.Query()
	if len(params) == 0 {
		return urlPath
	}

	// 构建缓存路径
	cachePath := buildCachePath(basePath, params)
	return cachePath
}

// buildCachePath 根据基础路径和参数构建缓存路径
func buildCachePath(basePath string, params url.Values) string {
	// 移除路径开头的斜杠
	basePath = strings.TrimPrefix(basePath, "/")
	
	// 处理特殊情况：如果是PHP文件，保留文件名
	var baseDir, fileName string
	if strings.HasSuffix(basePath, ".php") {
		baseDir = basePath
		fileName = "index.html"
	} else if strings.HasSuffix(basePath, ".asp") || strings.HasSuffix(basePath, ".aspx") {
		baseDir = basePath
		fileName = "index.html"
	} else if strings.HasSuffix(basePath, ".jsp") {
		baseDir = basePath
		fileName = "index.html"
	} else {
		baseDir = basePath
		fileName = "index.html"
	}

	// 构建参数路径 - 按照固定顺序处理常见参数
	var paramParts []string
	
	// 优先处理MVC框架的常见参数顺序
	mvcParams := []string{"m", "c", "a", "id", "catid", "page"}
	for _, k := range mvcParams {
		if values := params[k]; len(values) > 0 {
			cleanValue := cleanParamValue(values[0])
			paramParts = append(paramParts, fmt.Sprintf("%s_%s", k, cleanValue))
			delete(params, k) // 移除已处理的参数
		}
	}
	
	// 处理剩余参数（按字母顺序）
	var otherKeys []string
	for k := range params {
		otherKeys = append(otherKeys, k)
	}
	sort.Strings(otherKeys)
	
	for _, k := range otherKeys {
		values := params[k]
		if len(values) > 0 {
			cleanValue := cleanParamValue(values[0])
			paramParts = append(paramParts, fmt.Sprintf("%s_%s", k, cleanValue))
		}
	}

	// 组合最终路径
	if len(paramParts) > 0 {
		return path.Join(baseDir, strings.Join(paramParts, "/"), fileName)
	}
	return path.Join(baseDir, fileName)
}

// cleanParamValue 清理参数值，移除不合法的文件名字符
func cleanParamValue(value string) string {
	// 移除或替换文件系统不允许的字符
	// 只保留字母、数字、中横线和下划线
	reg := regexp.MustCompile(`[^a-zA-Z0-9\-_]`)
	cleaned := reg.ReplaceAllString(value, "_")
	
	// 限制长度
	if len(cleaned) > 50 {
		// 如果太长，使用MD5摘要
		hash := md5.Sum([]byte(value))
		return fmt.Sprintf("%x", hash)[:16]
	}
	
	return cleaned
}

// GetDynamicCacheKey 获取动态URL的缓存键
// 用于Redis等内存缓存
func GetDynamicCacheKey(domain, urlPath string) string {
	normalizedPath := NormalizeDynamicURL(urlPath)
	return fmt.Sprintf("cache:%s:%s", domain, normalizedPath)
}

// IsDynamicURL 判断是否为动态URL
func IsDynamicURL(urlPath string) bool {
	// 检查是否包含查询参数
	if strings.Contains(urlPath, "?") {
		return true
	}
	
	// 检查常见的动态URL模式
	dynamicPatterns := []string{
		`.php`,
		`.asp`,
		`.aspx`,
		`.jsp`,
		`.do`,
		`.action`,
		`.cgi`,
	}
	
	for _, pattern := range dynamicPatterns {
		if strings.Contains(urlPath, pattern) {
			return true
		}
	}
	
	return false
}

// ParseDynamicURL 解析动态URL，返回基础路径和参数
func ParseDynamicURL(urlPath string) (basePath string, params map[string]string, err error) {
	u, err := url.Parse(urlPath)
	if err != nil {
		return "", nil, err
	}
	
	basePath = u.Path
	params = make(map[string]string)
	
	for k, v := range u.Query() {
		if len(v) > 0 {
			params[k] = v[0]
		}
	}
	
	return basePath, params, nil
}