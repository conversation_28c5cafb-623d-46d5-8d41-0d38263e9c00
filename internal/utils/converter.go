package utils

import (
	"fmt"
	"regexp"
	"strings"
	"github.com/siongui/gojianfan"
)

// SimplifiedToTraditional 将简体中文转换为繁体中文
func SimplifiedToTraditional(text string) string {
	return gojianfan.S2T(text)
}

// TraditionalToSimplified 将繁体中文转换为简体中文
func TraditionalToSimplified(text string) string {
	return gojianfan.T2S(text)
}

// IsSimplifiedChinese 检测文本是否包含简体中文
func IsSimplifiedChinese(text string) bool {
	// 常见的简体中文字符
	simplifiedChars := []string{
		"的", "了", "是", "我", "你", "在", "有", "这", "个", "们",
		"中", "来", "上", "大", "为", "和", "国", "地", "到", "以",
		"说", "时", "要", "就", "出", "会", "可", "也", "你", "对",
		"生", "能", "而", "子", "那", "得", "于", "着", "下", "自",
		"之", "年", "过", "发", "后", "作", "里", "用", "道", "行",
		"所", "然", "家", "种", "事", "成", "方", "多", "经", "么",
		"去", "法", "学", "如", "都", "同", "现", "当", "没", "动",
		"面", "起", "看", "定", "天", "分", "还", "进", "好", "小",
		"部", "其", "些", "主", "样", "理", "心", "她", "本", "前",
		"开", "但", "因", "只", "从", "想", "实", "日", "军", "者",
		"意", "无", "力", "它", "与", "长", "把", "机", "十", "民",
		"第", "公", "此", "已", "工", "使", "情", "明", "性", "知",
		"全", "三", "又", "关", "点", "正", "业", "外", "将", "两",
		"高", "间", "由", "问", "很", "最", "重", "并", "物", "手",
		"应", "战", "向", "头", "文", "体", "政", "美", "相", "见",
		"被", "利", "什", "二", "等", "产", "或", "新", "己", "制",
		"身", "果", "加", "西", "斯", "月", "话", "合", "回", "特",
		"代", "内", "信", "表", "化", "老", "给", "世", "位", "次",
		"度", "门", "任", "常", "先", "海", "通", "教", "儿", "原",
		"东", "声", "提", "立", "及", "比", "员", "解", "水", "名",
		"真", "论", "处", "走", "义", "各", "入", "几", "口", "认",
		"条", "平", "系", "气", "题", "活", "尔", "更", "别", "打",
		"女", "变", "四", "神", "总", "何", "电", "数", "安", "少",
		"报", "才", "结", "反", "受", "目", "太", "量", "再", "感",
		"建", "务", "做", "接", "必", "场", "件", "计", "管", "期",
		"市", "直", "德", "资", "命", "山", "金", "指", "克", "许",
		"统", "区", "保", "至", "队", "形", "社", "便", "空", "决",
		"治", "展", "马", "科", "司", "五", "基", "眼", "书", "非",
		"则", "听", "白", "却", "界", "达", "光", "放", "强", "即",
		"像", "难", "且", "权", "思", "王", "象", "完", "设", "式",
		"色", "路", "记", "南", "品", "住", "告", "类", "求", "据",
		"程", "北", "边", "死", "张", "该", "交", "规", "万", "取",
		"拉", "格", "望", "觉", "术", "领", "共", "确", "传", "师",
		"观", "清", "今", "切", "院", "让", "识", "候", "带", "导",
		"争", "运", "笑", "飞", "风", "步", "改", "收", "根", "干",
		"造", "言", "联", "持", "组", "每", "济", "车", "亲", "极",
		"林", "服", "快", "办", "议", "往", "元", "英", "士", "证",
		"近", "失", "转", "夫", "令", "准", "布", "始", "怎", "呢",
		"存", "未", "远", "叫", "台", "单", "影", "具", "罗", "字",
		"爱", "击", "流", "备", "兵", "连", "调", "深", "商", "算",
		"质", "团", "集", "百", "需", "价", "花", "党", "华", "城",
		"石", "级", "整", "府", "离", "况", "亚", "请", "技", "际",
		"约", "示", "复", "病", "息", "究", "线", "似", "官", "火",
		"断", "精", "满", "支", "视", "消", "越", "器", "容", "照",
		"须", "九", "增", "研", "写", "称", "企", "八", "功", "吗",
		"包", "片", "史", "委", "乎", "查", "轻", "易", "早", "曾",
		"除", "农", "找", "装", "广", "显", "吧", "阿", "李", "标",
		"谈", "吃", "图", "念", "六", "引", "历", "首", "医", "局",
		"突", "专", "费", "号", "尽", "另", "周", "较", "注", "语",
		"仅", "考", "落", "青", "随", "选", "列", "武", "红", "响",
		"虽", "推", "势", "参", "希", "古", "众", "构", "房", "半",
		"节", "土", "投", "某", "案", "黑", "维", "革", "划", "敌",
		"致", "陈", "律", "足", "态", "护", "七", "兴", "派", "孩",
		"验", "责", "营", "星", "够", "章", "音", "跟", "志", "底",
		"站", "严", "巴", "例", "防", "族", "供", "效", "续", "施",
		"留", "讲", "型", "料", "终", "答", "紧", "黄", "绝", "奇",
		"察", "母", "京", "段", "依", "批", "群", "项", "故", "按",
		"河", "米", "围", "江", "织", "害", "斗", "双", "境", "客",
		"纪", "采", "举", "杀", "攻", "父", "苏", "密", "低", "朝",
		"友", "诉", "止", "细", "愿", "千", "值", "仍", "男", "钱",
		"破", "网", "热", "助", "倒", "育", "属", "坐", "帝", "限",
		"船", "脸", "职", "速", "刻", "乐", "否", "刚", "威", "毛",
		"状", "率", "甚", "独", "球", "般", "普", "怕", "弹", "校",
		"苦", "创", "假", "久", "错", "承", "印", "晚", "兰", "试",
		"股", "拿", "脑", "预", "谁", "益", "阳", "若", "送", "急",
		"血", "惊", "伤", "素", "药", "适", "波", "夜", "省", "初",
		"喜", "卫", "源", "食", "险", "待", "述", "陆", "习", "置",
		"居", "劳", "财", "环", "排", "福", "纳", "欢", "雷", "警",
		"获", "模", "充", "负", "云", "停", "木", "游", "龙", "树",
		"疑", "层", "冷", "洲", "冲", "射", "略", "范", "竟", "句",
		"室", "异", "激", "汉", "村", "哈", "策", "演", "简", "卡",
		"罪", "判", "担", "州", "静", "退", "既", "衣", "您", "宗",
		"积", "余", "痛", "检", "差", "富", "灵", "协", "角", "占",
		"配", "征", "修", "皮", "挥", "胜", "降", "阶", "审", "沉",
		"坚", "善", "妈", "刘", "读", "啊", "超", "免", "压", "银",
		"买", "皇", "养", "伊", "怀", "执", "副", "乱", "抗", "犯",
		"追", "帮", "宣", "佛", "岁", "航", "优", "怪", "香", "著",
		"田", "铁", "控", "税", "左", "右", "份", "穿", "艺", "背",
		"阵", "草", "脚", "概", "恶", "块", "顿", "敢", "守", "酒",
		"岛", "托", "央", "户", "烈", "洋", "哥", "索", "胡", "款",
		"靠", "评", "版", "宝", "座", "释", "景", "顾", "弟", "登",
		"货", "互", "付", "伯", "慢", "欧", "换", "闻", "危", "忙",
		"核", "暗", "姐", "介", "坏", "讨", "丽", "良", "序", "升",
		"监", "临", "亮", "露", "永", "呼", "味", "野", "架", "域",
		"沙", "掉", "括", "舰", "鱼", "杂", "误", "湾", "吉", "减",
		"编", "楚", "肯", "测", "败", "屋", "跑", "梦", "散", "温",
		"困", "剑", "渐", "封", "救", "贵", "枪", "缺", "楼", "县",
		"尚", "毫", "移", "娘", "朋", "画", "班", "智", "亦", "耳",
		"恩", "短", "掌", "恐", "遗", "固", "席", "松", "秘", "谢",
		"鲁", "遇", "康", "虑", "幸", "均", "销", "钟", "诗", "藏",
		"赶", "剧", "票", "损", "忽", "巨", "炮", "旧", "端", "探",
		"湖", "录", "叶", "春", "乡", "附", "吸", "予", "礼", "港",
		"雨", "呀", "板", "庭", "妇", "归", "睛", "饭", "额", "含",
		"顺", "输", "摇", "招", "婚", "脱", "补", "谓", "督", "毒",
		"油", "疗", "旅", "泽", "材", "灭", "逐", "莫", "笔", "亡",
		"鲜", "词", "圣", "择", "寻", "厂", "睡", "博", "勒", "烟",
		"授", "诺", "伦", "岸", "奥", "唐", "卖", "俄", "炸", "载",
		"洛", "健", "堂", "旁", "宫", "喝", "借", "君", "禁", "阴",
		"园", "谋", "宋", "避", "抓", "荣", "姑", "孙", "逃", "牙",
		"束", "跳", "顶", "玉", "镇", "雪", "午", "练", "迫", "爷",
		"篇", "肉", "嘴", "馆", "遍", "凡", "础", "洞", "卷", "信",
		"叹", "氏", "紧", "罚", "扩", "欣", "载", "迅", "懂", "忘",
		"泪", "洗", "猛", "街", "诞", "补", "倍", "宁", "魔", "糊",
		"启", "箱", "枚", "硬", "盘", "县", "偷", "雄", "迈", "览",
		"财", "帽", "栏", "胸", "佩", "偶", "浪", "港", "恋", "婴",
		"销", "词", "宇", "辰", "肥", "龄", "岛", "秒", "帐", "斗",
		"寒", "驻", "泊", "辽", "废", "纯", "埋", "掩", "躺", "晋",
		"尺", "燃", "仙", "轨", "递", "奶", "宁", "唯", "链", "鸣",
		"蒸", "翼", "辉", "晨", "逆", "脏", "狱", "杰", "迁", "泰",
		"誓", "替", "厅", "脉", "宛", "轰", "蓝", "锦", "仪", "谷",
		"涉", "租", "荒", "哭", "尊", "隐", "症", "浮", "蓄", "眉",
		"匹", "辟", "粮", "肤", "盟", "烧", "奸", "扬", "奏", "罢",
		"巧", "拒", "姆", "昨", "债", "沈", "禅", "炒", "诈", "惨",
		"畏", "咱", "夸", "袭", "兹", "泛", "倾", "虚", "荡", "析",
		"贯", "淡", "婆", "疾", "绍", "贺", "裂", "塔", "狗", "怨",
		"偏", "蒙", "泥", "媒", "辅", "腿", "跨", "寄", "恨", "挂",
		"偿", "惑", "佳", "垂", "振", "挑", "滚", "侧", "辞", "俗",
		"泉", "奈", "纷", "漫", "符", "扫", "侵", "忍", "鼓", "暴",
		"邦", "笼", "桥", "岂", "伐", "骗", "典", "慧", "抽", "绪",
		"添", "奉", "廷", "尉", "滑", "弄", "聊", "陶", "伴", "逻",
		"昔", "艳", "扶", "慈", "杨", "刺", "绕", "驾", "纵", "残",
		"邓", "赵", "乏", "阅", "竞", "熟", "颜", "薄", "纹", "折",
		"拥", "卢", "沟", "堆", "捕", "慨", "蛋", "柔", "鬼", "览",
		"汤", "尘", "陷", "缩", "牢", "颗", "池", "笛", "腰", "哲",
		"矿", "耶", "恰", "纠", "郎", "诸", "驱", "篇", "迎", "币",
		"殖", "励", "傅", "筑", "凭", "宿", "鞋", "棋", "裁", "狂",
		"冒", "邀", "截", "拖", "饰", "趣", "觅", "魂", "辆", "轮",
		"丛", "棒", "暂", "奴", "辩", "拳", "猜", "崇", "宽", "码",
		"廉", "稍", "私", "拆", "晶", "坊", "跃", "筋", "凝", "孔",
		"贝", "尸", "垃", "圾", "陀", "框", "氧", "恒", "寿", "摸",
		"吴", "涌", "纽", "谨", "糟", "颤", "申", "辟", "忌", "惯",
		"裙", "俊", "怒", "吓", "骂", "呵", "侦", "圆", "催", "漂",
		"狠", "祝", "捉", "吊", "陪", "哀", "惧", "肩", "豪", "侣",
		"址", "耐", "奔", "摩", "盛", "凉", "宅", "赞", "肃", "综",
		"赛", "趋", "筹", "堡", "稿", "纯", "援", "弱", "惜", "贴",
		"厌", "碍", "恢", "汇", "茫", "贪", "震", "娜", "丰", "撞",
		"仿", "徐", "搜", "泡", "仁", "鼻", "翻", "奋", "乙", "恼",
		"驰", "拼", "葛", "摄", "贡", "疯", "吞", "翰", "胁", "挤",
		"宴", "尝", "挺", "插", "御", "朗", "蛇", "舍", "逼", "肌",
		"帅", "酸", "荐", "御", "欲", "汗", "廊", "妹", "遭", "姿",
		"劫", "夺", "锅", "贤", "拾", "赚", "抑", "粗", "颠", "遮",
		"唇", "拔", "巩", "扎", "凝", "捐", "届", "圈", "抬", "腾",
		"撤", "碰", "丢", "夕", "猪", "宏", "旗", "淀", "喂", "拜",
		"乳", "撒", "肚", "踏", "溪", "玛", "丈", "悟", "拦", "坑",
		"晓", "逝", "纠", "返", "井", "驶", "彻", "拟", "抢", "戴",
		"厉", "炎", "嫌", "朴", "咳", "袋", "欠", "吹", "页", "瓜",
		"斜", "匙", "邮", "凌", "锁", "荷", "暖", "柏", "狼", "臣",
		"猎", "涯", "夏", "勃", "眠", "抵", "弃", "咬", "哎", "搭",
		"灾", "谅", "凶", "吐", "狐", "玻", "璃", "挖", "脖", "闲",
		"努", "墓", "逮", "耀", "庆", "憾", "踢", "卓", "罕", "塞",
		"仔", "融", "赖", "潮", "碎", "岩", "仲", "尖", "谱", "渡",
		"涨", "且", "贸", "乘", "伏", "吵", "隆", "诱", "袁", "丁",
		"择", "辣", "膝", "忧", "甜", "趴", "唤", "帆", "辛", "梁",
		"沃", "邻", "泊", "呈", "努", "钻", "吕", "曹", "搁", "滋",
		"勾", "扣", "姻", "甲", "隔", "邪", "寂", "桃", "扇", "灰",
		"惹", "蔑", "喘", "妻", "帘", "汪", "填", "瓦", "飘", "疼",
		"辱", "盒", "憋", "仇", "鸟", "肺", "辜", "勇", "鸡", "捆",
		"懒", "佐", "宾", "郁", "乌", "凤", "暮", "纸", "挣", "硕",
		"钓", "综", "葬", "雇", "泄", "堵", "嗓", "壁", "垫", "卷",
		"攀", "屈", "笨", "盆", "窝", "储", "癌", "愉", "诱", "狱",
		"剩", "斥", "肖", "丸", "绩", "坡", "瞬", "冻", "甩", "陌",
		"啥", "寸", "胖", "崖", "砍", "煤", "矮", "艾", "抹", "剪",
		"咙", "镰", "辩", "穷", "审", "捂", "俺", "晃", "猫", "邱",
		"寺", "辨", "誉", "缓", "摊", "搬", "雀", "钩", "晴", "赤",
		"蝶", "驴", "姥", "卜", "竹", "烦", "蹦", "矛", "糕", "哟",
		"棚", "伸", "熊", "腐", "浑", "恭", "梯", "冈", "苍", "盏",
		"沮", "雕", "酬", "厕", "恳", "磨", "叔", "溜", "囊", "怜",
		"愤", "愁", "扭", "棍", "俯", "涛", "镜", "汹", "咖", "呜",
		"祸", "搏", "刷", "劈", "缠", "曰", "悬", "乞", "丐", "溅",
		"狮", "澡", "钥", "侯", "押", "诧", "琴", "拖", "喊", "娃",
		"嘛", "胶", "屏", "味", "葱", "雾", "莱", "咕", "妨", "彦",
		"萨", "嫁", "畜", "挠", "窄", "樱", "弊", "哼", "穴", "跪",
		"揉", "渗", "撑", "鲍", "翁", "蝴", "鑫", "犬", "吻", "污",
		"埃", "啤", "擦", "芬", "骇", "扔", "兜", "拢", "桂", "窃",
		"贾", "赴", "禄", "桌", "舟", "鹿", "逢", "蟹", "漏", "俩",
		"罩", "乖", "寨", "拘", "凑", "珍", "爪", "赌", "哇", "蹈",
		"拐", "喧", "蓦", "拯", "溃", "憎", "跌", "懈", "肝", "擅",
		"酗", "酒", "炉", "帖", "潘", "晒", "嚷", "贫", "粹", "猴",
		"蜜", "哄", "晰", "挫", "厢", "硫", "逊", "霸", "拎", "麦",
		"颇", "擎", "拽", "荫", "捷", "亨", "惬", "吝", "啬", "泻",
		"垦", "烘", "绑", "砖", "嫩", "坤", "厘", "愚", "肢", "娇",
		"瑟", "谊", "髦", "琦", "窟", "盲", "宰", "躲", "虏", "廖",
		"挽", "荤", "啸", "亏", "雁", "戈", "盼", "割", "萍", "删",
		"涂", "挨", "歌", "挪", "椅", "辖", "匆", "眨", "芒", "啪",
		"诵", "恍", "篷", "淋", "蕾", "霜", "奢", "哦", "韩", "忖",
		"苟", "讶", "瘾", "呐", "屯", "妄", "膨", "匀", "喻", "涵",
		"泣", "衫", "豹", "敞", "魅", "呕", "轿", "抖", "弯", "亭",
		"鸭", "傲", "孕", "篮", "徘", "徊", "秤", "灌", "甭", "恕",
		"虹", "痒", "诊", "刮", "奎", "瓷", "韵", "谐", "熬", "枕",
		"汰", "扳", "漆", "袱", "锐", "咽", "御", "埔", "唉", "窒",
		"息", "瞒", "僵", "猾", "稽", "劣", "炯", "沸", "泌", "尿",
		"毙", "淹", "唱", "衷", "愈", "蜂", "罐", "裸", "廷", "蔽",
		"倦", "徽", "颈", "袍", "裕", "炬", "勘", "鸿", "锡", "拄",
		"蹭", "潇", "捞", "淤", "焦", "灿", "琢", "锣", "筒", "峡",
		"蒸", "饼", "亩", "掏", "萎", "慰", "糙", "叠", "湖", "扰",
		"匪", "咋", "嗑", "厦", "腻", "烤", "蝇", "笨", "傻", "尬",
		"圳", "绣", "杭", "冤", "聋", "赦", "挚", "鸽", "侄", "踊",
		"糖", "悍", "犹", "懦", "卸", "妓", "捣", "谣", "肋", "遏",
		"雌", "拴", "诬", "僻", "羊", "搅", "惕", "嘲", "痴", "靓",
		"皆", "粥", "鹤", "跋", "槐", "禾", "眩", "眯", "廓", "拙",
		"尴", "拭", "涕", "蜡", "嗅", "叛", "臊", "斟", "侮", "脆",
		"枯", "橙", "杏", "稻", "墟", "憨", "殿", "喝", "遵", "搂",
		"梳", "渺", "弓", "翔", "雹", "颁", "娱", "刹", "躯", "柳",
		"絮", "募", "窜", "竭", "酌", "膊", "拧", "厄", "怯", "虾",
		"媳", "膛", "恤", "鞭", "芦", "苇", "槽", "沫", "晕", "烫",
		"卑", "笋", "匈", "粪", "掌", "韧", "噪", "卸", "枉", "腥",
		"洁", "兆", "玫", "瑰", "溢", "钮", "珠", "逍", "遥", "噩",
		"霍", "蜒", "颖", "润", "芽", "莲", "沾", "诧", "衍", "悔",
		"婴", "幼", "焰", "袄", "咸", "庞", "镶", "攒", "胀", "扯",
		"禽", "兢", "蓬", "嗡", "瞩", "嵌", "巫", "榜", "牵", "械",
		"砸", "寓", "瞄", "厨", "剿", "弦", "焚", "窑", "涩", "铺",
		"颅", "淆", "吼", "啃", "憔", "悴", "亦", "舱", "酶", "瘦",
		"踩", "衅", "崔", "嗽", "蔓", "矢", "簿", "寡", "狡", "晦",
		"轴", "辐", "挡", "缴", "矫", "嘱", "樊", "轩", "兀", "洼",
		"蜀", "峰", "逸", "棕", "熔", "漠", "刑", "咱", "坟", "滞",
		"猖", "狂", "蚀", "灶", "沧", "绞", "搓", "豁", "惫", "溺",
		"咒", "钞", "吩", "咐", "藤", "匕", "苑", "霞", "柱", "拱",
		"墩", "绰", "垒", "坝", "堤", "夹", "剥", "颊", "畔", "沦",
		"锈", "淫", "奚", "戳", "滔", "蹬", "焕", "雍", "潭", "柜",
		"璋", "栈", "橱", "橡", "礁", "蔚", "嬉", "弥", "嚣", "魄",
		"圃", "拣", "掐", "榴", "亟", "凋", "茂", "渣", "滓", "泅",
		"癖", "澜", "憬", "韬", "砌", "毯", "钳", "迄", "橘", "躁",
		"唬", "沼", "噬", "坎", "摹", "竖", "峙", "庸", "袖", "沐",
		"踝", "沛", "榷", "拂", "痰", "踱", "瞟", "虔", "肪", "怠",
		"桨", "禹", "褂", "屹", "篓", "涟", "诫", "谭", "祈", "澎",
		"柠", "檬", "狸", "猕", "猿", "抨", "舵", "敛", "拇", "澈",
		"鳞", "狭", "蹋", "枫", "谤", "隧", "懊", "搔", "莽", "慷",
		"睬", "胯", "赎", "蔬", "橄", "榄", "芯", "屡", "喇", "吭",
		"谜", "砧", "辫", "笃", "辙", "瓢", "哮", "喘", "拌", "搀",
		"簇", "掷", "唾", "董", "迸", "芙", "蓉", "椒", "舔", "琐",
		"贬", "亢", "灼", "炊", "滩", "晋", "拙", "株", "撰", "肆",
		"俭", "魁", "哑", "瘫", "趾", "坞", "嫡", "虞", "锭", "嗦",
		"攘", "辄", "蹄", "骤", "荆", "棘", "酿", "寝", "烹", "胰",
		"吱", "嘎", "巷", "蝎", "禀", "邑", "眷", "袒", "陲", "屉",
		"湛", "笙", "瑕", "疵", "赂", "阱", "瞎", "缚", "庐", "汁",
		"腕", "炽", "哆", "嗦", "伎", "俩", "榆", "咪", "镖", "泼",
		"妃", "椎", "疹", "渝", "蔗", "噎", "猝", "拷", "宪", "喳",
		"氢", "漓", "笺", "蝗", "厮", "赘", "衙", "檐", "嫉", "妒",
		"寥", "惰", "巢", "窍", "穗", "撩", "撕", "诀", "秩", "椭",
		"拨", "谴", "债", "勉", "殷", "辰", "吠", "菌", "盔", "挎",
		"铸", "瑶", "恪", "溯", "黯", "蘑", "菇", "娴", "阀", "逾",
		"蚊", "盯", "钝", "哺", "乳", "颂", "廓", "腋", "眶", "阁",
		"逅", "昂", "躬", "霄", "惶", "邸", "蕊", "鸥", "咏", "拐",
		"棱", "窥", "褐", "渠", "灸", "渴", "叮", "嘱", "泳", "棵",
		"肴", "舶", "歼", "嘿", "蚂", "蚁", "宵", "咆", "哮", "岫",
		"栽", "饲", "揽", "贿", "札", "阐", "譬", "纬", "捡", "眺",
		"屿", "岔", "苛", "氨", "钠", "冥", "雏", "鹊", "蔑", "臀",
		"烘", "焙", "诡", "譬", "搐", "嗜", "扼", "茵", "苔", "莓",
		"荚", "菩", "缆", "缸", "揩", "瞪", "囤", "犁", "氮", "钾",
		"锌", "锰", "钙", "酚", "酮", "酯", "醇", "醛", "烷", "烯",
		"炔", "苯", "酚", "醚", "胺", "酰", "卤", "硝", "磺", "磷",
		"氯", "溴", "碘", "氟", "羟", "羧", "氰", "巯", "腈", "肼",
		"脒", "胍", "嘌", "嘧", "啶", "吡", "哒", "嗪", "吲", "哚",
		"噁", "唑", "噻", "吩", "呋", "喃", "咔", "唑", "咪", "唑",
		"吡", "咯", "吡", "啶", "嘧", "啶", "哒", "嗪", "喹", "啉",
		"异", "喹", "啉", "吖", "啶", "嘌", "呤", "蝶", "啶", "吩",
		"嗪", "吩", "噻", "嗪", "吩", "噁", "嗪", "吲", "哚", "咔",
		"唑", "嘌", "呤", "苯", "并", "咪", "唑", "吲", "唑", "苯",
		"并", "三", "唑", "蝶", "呤", "喹", "喔", "啉", "喹", "唑",
		"啉", "噌", "啉", "酞", "嗪", "萘", "啶", "吡", "嗪", "并",
		"嘧", "啶", "喹", "恶", "啉", "苯", "并", "恶", "嗪", "吩",
		"并", "噻", "嗪", "蒽", "醌",
	}
	
	// 检查文本中是否包含这些简体字符
	for _, char := range simplifiedChars {
		if strings.Contains(text, char) {
			return true
		}
	}
	
	return false
}

// ConvertHTMLToTraditional 将HTML内容中的简体中文转换为繁体中文
// 保留HTML标签和属性不变，只转换文本内容
func ConvertHTMLToTraditional(html string) string {
	// 这是一个简化版本，实际使用时可能需要更复杂的HTML解析
	// 为了避免转换HTML标签和属性，我们只转换纯文本部分
	
	// 保护HTML标签
	tagPattern := `<[^>]+>`
	tags := regexp.MustCompile(tagPattern).FindAllString(html, -1)
	
	// 临时替换标签为占位符
	result := html
	for i, tag := range tags {
		placeholder := fmt.Sprintf("__TAG_%d__", i)
		result = strings.Replace(result, tag, placeholder, 1)
	}
	
	// 转换文本内容
	result = SimplifiedToTraditional(result)
	
	// 恢复标签
	for i, tag := range tags {
		placeholder := fmt.Sprintf("__TAG_%d__", i)
		result = strings.Replace(result, placeholder, tag, 1)
	}
	
	return result
}

// ConvertHTMLToTraditionalWithExclusions 将HTML内容中的简体中文转换为繁体中文
// 但排除指定的内容（如关键词、SEO设置等）
func ConvertHTMLToTraditionalWithExclusions(html string, excludeKeywords []string, isHomePage bool) string {
	// 保护需要排除的内容
	protectedContents := make(map[string]string)
	protectIndex := 0
	
	// 无论是否为首页，都保护title和meta标签内容（因为内页也会注入关键词到这些位置）
	// 保护title标签内容
	titlePattern := regexp.MustCompile(`(?i)<title>([^<]*)</title>`)
	html = titlePattern.ReplaceAllStringFunc(html, func(match string) string {
		protectKey := fmt.Sprintf("__PROTECT_%d__", protectIndex)
		protectedContents[protectKey] = match
		protectIndex++
		return protectKey
	})
	
	// 保护meta description内容
	descPattern := regexp.MustCompile(`(?i)<meta\s+name=["']?description["']?\s+content=["']([^"']*)["'][^>]*>`)
	html = descPattern.ReplaceAllStringFunc(html, func(match string) string {
		protectKey := fmt.Sprintf("__PROTECT_%d__", protectIndex)
		protectedContents[protectKey] = match
		protectIndex++
		return protectKey
	})
	
	// 保护meta keywords内容
	keywordsPattern := regexp.MustCompile(`(?i)<meta\s+name=["']?keywords["']?\s+content=["']([^"']*)["'][^>]*>`)
	html = keywordsPattern.ReplaceAllStringFunc(html, func(match string) string {
		protectKey := fmt.Sprintf("__PROTECT_%d__", protectIndex)
		protectedContents[protectKey] = match
		protectIndex++
		return protectKey
	})
	
	// 保护注入的关键词（这些关键词可能被特殊标记包裹）
	// 例如：<span class="injected-keyword">关键词</span>
	if len(excludeKeywords) > 0 {
		for _, keyword := range excludeKeywords {
			if keyword != "" {
				// 转义特殊字符
				escapedKeyword := regexp.QuoteMeta(keyword)
				// 创建正则表达式，匹配整个词
				keywordPattern := regexp.MustCompile(`\b` + escapedKeyword + `\b`)
				
				// 替换所有出现的关键词为占位符
				html = keywordPattern.ReplaceAllStringFunc(html, func(match string) string {
					protectKey := fmt.Sprintf("__PROTECT_%d__", protectIndex)
					protectedContents[protectKey] = match
					protectIndex++
					return protectKey
				})
			}
		}
	}
	
	// 保护HTML标签
	tagPattern := `<[^>]+>`
	tags := regexp.MustCompile(tagPattern).FindAllString(html, -1)
	
	// 临时替换标签为占位符
	for i, tag := range tags {
		placeholder := fmt.Sprintf("__TAG_%d__", i)
		html = strings.Replace(html, tag, placeholder, 1)
	}
	
	// 转换文本内容（此时已经保护了关键词和SEO内容）
	html = SimplifiedToTraditional(html)
	
	// 恢复HTML标签
	for i, tag := range tags {
		placeholder := fmt.Sprintf("__TAG_%d__", i)
		html = strings.Replace(html, placeholder, tag, 1)
	}
	
	// 恢复被保护的内容（关键词和SEO设置）
	for protectKey, protectedContent := range protectedContents {
		html = strings.Replace(html, protectKey, protectedContent, -1)
	}
	
	return html
}