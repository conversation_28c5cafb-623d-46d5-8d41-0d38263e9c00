# 站群系统环境配置文件
# 复制此文件为 .env 并修改配置

# ========================
# Docker Compose配置
# ========================
COMPOSE_PROJECT_NAME=sitecluster

# ========================
# 数据库配置
# ========================
DB_HOST=postgres
DB_PORT=5432
DB_USER=sitecluster
DB_PASSWORD=sitecluster123
DB_NAME=sitecluster

# ========================
# Redis配置
# ========================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ========================
# 应用配置
# ========================
# 前台端口
PORT=9090
# 后台管理端口
ADMIN_PORT=9999
# 运行模式: debug, release
GIN_MODE=release

# ========================
# 系统配置
# ========================
# 时区
TZ=Asia/Shanghai
# 日志级别: debug, info, warn, error
LOG_LEVEL=info
# 最大上传文件大小(MB)
MAX_UPLOAD_SIZE=100

# ========================
# 性能配置
# ========================
# 最大并发爬虫数
MAX_CONCURRENT_CRAWLS=10
# 工作池最小协程数
WORKER_POOL_MIN=5
# 工作池最大协程数
WORKER_POOL_MAX=20
# 缓存清理间隔(小时)
CACHE_CLEANUP_INTERVAL=24
# 缓存过期天数
CACHE_EXPIRE_DAYS=7

# ========================
# 安全配置
# ========================
# Session密钥(请修改为随机字符串)
SESSION_SECRET=change-this-to-random-string
# Session过期时间(小时)
SESSION_EXPIRE_HOURS=24
# 允许的CORS域名
CORS_ORIGINS=*

# ========================
# 限流配置
# ========================
# 全局请求限制(每分钟)
GLOBAL_RATE_LIMIT=1000
# IP请求限制(每分钟)
IP_RATE_LIMIT=100
# 域名请求限制(每分钟)
DOMAIN_RATE_LIMIT=500

# ========================
# 监控配置(可选)
# ========================
# Grafana密码
GRAFANA_PASSWORD=admin123
# Prometheus数据保留天数
PROMETHEUS_RETENTION_DAYS=30

# ========================
# 备份配置
# ========================
# 自动备份间隔(小时，0表示禁用)
AUTO_BACKUP_INTERVAL=24
# 备份保留天数
BACKUP_RETENTION_DAYS=30
# 备份目录
BACKUP_DIR=/app/backup