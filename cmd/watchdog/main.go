package main

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"os/signal"
	"sync"
	"syscall"
	"time"
)

// WatchDog 看门狗进程，监控主进程健康状态
type WatchDog struct {
	mainProcess     *exec.Cmd
	healthCheckURL  string
	checkInterval   time.Duration
	maxRestarts     int
	restartCount    int
	restartDelay    time.Duration
	logFile         *os.File
	mu              sync.Mutex
	isRunning       bool
}

func NewWatchDog() *WatchDog {
	return &WatchDog{
		healthCheckURL: "http://localhost:9090/health",
		checkInterval:  30 * time.Second,
		maxRestarts:    10,
		restartDelay:   10 * time.Second,
	}
}

// Start 启动看门狗
func (wd *WatchDog) Start() error {
	// 打开日志文件
	var err error
	wd.logFile, err = os.OpenFile("watchdog.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("无法打开日志文件: %w", err)
	}
	defer wd.logFile.Close()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动主进程
	if err := wd.startMainProcess(); err != nil {
		return fmt.Errorf("启动主进程失败: %w", err)
	}

	// 启动健康检查
	go wd.healthCheckLoop()

	// 等待信号
	<-sigChan
	wd.log("收到停止信号，正在关闭...")

	// 停止主进程
	wd.stopMainProcess()

	return nil
}

// startMainProcess 启动主进程
func (wd *WatchDog) startMainProcess() error {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	if wd.isRunning {
		return nil
	}

	wd.log("启动主进程...")

	// 设置环境变量
	env := os.Environ()
	env = append(env, "WATCHDOG=true")

	// 创建命令
	wd.mainProcess = exec.Command("./site-cluster")
	wd.mainProcess.Env = env
	
	// 重定向输出到日志
	stdout, err := wd.mainProcess.StdoutPipe()
	if err != nil {
		return err
	}
	stderr, err := wd.mainProcess.StderrPipe()
	if err != nil {
		return err
	}

	// 启动进程
	if err := wd.mainProcess.Start(); err != nil {
		return err
	}

	wd.isRunning = true
	wd.log(fmt.Sprintf("主进程已启动 PID: %d", wd.mainProcess.Process.Pid))

	// 异步读取输出
	go wd.readOutput(stdout, "STDOUT")
	go wd.readOutput(stderr, "STDERR")

	// 监控进程退出
	go wd.waitForExit()

	return nil
}

// stopMainProcess 停止主进程
func (wd *WatchDog) stopMainProcess() {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	if !wd.isRunning || wd.mainProcess == nil {
		return
	}

	wd.log("停止主进程...")

	// 发送SIGTERM信号
	if err := wd.mainProcess.Process.Signal(syscall.SIGTERM); err != nil {
		wd.log(fmt.Sprintf("发送SIGTERM失败: %v", err))
	}

	// 等待30秒
	done := make(chan bool)
	go func() {
		wd.mainProcess.Wait()
		done <- true
	}()

	select {
	case <-done:
		wd.log("主进程已正常退出")
	case <-time.After(30 * time.Second):
		// 强制杀死
		wd.log("超时，强制杀死进程")
		wd.mainProcess.Process.Kill()
	}

	wd.isRunning = false
	wd.mainProcess = nil
}

// waitForExit 等待进程退出
func (wd *WatchDog) waitForExit() {
	if wd.mainProcess == nil {
		return
	}

	err := wd.mainProcess.Wait()
	
	wd.mu.Lock()
	wd.isRunning = false
	exitCode := wd.mainProcess.ProcessState.ExitCode()
	wd.mu.Unlock()

	if err != nil {
		wd.log(fmt.Sprintf("主进程异常退出: %v, 退出码: %d", err, exitCode))
		
		// 尝试重启
		wd.handleCrash()
	} else {
		wd.log(fmt.Sprintf("主进程正常退出，退出码: %d", exitCode))
	}
}

// handleCrash 处理崩溃
func (wd *WatchDog) handleCrash() {
	wd.restartCount++
	
	if wd.restartCount > wd.maxRestarts {
		wd.log(fmt.Sprintf("重启次数超过限制(%d)，停止重启", wd.maxRestarts))
		os.Exit(1)
	}

	wd.log(fmt.Sprintf("等待%v后重启... (第%d次)", wd.restartDelay, wd.restartCount))
	time.Sleep(wd.restartDelay)

	// 增加延迟时间（指数退避）
	wd.restartDelay = wd.restartDelay * 2
	if wd.restartDelay > 5*time.Minute {
		wd.restartDelay = 5 * time.Minute
	}

	// 重启
	if err := wd.startMainProcess(); err != nil {
		wd.log(fmt.Sprintf("重启失败: %v", err))
		os.Exit(1)
	}
}

// healthCheckLoop 健康检查循环
func (wd *WatchDog) healthCheckLoop() {
	// 等待服务启动
	time.Sleep(10 * time.Second)

	ticker := time.NewTicker(wd.checkInterval)
	defer ticker.Stop()

	failureCount := 0
	for range ticker.C {
		if !wd.isRunning {
			continue
		}

		if err := wd.performHealthCheck(); err != nil {
			failureCount++
			wd.log(fmt.Sprintf("健康检查失败(%d): %v", failureCount, err))

			// 连续失败3次，重启服务
			if failureCount >= 3 {
				wd.log("健康检查连续失败，准备重启服务")
				wd.stopMainProcess()
				time.Sleep(5 * time.Second)
				wd.handleCrash()
				failureCount = 0
			}
		} else {
			if failureCount > 0 {
				wd.log("健康检查恢复正常")
			}
			failureCount = 0
			wd.restartCount = 0 // 重置重启计数
			wd.restartDelay = 10 * time.Second // 重置延迟
		}
	}
}

// performHealthCheck 执行健康检查
func (wd *WatchDog) performHealthCheck() error {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Get(wd.healthCheckURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("健康检查返回状态码: %d", resp.StatusCode)
	}

	return nil
}

// readOutput 读取进程输出
func (wd *WatchDog) readOutput(pipe io.ReadCloser, prefix string) {
	defer pipe.Close()
	scanner := bufio.NewScanner(pipe)
	for scanner.Scan() {
		line := scanner.Text()
		wd.log(fmt.Sprintf("[%s] %s", prefix, line))
	}
}

// log 记录日志
func (wd *WatchDog) log(message string) {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logMessage := fmt.Sprintf("[%s] %s\n", timestamp, message)
	
	// 输出到控制台
	fmt.Print(logMessage)
	
	// 写入日志文件
	if wd.logFile != nil {
		wd.logFile.WriteString(logMessage)
	}
}

func main() {
	fmt.Println("=== 站群系统看门狗 ===")
	fmt.Println("监控和自动恢复服务")
	fmt.Println("")

	watchdog := NewWatchDog()
	if err := watchdog.Start(); err != nil {
		log.Fatal(err)
	}
}