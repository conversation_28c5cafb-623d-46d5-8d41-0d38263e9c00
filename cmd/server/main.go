package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"site-cluster/internal/api"
	"site-cluster/internal/crawler"
	"site-cluster/internal/database"
	"site-cluster/internal/repository"
	"site-cluster/internal/repository/gorm"
	"site-cluster/internal/scheduler"
	"site-cluster/internal/service"
	"site-cluster/internal/service/captcha"
	"site-cluster/internal/service/weight"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func main() {
	// 初始化日志 - 生产模式
	logConfig := zap.NewProductionConfig()
	logConfig.Level = zap.NewAtomicLevelAt(zap.ErrorLevel) // 只记录Error级别及以上日志
	logConfig.DisableStacktrace = true // 禁用堆栈跟踪
	logConfig.DisableCaller = false     // 启用调用者信息
	
	logger, err := logConfig.Build()
	if err != nil {
		panic(err)
	}
	defer logger.Sync()

	// 配置
	config := &Config{
		Port:      getEnv("PORT", "9090"),
		AdminPort: getEnv("ADMIN_PORT", "9999"),
		CachePath: getEnv("CACHE_PATH", "./cache"),
		LogLevel:  getEnv("LOG_LEVEL", "production"),
	}

	// 初始化数据库
	db, err := database.InitOptimizedDB()
	if err != nil {
		logger.Fatal("初始化数据库失败", zap.Error(err))
	}
	// logger.Info("数据库初始化成功") // 移除日志

	// 初始化仓储层（使用GORM实现）
	siteRepo := gorm.NewSiteRepository(db)
	keywordRepo := gorm.NewKeywordRepository(db)
	adminRepo := gorm.NewAdminRepository(db)
	siteCategoryRepo := repository.NewSiteCategoryRepository(db)
	weightRepo := repository.NewWeightRepository(db)
	loginLogRepo := repository.NewLoginLogRepository(db)

	// 初始化缓存服务
	fileCacheService := service.NewFileCacheService(logger, config.CachePath)
	
	// 初始化Redis缓存
	redisAddr := fmt.Sprintf("%s:%s", getEnv("REDIS_HOST", "localhost"), getEnv("REDIS_PORT", "6379"))
	redisPassword := getEnv("REDIS_PASSWORD", "")
	redisCacheService := service.NewRedisCacheService(logger, redisAddr, redisPassword, 0)
	// logger.Info("Redis缓存初始化成功", zap.String("addr", redisAddr)) // 移除日志

	// 初始化系统设置服务（需要先创建，因为爬虫服务依赖它）
	systemSettingsService := service.NewSystemSettingsService(db, logger)
	
	// 初始化资源限流器
	// 从系统设置加载配置
	systemSettings, err := systemSettingsService.GetSystemSettings()
	if err != nil {
		logger.Error("加载系统设置失败，使用默认资源限流配置", zap.Error(err))
	}
	
	resourceConfig := &service.ResourceLimitConfig{
		MaxDatabaseConn:  int64(systemSettings.MaxDatabaseConn),
		MaxRedisConn:     int64(systemSettings.MaxRedisConn),
		MaxHTTPRequests:  int64(systemSettings.MaxHTTPRequests),
		MaxFileOps:       int64(systemSettings.MaxFileOps),
		MaxCrawlerTasks:  int64(systemSettings.MaxCrawlerTasks),
		RouteTimeout:     systemSettings.RouteTimeout,
		DatabaseTimeout:  systemSettings.DatabaseQueryTimeout,
		RedisTimeout:     systemSettings.RedisOpTimeout,
		HTTPTimeout:      systemSettings.HTTPRequestTimeout,
		FileTimeout:      systemSettings.FileOpTimeout,
		CrawlerTimeout:   systemSettings.CrawlerTaskTimeout,
	}
	
	// 如果配置为空或无效，使用默认配置
	if resourceConfig.MaxDatabaseConn <= 0 {
		resourceConfig = service.GetDefaultResourceConfig()
		// logger.Info("使用默认资源限流配置")
	} else {
		// logger.Info("从数据库加载资源限流配置",
		// 	zap.Int64("数据库并发", resourceConfig.MaxDatabaseConn),
		// 	zap.Int64("Redis并发", resourceConfig.MaxRedisConn),
		// 	zap.Int64("HTTP并发", resourceConfig.MaxHTTPRequests))
	}
	
	resourceLimiter := service.NewResourceLimiter(logger, resourceConfig)
	
	// 将resourceLimiter设置到systemSettingsService
	systemSettingsService.SetResourceLimiter(resourceLimiter)
	
	// 初始化爬虫（传入resourceLimiter）
	crawlerService := crawler.NewCollyCrawler(logger, fileCacheService, systemSettingsService)

	// 初始化任务队列
	taskQueue := scheduler.NewTaskQueue(logger, 5)
	taskQueue.Start()
	defer taskQueue.Stop() // 确保程序退出时停止任务队列

	// 先创建一个临时的siteService
	tempSiteService := &service.SiteService{}

	// 初始化调度器
	cronScheduler := scheduler.NewCronScheduler(logger, tempSiteService, taskQueue)
	cronScheduler.Start()
	defer cronScheduler.Stop() // 确保程序退出时停止调度器

	// 初始化工作池（根据配置动态计算大小）
	var workerPool interface {
		Start()
		Stop()
		Submit(scheduler.Task) error
	}
	
	if systemSettings.WorkerPoolMode == "dynamic" {
		// 动态模式：使用动态工作池
		minWorkers := systemSettings.WorkerPoolMinSize
		maxWorkers := systemSettings.WorkerPoolMaxSize
		if minWorkers <= 0 {
			minWorkers = 100
		}
		if maxWorkers <= 0 {
			// 基于资源限流配置计算
			maxWorkers = systemSettingsService.CalculateWorkerPoolSize(systemSettings, resourceLimiter)
		}
		// logger.Info("初始化动态工作池", 
		// 	zap.Int("min_workers", minWorkers),
		// 	zap.Int("max_workers", maxWorkers))
		// 使用动态工作池实现
		dynamicPool := scheduler.NewDynamicWorkerPool(logger, minWorkers, maxWorkers)
		dynamicPool.SetCrawler(crawlerService)
		dynamicPool.SetSiteService(tempSiteService)
		dynamicPool.SetCacheService(fileCacheService)
		workerPool = dynamicPool
	} else {
		// 固定或自动模式：计算工作池大小
		schedulerWorkers := systemSettingsService.CalculateWorkerPoolSize(systemSettings, resourceLimiter)
		logger.Info("初始化工作池", 
			zap.Int("workers", schedulerWorkers),
			zap.String("mode", systemSettings.WorkerPoolMode),
			zap.Float64("scale_ratio", systemSettings.WorkerPoolScaleRatio))
		
		// 确保工作池大小在合理范围内
		if schedulerWorkers < 100 {
			schedulerWorkers = 100
		}
		if schedulerWorkers > 5000 {
			schedulerWorkers = 5000
		}
		
		workerPool = scheduler.NewWorkerPool(logger, schedulerWorkers, crawlerService, tempSiteService, fileCacheService)
	}
	
	workerPool.Start()
	defer workerPool.Stop() // 确保程序退出时停止工作池

	// 初始化服务层
	keywordService := service.NewKeywordService(logger, keywordRepo)
	// 设置Redis缓存服务
	keywordService.SetRedisCache(redisCacheService)
	
	siteService := service.NewSiteService(logger, siteRepo, cronScheduler, taskQueue, systemSettingsService, keywordService)
	*tempSiteService = *siteService // 更新临时service
	authService := service.NewAuthService(logger, adminRepo)
	pseudoService := service.NewPseudoService(db, logger)
	// 设置Redis缓存服务
	pseudoService.SetRedisCache(redisCacheService)
	
	analyticsService := service.NewAnalyticsService(db, logger)
	spiderBlockService := service.NewSpiderBlockService(db, logger)
	spiderBlockService.SetSystemSettingsService(systemSettingsService) // 注入系统设置服务，以便检查全局开关
	uaStatsService := service.NewUAStatsService(db, logger, systemSettingsService, spiderBlockService)
	spiderStatsService := service.NewSpiderStatsService(db, logger)
	sitemapService := service.NewSitemapService(db, logger)
	cache404Service := service.NewCache404Service(logger, redisCacheService, systemSettingsService)
	companyLibraryService := service.NewCompanyLibraryService(db, logger)
	siteCategoryService := service.NewSiteCategoryService(siteCategoryRepo)
	
	// 初始化权重监测服务
	weightMonitorService := weight.NewMonitorService(weightRepo, logger)
	
	// 初始化验证码服务
	captchaService := captcha.NewCaptchaService()
	// 从系统设置中设置验证码长度
	if systemSettings.CaptchaLength > 0 {
		captchaService.SetCaptchaLength(systemSettings.CaptchaLength)
	}
	
	// 设置企业名称库服务到站点服务
	siteService.SetCompanyLibraryService(companyLibraryService)
	// 设置Redis缓存服务到站点服务
	siteService.SetRedisCacheService(redisCacheService)
	// 设置站点地图服务到站点服务
	siteService.SetSitemapService(sitemapService)
	// 设置文件缓存服务到站点服务
	siteService.SetFileCacheService(fileCacheService)
	
	// 创建默认管理员账号
	if err := authService.CreateDefaultAdmin(); err != nil {
		logger.Error("创建默认管理员失败", zap.Error(err))
	}
	
	// 不再自动初始化爬虫配置 - 所有配置需要手动添加
	// InitDefaultSpiderConfigs已移除
	
	// 启动权重监测服务
	if err := weightMonitorService.Start(); err != nil {
		logger.Error("启动权重监测服务失败", zap.Error(err))
	}
	defer weightMonitorService.Stop() // 确保程序退出时停止权重监测服务
	
	// 初始化并启动站点地图调度器
	sitemapScheduler := scheduler.NewSitemapScheduler(db, logger, sitemapService, systemSettingsService)
	if err := sitemapScheduler.Start(); err != nil {
		logger.Error("启动站点地图调度器失败", zap.Error(err))
	}
	defer sitemapScheduler.Stop() // 确保程序退出时停止站点地图调度器

	// 设置Gin模式
	if config.LogLevel == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 使用环境变量或默认端口配置
	webPort := config.Port
	adminPort := config.AdminPort

	// 初始化路由
	router := api.NewRouter(logger, siteService, keywordService, fileCacheService, crawlerService, authService, pseudoService, analyticsService, spiderBlockService, spiderStatsService, systemSettingsService, companyLibraryService, siteCategoryService)
	router.SetDB(db) // 设置数据库连接
	router.SetRedisCache(redisCacheService) // 设置Redis缓存
	router.SetSitemapService(sitemapService) // 设置Sitemap服务
	cronScheduler.SetSitemapService(sitemapService) // 设置Sitemap服务到调度器
	cronScheduler.SetAnalyticsService(analyticsService) // 设置Analytics服务到调度器
	crawlerService.SetSitemapService(sitemapService) // 设置Sitemap服务到爬虫（修复预加载不记录sitemap的问题）
	cache404Service.SetDB(db) // 设置404缓存服务的数据库连接
	router.SetCache404Service(cache404Service) // 设置404缓存服务
	router.SetWeightMonitorService(weightMonitorService, weightRepo) // 设置权重监测服务
	router.SetLoginLogServices(captchaService, loginLogRepo) // 设置登录日志服务
	router.SetResourceLimiter(resourceLimiter) // 设置资源限流器
	router.SetUAStatsService(uaStatsService) // 设置UA统计服务
	webEngine, adminEngine := router.SetupSeparate() // 使用新的分离设置方法

	// 启动前台网站服务器
	webSrv := &http.Server{
		Addr:    ":" + webPort,
		Handler: webEngine,
	}

	// 启动后台管理服务器
	adminSrv := &http.Server{
		Addr:    ":" + adminPort,
		Handler: adminEngine,
	}

	// 启动前台服务器
	go func() {
		// logger.Info("前台网站服务器启动", zap.String("port", webPort)) // 移除日志
		// fmt.Printf("前台服务器启动在端口 %s\n", webPort) // 静默启动
		if err := webSrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("前台服务器启动失败", zap.Error(err))
		}
	}()

	// 启动后台服务器
	go func() {
		// logger.Info("后台管理服务器启动", zap.String("port", adminPort)) // 移除日志
		// fmt.Printf("后台管理服务器启动在端口 %s\n", adminPort) // 静默启动
		if err := adminSrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("后台服务器启动失败", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// logger.Info("正在关闭服务器...") // 生产模式：静默关闭

	// 5秒超时关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 停止所有服务（defer语句会自动处理，这里注释掉避免重复调用）
	// taskQueue.Stop()
	// cronScheduler.Stop()
	// workerPool.Stop()
	// sitemapScheduler.Stop()
	// weightMonitorService.Stop() // 这个之前漏掉了

	// 关闭两个服务器
	if err := webSrv.Shutdown(ctx); err != nil {
		logger.Error("前台服务器关闭失败", zap.Error(err))
	}
	if err := adminSrv.Shutdown(ctx); err != nil {
		logger.Error("后台服务器关闭失败", zap.Error(err))
	}

	// logger.Info("服务器已关闭") // 生产模式：静默关闭
}

// Config 应用配置
type Config struct {
	Port      string
	AdminPort string
	CachePath string
	LogLevel  string
}

// getEnv 获取环境变量
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}