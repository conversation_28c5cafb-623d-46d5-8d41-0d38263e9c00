#!/bin/bash

echo "==================================="
echo "完全重置Docker PostgreSQL环境"
echo "==================================="
echo
echo "警告：这将删除所有数据库数据！"
echo "按 Ctrl+C 取消，或按 Enter 继续..."
read

# 1. 停止并删除容器
echo "步骤1: 停止并删除PostgreSQL容器..."
docker stop site-cluster-postgres 2>/dev/null
docker rm site-cluster-postgres 2>/dev/null
echo "✅ 容器已删除"

# 2. 删除数据卷（这是关键！）
echo -e "\n步骤2: 删除数据卷..."
docker volume rm site-cluster_postgres_data 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ 数据卷 site-cluster_postgres_data 已删除"
else
    echo "⚠️ 数据卷可能不存在或删除失败"
fi

# 3. 清理未使用的卷
echo -e "\n步骤3: 清理所有未使用的Docker卷..."
docker volume prune -f
echo "✅ 未使用的卷已清理"

# 4. 创建新的PostgreSQL容器（不使用旧卷）
echo -e "\n步骤4: 创建全新的PostgreSQL容器..."
docker run -d \
  --name site-cluster-postgres \
  --health-cmd="pg_isready -U sitecluster" \
  --health-interval=10s \
  --health-timeout=5s \
  --health-retries=5 \
  -e POSTGRES_USER=sitecluster \
  -e POSTGRES_PASSWORD=sitecluster123 \
  -e POSTGRES_DB=sitecluster \
  -p 5432:5432 \
  -v site-cluster_postgres_data_clean:/var/lib/postgresql/data \
  -v $(pwd)/scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql \
  postgres:15-alpine

if [ $? -eq 0 ]; then
    echo "✅ 新容器已创建"
else
    echo "❌ 容器创建失败"
    exit 1
fi

# 5. 等待PostgreSQL启动
echo -e "\n步骤5: 等待PostgreSQL启动..."
for i in {1..30}; do
  if docker exec site-cluster-postgres pg_isready -U sitecluster > /dev/null 2>&1; then
    echo "✅ PostgreSQL已就绪"
    break
  fi
  echo "等待PostgreSQL启动... ($i/30)"
  sleep 2
done

# 6. 验证数据库状态
echo -e "\n步骤6: 验证数据库状态..."
echo "检查spider_blocks表："
docker exec site-cluster-postgres psql -U sitecluster -d sitecluster -c "SELECT COUNT(*) as count FROM spider_blocks;" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "表还不存在，等待应用创建表结构..."
else
    echo "✅ 数据库已就绪"
fi

# 7. 显示容器信息
echo -e "\n步骤7: 容器信息..."
docker ps | grep site-cluster-postgres

echo -e "\n==================================="
echo "✅ 完成！"
echo "==================================="
echo
echo "现在你有一个全新的PostgreSQL数据库："
echo "  - 容器名: site-cluster-postgres"
echo "  - 数据卷: site-cluster_postgres_data_clean (新的！)"
echo "  - 端口: 5432"
echo "  - 用户: sitecluster"
echo "  - 密码: sitecluster123"
echo "  - 数据库: sitecluster"
echo
echo "重要提示："
echo "1. 所有旧数据已被删除"
echo "2. spider_blocks表将是空的"
echo "3. 运行 ./start.sh 启动应用"
echo "4. 应用会自动创建表结构（但不会有45条规则了！）"