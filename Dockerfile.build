# 多阶段构建 - 编译静态二进制
FROM golang:1.23-alpine AS builder

# 安装编译依赖
RUN apk add --no-cache git gcc musl-dev

# 设置工作目录
WORKDIR /build

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 编译静态二进制（完全静态链接）
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
    go build -a -installsuffix cgo \
    -ldflags="-w -s -extldflags '-static'" \
    -o site-cluster cmd/server/main.go

# 最终镜像
FROM scratch
COPY --from=builder /build/site-cluster /site-cluster
ENTRYPOINT ["/site-cluster"]