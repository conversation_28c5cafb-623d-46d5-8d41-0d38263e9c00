<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爬虫统计 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* 优化表格显示 */
        @media (min-width: 1920px) {
            .overflow-x-auto {
                overflow-x: visible;
            }
        }
        
        /* 固定列样式 */
        th.sticky,
        td.sticky {
            position: sticky;
            background-color: inherit;
        }
        
        /* 固定列阴影效果 */
        th.sticky.left-0,
        td.sticky.left-0 {
            box-shadow: 2px 0 4px -2px rgba(0, 0, 0, 0.1);
        }
        
        /* 表格紧凑模式 */
        .compact-table th,
        .compact-table td {
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">爬虫统计</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="showSpiderConfigModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-cog mr-2"></i>
                            配置爬虫
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 图表区域 -->
                <div class="bg-white rounded-lg shadow mb-6">
                    <div class="px-6 py-4 border-b flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-800">爬虫访问趋势</h3>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm font-medium text-gray-700" id="current-domain-display">当前筛选：所有域名</span>
                            <button onclick="clearDomainFilter()" class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 rounded border" id="clear-filter-btn" style="display:none;">
                                <i class="fas fa-times mr-1"></i>取消筛选
                            </button>
                            <label class="text-sm font-medium text-gray-700 ml-4">时间范围：</label>
                            <select id="time-range" onchange="changeTimeRange(this.value)" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="day">最近24小时</option>
                                <option value="week">最近7天</option>
                                <option value="15days" selected>最近15天</option>
                                <option value="month">最近30天</option>
                            </select>
                        </div>
                    </div>
                    <div class="p-6" style="height: 300px;">
                        <canvas id="spider-chart"></canvas>
                    </div>
                </div>
                
                <!-- 域名统计表格 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-800">爬虫访问统计</h3>
                            <div class="flex items-center space-x-4">
                                <div class="text-sm text-gray-600">
                                    <span id="stats-time-range">最近1小时</span>统计数据
                                </div>
                                <div class="flex items-center space-x-2">
                                    <input type="text" id="domain-filter" placeholder="筛选域名..." 
                                           class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           onkeyup="handleDomainFilter(event)">
                                    <button onclick="applyFilters()" class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg text-sm">
                                        <i class="fas fa-search mr-1"></i>查询
                                    </button>
                                    <button onclick="showAllDomains()" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg text-sm">
                                        <i class="fas fa-list mr-1"></i>显示所有
                                    </button>
                                    <button onclick="clearStats()" class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg text-sm">
                                        <i class="fas fa-trash mr-1"></i>清空统计
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 批量操作栏 -->
                    <div class="px-6 py-3 bg-gray-50 border-b flex items-center justify-between" id="batch-actions" style="display:none;">
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-700">已选择 <span id="selected-count" class="font-semibold">0</span> 个域名</span>
                            <button onclick="deleteSelectedDomains()" class="px-3 py-1 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700">
                                <i class="fas fa-trash mr-1"></i>批量删除
                            </button>
                            <button onclick="clearSelection()" class="px-3 py-1 border border-gray-300 text-gray-700 text-sm rounded-lg hover:bg-gray-100">
                                取消选择
                            </button>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50 border-b" id="domain-stats-header">
                                <!-- 动态生成表头 -->
                            </thead>
                            <tbody id="domain-stats-body" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="10" class="px-6 py-12 text-center text-gray-500">
                                        <div class="flex justify-center">
                                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                                        </div>
                                        <p class="mt-4">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-700">每页显示</span>
                                <select id="page-size" onchange="changePageSize()" class="border border-gray-300 rounded px-2 py-1 text-sm">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="text-sm text-gray-700">条</span>
                                <span class="text-sm text-gray-600 ml-4">
                                    共 <span id="total-domains">0</span> 个域名
                                </span>
                            </div>
                            <div id="pagination" class="flex space-x-2">
                                <!-- 分页按钮将在这里渲染 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <!-- 爬虫配置模态框 -->
    <div id="spider-config-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">爬虫配置管理</h3>
                    <button onclick="closeSpiderConfigModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div class="mb-4">
                        <button onclick="showAddConfigModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            <i class="fas fa-plus mr-2"></i>
                            添加爬虫
                        </button>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                        <thead class="bg-gray-50 border-b">
                            <tr>
                                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap">名称</th>
                                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap">显示名称</th>
                                <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap min-w-[200px]">USER-AGENT特征</th>
                                <th class="px-4 py-3 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap">颜色</th>
                                <th class="px-4 py-3 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap">优先级</th>
                                <th class="px-4 py-3 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap">状态</th>
                                <th class="px-4 py-3 text-center text-sm font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap">操作</th>
                            </tr>
                        </thead>
                        <tbody id="spider-configs-tbody" class="bg-white divide-y divide-gray-200">
                            <!-- 动态生成 -->
                        </tbody>
                    </table>
                    </div>
                </div>
                
                <div class="px-6 py-4 border-t flex justify-end">
                    <button onclick="closeSpiderConfigModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑爬虫配置模态框 -->
    <div id="add-config-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold" id="config-modal-title">添加爬虫配置</h3>
                    <button onclick="closeAddConfigModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="config-form" class="p-6">
                    <input type="hidden" id="config-id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="config-name" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：google">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">显示名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="config-display-name" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：谷歌">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">User-Agent特征</label>
                        <input type="text" id="config-user-agent"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：googlebot">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">图表颜色</label>
                        <input type="color" id="config-color" value="#1890ff"
                               class="w-full h-10 border border-gray-300 rounded-lg cursor-pointer">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">显示优先级</label>
                        <input type="number" id="config-priority" value="0" min="0"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="config-enabled" checked class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <span class="text-sm font-medium text-gray-700">启用统计</span>
                        </label>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeAddConfigModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveSpiderConfig()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        let currentTimeRange = '15days';
        let currentPage = 1;
        let pageSize = 10;  // 改为可变
        let spiderChart = null;
        let currentConfigs = [];
        let domainFilter = '';
        let sortColumn = '';  // 当前排序列
        let sortOrder = 'desc';  // 排序方向
        let currentDomainStats = [];  // 保存当前的域名统计数据
        let currentSummary = {};  // 保存当前的汇总数据
        
        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 兼容旧的通知函数
        function showSuccess(message) { showToast(message, 'success'); }
        function showError(message) { showToast(message, 'error'); }
        function showWarning(message) { showToast(message, 'warning'); }
        function showInfo(message) { showToast(message, 'info'); }

        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadSpiderStats();
            // 每分钟自动刷新（保留当前筛选条件）
            setInterval(() => {
                loadSpiderStats(currentPage);
            }, 60000);
        });
        
        // 切换时间范围
        function changeTimeRange(range) {
            currentTimeRange = range;
            currentPage = 1;
            document.getElementById('time-range').value = range;
            loadSpiderStats();
        }
        
        
        
        // 改变每页显示数量
        function changePageSize() {
            pageSize = parseInt(document.getElementById('page-size').value);
            currentPage = 1;
            loadSpiderStats();
        }
        
        // 排序功能（客户端排序当前页数据）
        function sortByColumn(column) {
            // 如果点击同一列，切换排序顺序
            if (sortColumn === column) {
                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                sortColumn = column;
                sortOrder = 'desc'; // 默认降序
            }
            
            // 通过API重新加载数据（会使用新的排序参数）
            loadSpiderStats(currentPage);
        }
        
        // 处理域名筛选输入
        function handleDomainFilter(event) {
            // 按下Enter键时触发查询
            if (event.key === 'Enter') {
                applyFilters();
            }
        }
        
        // 应用筛选条件
        function applyFilters() {
            const domainFilterEl = document.getElementById('domain-filter');
            domainFilter = domainFilterEl ? domainFilterEl.value : '';
            currentPage = 1;
            
            // 更新图表上方的显示文字
            const displayEl = document.getElementById('current-domain-display');
            if (displayEl) {
                displayEl.textContent = domainFilter ? `当前筛选：${domainFilter}` : '当前筛选：所有域名';
            }
            
            // 控制取消筛选按钮的显示
            const clearBtn = document.getElementById('clear-filter-btn');
            if (clearBtn) {
                clearBtn.style.display = domainFilter ? 'inline-block' : 'none';
            }
            
            // 高亮筛选的行
            highlightFilteredRow(domainFilter);
            
            loadSpiderStats();
        }
        
        // 切换域名筛选（点击同一域名可取消筛选）
        function toggleDomainFilter(domain) {
            // 如果当前筛选的就是这个域名，则取消筛选
            if (domainFilter === domain) {
                filterChart(''); // 取消筛选
            } else {
                filterChart(domain); // 应用筛选
            }
        }
        
        // 图表筛选功能
        function filterChart(domain) {
            domainFilter = domain;
            currentPage = 1;
            
            // 更新显示文字
            const displayEl = document.getElementById('current-domain-display');
            if (displayEl) {
                displayEl.textContent = domain ? `当前筛选：${domain}` : '当前筛选：所有域名';
            }
            
            // 控制取消筛选按钮的显示
            const clearBtn = document.getElementById('clear-filter-btn');
            if (clearBtn) {
                clearBtn.style.display = domain ? 'inline-block' : 'none';
            }
            
            // 同步更新表格筛选框
            const domainFilterEl = document.getElementById('domain-filter');
            if (domainFilterEl) {
                domainFilterEl.value = domain;
            }
            
            // 高亮选中的行
            highlightFilteredRow(domain);
            
            loadSpiderStats();
        }
        
        // 高亮筛选的行
        function highlightFilteredRow(selectedDomain) {
            // 移除所有行的高亮
            const allRows = document.querySelectorAll('#domain-stats-body tr');
            allRows.forEach(row => {
                row.classList.remove('bg-blue-50', 'border-l-4', 'border-blue-500');
            });
            
            // 高亮当前筛选的行
            if (selectedDomain) {
                const domainButtons = document.querySelectorAll('button[onclick*="toggleDomainFilter"]');
                domainButtons.forEach(button => {
                    if (button.textContent.trim() === selectedDomain) {
                        const row = button.closest('tr');
                        if (row) {
                            row.classList.add('bg-blue-50', 'border-l-4', 'border-blue-500');
                        }
                    }
                });
            }
        }
        
        
        // 清除域名筛选
        function clearDomainFilter() {
            filterChart(''); // 直接调用图表筛选函数
        }
        
        // 显示所有域名（清除筛选）
        function showAllDomains() {
            filterChart(''); // 直接调用图表筛选函数
        }
        
        // 更新时间范围显示
        function updateTimeRangeDisplay() {
            const display = document.getElementById('stats-time-range');
            if (!display) {
                console.warn('stats-time-range 元素不存在');
                return;
            }
            
            const domainFilterEl = document.getElementById('domain-filter');
            const domainFilter = domainFilterEl ? domainFilterEl.value : '';
            
            let timeText = '';
            switch(currentTimeRange) {
                case 'hour':
                    timeText = '最近1小时';
                    break;
                case 'day':
                    timeText = '最近24小时';
                    break;
                case 'week':
                    timeText = '最近7天';
                    break;
                case '15days':
                    timeText = '最近15天';
                    break;
                case 'month':
                    timeText = '最近30天';
                    break;
            }
            
            // 如果有域名筛选，显示筛选信息
            if (domainFilter) {
                display.textContent = `${timeText} (域名: ${domainFilter})`;
            } else {
                display.textContent = timeText;
            }
        }
        
        // 加载爬虫统计数据
        async function loadSpiderStats(page = 1) {
            currentPage = page;

            
            try {
                const params = new URLSearchParams({
                    range: currentTimeRange,
                    domain: domainFilter,
                    page: currentPage,
                    page_size: pageSize,
                    sort_by: sortColumn || 'total',  // 添加排序字段
                    sort_order: sortOrder || 'desc'  // 添加排序方向
                });
                
                const res = await fetch(`/api/v1/spider-stats?${params}`);
                const data = await res.json();
                
                if (data.success) {
                    // 保存当前配置，用于后续渲染
                    if (data.data.configs) {
                        currentConfigs = data.data.configs;
                    }
                    renderSpiderStats(data.data);
                } else {
                    showError(data.error || '加载失败');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        // 渲染爬虫统计
        function renderSpiderStats(stats) {
            console.log('renderSpiderStats called with:', stats);
            
            
            // 渲染图表
            if (stats.charts) {
                console.log('Rendering chart with data:', stats.charts);
                renderChart(stats.charts);
            } else if (stats.chart_data) {
                console.log('Rendering chart with chart_data:', stats.chart_data);
                renderChart(stats.chart_data);
            } else {
                console.warn('No chart data found in stats');
            }
            
            // 保存当前数据
            currentDomainStats = stats.domain_stats || [];
            currentSummary = stats.summary || {};
            
            // 渲染域名统计表格
            renderDomainStats(currentDomainStats, currentSummary);
            
            // 渲染分页
            renderPagination(stats.pagination);
            
            // 更新时间范围显示
            updateTimeRangeDisplay();
            
            // 保持高亮状态
            setTimeout(() => highlightFilteredRow(domainFilter), 100);
            
        }
        
        // 渲染汇总数据（已移除，数据在表格中显示）
        // function renderSummary(summary) {
        //     // 功能已移除，汇总数据直接在表格中显示
        // }
        
        // 渲染图表
        function renderChart(chartData) {
            console.log('renderChart called with:', chartData);
            
            const canvas = document.getElementById('spider-chart');
            if (!canvas) {
                console.error('Canvas element "spider-chart" not found');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            
            if (spiderChart) {
                spiderChart.destroy();
            }
            
            try {
                spiderChart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 15,
                                padding: 15,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: false
                            },
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: false
                            },
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                font: {
                                    size: 11
                                }
                            },
                            grid: {
                                drawBorder: false,
                                color: '#e5e7eb'
                            }
                        }
                    },
                    elements: {
                        line: {
                            tension: 0.3,
                            borderWidth: 2
                        },
                        point: {
                            radius: 3,
                            hoverRadius: 5
                        }
                    },
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    }
                }
            });
                console.log('Chart created successfully');
            } catch (error) {
                console.error('Error creating chart:', error);
            }
        }
        
        // 渲染域名统计表格
        function renderDomainStats(domainStats, summary) {
            // 渲染表头
            const header = document.getElementById('domain-stats-header');
            if (!header) {
                console.warn('domain-stats-header 元素不存在');
                return;
            }
            
            // 按优先级排序的爬虫，只显示有数据的
            const sortedSpiders = Object.keys(summary)
                .filter(name => summary[name].total > 0)
                .sort((a, b) => {
                    const configA = currentConfigs.find(c => c.name === a);
                    const configB = currentConfigs.find(c => c.name === b);
                    return (configA?.priority || 999) - (configB?.priority || 999);
                });
            
            let headerHTML = `
                <tr>
                    <th class="px-3 py-3 text-center">
                        <input type="checkbox" id="select-all-domains" onchange="toggleSelectAll()" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('domain')">
                        域名 <i class="fas fa-sort text-xs ml-1"></i>
                    </th>
            `;
            
            sortedSpiders.forEach(spiderName => {
                const spider = summary[spiderName];
                const config = currentConfigs.find(c => c.name === spiderName);
                // 使用爬虫的内部名称（如 "baidu"）而不是显示名称（如 "百度"）
                headerHTML += `
                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('${spiderName}')">
                        <div class="flex items-center justify-center space-x-1">
                            <span>${spider.display_name}</span>
                            ${config?.color ? `<div class="w-2 h-2 rounded-full" style="background-color: ${config.color}"></div>` : ''}
                            <i class="fas fa-sort text-xs ml-1"></i>
                        </div>
                    </th>`;
            });
            
            headerHTML += `
                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortByColumn('total')">总计 <i class="fas fa-sort text-xs ml-1"></i></th>
                <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>`;
            header.innerHTML = headerHTML;
            
            // 渲染数据
            const tbody = document.getElementById('domain-stats-body');
            if (!tbody) {
                console.warn('domain-stats-body 元素不存在');
                return;
            }
            
            if (!domainStats || domainStats.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="${4 + sortedSpiders.length}" class="px-6 py-12 text-center text-gray-500">
                            暂无数据
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = domainStats.map(domain => {
                let row = `
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-4 text-center">
                            <input type="checkbox" class="domain-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" 
                                   value="${domain.domain}" onchange="updateSelectedCount()">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="toggleDomainFilter('${domain.domain}')" 
                                    class="text-blue-600 hover:text-blue-800 hover:underline cursor-pointer font-medium"
                                    title="点击筛选/取消筛选此域名">
                                ${domain.domain}
                            </button>
                        </td>
                `;
                
                let total = 0;
                sortedSpiders.forEach(spiderName => {
                    const stats = domain.stats[spiderName] || { today: 0, yesterday: 0, five_days: 0, week: 0, hour: 0 };
                    const count = getCountByTimeRange(stats);
                    total += count;
                    const cellClass = count > 0 ? 'text-gray-900 font-medium' : 'text-gray-400';
                    row += `<td class="px-6 py-4 text-center text-sm ${cellClass}">${count > 0 ? count.toLocaleString() : '-'}</td>`;
                });
                
                row += `<td class="px-6 py-4 text-center text-sm font-medium text-gray-900">${total.toLocaleString()}</td>`;
                row += `
                    <td class="px-6 py-4 text-center text-sm">
                        <button onclick="deleteDomain('${domain.domain}')" class="text-red-600 hover:text-red-800" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                row += '</tr>';
                return row;
            }).join('');
        }
        
        // 根据时间范围获取计数
        function getCountByTimeRange(stats) {
            // 根据当前选择的时间范围返回对应的数据
            switch(currentTimeRange) {
                case 'hour':
                    // 最近1小时的数据（可能需要后端提供）
                    return stats.hour || stats.today || 0;
                case 'day':
                    // 今日数据
                    return stats.today || 0;
                case 'week':
                    // 最近7天数据
                    return stats.week || stats.five_days || 0;
                case '15days':
                    // 最近15天数据（使用five_days作为近似值）
                    return stats.fifteen_days || stats.five_days || 0;
                case 'month':
                    // 最近30天数据
                    return stats.month || stats.five_days || 0;
                default:
                    return stats.today || 0;
            }
        }
        
        // 渲染分页
        function renderPagination(pagination) {
            if (!pagination) return;
            
            const totalDomainsEl = document.getElementById('total-domains');
            if (totalDomainsEl) {
                totalDomainsEl.textContent = pagination.total;
            }
            
            const container = document.getElementById('pagination');
            if (!container) {
                console.warn('pagination 元素不存在');
                return;
            }
            container.innerHTML = '';
            
            if (pagination.total_page <= 1) return;
            
            // 上一页
            if (currentPage > 1) {
                const prevBtn = document.createElement('button');
                prevBtn.className = 'px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50';
                prevBtn.textContent = '上一页';
                prevBtn.onclick = () => loadSpiderStats(currentPage - 1);
                container.appendChild(prevBtn);
            }
            
            // 页码
            for (let i = 1; i <= pagination.total_page; i++) {
                if (
                    i === 1 || 
                    i === pagination.total_page || 
                    (i >= currentPage - 2 && i <= currentPage + 2)
                ) {
                    const pageBtn = document.createElement('button');
                    if (i === currentPage) {
                        pageBtn.className = 'px-3 py-1 text-sm bg-blue-500 text-white rounded';
                    } else {
                        pageBtn.className = 'px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50';
                        pageBtn.onclick = () => loadSpiderStats(i);
                    }
                    pageBtn.textContent = i;
                    container.appendChild(pageBtn);
                } else if (
                    i === currentPage - 3 || 
                    i === currentPage + 3
                ) {
                    const dots = document.createElement('span');
                    dots.className = 'px-2 text-gray-500';
                    dots.textContent = '...';
                    container.appendChild(dots);
                }
            }
            
            // 下一页
            if (currentPage < pagination.total_page) {
                const nextBtn = document.createElement('button');
                nextBtn.className = 'px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50';
                nextBtn.textContent = '下一页';
                nextBtn.onclick = () => loadSpiderStats(currentPage + 1);
                container.appendChild(nextBtn);
            }
        }
        
        // 显示爬虫配置模态框
        async function showSpiderConfigModal() {
            document.getElementById('spider-config-modal').classList.remove('hidden');
            await loadSpiderConfigs();
        }
        
        // 关闭爬虫配置模态框
        function closeSpiderConfigModal() {
            document.getElementById('spider-config-modal').classList.add('hidden');
        }
        
        // 加载爬虫配置
        async function loadSpiderConfigs() {
            try {
                const res = await fetch('/api/v1/spider-stats/configs');
                const data = await res.json();
                
                if (data.success) {
                    currentConfigs = data.data || [];
                    renderSpiderConfigs(currentConfigs);
                }
            } catch (error) {
                showError('加载配置失败: ' + error.message);
            }
        }
        
        // 渲染爬虫配置
        function renderSpiderConfigs(configs) {
            const tbody = document.getElementById('spider-configs-tbody');
            if (!tbody) {
                console.warn('spider-configs-tbody 元素不存在');
                return;
            }
            
            if (!configs || configs.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">暂无配置</td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = configs.map(config => `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900">${config.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-gray-700">${config.display_name}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <code class="bg-gray-100 px-3 py-1.5 rounded text-sm text-gray-700 font-mono">${config.user_agent || '-'}</code>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded border border-gray-300 shadow-sm" style="background-color: ${config.color}"></div>
                            <span class="ml-3 text-gray-600 font-mono text-sm">${config.color}</span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-gray-700 text-center font-medium">${config.priority}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-center">
                        ${config.enabled 
                            ? '<span class="px-3 py-1.5 text-sm font-medium bg-green-100 text-green-800 rounded-full">启用</span>' 
                            : '<span class="px-3 py-1.5 text-sm font-medium bg-gray-100 text-gray-800 rounded-full">禁用</span>'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-center">
                        <button onclick="editSpiderConfig(${JSON.stringify(config).replace(/"/g, '&quot;')})" class="text-blue-600 hover:text-blue-800 mr-3" title="编辑">
                            <i class="fas fa-edit text-lg"></i>
                        </button>
                        <button onclick="toggleSpiderConfig(${config.id})" class="text-${config.enabled ? 'yellow' : 'green'}-600 hover:text-${config.enabled ? 'yellow' : 'green'}-800 mr-3" title="${config.enabled ? '禁用' : '启用'}">
                            <i class="fas fa-${config.enabled ? 'pause' : 'play'} text-lg"></i>
                        </button>
                        <button onclick="deleteSpiderConfig(${config.id})" class="text-red-600 hover:text-red-800" title="删除">
                            <i class="fas fa-trash text-lg"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }
        
        // 显示添加配置模态框
        function showAddConfigModal() {
            document.getElementById('config-modal-title').textContent = '添加爬虫配置';
            document.getElementById('config-form').reset();
            document.getElementById('config-id').value = '';
            document.getElementById('add-config-modal').classList.remove('hidden');
        }
        
        // 关闭添加配置模态框
        function closeAddConfigModal() {
            document.getElementById('add-config-modal').classList.add('hidden');
        }
        
        // 编辑爬虫配置
        function editSpiderConfig(config) {
            document.getElementById('config-modal-title').textContent = '编辑爬虫配置';
            document.getElementById('config-id').value = config.id;
            document.getElementById('config-name').value = config.name;
            document.getElementById('config-display-name').value = config.display_name;
            document.getElementById('config-user-agent').value = config.user_agent || '';
            document.getElementById('config-color').value = config.color;
            document.getElementById('config-priority').value = config.priority;
            document.getElementById('config-enabled').checked = config.enabled;
            document.getElementById('add-config-modal').classList.remove('hidden');
        }
        
        // 保存爬虫配置
        async function saveSpiderConfig() {
            const id = document.getElementById('config-id').value;
            const isEdit = !!id;
            
            const configData = {
                name: document.getElementById('config-name').value.trim(),
                display_name: document.getElementById('config-display-name').value.trim(),
                user_agent: document.getElementById('config-user-agent').value.trim(),
                color: document.getElementById('config-color').value,
                priority: parseInt(document.getElementById('config-priority').value) || 0,
                enabled: document.getElementById('config-enabled').checked
            };
            
            // 基本验证
            if (!configData.name) {
                showError('请输入爬虫名称');
                return;
            }
            
            if (!configData.display_name) {
                showError('请输入显示名称');
                return;
            }
            
            if (!configData.user_agent) {
                showError('请输入User-Agent特征');
                return;
            }
            
            // 前端验证：检查UA是否重复
            if (currentConfigs && currentConfigs.length > 0) {
                const existingConfig = currentConfigs.find(config => {
                    // 编辑时排除自身
                    if (isEdit && config.id == id) return false;
                    // 检查user_agent是否重复（不区分大小写）
                    return config.user_agent.toLowerCase() === configData.user_agent.toLowerCase();
                });
                
                if (existingConfig) {
                    showError(`User-Agent特征已存在：${existingConfig.display_name} (${existingConfig.user_agent})`);
                    return;
                }
            }
            
            try {
                const url = isEdit ? `/api/v1/spider-stats/configs/${id}` : '/api/v1/spider-stats/configs';
                const method = isEdit ? 'PUT' : 'POST';
                
                const res = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(configData)
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(isEdit ? '更新成功' : '添加成功');
                    closeAddConfigModal();
                    await loadSpiderConfigs();
                    loadSpiderStats(); // 刷新统计数据
                } else {
                    showError(data.error || '保存失败');
                }
            } catch (error) {
                showError('保存失败: ' + error.message);
            }
        }
        
        // 切换爬虫配置状态
        async function toggleSpiderConfig(id) {
            try {
                const res = await fetch(`/api/v1/spider-stats/configs/${id}/toggle`, {
                    method: 'POST'
                });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('状态切换成功');
                    await loadSpiderConfigs();
                    loadSpiderStats(); // 刷新统计数据
                } else {
                    showError(data.error || '操作失败');
                }
            } catch (error) {
                showError('操作失败: ' + error.message);
            }
        }
        
        // 删除爬虫配置
        async function deleteSpiderConfig(id) {
            if (!confirm('确定要删除这个爬虫配置吗？')) return;
            
            try {
                const res = await fetch(`/api/v1/spider-stats/configs/${id}`, {
                    method: 'DELETE'
                });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('删除成功');
                    await loadSpiderConfigs();
                    loadSpiderStats(); // 刷新统计数据
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 清空统计数据
        async function clearStats() {
            if (!confirm('确定要清空所有爬虫统计数据吗？此操作不可恢复！')) return;
            
            try {
                const res = await fetch('/api/v1/spider-stats/clear', {
                    method: 'DELETE'
                });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('统计数据已清空');
                    // 重新加载统计数据，这会自动更新图表
                    loadSpiderStats();
                } else {
                    showError(data.error || '清空失败');
                }
            } catch (error) {
                showError('清空失败: ' + error.message);
            }
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all-domains');
            const checkboxes = document.querySelectorAll('.domain-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateSelectedCount();
        }
        
        // 更新选中计数
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.domain-checkbox:checked');
            const count = checkboxes.length;
            const selectedCountEl = document.getElementById('selected-count');
            const batchActionsEl = document.getElementById('batch-actions');
            
            if (selectedCountEl) {
                selectedCountEl.textContent = count;
            }
            
            // 显示/隐藏批量操作栏
            if (batchActionsEl) {
                batchActionsEl.style.display = count > 0 ? 'block' : 'none';
            }
            
            // 更新全选框状态
            const selectAll = document.getElementById('select-all-domains');
            const allCheckboxes = document.querySelectorAll('.domain-checkbox');
            if (selectAll && allCheckboxes.length > 0) {
                selectAll.checked = count === allCheckboxes.length;
                selectAll.indeterminate = count > 0 && count < allCheckboxes.length;
            }
        }
        
        // 清除选择
        function clearSelection() {
            const selectAll = document.getElementById('select-all-domains');
            const checkboxes = document.querySelectorAll('.domain-checkbox');
            
            if (selectAll) selectAll.checked = false;
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            
            updateSelectedCount();
        }
        
        // 删除单个域名
        async function deleteDomain(domain) {
            if (!confirm(`确定要删除域名 ${domain} 的所有爬虫统计数据吗？`)) return;
            
            try {
                const res = await fetch(`/api/v1/spider-stats/domains/${encodeURIComponent(domain)}`, {
                    method: 'DELETE'
                });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(`域名 ${domain} 的统计数据已删除`);
                    loadSpiderStats(); // 重新加载数据
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 批量删除选中的域名
        async function deleteSelectedDomains() {
            const checkboxes = document.querySelectorAll('.domain-checkbox:checked');
            const domains = Array.from(checkboxes).map(cb => cb.value);
            
            if (domains.length === 0) {
                showError('请先选择要删除的域名');
                return;
            }
            
            if (!confirm(`确定要删除选中的 ${domains.length} 个域名的所有爬虫统计数据吗？\n域名：${domains.join(', ')}`)) return;
            
            try {
                const res = await fetch('/api/v1/spider-stats/domains/batch', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ domains })
                });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(`成功删除 ${domains.length} 个域名的统计数据`);
                    clearSelection(); // 清除选择
                    loadSpiderStats(); // 重新加载数据
                } else {
                    showError(data.error || '批量删除失败');
                }
            } catch (error) {
                showError('批量删除失败: ' + error.message);
            }
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.id === 'spider-config-modal' || event.target.id === 'add-config-modal') {
                event.target.classList.add('hidden');
            }
        }
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
