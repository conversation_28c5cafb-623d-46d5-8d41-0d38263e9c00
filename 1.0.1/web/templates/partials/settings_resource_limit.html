<!-- 资源限流配置面板 -->
<div class="settings-panel p-6" id="resource-limit-panel">
    <h3 class="text-lg font-semibold text-gray-800 mb-6">资源限流与超时配置</h3>
    
    <!-- 一键优化按钮 -->
    <div class="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
            <div>
                <h4 class="font-medium text-blue-800 mb-1">
                    <i class="fas fa-magic mr-2"></i>智能优化
                </h4>
                <p class="text-sm text-blue-600">根据当前服务器配置自动优化参数</p>
            </div>
            <button onclick="autoOptimizeSettings()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-bolt mr-2"></i>一键优化
            </button>
        </div>
    </div>

    <div class="space-y-6">
        <!-- 并发限制配置 -->
        <div class="border border-gray-200 rounded-lg p-4">
            <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                <i class="fas fa-users mr-2 text-green-600"></i>
                并发限制配置
            </h4>
            <p class="text-sm text-gray-600 mb-4">控制不同类型操作的最大并发数，防止资源耗尽</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        数据库并发连接数
                        <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×10)</span>
                    </label>
                    <input type="number" id="max-database-conn" min="10" max="500" step="10"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="80">
                    <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-database-conn">80</span></p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Redis并发连接数
                        <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×20)</span>
                    </label>
                    <input type="number" id="max-redis-conn" min="20" max="1000" step="20"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="160">
                    <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-redis-conn">160</span></p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        HTTP并发请求数
                        <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×5)</span>
                    </label>
                    <input type="number" id="max-http-requests" min="5" max="200" step="5"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="40">
                    <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-http-requests">40</span></p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        文件并发操作数
                        <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×3)</span>
                    </label>
                    <input type="number" id="max-file-ops" min="3" max="100" step="3"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="24">
                    <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-file-ops">24</span></p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        爬虫并发任务数
                        <span class="text-xs text-gray-500 ml-1">(推荐: CPU核心数×2)</span>
                    </label>
                    <input type="number" id="max-crawler-tasks" min="2" max="100" step="2"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="16">
                    <p class="mt-1 text-xs text-gray-600">当前值: <span id="current-crawler-tasks">16</span></p>
                </div>
            </div>
        </div>

        <!-- 统一超时配置 -->
        <div class="border border-gray-200 rounded-lg p-4">
            <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                <i class="fas fa-clock mr-2 text-orange-600"></i>
                统一超时配置
            </h4>
            <p class="text-sm text-gray-600 mb-4">设置各类操作的超时时间，避免请求堆积（单位：毫秒）</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        路由处理总超时
                        <span class="text-xs text-gray-500 ml-1">(推荐: 800-1500ms)</span>
                    </label>
                    <input type="number" id="route-timeout" min="500" max="5000" step="100"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="1200">
                    <p class="mt-1 text-xs text-gray-600">整个请求的最大处理时间</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        数据库查询超时
                        <span class="text-xs text-gray-500 ml-1">(推荐: 500-1000ms)</span>
                    </label>
                    <input type="number" id="database-query-timeout" min="200" max="3000" step="100"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="800">
                    <p class="mt-1 text-xs text-gray-600">单个数据库查询超时</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Redis操作超时
                        <span class="text-xs text-gray-500 ml-1">(推荐: 100-300ms)</span>
                    </label>
                    <input type="number" id="redis-op-timeout" min="50" max="1000" step="50"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="200">
                    <p class="mt-1 text-xs text-gray-600">Redis读写操作超时</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        HTTP请求超时
                        <span class="text-xs text-gray-500 ml-1">(推荐: 300-800ms)</span>
                    </label>
                    <input type="number" id="http-request-timeout" min="200" max="3000" step="100"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="500">
                    <p class="mt-1 text-xs text-gray-600">出站HTTP请求超时</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        文件操作超时
                        <span class="text-xs text-gray-500 ml-1">(推荐: 500-2000ms)</span>
                    </label>
                    <input type="number" id="file-op-timeout" min="200" max="5000" step="100"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="1000">
                    <p class="mt-1 text-xs text-gray-600">文件读写操作超时</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        爬虫任务超时
                        <span class="text-xs text-gray-500 ml-1">(推荐: 5000-15000ms)</span>
                    </label>
                    <input type="number" id="crawler-task-timeout" min="3000" max="30000" step="1000"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="10000">
                    <p class="mt-1 text-xs text-gray-600">爬虫抓取页面超时</p>
                </div>
            </div>
        </div>

        <!-- 实时监控 -->
        <div class="border border-gray-200 rounded-lg p-4">
            <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                <i class="fas fa-chart-line mr-2 text-purple-600"></i>
                资源使用监控
            </h4>
            
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="text-xs text-gray-600 mb-1">数据库</div>
                    <div class="text-lg font-semibold">
                        <span id="db-used">0</span>/<span id="db-max">80</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div id="db-usage-bar" class="bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="text-xs text-gray-600 mb-1">Redis</div>
                    <div class="text-lg font-semibold">
                        <span id="redis-used">0</span>/<span id="redis-max">160</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div id="redis-usage-bar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="text-xs text-gray-600 mb-1">HTTP</div>
                    <div class="text-lg font-semibold">
                        <span id="http-used">0</span>/<span id="http-max">40</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div id="http-usage-bar" class="bg-yellow-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="text-xs text-gray-600 mb-1">文件</div>
                    <div class="text-lg font-semibold">
                        <span id="file-used">0</span>/<span id="file-max">24</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div id="file-usage-bar" class="bg-purple-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="text-xs text-gray-600 mb-1">爬虫</div>
                    <div class="text-lg font-semibold">
                        <span id="crawler-used">0</span>/<span id="crawler-max">16</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div id="crawler-usage-bar" class="bg-red-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div class="bg-red-50 p-2 rounded">
                    <span class="text-gray-600">请求拒绝总数:</span>
                    <span class="font-semibold text-red-600 ml-1" id="total-rejected">0</span>
                </div>
                <div class="bg-orange-50 p-2 rounded">
                    <span class="text-gray-600">超时总数:</span>
                    <span class="font-semibold text-orange-600 ml-1" id="total-timeouts">0</span>
                </div>
                <div class="bg-green-50 p-2 rounded">
                    <span class="text-gray-600">成功率:</span>
                    <span class="font-semibold text-green-600 ml-1" id="success-rate">100%</span>
                </div>
                <div class="bg-blue-50 p-2 rounded">
                    <span class="text-gray-600">平均响应时间:</span>
                    <span class="font-semibold text-blue-600 ml-1" id="avg-response">0ms</span>
                </div>
            </div>
        </div>

        <!-- 提示信息 -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="font-medium text-yellow-800 mb-2">
                <i class="fas fa-info-circle mr-2"></i>配置建议
            </h4>
            <ul class="space-y-1 text-sm text-yellow-700">
                <li>• <strong>高配服务器（16GB+内存）：</strong>可适当增加并发数，缩短超时时间</li>
                <li>• <strong>中配服务器（8-16GB内存）：</strong>使用默认配置即可</li>
                <li>• <strong>低配服务器（8GB以下）：</strong>降低并发数，适当延长超时时间</li>
                <li>• <strong>SSD存储：</strong>文件操作超时可设置较短（500-1000ms）</li>
                <li>• <strong>HDD存储：</strong>文件操作超时建议设置较长（1000-3000ms）</li>
                <li>• 修改配置后需要重启服务才能生效</li>
            </ul>
        </div>
    </div>
</div>

<script>
// 一键优化功能
async function autoOptimizeSettings() {
    try {
        // 获取系统信息
        const response = await fetch('/api/v1/system/info', {
            headers: {
                'X-CSRF-Token': window.csrfToken
            }
        });
        
        if (!response.ok) throw new Error('获取系统信息失败');
        
        const data = await response.json();
        const systemInfo = data.data;
        
        // 根据系统信息计算优化值
        const cpuCores = systemInfo.cpu_cores || 8;
        const memoryGB = Math.floor((systemInfo.total_memory || 8589934592) / 1024 / 1024 / 1024);
        const isSSD = systemInfo.storage_type === 'SSD' || true; // 默认假设SSD
        
        // 计算并发限制
        let multiplier = 1;
        if (memoryGB >= 16) {
            multiplier = 1.5; // 高配
        } else if (memoryGB >= 8) {
            multiplier = 1.0; // 中配
        } else {
            multiplier = 0.5; // 低配
        }
        
        // 设置并发数
        document.getElementById('max-database-conn').value = Math.floor(cpuCores * 10 * multiplier);
        document.getElementById('max-redis-conn').value = Math.floor(cpuCores * 20 * multiplier);
        document.getElementById('max-http-requests').value = Math.floor(cpuCores * 5 * multiplier);
        document.getElementById('max-file-ops').value = Math.floor(cpuCores * 3 * multiplier);
        document.getElementById('max-crawler-tasks').value = Math.floor(cpuCores * 2 * multiplier);
        
        // 设置超时时间
        if (isSSD) {
            // SSD优化配置
            document.getElementById('route-timeout').value = 1000;
            document.getElementById('database-query-timeout').value = 600;
            document.getElementById('redis-op-timeout').value = 150;
            document.getElementById('http-request-timeout').value = 400;
            document.getElementById('file-op-timeout').value = 500;
            document.getElementById('crawler-task-timeout').value = 8000;
        } else {
            // HDD配置
            document.getElementById('route-timeout').value = 1500;
            document.getElementById('database-query-timeout').value = 1000;
            document.getElementById('redis-op-timeout').value = 300;
            document.getElementById('http-request-timeout').value = 800;
            document.getElementById('file-op-timeout').value = 2000;
            document.getElementById('crawler-task-timeout').value = 15000;
        }
        
        // 显示优化信息
        showNotification(`已根据系统配置自动优化参数：${cpuCores}核CPU, ${memoryGB}GB内存, ${isSSD ? 'SSD' : 'HDD'}存储`, 'success');
        
    } catch (error) {
        console.error('自动优化失败:', error);
        showNotification('自动优化失败：' + error.message, 'error');
    }
}

// 加载资源限流配置
function loadResourceLimitSettings(data) {
    if (data.data) {
        // 并发限制
        document.getElementById('max-database-conn').value = data.data.max_database_conn || 80;
        document.getElementById('max-redis-conn').value = data.data.max_redis_conn || 160;
        document.getElementById('max-http-requests').value = data.data.max_http_requests || 40;
        document.getElementById('max-file-ops').value = data.data.max_file_ops || 24;
        document.getElementById('max-crawler-tasks').value = data.data.max_crawler_tasks || 16;
        
        // 更新当前值显示
        document.getElementById('current-database-conn').textContent = data.data.max_database_conn || 80;
        document.getElementById('current-redis-conn').textContent = data.data.max_redis_conn || 160;
        document.getElementById('current-http-requests').textContent = data.data.max_http_requests || 40;
        document.getElementById('current-file-ops').textContent = data.data.max_file_ops || 24;
        document.getElementById('current-crawler-tasks').textContent = data.data.max_crawler_tasks || 16;
        
        // 超时配置
        document.getElementById('route-timeout').value = data.data.route_timeout || 1200;
        document.getElementById('database-query-timeout').value = data.data.database_query_timeout || 800;
        document.getElementById('redis-op-timeout').value = data.data.redis_op_timeout || 200;
        document.getElementById('http-request-timeout').value = data.data.http_request_timeout || 500;
        document.getElementById('file-op-timeout').value = data.data.file_op_timeout || 1000;
        document.getElementById('crawler-task-timeout').value = data.data.crawler_task_timeout || 10000;
    }
}

// 保存资源限流配置
function saveResourceLimitSettings() {
    return {
        max_database_conn: parseInt(document.getElementById('max-database-conn').value),
        max_redis_conn: parseInt(document.getElementById('max-redis-conn').value),
        max_http_requests: parseInt(document.getElementById('max-http-requests').value),
        max_file_ops: parseInt(document.getElementById('max-file-ops').value),
        max_crawler_tasks: parseInt(document.getElementById('max-crawler-tasks').value),
        route_timeout: parseInt(document.getElementById('route-timeout').value),
        database_query_timeout: parseInt(document.getElementById('database-query-timeout').value),
        redis_op_timeout: parseInt(document.getElementById('redis-op-timeout').value),
        http_request_timeout: parseInt(document.getElementById('http-request-timeout').value),
        file_op_timeout: parseInt(document.getElementById('file-op-timeout').value),
        crawler_task_timeout: parseInt(document.getElementById('crawler-task-timeout').value)
    };
}

// 更新资源使用监控（定期刷新）
async function updateResourceMonitor() {
    try {
        const response = await fetch('/api/v1/system/resource-stats', {
            headers: {
                'X-CSRF-Token': window.csrfToken
            }
        });
        
        if (!response.ok) return;
        
        const data = await response.json();
        const stats = data.data;
        
        // 更新使用情况
        updateResourceBar('db', stats.database_used, stats.max_database_conn);
        updateResourceBar('redis', stats.redis_used, stats.max_redis_conn);
        updateResourceBar('http', stats.http_used, stats.max_http_requests);
        updateResourceBar('file', stats.file_used, stats.max_file_ops);
        updateResourceBar('crawler', stats.crawler_used, stats.max_crawler_tasks);
        
        // 更新统计
        document.getElementById('total-rejected').textContent = 
            (stats.database_rejected + stats.redis_rejected + stats.http_rejected + 
             stats.file_rejected + stats.crawler_rejected) || 0;
        document.getElementById('total-timeouts').textContent = 
            (stats.route_timeouts + stats.database_timeouts + stats.redis_timeouts + 
             stats.http_timeouts + stats.file_timeouts) || 0;
        
    } catch (error) {
        console.error('更新资源监控失败:', error);
    }
}

function updateResourceBar(type, used, max) {
    document.getElementById(`${type}-used`).textContent = used || 0;
    document.getElementById(`${type}-max`).textContent = max || 0;
    
    const percentage = max > 0 ? (used / max * 100) : 0;
    const bar = document.getElementById(`${type}-usage-bar`);
    bar.style.width = percentage + '%';
    
    // 根据使用率改变颜色
    if (percentage > 80) {
        bar.className = bar.className.replace(/bg-\w+-600/, 'bg-red-600');
    } else if (percentage > 60) {
        bar.className = bar.className.replace(/bg-\w+-600/, 'bg-yellow-600');
    }
}

// 定期更新监控数据
setInterval(updateResourceMonitor, 5000);

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    updateResourceMonitor();
});
</script>