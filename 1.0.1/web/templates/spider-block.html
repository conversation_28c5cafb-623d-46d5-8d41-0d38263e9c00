<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蜘蛛屏蔽管理 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">蜘蛛屏蔽管理</h1>
                    <div class="flex items-center space-x-3">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="showAddRuleModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i>
                            添加规则
                        </button>
                        <button onclick="showBatchAddModal()" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-list mr-2"></i>
                            批量添加
                        </button>
                        <button onclick="resetAllHitCounts()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-redo mr-2"></i>
                            重置所有数据
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 全局设置和提示信息 - 使用紧凑布局 -->
                <div class="bg-white rounded-lg shadow mb-6 p-4">
                    <div class="flex items-start justify-between">
                        <!-- 左侧：全局设置 -->
                        <div class="flex-1">
                            <div class="flex items-center space-x-6">
                                <!-- 全局开关 -->
                                <div class="flex items-center">
                                    <label class="flex items-center cursor-pointer">
                                        <input type="checkbox" id="globalSpiderBlockSwitch" 
                                               class="sr-only peer" onchange="toggleGlobalSpiderBlock()">
                                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        <span class="ml-3 text-sm font-medium text-gray-900">启用全局蜘蛛屏蔽</span>
                                    </label>
                                    <span id="globalStatus" class="ml-2 text-xs text-gray-500">(已启用)</span>
                                </div>
                                
                                <!-- 屏蔽模板按钮 -->
                                <button onclick="show403TemplateModal()" 
                                        class="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1.5 rounded text-sm flex items-center">
                                    <i class="fas fa-file-code mr-1.5 text-xs"></i>
                                    编辑响应模板
                                </button>
                            </div>
                        </div>
                        
                        <!-- 右侧：提示信息 -->
                        <div class="flex items-center space-x-2 text-xs text-blue-700 bg-blue-50 px-3 py-2 rounded">
                            <i class="fas fa-info-circle"></i>
                            <span>系统会自动屏蔽匹配到的垃圾蜘蛛，返回相应的HTTP状态码。</span>
                        </div>
                    </div>
                </div>
                
               
                
                <!-- 规则管理标签页内容 -->
                <div id="content-rules">
                <!-- 统计图表 -->
                <div class="bg-white rounded-lg shadow mb-6 p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">屏蔽统计趋势</h3>
                        <div class="flex space-x-2">
                            <select id="periodSelect" onchange="loadStats()" 
                                    class="border border-gray-300 rounded px-3 py-1 text-sm">
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="7days" selected>最近7天</option>
                                <option value="15days">最近15天</option>
                            </select>
                            <button onclick="loadStats()" class="bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded text-sm">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                    
                    <!-- 统计摘要 -->
                    <div class="grid grid-cols-4 gap-4 mb-6">
                        <div class="bg-gray-50 rounded p-3">
                            <div class="text-2xl font-bold text-blue-600" id="totalBlocks">0</div>
                            <div class="text-sm text-gray-600">总屏蔽次数</div>
                        </div>
                        <div class="bg-gray-50 rounded p-3">
                            <div class="text-2xl font-bold text-green-600" id="spiderCount">0</div>
                            <div class="text-sm text-gray-600">独立蜘蛛数</div>
                        </div>
                        <div class="bg-gray-50 rounded p-3">
                            <div class="text-2xl font-bold text-purple-600" id="avgPerHour">0</div>
                            <div class="text-sm text-gray-600">平均每小时</div>
                        </div>
                        <div class="bg-gray-50 rounded p-3">
                            <div class="text-sm font-semibold text-gray-700" id="topSpider">-</div>
                            <div class="text-sm text-gray-600">最活跃蜘蛛</div>
                        </div>
                    </div>
                    
                    <!-- 图表容器 -->
                    <div id="statsChart" style="height: 350px;"></div>
                </div>
                
                <!-- 规则列表 -->
                <div class="bg-white rounded-lg shadow">
                    <!-- 搜索栏 -->
                    <div class="px-6 py-4 border-b">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="relative">
                                    <input type="text" id="searchInput" placeholder="搜索用户代理、描述..." 
                                           class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 w-80"
                                           onkeyup="handleSearch()">
                                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                                </div>
                                <button onclick="clearSearch()" class="text-gray-500 hover:text-gray-700">
                                    <i class="fas fa-times-circle"></i> 清除
                                </button>
                            </div>
                            <div class="text-sm text-gray-600">
                                共 <span id="totalCount" class="font-semibold">0</span> 条规则，
                                显示 <span id="visibleCount" class="font-semibold">0</span> 条
                            </div>
                        </div>
                        <!-- 批量操作栏 -->
                        <div id="bulk-actions" class="px-6 py-3 bg-gray-50 border-t hidden">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <span class="text-sm text-gray-700">
                                        已选择 <span id="selected-count" class="font-semibold text-blue-600">0</span> 项
                                    </span>
                                    <button onclick="clearSelection()" class="text-sm text-gray-500 hover:text-gray-700">
                                        取消选择
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <button onclick="batchDelete()" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1.5 rounded text-sm">
                                        <i class="fas fa-trash-alt mr-1"></i>批量删除
                                    </button>
                                    <button onclick="batchToggle(true)" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded text-sm">
                                        <i class="fas fa-check mr-1"></i>批量启用
                                    </button>
                                    <button onclick="batchToggle(false)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1.5 rounded text-sm">
                                        <i class="fas fa-ban mr-1"></i>批量禁用
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50 border-b">
                                <tr>
                                    <th class="px-6 py-3 text-center" style="width: 40px;">
                                        <input type="checkbox" id="select-all" onclick="toggleSelectAll()" 
                                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortBy('id')">
                                        ID <i class="fas fa-sort ml-1 text-gray-400" id="sort-icon-id"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户代理特征</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortBy('return_code')">
                                        返回状态码 <i class="fas fa-sort ml-1 text-gray-400" id="sort-icon-return_code"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortBy('hit_count')">
                                        命中次数 <i class="fas fa-sort ml-1 text-gray-400" id="sort-icon-hit_count"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="sortBy('enabled')">
                                        状态 <i class="fas fa-sort ml-1 text-gray-400" id="sort-icon-enabled"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="rules-list" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                                        <div class="flex justify-center">
                                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                                        </div>
                                        <p class="mt-4">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="text-sm text-gray-700">
                                共 <span id="total-rules">0</span> 条规则
                            </div>
                            <div class="flex items-center space-x-2">
                                <label class="text-sm text-gray-700">每页显示</label>
                                <select id="page-size-select" onchange="changePageSize()" class="px-2 py-1 border rounded text-sm">
                                    <option value="10">10条</option>
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button id="prev-page" onclick="changePage('prev')" 
                                    class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled>
                                <i class="fas fa-chevron-left"></i> 上一页
                            </button>
                            <span class="text-sm text-gray-600">
                                第 <span id="current-page">1</span> / <span id="total-pages">1</span> 页
                            </span>
                            <button id="next-page" onclick="changePage('next')" 
                                    class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled>
                                下一页 <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                </div> <!-- 结束规则管理标签页 -->
                
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <!-- 添加/编辑规则模态框 -->
    <div id="ruleModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold" id="ruleModalTitle">添加规则</h3>
                    <button onclick="closeRuleModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="ruleForm" class="p-6">
                    <input type="hidden" id="ruleId">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">用户代理特征 <span class="text-red-500">*</span></label>
                        <input type="text" id="userAgent" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：AhrefsBot、python-requests">
                        <p class="mt-1 text-xs text-gray-600">输入要屏蔽的User-Agent中的特征字符串（不区分大小写）</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                        <input type="text" id="description"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="例如：Ahrefs SEO爬虫">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">返回状态码</label>
                        <select id="returnCode" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="403">403 Forbidden（禁止访问）</option>
                            <option value="404">404 Not Found（找不到）</option>
                            <option value="410">410 Gone（已删除）</option>
                            <option value="451">451 Unavailable（法律原因不可用）</option>
                            <option value="503">503 Service Unavailable（服务不可用）</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="enabled" checked class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <span class="text-sm font-medium text-gray-700">启用规则</span>
                        </label>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeRuleModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveRule()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 批量添加模态框 -->
    <div id="batchAddModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">批量添加规则</h3>
                    <button onclick="closeBatchAddModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="batchAddForm" class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">批量规则列表</label>
                        <textarea id="batchRules" rows="10"
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="每行一个规则，格式：用户代理特征|描述|返回码&#10;例如：&#10;AhrefsBot|Ahrefs SEO爬虫|403&#10;python-requests|Python请求库|403&#10;curl|curl命令行工具|403"></textarea>
                        <div class="mt-2 text-xs text-gray-600">
                            <p class="font-medium">格式说明：</p>
                            <p>1. 每行一个规则</p>
                            <p>2. 用竖线(|)分隔：用户代理特征|描述|返回码</p>
                            <p>3. 返回码可选，默认为403</p>
                            <p>4. 描述可选</p>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="batchEnabled" checked class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <span class="text-sm font-medium text-gray-700">批量启用规则</span>
                        </label>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeBatchAddModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="batchAddRules()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        批量添加
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 403模板编辑模态框 -->
    <div id="template403Modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
                <div class="px-6 py-4 border-b flex justify-between items-center flex-shrink-0">
                    <h3 class="text-lg font-semibold">编辑屏蔽响应模板</h3>
                    <button onclick="close403TemplateModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="template403Form" class="p-6 overflow-y-auto flex-grow">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">HTML模板内容</label>
                        <textarea id="template403Content" rows="10"
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="&lt;!DOCTYPE html&gt;&#10;&lt;html&gt;&#10;&lt;head&gt;&#10;    &lt;title&gt;{{.Status}}&lt;/title&gt;&#10;&lt;/head&gt;&#10;&lt;body&gt;&#10;    &lt;h1&gt;{{.Status}}&lt;/h1&gt;&#10;    &lt;p&gt;Access to this resource is forbidden.&lt;/p&gt;&#10;&lt;/body&gt;&#10;&lt;/html&gt;"></textarea>
                        <div class="mt-2">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <p class="font-semibold text-sm text-blue-800 mb-2">
                                    <i class="fas fa-info-circle mr-1"></i>支持的模板变量：
                                </p>
                                <div class="space-y-1 text-xs text-gray-700">
                                    <div class="flex items-center">
                                        <code class="bg-white px-2 py-0.5 rounded text-blue-600 font-mono mr-2">{{`{{.StatusCode}}`}}</code>
                                        <span>- 状态码数字 (如: 404)</span>
                                    </div>
                                    <div class="flex items-center">
                                        <code class="bg-white px-2 py-0.5 rounded text-blue-600 font-mono mr-2">{{`{{.StatusText}}`}}</code>
                                        <span>- 状态文本 (如: Not Found)</span>
                                    </div>
                                    <div class="flex items-center">
                                        <code class="bg-white px-2 py-0.5 rounded text-blue-600 font-mono mr-2">{{`{{.Status}}`}}</code>
                                        <span>- 完整状态 (如: 404 Not Found)</span>
                                    </div>
                                    <div class="flex items-center">
                                        <code class="bg-white px-2 py-0.5 rounded text-blue-600 font-mono mr-2">{{`{{.UserAgent}}`}}</code>
                                        <span>- 访问者的User-Agent</span>
                                    </div>
                                    <div class="flex items-center">
                                        <code class="bg-white px-2 py-0.5 rounded text-blue-600 font-mono mr-2">{{`{{.Time}}`}}</code>
                                        <span>- 当前时间</span>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mt-2 text-xs">
                                <p class="flex items-center text-yellow-800">
                                    <i class="fas fa-lightbulb text-yellow-500 mr-1"></i>
                                    <strong>提示：</strong>
                                </p>
                                <ul class="ml-5 mt-1 space-y-0.5 list-disc text-gray-600">
                                    <li>使用状态码变量可以让模板自动适应不同的HTTP状态码</li>
                                    <li>留空将使用系统根据状态码自动生成的默认页面</li>
                                    <li>模板将根据规则中设置的返回码动态显示（403/404/500等）</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <button type="button" onclick="preview403Template()" 
                                class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-eye mr-2"></i>
                            预览模板
                        </button>
                    </div>
                    
                    <!-- 预览区域 -->
                    <div id="template403Preview" class="mb-4 hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">预览效果</label>
                        <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
                            <iframe id="previewFrame" class="w-full h-64 bg-white rounded"></iframe>
                        </div>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3 flex-shrink-0">
                    <button onclick="reset403Template()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        重置为默认
                    </button>
                    <button onclick="close403TemplateModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="save403Template()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存模板
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        let currentRules = [];
        let currentPage = 1;
        let pageSize = 10; // 默认10条
        let totalRules = 0;
        let totalPages = 1;
        let sortField = 'id';
        let sortOrder = 'desc'; // 默认降序，从大到小
        let searchTerm = ''; // 搜索关键词
        
        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 兼容旧的通知函数
        function showSuccess(message) { showToast(message, 'success'); }
        function showError(message) { showToast(message, 'error'); }
        function showWarning(message) { showToast(message, 'warning'); }
        function showInfo(message) { showToast(message, 'info'); }

        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }
        
        // 页面加载时获取规则列表
        document.addEventListener('DOMContentLoaded', function() {
            // 设置分页大小选择器的默认值
            const pageSizeSelect = document.getElementById('page-size-select');
            if (pageSizeSelect) {
                pageSizeSelect.value = pageSize.toString();
            }
            loadRules();
            loadGlobalSettings();
        });
        
        // 加载规则列表（支持分页和排序）
        async function loadRules(page = 1) {
            try {
                currentPage = page;
                
                // 构建查询URL
                let url = `/api/v1/spider-block?page=${currentPage}&page_size=${pageSize}&sort_by=${sortField}&sort_order=${sortOrder}`;
                if (searchTerm) {
                    url += `&search=${encodeURIComponent(searchTerm)}`;
                }
                
                const response = await get(url);
                
                if (response.success && response.data) {
                    currentRules = response.data.rules || [];
                    totalRules = response.data.total || 0;
                    totalPages = Math.ceil(totalRules / pageSize);
                    
                    // 规则加载完成
                    
                    // 更新统计
                    document.getElementById('totalCount').textContent = totalRules;
                    document.getElementById('visibleCount').textContent = totalRules;
                    
                    renderRules();
                    updatePagination();
                    updateSortIcons();
                }
            } catch (error) {
                showError('加载规则失败');
            }
        }
        
        // 搜索处理函数
        function handleSearch() {
            searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
            currentPage = 1; // 搜索时重置到第一页
            loadRules(1);
        }
        
        // 清除搜索
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            searchTerm = '';
            currentPage = 1;
            loadRules(1);
        }
        
        // 排序函数
        function sortBy(field) {
            if (sortField === field) {
                // 如果点击同一列，切换排序方向
                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                // 如果点击不同列，使用默认升序
                sortField = field;
                sortOrder = 'asc';
            }
            loadRules(currentPage);
        }
        
        // 更新排序图标
        function updateSortIcons() {
            // 重置所有图标
            const icons = ['id', 'return_code', 'hit_count', 'enabled'];
            icons.forEach(field => {
                const icon = document.getElementById(`sort-icon-${field}`);
                if (icon) {
                    icon.className = 'fas fa-sort ml-1 text-gray-400';
                }
            });
            
            // 更新当前排序列的图标
            const currentIcon = document.getElementById(`sort-icon-${sortField}`);
            if (currentIcon) {
                if (sortOrder === 'asc') {
                    currentIcon.className = 'fas fa-sort-up ml-1 text-blue-500';
                } else {
                    currentIcon.className = 'fas fa-sort-down ml-1 text-blue-500';
                }
            }
        }
        
        // 渲染规则列表
        function renderRules() {
            const tbody = document.getElementById('rules-list');
            tbody.innerHTML = '';
            
            if (currentRules.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="px-6 py-12 text-center text-gray-500">暂无规则</td>
                    </tr>
                `;
                return;
            }
            
            currentRules.forEach(rule => {
                const tr = document.createElement('tr');
                tr.className = 'hover:bg-gray-50';
                tr.innerHTML = `
                    <td class="px-6 py-4 text-center">
                        <input type="checkbox" class="rule-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                               value="${rule.id}" onchange="updateSelection()">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${rule.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <code class="text-sm bg-gray-100 px-2 py-1 rounded">${escapeHtml(rule.user_agent)}</code>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">${escapeHtml(rule.description || '-')}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded">${rule.return_code}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">${rule.hit_count || 0}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${rule.enabled 
                            ? '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">启用</span>' 
                            : '<span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">禁用</span>'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <button class="text-blue-600 hover:text-blue-800 mr-2" onclick="editRule(${rule.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="text-${rule.enabled ? 'yellow' : 'green'}-600 hover:text-${rule.enabled ? 'yellow' : 'green'}-800 mr-2" 
                                onclick="toggleRule(${rule.id})" title="${rule.enabled ? '禁用' : '启用'}">
                            <i class="fas fa-${rule.enabled ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="text-indigo-600 hover:text-indigo-800 mr-2" onclick="resetHitCount(${rule.id})" title="重置计数">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="text-red-600 hover:text-red-800" onclick="deleteRule(${rule.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            
            // 重置全选框状态
            document.getElementById('select-all').checked = false;
            updateSelection();
        }
        
        // 显示添加规则模态框
        function showAddRuleModal() {
            document.getElementById('ruleModalTitle').textContent = '添加规则';
            document.getElementById('ruleForm').reset();
            document.getElementById('ruleId').value = '';
            document.getElementById('enabled').checked = true;
            document.getElementById('ruleModal').classList.remove('hidden');
        }
        
        // 显示批量添加模态框
        function showBatchAddModal() {
            document.getElementById('batchAddForm').reset();
            document.getElementById('batchEnabled').checked = true;
            document.getElementById('batchAddModal').classList.remove('hidden');
        }
        
        // 关闭规则模态框
        function closeRuleModal() {
            document.getElementById('ruleModal').classList.add('hidden');
        }
        
        // 关闭批量添加模态框
        function closeBatchAddModal() {
            document.getElementById('batchAddModal').classList.add('hidden');
        }
        
        // 编辑规则
        function editRule(id) {
            const rule = currentRules.find(r => r.id === id);
            if (!rule) return;
            
            document.getElementById('ruleModalTitle').textContent = '编辑规则';
            document.getElementById('ruleId').value = rule.id;
            document.getElementById('userAgent').value = rule.user_agent;
            document.getElementById('description').value = rule.description || '';
            document.getElementById('returnCode').value = rule.return_code;
            document.getElementById('enabled').checked = rule.enabled;
            document.getElementById('ruleModal').classList.remove('hidden');
        }
        
        // 保存规则
        async function saveRule() {
            const id = document.getElementById('ruleId').value;
            const data = {
                user_agent: document.getElementById('userAgent').value.trim(),
                description: document.getElementById('description').value.trim(),
                return_code: parseInt(document.getElementById('returnCode').value),
                enabled: document.getElementById('enabled').checked
            };
            
            if (!data.user_agent) {
                showError('请输入用户代理特征');
                return;
            }
            
            // 前端验证：检查UA是否重复
            const isEdit = !!id;
            if (currentRules && currentRules.length > 0) {
                const existingRule = currentRules.find(rule => {
                    // 编辑时排除自身
                    if (isEdit && rule.id == id) return false;
                    // 检查user_agent是否重复（不区分大小写）
                    return rule.user_agent.toLowerCase() === data.user_agent.toLowerCase();
                });
                
                if (existingRule) {
                    showError(`用户代理特征已存在：${existingRule.user_agent} (${existingRule.description || '无描述'})`);
                    return;
                }
            }
            
            try {
                const response = id 
                    ? await put(`/api/v1/spider-block/${id}`, data)
                    : await post('/api/v1/spider-block', data);
                    
                if (response.success) {
                    closeRuleModal();
                    showSuccess(id ? '规则更新成功' : '规则添加成功');
                    loadRules();
                } else {
                    showError(response.error || '操作失败');
                }
            } catch (error) {
                showError('操作失败');
            }
        }
        
        // 批量添加规则
        async function batchAddRules() {
            const rulesText = document.getElementById('batchRules').value.trim();
            const enabled = document.getElementById('batchEnabled').checked;
            
            if (!rulesText) {
                showError('请输入规则列表');
                return;
            }
            
            const lines = rulesText.split('\n').filter(line => line.trim());
            const rules = [];
            const duplicates = [];
            
            for (const line of lines) {
                const parts = line.split('|').map(p => p.trim());
                if (parts[0]) {
                    const userAgent = parts[0];
                    
                    // 检查是否与现有规则重复
                    const existingRule = allRules && allRules.find(rule => 
                        rule.user_agent.toLowerCase() === userAgent.toLowerCase()
                    );
                    
                    if (existingRule) {
                        duplicates.push(`${userAgent} (已存在: ${existingRule.description || '无描述'})`);
                        continue; // 跳过重复的规则
                    }
                    
                    // 检查批量添加中是否有重复
                    const isDuplicate = rules.some(rule => 
                        rule.user_agent.toLowerCase() === userAgent.toLowerCase()
                    );
                    
                    if (!isDuplicate) {
                        rules.push({
                            user_agent: userAgent,
                            description: parts[1] || '',
                            return_code: parseInt(parts[2]) || 403,
                            enabled: enabled
                        });
                    }
                }
            }
            
            // 如果有重复，提示用户
            if (duplicates.length > 0) {
                showWarning(`以下规则已存在，已自动跳过：\n${duplicates.join('\n')}`);
            }
            
            if (rules.length === 0) {
                showError('没有有效的规则');
                return;
            }
            
            let successCount = 0;
            let errorCount = 0;
            
            for (const rule of rules) {
                try {
                    const response = await post('/api/v1/spider-block', rule);
                    if (response.success) {
                        successCount++;
                    } else {
                        errorCount++;
                    }
                } catch (error) {
                    errorCount++;
                }
            }
            
            closeBatchAddModal();
            
            if (successCount > 0) {
                showSuccess(`成功添加 ${successCount} 条规则`);
            }
            if (errorCount > 0) {
                showWarning(`失败 ${errorCount} 条规则`);
            }
            
            loadRules();
        }
        
        // 切换规则状态
        async function toggleRule(id) {
            try {
                const response = await post(`/api/v1/spider-block/${id}/toggle`);
                if (response.success) {
                    showSuccess('状态切换成功');
                    loadRules();
                } else {
                    showError(response.error || '操作失败');
                }
            } catch (error) {
                showError('操作失败');
            }
        }
        
        // 重置命中计数
        async function resetHitCount(id) {
            if (!confirm('确定要重置该规则的命中计数吗？')) return;
            
            try {
                const response = await post(`/api/v1/spider-block/${id}/reset`);
                if (response.success) {
                    showSuccess('计数重置成功');
                    loadRules();
                } else {
                    showError(response.error || '操作失败');
                }
            } catch (error) {
                showError('操作失败');
            }
        }
        
        // 重置所有命中计数和统计数据
        async function resetAllHitCounts() {
            if (!confirm('确定要重置所有规则的命中计数并清空图表统计数据吗？\n此操作不可恢复！')) return;
            
            try {
                const response = await post('/api/v1/spider-block/reset-all');
                if (response.success) {
                    showSuccess('所有数据已重置');
                    loadRules();
                    loadStats(); // 刷新图表
                } else {
                    showError(response.error || '操作失败');
                }
            } catch (error) {
                showError('操作失败');
            }
        }
        
        // 删除规则
        async function deleteRule(id) {
            if (!confirm('确定要删除该规则吗？')) return;
            
            try {
                const response = await del(`/api/v1/spider-block/${id}`);
                if (response.success) {
                    showSuccess('规则删除成功');
                    loadRules();
                } else {
                    showError(response.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败');
            }
        }
        
        // HTML转义
        function escapeHtml(text) {
            if (!text) return '';
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, m => map[m]);
        }
        
        // 加载全局设置
        async function loadGlobalSettings() {
            try {
                const response = await get('/api/v1/system/settings');
                if (response.success && response.data) {
                    const settings = response.data;
                    document.getElementById('globalSpiderBlockSwitch').checked = settings.enable_global_spider_block || false;
                    updateGlobalStatus(settings.enable_global_spider_block || false);
                }
            } catch (error) {
                showError('加载全局设置失败');
            }
        }
        
        // 更新全局状态显示
        function updateGlobalStatus(enabled) {
            const statusEl = document.getElementById('globalStatus');
            if (enabled) {
                statusEl.textContent = '(已启用)';
                statusEl.className = 'ml-2 text-xs text-green-600';
            } else {
                statusEl.textContent = '(已禁用)';
                statusEl.className = 'ml-2 text-xs text-gray-500';
            }
        }
        
        // 切换全局蜘蛛屏蔽开关
        async function toggleGlobalSpiderBlock() {
            const enabled = document.getElementById('globalSpiderBlockSwitch').checked;
            try {
                // 先获取当前设置，保留其他字段
                let currentSettings = {};
                const getResponse = await get('/api/v1/system/settings');
                if (getResponse.success && getResponse.data) {
                    currentSettings = getResponse.data;
                }
                
                // 只更新enable_global_spider_block字段，保留其他所有设置
                const response = await put('/api/v1/system/settings', {
                    ...currentSettings,
                    enable_global_spider_block: enabled
                });
                
                if (response.success) {
                    showSuccess(enabled ? '全局蜘蛛屏蔽已启用' : '全局蜘蛛屏蔽已禁用');
                    updateGlobalStatus(enabled);
                } else {
                    showError(response.error || '操作失败');
                    document.getElementById('globalSpiderBlockSwitch').checked = !enabled;
                }
            } catch (error) {
                showError('操作失败');
                document.getElementById('globalSpiderBlockSwitch').checked = !enabled;
            }
        }
        
        // 显示403模板模态框
        async function show403TemplateModal() {
            try {
                const response = await get('/api/v1/system/settings');
                if (response.success && response.data) {
                    const template = response.data.spider_block_403_template || '';
                    document.getElementById('template403Content').value = template || getDefault403Template();
                }
            } catch (error) {
                document.getElementById('template403Content').value = getDefault403Template();
            }
            document.getElementById('template403Modal').classList.remove('hidden');
        }
        
        // 关闭403模板模态框
        function close403TemplateModal() {
            document.getElementById('template403Modal').classList.add('hidden');
            document.getElementById('template403Preview').classList.add('hidden');
        }
        
        // 获取默认403模板
        function getDefault403Template() {
            return `<!DOCTYPE html>
<html>
<head>
    <title>{{.Status}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f0f0f0;
        }
        h1 {
            color: #d32f2f;
            font-size: 48px;
        }
        .message {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .details {
            color: #666;
            margin-top: 20px;
            font-size: 14px;
        }
        p {
            font-size: 18px;
            color: #333;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="message">
        <h1>{{.Status}}</h1>
        <p>Access denied</p>
        <div class="details">
            <p>Status: {{.Status}}</p>
            <p>User-Agent: {{.UserAgent}}</p>
            <p>Time: {{.Time}}</p>
        </div>
    </div>
</body>
</html>`;
        }
        
        // 预览403模板
        function preview403Template() {
            const content = document.getElementById('template403Content').value;
            const previewFrame = document.getElementById('previewFrame');
            
            // 获取选择的状态码（如果有规则编辑器打开）
            let statusCode = '403';
            let statusText = 'Forbidden';
            const returnCodeSelect = document.getElementById('returnCode');
            if (returnCodeSelect && returnCodeSelect.value) {
                statusCode = returnCodeSelect.value;
                switch(statusCode) {
                    case '404':
                        statusText = 'Not Found';
                        break;
                    case '410':
                        statusText = 'Gone';
                        break;
                    case '500':
                        statusText = 'Internal Server Error';
                        break;
                    case '503':
                        statusText = 'Service Unavailable';
                        break;
                    default:
                        statusText = 'Forbidden';
                }
            }
            
            // 替换模板变量
            const previewContent = content
                .replace(/\{\{\.StatusCode\}\}/g, statusCode)
                .replace(/\{\{\.StatusText\}\}/g, statusText)
                .replace(/\{\{\.Status\}\}/g, `${statusCode} ${statusText}`)
                .replace(/\{\{\.UserAgent\}\}/g, 'Mozilla/5.0 (Example Bot)')
                .replace(/\{\{\.Time\}\}/g, new Date().toLocaleString());
            
            // 显示预览
            previewFrame.srcdoc = previewContent;
            document.getElementById('template403Preview').classList.remove('hidden');
        }
        
        // 重置403模板
        function reset403Template() {
            document.getElementById('template403Content').value = getDefault403Template();
            showInfo('模板已重置为默认');
        }
        
        // 保存403模板
        async function save403Template() {
            const template = document.getElementById('template403Content').value;
            try {
                const response = await put('/api/v1/system/settings', {
                    spider_block_403_template: template
                });
                if (response.success) {
                    showSuccess('403模板保存成功');
                    close403TemplateModal();
                } else {
                    showError(response.error || '保存失败');
                }
            } catch (error) {
                showError('保存失败');
            }
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.id === 'ruleModal' || event.target.id === 'batchAddModal' || event.target.id === 'template403Modal') {
                event.target.classList.add('hidden');
            }
        }
        
        // 统计图表相关
        let statsChart = null;
        
        // 加载统计数据
        async function loadStats() {
            const period = document.getElementById('periodSelect').value;
            
            try {
                const response = await fetch(`/api/v1/spider-block/stats?period=${period}`);
                const result = await response.json();
                
                if (result.success) {
                    updateStatsDisplay(result.data);
                    renderChart(result.data.chart);
                } else {
                    showToast('加载统计数据失败', 'error');
                }
            } catch (error) {
                console.error('加载统计失败:', error);
                showToast('网络错误', 'error');
            }
        }
        
        // 更新统计显示
        function updateStatsDisplay(data) {
            const summary = data.summary;
            
            // 更新统计卡片
            document.getElementById('totalBlocks').textContent = summary.total_blocks || 0;
            document.getElementById('spiderCount').textContent = summary.spider_count || 0;
            
            // 计算平均每小时
            let avgPerHour = 0;
            if (data.period === 'today' || data.period === 'yesterday') {
                avgPerHour = Math.round(summary.total_blocks / 24);
            } else if (data.period === '7days') {
                avgPerHour = Math.round(summary.total_blocks / (7 * 24));
            } else if (data.period === '15days') {
                avgPerHour = Math.round(summary.total_blocks / (15 * 24));
            }
            document.getElementById('avgPerHour').textContent = avgPerHour;
            
            // 显示最活跃蜘蛛
            if (summary.top_spiders && summary.top_spiders.length > 0) {
                document.getElementById('topSpider').textContent = summary.top_spiders[0].spider_name;
            }
        }
        
        // 渲染图表
        function renderChart(chartData) {
            const chartDom = document.getElementById('statsChart');
            
            if (!statsChart) {
                statsChart = echarts.init(chartDom);
            }
            
            // 检查chartData是否为空
            if (!chartData || !chartData.series || !chartData.labels) {
                // 显示空数据图表
                const emptyOption = {
                    title: {
                        text: '暂无数据',
                        left: 'center',
                        top: 'center',
                        textStyle: {
                            color: '#999',
                            fontSize: 14
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: []
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: []
                };
                statsChart.setOption(emptyOption);
                return;
            }
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    data: chartData.series.map(s => s.name),
                    bottom: 0
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '10%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: chartData.labels
                },
                yAxis: {
                    type: 'value',
                    name: '屏蔽次数',
                    minInterval: 1
                },
                series: chartData.series.map(s => ({
                    name: s.name,
                    type: 'line',
                    smooth: true,
                    data: s.data,
                    areaStyle: {
                        opacity: 0.3
                    },
                    emphasis: {
                        focus: 'series'
                    }
                }))
            };
            
            statsChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', () => {
                statsChart && statsChart.resize();
            });
        }
        
        // 清空所有规则
        // 清空统计数据（原clearAllRules函数保留但不调用）
        async function clearStatsData() {
            if (!confirm('确定要清空所有统计图表数据吗？规则将保留，只清空统计数据。')) return;
            
            try {
                const response = await del('/api/v1/spider-block/stats/clear');
                if (response.success) {
                    showSuccess('统计数据已清空');
                    loadStats(); // 刷新统计图表
                } else {
                    showError(response.error || '清空失败');
                }
            } catch (error) {
                showError('清空失败: ' + error.message);
            }
        }
        
        async function clearAllRules() {
            if (!confirm('确定要清空所有屏蔽规则吗？此操作不可恢复！')) return;
            
            try {
                const response = await del('/api/v1/spider-block/clear');
                if (response.success) {
                    showSuccess('已清空所有规则');
                    loadRules();
                } else {
                    showError(response.error || '清空失败');
                }
            } catch (error) {
                showError('清空失败: ' + error.message);
            }
        }
        
        // 更新分页状态
        function updatePagination() {
            document.getElementById('total-rules').textContent = totalRules;
            document.getElementById('current-page').textContent = currentPage;
            document.getElementById('total-pages').textContent = totalPages;
            
            // 更新分页大小选择器
            const pageSizeSelect = document.getElementById('page-size-select');
            if (pageSizeSelect) {
                pageSizeSelect.value = pageSize.toString();
            }
            
            const prevBtn = document.getElementById('prev-page');
            const nextBtn = document.getElementById('next-page');
            
            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }
        
        // 切换页面
        function changePage(direction) {
            if (direction === 'prev' && currentPage > 1) {
                loadRules(currentPage - 1);
            } else if (direction === 'next' && currentPage < totalPages) {
                loadRules(currentPage + 1);
            }
        }
        
        // 改变分页大小
        function changePageSize() {
            const select = document.getElementById('page-size-select');
            pageSize = parseInt(select.value);
            currentPage = 1; // 重置到第一页
            loadRules(1);
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            updateSelection();
        }
        
        // 更新选择状态
        function updateSelection() {
            const checkboxes = document.querySelectorAll('.rule-checkbox:checked');
            const selectedCount = checkboxes.length;
            const bulkActions = document.getElementById('bulk-actions');
            const selectedCountEl = document.getElementById('selected-count');
            
            if (selectedCount > 0) {
                bulkActions.classList.remove('hidden');
                selectedCountEl.textContent = selectedCount;
            } else {
                bulkActions.classList.add('hidden');
            }
            
            // 更新全选框状态
            const allCheckboxes = document.querySelectorAll('.rule-checkbox');
            const selectAll = document.getElementById('select-all');
            if (allCheckboxes.length > 0) {
                selectAll.checked = selectedCount === allCheckboxes.length;
                selectAll.indeterminate = selectedCount > 0 && selectedCount < allCheckboxes.length;
            }
        }
        
        // 清除选择
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('select-all').checked = false;
            updateSelection();
        }
        
        // 批量删除
        async function batchDelete() {
            const checkboxes = document.querySelectorAll('.rule-checkbox:checked');
            const ids = Array.from(checkboxes).map(cb => parseInt(cb.value));
            
            if (ids.length === 0) {
                showWarning('请先选择要删除的规则');
                return;
            }
            
            if (!confirm(`确定要删除选中的 ${ids.length} 条规则吗？此操作不可恢复！`)) {
                return;
            }
            
            try {
                // 批量删除API - 改用POST请求，因为某些服务器不支持DELETE请求带body
                const response = await post('/api/v1/spider-block/batch-delete', { ids });
                
                if (response.success) {
                    showSuccess(`成功删除 ${ids.length} 条规则`);
                    clearSelection();
                    loadRules();
                } else {
                    showError(response.error || '批量删除失败');
                }
            } catch (error) {
                showError('批量删除失败: ' + error.message);
            }
        }
        
        // 批量启用/禁用
        async function batchToggle(enable) {
            const checkboxes = document.querySelectorAll('.rule-checkbox:checked');
            const ids = Array.from(checkboxes).map(cb => parseInt(cb.value));
            
            if (ids.length === 0) {
                showWarning('请先选择要操作的规则');
                return;
            }
            
            try {
                const response = await put('/api/v1/spider-block/batch/toggle', { 
                    ids, 
                    enabled: enable 
                });
                
                if (response.success) {
                    showSuccess(`成功${enable ? '启用' : '禁用'} ${ids.length} 条规则`);
                    clearSelection();
                    loadRules();
                } else {
                    showError(response.error || '批量操作失败');
                }
            } catch (error) {
                showError('批量操作失败: ' + error.message);
            }
        }
        
        // 标签页切换
        function switchTab(tab) {
            // 更新标签样式
            const tabs = {
                'rules': document.getElementById('tab-rules')
            };
            
            const contents = {
                'rules': document.getElementById('content-rules')
            };
            
            // 重置所有标签
            Object.values(tabs).forEach(t => {
                t.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
                t.classList.add('text-gray-600');
            });
            
            Object.values(contents).forEach(c => {
                c.classList.add('hidden');
            });
            
            // 激活选中的标签
            tabs[tab].classList.remove('text-gray-600');
            tabs[tab].classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
            contents[tab].classList.remove('hidden');
        }
        
        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        // HTML转义
        function escapeHtml(str) {
            const div = document.createElement('div');
            div.textContent = str;
            return div.innerHTML;
        }
        
        // 页面加载时加载统计
        loadStats();
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
