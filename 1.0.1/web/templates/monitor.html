<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">系统监控</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="exportMetrics()" class="text-gray-600 hover:text-gray-900 mr-2">
                            <i class="fas fa-download"></i>
                        </button>
                        <button onclick="location.reload()" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 自动刷新控制 -->
                <div class="mb-6 flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="auto-refresh" checked class="mr-2">
                            <span class="text-sm text-gray-700">自动刷新</span>
                        </label>
                        <select id="refresh-interval" class="border border-gray-300 rounded-lg px-3 py-1 text-sm">
                            <option value="5">5秒</option>
                            <option value="10" selected>10秒</option>
                            <option value="30">30秒</option>
                            <option value="60">60秒</option>
                        </select>
                    </div>
                    <div class="text-sm text-gray-600">
                        最后更新：<span id="last-update">-</span>
                    </div>
                </div>
                
                <!-- 健康状态 -->
                <div id="health-status" class="mb-6 p-4 rounded-lg border bg-green-50 border-green-200">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 text-xl mr-3"></i>
                        <div>
                            <strong class="text-green-800">系统状态：</strong>
                            <span id="health-text" class="text-green-700">健康</span>
                        </div>
                    </div>
                    <div id="warnings-list" class="mt-2 hidden"></div>
                </div>
                
                <!-- 关键指标卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-sm font-medium text-gray-600 mb-2">并发请求数</h3>
                        <div class="text-3xl font-bold text-gray-800">
                            <span id="concurrent-requests">0</span>
                        </div>
                        <div class="text-sm text-gray-500 mt-1" id="concurrent-trend"></div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-sm font-medium text-gray-600 mb-2">任务队列大小</h3>
                        <div class="text-3xl font-bold text-gray-800">
                            <span id="queue-size">0</span>
                        </div>
                        <div class="text-sm text-gray-500 mt-1" id="queue-trend"></div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-sm font-medium text-gray-600 mb-2">缓存命中率</h3>
                        <div class="text-3xl font-bold text-gray-800">
                            <span id="cache-hit-rate">0</span><span class="text-xl">%</span>
                        </div>
                        <div class="text-sm text-gray-500 mt-1" id="cache-trend"></div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-sm font-medium text-gray-600 mb-2">平均响应时间</h3>
                        <div class="text-3xl font-bold text-gray-800">
                            <span id="avg-response-time">0</span><span class="text-xl">ms</span>
                        </div>
                        <div class="text-sm text-gray-500 mt-1" id="response-trend"></div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-sm font-medium text-gray-600 mb-2">错误率</h3>
                        <div class="text-3xl font-bold text-gray-800">
                            <span id="error-rate">0</span><span class="text-xl">%</span>
                        </div>
                        <div class="text-sm text-gray-500 mt-1" id="error-trend"></div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-sm font-medium text-gray-600 mb-2">活跃工作线程</h3>
                        <div class="text-3xl font-bold text-gray-800">
                            <span id="active-workers">0</span>
                        </div>
                        <div class="text-sm text-gray-500 mt-1" id="workers-trend"></div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-sm font-medium text-gray-600 mb-2">
                            <i class="fas fa-arrow-up text-green-600 mr-1"></i>上传速度
                        </h3>
                        <div class="text-2xl font-bold text-gray-800">
                            <span id="upload-speed">0</span>
                            <span class="text-sm" id="upload-unit">KB/s</span>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">总计: <span id="total-upload">0</span></div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-sm font-medium text-gray-600 mb-2">
                            <i class="fas fa-arrow-down text-blue-600 mr-1"></i>下载速度
                        </h3>
                        <div class="text-2xl font-bold text-gray-800">
                            <span id="download-speed">0</span>
                            <span class="text-sm" id="download-unit">KB/s</span>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">总计: <span id="total-download">0</span></div>
                    </div>
                </div>
                
                <!-- 性能图表 -->
                <div class="bg-white rounded-lg shadow mb-8">
                    <div class="px-6 py-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-800">实时性能趋势</h2>
                    </div>
                    <div class="p-6">
                        <canvas id="performance-chart" style="height: 300px;"></canvas>
                    </div>
                </div>
                
                <!-- 系统信息 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-base font-semibold text-gray-800">系统资源</h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">CPU使用率</span>
                                <span class="text-sm font-medium" id="cpu-usage">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">内存使用</span>
                                <span class="text-sm font-medium" id="memory-usage">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">协程数量</span>
                                <span class="text-sm font-medium" id="goroutine-count">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">数据库连接</span>
                                <span class="text-sm font-medium" id="db-connections">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-base font-semibold text-gray-800">请求统计</h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">总请求数</span>
                                <span class="text-sm font-medium" id="total-requests">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">成功请求</span>
                                <span class="text-sm font-medium" id="success-requests">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">失败请求</span>
                                <span class="text-sm font-medium" id="failed-requests">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">P95响应时间</span>
                                <span class="text-sm font-medium" id="p95-latency">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-base font-semibold text-gray-800">缓存统计</h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Redis命中次数</span>
                                <span class="text-sm font-medium" id="redis-hits">-</span>
                            </div>
                            <!-- 文件缓存大小统计已移除，避免全盘扫描影响性能 -->
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">写入队列长度</span>
                                <span class="text-sm font-medium" id="write-queue-size">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">缓存过期数</span>
                                <span class="text-sm font-medium" id="expired-count">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b">
                            <h3 class="text-base font-semibold text-gray-800">站点统计</h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">活跃站点数</span>
                                <span class="text-sm font-medium" id="active-sites">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">总站点数</span>
                                <span class="text-sm font-medium" id="total-sites">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">正在爬取</span>
                                <span class="text-sm font-medium" id="crawling-sites">-</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">爬取失败</span>
                                <span class="text-sm font-medium" id="failed-sites">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        let refreshTimer;
        let performanceChart;
        let chartData = {
            labels: [],
            datasets: [
                {
                    label: '并发请求',
                    data: [],
                    borderColor: '#3B82F6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    yAxisID: 'y',
                },
                {
                    label: '响应时间(ms)',
                    data: [],
                    borderColor: '#10B981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    yAxisID: 'y1',
                },
                {
                    label: '错误率(%)',
                    data: [],
                    borderColor: '#EF4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    yAxisID: 'y1',
                }
            ]
        };

        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }
        
        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    stacked: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '并发数'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '时间/百分比'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                    },
                }
            });
        }
        
        // 加载监控数据
        async function loadMetrics() {
            try {
                const res = await fetch('/api/v1/system/metrics');
                const data = await res.json();
                
                if (data.success) {
                    updateMetrics(data.data);
                    document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
                }
            } catch (error) {
                console.error('加载监控数据失败:', error);
            }
        }
        
        // 更新指标显示
        function updateMetrics(metrics) {
            // 更新卡片数据
            updateMetricCard('concurrent-requests', metrics.concurrent_requests || 0);
            updateMetricCard('queue-size', metrics.queue_size || 0);
            updateMetricCard('cache-hit-rate', ((metrics.cache_hit_rate || 0) * 100).toFixed(1));
            updateMetricCard('avg-response-time', ((metrics.avg_response_time || 0) * 1000).toFixed(0));
            updateMetricCard('error-rate', ((metrics.error_rate || 0) * 100).toFixed(1));
            updateMetricCard('active-workers', metrics.active_workers || 0);
            
            // 更新带宽数据
            updateBandwidthMetrics(metrics);
            
            // 更新健康状态
            updateHealthStatus(metrics.health);
            
            // 更新系统信息
            document.getElementById('cpu-usage').textContent = (metrics.cpu_usage || 0).toFixed(1) + '%';
            document.getElementById('memory-usage').textContent = formatBytes(metrics.memory_usage || 0);
            document.getElementById('goroutine-count').textContent = metrics.goroutine_count || 0;
            document.getElementById('db-connections').textContent = metrics.db_connections || 0;
            
            // 更新请求统计
            document.getElementById('total-requests').textContent = formatNumber(metrics.total_requests || 0);
            document.getElementById('success-requests').textContent = formatNumber(metrics.success_requests || 0);
            document.getElementById('failed-requests').textContent = formatNumber(metrics.failed_requests || 0);
            document.getElementById('p95-latency').textContent = ((metrics.p95_latency || 0) * 1000).toFixed(0) + 'ms';
            
            // 更新缓存统计
            document.getElementById('redis-hits').textContent = formatNumber(metrics.redis_hits || 0);
            // 文件缓存大小统计已移除，避免全盘扫描影响性能
            // document.getElementById('file-cache-size').textContent = formatBytes(metrics.file_cache_size || 0);
            document.getElementById('write-queue-size').textContent = metrics.write_queue_size || 0;
            document.getElementById('expired-count').textContent = formatNumber(metrics.expired_count || 0);
            
            // 更新站点统计
            document.getElementById('active-sites').textContent = metrics.active_sites || 0;
            document.getElementById('total-sites').textContent = metrics.total_sites || 0;
            document.getElementById('crawling-sites').textContent = metrics.crawling_sites || 0;
            document.getElementById('failed-sites').textContent = metrics.failed_sites || 0;
            
            // 更新图表
            updateChart(metrics);
        }
        
        // 更新指标卡片
        function updateMetricCard(id, value) {
            const element = document.getElementById(id);
            const oldValue = parseFloat(element.textContent) || 0;
            element.textContent = value;
            
            // 更新趋势
            const trendElement = document.getElementById(id.replace(/-(.*?)$/, '-trend'));
            if (trendElement) {
                if (value > oldValue) {
                    trendElement.className = 'text-sm text-green-600 mt-1';
                    trendElement.innerHTML = '<i class="fas fa-arrow-up"></i> ' + ((value - oldValue).toFixed(1));
                } else if (value < oldValue) {
                    trendElement.className = 'text-sm text-red-600 mt-1';
                    trendElement.innerHTML = '<i class="fas fa-arrow-down"></i> ' + ((oldValue - value).toFixed(1));
                } else {
                    trendElement.className = 'text-sm text-gray-500 mt-1';
                    trendElement.innerHTML = '<i class="fas fa-minus"></i> 0';
                }
            }
        }
        
        // 更新健康状态
        function updateHealthStatus(health) {
            if (!health) return;
            
            const statusElement = document.getElementById('health-status');
            const textElement = document.getElementById('health-text');
            const warningsElement = document.getElementById('warnings-list');
            
            if (health.healthy) {
                statusElement.className = 'mb-6 p-4 rounded-lg border bg-green-50 border-green-200';
                textElement.textContent = '健康';
                textElement.className = 'text-green-700';
                warningsElement.classList.add('hidden');
            } else {
                statusElement.className = 'mb-6 p-4 rounded-lg border bg-red-50 border-red-200';
                textElement.textContent = '异常';
                textElement.className = 'text-red-700';
                
                if (health.warnings && health.warnings.length > 0) {
                    warningsElement.classList.remove('hidden');
                    warningsElement.innerHTML = health.warnings.map(w => 
                        `<div class="text-sm text-red-600 mt-1"><i class="fas fa-exclamation-triangle mr-1"></i> ${w}</div>`
                    ).join('');
                }
            }
        }
        
        // 更新图表
        function updateChart(metrics) {
            const now = new Date().toLocaleTimeString();
            
            // 保持最近30个数据点
            if (chartData.labels.length > 30) {
                chartData.labels.shift();
                chartData.datasets.forEach(dataset => dataset.data.shift());
            }
            
            chartData.labels.push(now);
            chartData.datasets[0].data.push(metrics.concurrent_requests || 0);
            chartData.datasets[1].data.push((metrics.avg_response_time || 0) * 1000);
            chartData.datasets[2].data.push((metrics.error_rate || 0) * 100);
            
            performanceChart.update();
        }
        
        // 格式化数字
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
        
        // 格式化字节
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 格式化带宽速度
        function formatBandwidth(bytesPerSecond) {
            if (bytesPerSecond === 0) return {value: '0', unit: 'KB/s'};
            const k = 1024;
            if (bytesPerSecond < k) {
                return {value: bytesPerSecond.toFixed(0), unit: 'B/s'};
            } else if (bytesPerSecond < k * k) {
                return {value: (bytesPerSecond / k).toFixed(1), unit: 'KB/s'};
            } else {
                return {value: (bytesPerSecond / k / k).toFixed(2), unit: 'MB/s'};
            }
        }
        
        // 更新带宽指标
        function updateBandwidthMetrics(metrics) {
            // 更新上传速度
            const uploadSpeed = formatBandwidth(metrics.upload_speed || 0);
            document.getElementById('upload-speed').textContent = uploadSpeed.value;
            document.getElementById('upload-unit').textContent = uploadSpeed.unit;
            document.getElementById('total-upload').textContent = formatBytes(metrics.total_upload || 0);
            
            // 更新下载速度
            const downloadSpeed = formatBandwidth(metrics.download_speed || 0);
            document.getElementById('download-speed').textContent = downloadSpeed.value;
            document.getElementById('download-unit').textContent = downloadSpeed.unit;
            document.getElementById('total-download').textContent = formatBytes(metrics.total_download || 0);
        }
        
        // 自动刷新控制
        function setupAutoRefresh() {
            const checkbox = document.getElementById('auto-refresh');
            const interval = document.getElementById('refresh-interval');
            
            checkbox.addEventListener('change', toggleAutoRefresh);
            interval.addEventListener('change', toggleAutoRefresh);
            
            toggleAutoRefresh();
        }
        
        function toggleAutoRefresh() {
            const enabled = document.getElementById('auto-refresh').checked;
            const interval = parseInt(document.getElementById('refresh-interval').value) * 1000;
            
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }
            
            if (enabled) {
                refreshTimer = setInterval(loadMetrics, interval);
            }
        }
        
        // 导出指标
        async function exportMetrics() {
            try {
                const res = await fetch('/api/v1/system/metrics/export');
                const blob = await res.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `metrics_${new Date().toISOString()}.json`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                
                showToast('指标已导出', 'success');
            } catch (error) {
                showToast('导出失败: ' + error.message, 'error');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            loadMetrics();
            setupAutoRefresh();
        });
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
