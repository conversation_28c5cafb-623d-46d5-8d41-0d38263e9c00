<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录日志 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
            <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>

        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 头部 -->
            <header class="bg-white shadow-sm">
                <div class="px-6 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">登录日志</h1>
                    <p class="text-sm text-gray-600 mt-1">查看系统登录记录和统计信息</p>
                </div>
            </header>

            <div class="p-6">

            <!-- 搜索区域 -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="p-4 border-b">
                    <div class="grid grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                            <input type="text" id="search-username" placeholder="输入用户名搜索" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                            <input type="date" id="start-date" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                            <input type="date" id="end-date" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:border-indigo-500">
                        </div>
                        <div class="flex items-end gap-2">
                            <button onclick="searchLogs()" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">
                                <i class="fas fa-search mr-1"></i>搜索
                            </button>
                            <button onclick="resetSearch()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                                <i class="fas fa-redo mr-1"></i>重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="grid grid-cols-4 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500">今日登录</p>
                            <p class="text-2xl font-bold text-gray-800" id="today-count">0</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-day text-blue-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500">成功次数</p>
                            <p class="text-2xl font-bold text-green-600" id="success-count">0</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500">失败次数</p>
                            <p class="text-2xl font-bold text-red-600" id="failed-count">0</p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-times-circle text-red-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-500">活跃用户</p>
                            <p class="text-2xl font-bold text-purple-600" id="active-users">0</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-purple-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日志列表 -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">登录记录</h3>
                    <div class="flex space-x-2">
                        <button onclick="cleanOldLogs()" class="px-3 py-1 text-sm bg-yellow-500 text-white rounded hover:bg-yellow-600">
                            <i class="fas fa-broom mr-1"></i>清理30天前日志
                        </button>
                        <button onclick="cleanAllLogs()" class="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600">
                            <i class="fas fa-trash mr-1"></i>清空所有日志
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">失败原因</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User-Agent</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">登录时间</th>
                            </tr>
                        </thead>
                        <tbody id="logs-list" class="bg-white divide-y divide-gray-200">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                <!-- 分页 -->
                <div class="px-6 py-4 border-t flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="text-sm text-gray-600">
                            共 <span id="total-count">0</span> 条记录
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm text-gray-600">每页显示：</label>
                            <select id="page-size" onchange="changePageSize(this.value)" class="px-2 py-1 border border-gray-300 rounded text-sm">
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                                <option value="100">100条</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex space-x-2" id="pagination">
                        <!-- 分页按钮将动态生成 -->
                    </div>
                </div>
            </div>
            </div>
        </main>
    </div>

    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        let currentPage = 1;
        let pageSize = 10; // 默认显示10条
        let searchParams = {};

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期范围（最近7天）
            const today = new Date();
            const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            document.getElementById('end-date').value = formatDate(today);
            document.getElementById('start-date').value = formatDate(sevenDaysAgo);
            
            loadLogs();
            loadStats();
        });

        // 格式化日期
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 隐藏IP地址中间部分
        function maskIP(ip) {
            if (!ip) return '-';
            
            // 处理IPv4地址
            if (ip.includes('.')) {
                const parts = ip.split('.');
                if (parts.length === 4) {
                    return `${parts[0]}.***.***.${parts[3]}`;
                }
            }
            
            // 处理IPv6地址
            if (ip.includes(':')) {
                const parts = ip.split(':');
                if (parts.length > 2) {
                    return `${parts[0]}:***:***:${parts[parts.length - 1]}`;
                }
            }
            
            // 如果是localhost或其他特殊地址
            if (ip === '::1' || ip === '127.0.0.1' || ip === 'localhost') {
                return ip;
            }
            
            // 默认处理
            return ip;
        }

        // 加载登录日志
        async function loadLogs() {
            try {
                const params = new URLSearchParams({
                    page: currentPage,
                    page_size: pageSize,
                    ...searchParams
                });

                const res = await fetch(`/api/v1/login-logs?${params}`);
                const data = await res.json();

                if (data.success) {
                    renderLogs(data.data.list);
                    renderPagination(data.data.total);
                    document.getElementById('total-count').textContent = data.data.total;
                } else {
                    showToast('加载失败: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('加载登录日志失败:', error);
                showToast('加载失败', 'error');
            }
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const res = await fetch('/api/v1/login-logs/stats');
                const data = await res.json();

                if (data.success) {
                    document.getElementById('success-count').textContent = data.data.total_success || 0;
                    document.getElementById('failed-count').textContent = data.data.total_failed || 0;
                    
                    // 计算今日登录和活跃用户
                    const today = formatDate(new Date());
                    const todayStats = data.data.daily_stats[today];
                    if (todayStats) {
                        document.getElementById('today-count').textContent = 
                            (todayStats.success || 0) + (todayStats.failed || 0);
                    }
                    
                    const activeUsers = Object.keys(data.data.user_stats || {}).length;
                    document.getElementById('active-users').textContent = activeUsers;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 渲染日志列表
        function renderLogs(logs) {
            const tbody = document.getElementById('logs-list');
            
            if (!logs || logs.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            暂无数据
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = logs.map(log => {
                const statusClass = log.status === 'success' ? 'text-green-600' : 'text-red-600';
                const statusIcon = log.status === 'success' ? 'check-circle' : 'times-circle';
                const userAgent = log.user_agent || '-';
                const shortUserAgent = userAgent.length > 50 ? userAgent.substring(0, 50) + '...' : userAgent;
                
                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${log.username}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900" title="${log.ip}">${maskIP(log.ip)}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="${statusClass}">
                                <i class="fas fa-${statusIcon} mr-1"></i>
                                ${log.status === 'success' ? '成功' : '失败'}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-600">${log.fail_reason || '-'}</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-600" title="${userAgent}">${shortUserAgent}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">
                                ${new Date(log.login_time).toLocaleString('zh-CN')}
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // 渲染分页
        function renderPagination(total) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let html = '';
            
            // 上一页
            if (currentPage > 1) {
                html += `<button onclick="changePage(${currentPage - 1})" class="px-3 py-1 border rounded hover:bg-gray-100">上一页</button>`;
            }
            
            // 页码逻辑：显示当前页附近的页码
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, currentPage + 2);
            
            // 第一页
            if (startPage > 1) {
                html += `<button onclick="changePage(1)" class="px-3 py-1 border rounded hover:bg-gray-100">1</button>`;
                if (startPage > 2) {
                    html += `<span class="px-2">...</span>`;
                }
            }
            
            // 中间页码
            for (let i = startPage; i <= endPage; i++) {
                const active = i === currentPage ? 'bg-indigo-600 text-white' : 'hover:bg-gray-100';
                html += `<button onclick="changePage(${i})" class="px-3 py-1 border rounded ${active}">${i}</button>`;
            }
            
            // 最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="px-2">...</span>`;
                }
                html += `<button onclick="changePage(${totalPages})" class="px-3 py-1 border rounded hover:bg-gray-100">${totalPages}</button>`;
            }
            
            // 下一页
            if (currentPage < totalPages) {
                html += `<button onclick="changePage(${currentPage + 1})" class="px-3 py-1 border rounded hover:bg-gray-100">下一页</button>`;
            }
            
            // 显示页码信息
            html += `<span class="ml-2 text-sm text-gray-600">第 ${currentPage} / ${totalPages} 页</span>`;
            
            pagination.innerHTML = html;
        }

        // 切换页码
        function changePage(page) {
            currentPage = page;
            loadLogs();
        }

        // 搜索日志
        function searchLogs() {
            searchParams = {
                username: document.getElementById('search-username').value,
                start_date: document.getElementById('start-date').value,
                end_date: document.getElementById('end-date').value
            };
            
            // 移除空值
            Object.keys(searchParams).forEach(key => {
                if (!searchParams[key]) {
                    delete searchParams[key];
                }
            });
            
            currentPage = 1;
            loadLogs();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('search-username').value = '';
            const today = new Date();
            const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            document.getElementById('end-date').value = formatDate(today);
            document.getElementById('start-date').value = formatDate(sevenDaysAgo);
            searchParams = {};
            currentPage = 1;
            loadLogs();
        }

        // 切换每页显示数量
        function changePageSize(size) {
            pageSize = parseInt(size);
            currentPage = 1;
            loadLogs();
        }

        // 清理旧日志
        async function cleanOldLogs() {
            if (!confirm('确定要清理30天前的登录日志吗？\n注意：只会删除30天前的日志记录')) {
                return;
            }

            try {
                const res = await fetch('/api/v1/login-logs/clean', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    },
                    body: JSON.stringify({ days: 30 })
                });

                const data = await res.json();
                
                if (data.success) {
                    showToast('清理成功（已清理30天前的日志）', 'success');
                    loadLogs();
                    loadStats();
                } else {
                    showToast('清理失败: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('清理失败:', error);
                showToast('清理失败', 'error');
            }
        }

        // 清空所有日志
        async function cleanAllLogs() {
            if (!confirm('确定要清空所有登录日志吗？\n⚠️ 警告：此操作不可恢复！')) {
                return;
            }
            
            // 二次确认
            if (!confirm('再次确认：您确定要删除所有登录日志吗？')) {
                return;
            }

            try {
                const res = await fetch('/api/v1/login-logs/clean-all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    }
                });

                const data = await res.json();
                
                if (data.success) {
                    showToast('已清空所有登录日志', 'success');
                    loadLogs();
                    loadStats();
                } else {
                    showToast('清空失败: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('清空失败:', error);
                showToast('清空失败', 'error');
            }
        }

        // 获取CSRF Token
        function getCSRFToken() {
            return document.querySelector('meta[name="csrf-token"]')?.content || 
                   localStorage.getItem('csrf_token') || '';
        }
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>