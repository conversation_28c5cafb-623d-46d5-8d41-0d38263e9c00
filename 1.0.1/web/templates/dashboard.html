<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">控制台</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="location.reload()" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-6">
                <!-- 统计概览卡片 -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                    <!-- 站点总数 -->
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all hover:scale-105 p-5 cursor-pointer" onclick="window.location.href='/admin/sites'">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500 mb-1">站点总数</p>
                                <p class="text-2xl font-bold text-gray-900" id="total-sites">-</p>
                                <p class="text-xs text-gray-400 mt-1">全部站点</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                                <i class="fas fa-globe text-blue-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 活跃站点 -->
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all hover:scale-105 p-5 cursor-pointer" onclick="window.location.href='/admin/sites'">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500 mb-1">活跃站点</p>
                                <p class="text-2xl font-bold text-gray-900" id="active-sites-count">-</p>
                                <p class="text-xs text-gray-400 mt-1">正在运行</p>
                            </div>
                            <div class="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
                                <i class="fas fa-check-circle text-green-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 权重站点 -->
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all hover:scale-105 p-5 cursor-pointer" onclick="window.location.href='/admin/weight-monitor'">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500 mb-1">权重站点</p>
                                <p class="text-2xl font-bold text-gray-900" id="weighted-sites-count">-</p>
                                <p class="text-xs text-gray-400 mt-1">有权重数据</p>
                            </div>
                            <div class="w-12 h-12 bg-orange-50 rounded-lg flex items-center justify-center">
                                <i class="fas fa-trophy text-orange-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 系统状态 -->
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all hover:scale-105 p-5 cursor-pointer" onclick="window.location.href='/admin/monitor'">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500 mb-1">系统状态</p>
                                <p class="text-2xl font-bold text-green-600" id="system-load">正常</p>
                                <p class="text-xs text-gray-400 mt-1">运行良好</p>
                            </div>
                            <div class="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
                                <i class="fas fa-heartbeat text-green-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 今日拦截 -->
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-md transition-all hover:scale-105 p-5 cursor-pointer" onclick="window.location.href='/admin/spider-block'">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-500 mb-1">今日拦截</p>
                                <p class="text-2xl font-bold text-gray-900" id="today-blocks">-</p>
                                <p class="text-xs text-gray-400 mt-1">拦截次数</p>
                            </div>
                            <div class="w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center">
                                <i class="fas fa-shield-alt text-red-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                </div>

                <!-- 爬虫访问趋势图 -->
                <div class="bg-white rounded-xl shadow-sm mb-6">
                    <div class="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-chart-line text-gray-500 mr-2"></i>
                            爬虫访问趋势
                        </h2>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">时间范围：</span>
                            <select id="trend-period" onchange="loadSpiderTrend()" class="text-sm border border-gray-300 rounded px-3 py-1">
                                <option value="24h">最近24小时</option>
                                <option value="7d">最近7天</option>
                                <option value="15d" selected>最近15天</option>
                                <option value="30d">最近30天</option>
                            </select>
                            <a href="/admin/spider-stats" class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                                查看详情 <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="relative" style="height: 300px;" id="spider-trend-container">
                            <canvas id="spider-trend-chart"></canvas>
                            <div id="spider-trend-loading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- 活跃站点列表 -->
                <div class="bg-white rounded-xl shadow-sm">
                    <div class="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                            <i class="fas fa-list text-gray-500 mr-2"></i>
                            最近活跃站点
                        </h2>
                        <a href="/admin/sites" class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                            查看全部 <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                    <div class="p-6" id="active-sites">
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-200 rounded w-full mb-4"></div>
                            <div class="h-4 bg-gray-200 rounded w-full mb-4"></div>
                            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 加载仪表板数据
        async function loadDashboard() {
            try {
                // 获取站点统计
                const sitesRes = await fetch('/api/v1/sites?page=1&limit=1');
                const sitesData = await sitesRes.json();
                document.getElementById('total-sites').textContent = sitesData.data?.total || 0;
                
                // 获取活跃站点数
                const activeSitesRes = await fetch('/api/v1/sites?status=active&page=1&limit=1');
                const activeSitesData = await activeSitesRes.json();
                document.getElementById('active-sites-count').textContent = activeSitesData.data?.total || 0;
                
                // 获取权重站点数量
                const weightRes = await fetch('/api/v1/weight/stats');
                const weightData = await weightRes.json();
                if (weightData.success) {
                    document.getElementById('weighted-sites-count').textContent = weightData.data?.weighted_count || 0;
                } else {
                    document.getElementById('weighted-sites-count').textContent = '0';
                }
                
                // 获取今日拦截数
                const blocksRes = await fetch('/api/v1/spider-block/stats?period=today');
                const blocksData = await blocksRes.json();
                if (blocksData.success) {
                    const totalBlocks = blocksData.data?.summary?.total_blocks || 0;
                    document.getElementById('today-blocks').textContent = totalBlocks.toLocaleString();
                } else {
                    document.getElementById('today-blocks').textContent = '0';
                }
                
                // 加载活跃站点
                loadActiveSites();
                
                // 加载爬虫趋势图
                loadSpiderTrend();
                
            } catch (error) {
                console.error('加载数据失败:', error);
                showToast('加载数据失败', 'error');
            }
        }

        // 加载系统信息
        async function loadSystemInfo() {
            try {
                const res = await fetch('/api/v1/system/info');
                const data = await res.json();
                
                const container = document.getElementById('system-info');
                if (data.success && data.data) {
                    const info = data.data;
                    
                    container.innerHTML = `
                        <div class="space-y-3">
                            <div class="flex items-center justify-between py-2 border-b border-gray-50">
                                <span class="text-sm text-gray-500">操作系统</span>
                                <span class="text-sm font-medium text-gray-800">${info.os} ${info.arch}</span>
                            </div>
                            <div class="flex items-center justify-between py-2 border-b border-gray-50">
                                <span class="text-sm text-gray-500">Go版本</span>
                                <span class="text-sm font-medium text-gray-800">${info.go_version}</span>
                            </div>
                            <div class="flex items-center justify-between py-2 border-b border-gray-50">
                                <span class="text-sm text-gray-500">CPU核心</span>
                                <span class="text-sm font-medium text-gray-800">${info.num_cpu} 核</span>
                            </div>
                            <div class="flex items-center justify-between py-2 border-b border-gray-50">
                                <span class="text-sm text-gray-500">运行时间</span>
                                <span class="text-sm font-medium text-gray-800">${info.uptime || '计算中...'}</span>
                            </div>
                            <div class="flex items-center justify-between py-2">
                                <span class="text-sm text-gray-500">网络状态</span>
                                <span class="text-sm font-medium ${info.network?.internet_connected ? 'text-green-600' : 'text-red-600'}">
                                    <i class="fas fa-circle text-xs mr-1"></i>
                                    ${info.network?.internet_connected ? '在线' : '离线'}
                                </span>
                            </div>
                        </div>
                    `;
                } else {
                    container.innerHTML = '<div class="text-red-600">加载系统信息失败</div>';
                }
            } catch (error) {
                console.error('加载系统信息失败:', error);
                document.getElementById('system-info').innerHTML = '<div class="text-red-600">加载失败</div>';
            }
        }

        // 加载活跃站点
        async function loadActiveSites() {
            try {
                const res = await fetch('/api/v1/sites?status=active&page=1&limit=5');
                const data = await res.json();
                
                const container = document.getElementById('active-sites');
                if (data.data?.sites && data.data.sites.length > 0) {
                    container.innerHTML = `
                        <div class="space-y-2">
                            ${data.data.sites.map((site, index) => `
                                <a href="/admin/sites" class="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 transition-colors group">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-${['blue', 'green', 'purple', 'orange', 'pink'][index % 5]}-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-globe text-${['blue', 'green', 'purple', 'orange', 'pink'][index % 5]}-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-800 group-hover:text-blue-600 transition-colors">${site.domain}</p>
                                            <p class="text-sm text-gray-500">${site.target_url}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="px-3 py-1 text-xs font-medium rounded-full ${
                                            site.status === 'active' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                                        }">
                                            ${site.status === 'active' ? '运行中' : '已停止'}
                                        </span>
                                        <i class="fas fa-chevron-right text-gray-400 ml-3 group-hover:text-gray-600 transition-colors"></i>
                                    </div>
                                </a>
                            `).join('')}
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-inbox text-4xl text-gray-300 mb-3"></i>
                            <p class="text-gray-500">暂无活跃站点</p>
                            <a href="/admin/sites" class="inline-block mt-3 text-sm text-blue-600 hover:text-blue-700 font-medium">
                                添加第一个站点 <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('加载站点失败:', error);
                container.innerHTML = '<div class="text-red-600">加载失败</div>';
            }
        }

        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 清理过期缓存
        async function clearExpiredCache() {
            if (!confirm('确定要清理过期缓存吗？')) return;
            
            try {
                const res = await fetch('/api/v1/cache/expired', { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showToast(`成功清理 ${data.data.deleted_count} 个过期文件`, 'success');
                    loadDashboard();
                } else {
                    showToast(data.error || '清理失败', 'error');
                }
            } catch (error) {
                showToast('清理失败: ' + error.message, 'error');
            }
        }

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            // 触发动画
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            // 3秒后移除
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 爬虫趋势图实例
        let spiderTrendChart = null;
        
        // 加载爬虫趋势图
        async function loadSpiderTrend() {
            const periodSelect = document.getElementById('trend-period');
            const period = periodSelect.value;
            const loading = document.getElementById('spider-trend-loading');
            
            // 转换时间周期到range参数（与spider-stats页面保持一致）
            let range = '15days';
            if (period === '24h') {
                range = 'day';
            } else if (period === '7d') {
                range = 'week';
            } else if (period === '15d') {
                range = '15days';
            } else if (period === '30d') {
                range = 'month';
            }
            
            try {
                loading.style.display = 'flex';
                
                // 获取统计数据
                const res = await fetch(`/api/v1/spider-stats?range=${range}&page=1&page_size=100`);
                const data = await res.json();
                
                if (data.success && data.data && data.data.charts) {
                    // 直接使用API返回的图表数据
                    renderSpiderTrend(data.data.charts);
                }
            } catch (error) {
                console.error('加载爬虫趋势失败:', error);
            } finally {
                loading.style.display = 'none';
            }
        }
        
        // 渲染爬虫趋势图
        function renderSpiderTrend(chartsData) {
            const canvas = document.getElementById('spider-trend-chart');
            if (!canvas) {
                console.error('Canvas element "spider-trend-chart" not found');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            
            // 如果已有图表实例，先销毁
            if (spiderTrendChart) {
                spiderTrendChart.destroy();
            }
            
            try {
                // 直接使用爬虫统计页面的图表配置
                spiderTrendChart = new Chart(ctx, {
                    type: 'line',
                    data: chartsData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    boxWidth: 15,
                                    padding: 15,
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: false
                                },
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 11
                                    }
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: false
                                },
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1,
                                    font: {
                                        size: 11
                                    }
                                },
                                grid: {
                                    drawBorder: false,
                                    color: '#e5e7eb'
                                }
                            }
                        },
                        elements: {
                            line: {
                                tension: 0.3,
                                borderWidth: 2
                            },
                            point: {
                                radius: 3,
                                hoverRadius: 5
                            }
                        },
                        layout: {
                            padding: {
                                top: 10,
                                bottom: 10
                            }
                        }
                    }
                });
                console.log('Chart created successfully');
            } catch (error) {
                console.error('Error creating chart:', error);
            }
        }
        
        // 渲染爬虫摘要统计
        function renderSpiderSummary(data) {
            const container = document.getElementById('spider-summary');
            
            // 从图表数据中计算总访问量
            const summary = {};
            if (data.charts && data.charts.datasets) {
                data.charts.datasets.forEach(dataset => {
                    const total = dataset.data.reduce((sum, val) => sum + val, 0);
                    if (total > 0) {
                        summary[dataset.label] = total;
                    }
                });
            }
            
            // 按访问量排序并取前4个
            const sortedSpiders = Object.entries(summary)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 4);
            
            if (sortedSpiders.length > 0) {
                const summaryHtml = sortedSpiders.map(([spider, count]) => {
                    const iconClass = getSpiderIcon(spider);
                    const colorClass = getSpiderColor(spider);
                    
                    return `
                        <div class="text-center">
                            <div class="inline-flex items-center justify-center w-10 h-10 rounded-lg ${colorClass} mb-2">
                                <i class="${iconClass} text-white"></i>
                            </div>
                            <p class="text-xs text-gray-500">${spider}</p>
                            <p class="text-lg font-semibold text-gray-800">${count.toLocaleString()}</p>
                        </div>
                    `;
                }).join('');
                
                container.innerHTML = summaryHtml;
            } else {
                container.innerHTML = `
                    <div class="col-span-4 text-center text-gray-500 text-sm">
                        暂无爬虫访问数据
                    </div>
                `;
            }
        }
        
        // 获取爬虫图标
        function getSpiderIcon(spider) {
            const lowerSpider = spider.toLowerCase();
            const icons = {
                'google': 'fab fa-google',
                '百度': 'fas fa-search',
                'baidu': 'fas fa-search',
                'bing': 'fab fa-microsoft',
                'yandex': 'fas fa-globe',
                '360搜索': 'fas fa-shield-alt',
                '360': 'fas fa-shield-alt',
                '搜狗': 'fas fa-dog',
                'sogou': 'fas fa-dog',
                '神马搜索': 'fas fa-horse',
                '头条搜索': 'fas fa-newspaper',
                'duckduckgo': 'fas fa-duck',
                'yahoo': 'fab fa-yahoo',
                'other': 'fas fa-spider'
            };
            return icons[lowerSpider] || icons[spider] || icons['other'];
        }
        
        // 获取爬虫颜色类
        function getSpiderColor(spider) {
            const lowerSpider = spider.toLowerCase();
            const colors = {
                'google': 'bg-blue-500',
                '百度': 'bg-blue-600',
                'baidu': 'bg-blue-600',
                'bing': 'bg-indigo-500',
                'yandex': 'bg-yellow-500',
                '360搜索': 'bg-green-500',
                '360': 'bg-green-500',
                '搜狗': 'bg-orange-500',
                'sogou': 'bg-orange-500',
                '神马搜索': 'bg-orange-600',
                '头条搜索': 'bg-red-500',
                'duckduckgo': 'bg-orange-400',
                'yahoo': 'bg-purple-500',
                'other': 'bg-gray-500'
            };
            return colors[lowerSpider] || colors[spider] || colors['other'];
        }
        
        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            // 每30秒刷新一次数据
            setInterval(loadDashboard, 30000);
        });
    </script>
    
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
