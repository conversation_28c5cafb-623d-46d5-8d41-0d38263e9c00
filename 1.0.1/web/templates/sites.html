<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站点管理 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js?901"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* 加载动画 */
        .loader {
            border-top-color: #3B82F6;
            -webkit-animation: spinner 1.5s linear infinite;
            animation: spinner 1.5s linear infinite;
        }
        @-webkit-keyframes spinner {
            0% { -webkit-transform: rotate(0deg); }
            100% { -webkit-transform: rotate(360deg); }
        }
        @keyframes spinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">站点管理</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="location.reload()" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 页面操作栏 -->
                <div class="mb-6 flex justify-between items-end">
                    <div class="flex space-x-4">
                        <button onclick="showAddModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i>
                            添加站点
                        </button>
                        
                        <button onclick="refreshSites()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新
                        </button>
                    </div>
                    <div class="flex items-end space-x-4 relative">
                        <div class="relative">
                            <select id="category-filter" onchange="loadSites(); updateClearButton()" 
                                    class="border border-gray-300 rounded-lg px-4 py-2 appearance-none pr-10 bg-white">
                                <option value="">全部分类</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                                <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="relative w-64 h-10">
                            <textarea id="search-input" 
                                   placeholder="搜索域名或目标网址" 
                                   class="absolute top-0 left-0 w-full border border-gray-300 rounded-lg px-4 py-2 pr-10 resize-none transition-all duration-200"
                                   style="min-height: 40px; max-height: 120px; height: 40px; z-index: 20; overflow-y: hidden;"
                                   oninput="adjustSearchHeight(this)"
                                   onfocus="showSearchDropdown()"
                                   onblur="hideSearchDropdown(event)"
                                   onkeydown="handleSearchKeydown(event)">
</textarea>
                            <button onclick="executeSearch()" 
                                    class="absolute right-2 bottom-2.5 text-gray-400 hover:text-blue-600 transition-colors"
                                    style="z-index: 30;"
                                    title="点击搜索">
                                <i class="fas fa-search"></i>
                            </button>
                            <!-- 搜索下拉建议框 -->
                            <div id="search-dropdown" 
                                 class="absolute top-full left-0 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg hidden" 
                                 style="z-index: 100; max-height: 300px; overflow-y: auto;"
                                 onmousedown="event.preventDefault()">
                                <div class="p-2 text-sm text-gray-500">
                                    <div class="py-1 px-2 hover:bg-gray-50 cursor-pointer" onclick="insertSearchSuggestion('8459a.oktestfucxk.com')">8459a.oktestfucxk.com</div>
                                    <div class="py-1 px-2 hover:bg-gray-50 cursor-pointer" onclick="insertSearchSuggestion('8455a.oktestfucxk.com')">8455a.oktestfucxk.com</div>
                                    <div class="py-1 px-2 hover:bg-gray-50 cursor-pointer" onclick="insertSearchSuggestion('8458a.oktestfucxk.com')">8458a.oktestfucxk.com</div>
                                    <div class="py-1 px-2 hover:bg-gray-50 cursor-pointer" onclick="insertSearchSuggestion('8454a.oktestfucxk.com')">8454a.oktestfucxk.com</div>
                                </div>
                            </div>
                        </div>
                        <button id="clear-filters" onclick="clearFilters()" 
                                class="hidden bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg flex items-center text-sm">
                            <i class="fas fa-times mr-1"></i>
                            清除筛选
                        </button>
                    </div>
                </div>

                <!-- 站点列表 -->
                <div class="bg-white rounded-lg shadow mt-6">
                    <!-- 批量操作栏 -->
                    <div id="batch-actions" class="hidden px-6 py-3 bg-blue-50 border-b flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-700">
                                已选择 <span id="selected-count" class="font-bold text-blue-600">0</span> 个站点
                            </span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="showBatchSettings()" 
                                    class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                <i class="fas fa-cog mr-1"></i>
                                批量设置
                            </button>
                            <button onclick="clearSelection()" 
                                    class="px-3 py-1 text-sm border border-gray-300 rounded text-gray-600 hover:bg-gray-50">
                                <i class="fas fa-times mr-1"></i>
                                取消选择
                            </button>
                            <button onclick="copySelectedDomains()" 
                                    class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700">
                                <i class="fas fa-copy mr-1"></i>
                                复制域名
                            </button>
                            
                            <button onclick="copySelectedTargets()" 
                                    class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                <i class="fas fa-link mr-1"></i>
                                复制目标站
                            </button>
                             <button onclick="if(typeof batchOpenSites !== 'undefined'){batchOpenSites()}else{alert('请刷新页面后重试')}" 
                                    class="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700">
                                <i class="fas fa-external-link-alt mr-1"></i>
                                批量打开
                            </button>
                            <button onclick="batchClearCache()" 
                                    class="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700">
                                <i class="fas fa-broom mr-1"></i>
                                批量清除缓存
                            </button>
                            <button onclick="batchDelete()" 
                                    class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                                <i class="fas fa-trash-alt mr-1"></i>
                                批量删除
                            </button>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50 border-b">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input type="checkbox" id="select-all" 
                                               onchange="toggleSelectAll()" 
                                               class="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">域名</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标网址</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">功能配置</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="sortTable('cache_status')">
                                        缓存状态 <i class="fas fa-sort text-gray-400" id="sort-icon-cache_status"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="sortTable('sitemap_count')">
                                        地图 <i class="fas fa-sort text-gray-400" id="sort-icon-sitemap_count"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="sortTable('count_404')">
                                        404 <i class="fas fa-sort text-gray-400" id="sort-icon-count_404"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="sites-tbody" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="10" class="px-6 py-12 text-center text-gray-500">
                                        <div class="flex justify-center">
                                            <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-12 w-12"></div>
                                        </div>
                                        <p class="mt-4">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t bg-gray-50 flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <label class="text-sm text-gray-700">每页显示</label>
                            <select id="page-size" onchange="changePageSize()" 
                                    class="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                                <option value="200">200</option>
                                <option value="500">500</option>
                                <option value="1000">1000</option>
                            </select>
                            <span class="text-sm text-gray-600">
                                显示第 <span id="start-item" class="font-medium">1</span> - 
                                <span id="end-item" class="font-medium">10</span> 条，
                                共 <span id="total-items" class="font-medium">0</span> 条
                            </span>
                        </div>
                        <div class="flex space-x-2" id="pagination"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <!-- 加载遮罩 -->
    <div id="loading-overlay" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center h-full">
            <div class="bg-white rounded-lg p-8 flex flex-col items-center">
                <div class="loader ease-linear rounded-full border-4 border-t-4 border-gray-200 h-12 w-12 mb-4"></div>
                <p class="text-gray-700">加载中...</p>
            </div>
        </div>
    </div>

    <!-- 添加/编辑站点模态框 -->
    <div id="site-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden" style="z-index: 9999;">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl relative" style="max-height: 90vh; display: flex; flex-direction: column;">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold" id="modal-title">添加站点</h3>
                    <button onclick="hideModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 内容区域 -->
                <div style="flex: 1; overflow-y: auto;">
                    <form id="site-form">
                    <input type="hidden" id="site-id">
                    
                    <!-- 基础信息（始终显示） -->
                    <div class="px-6 py-4 border-b bg-gray-50">
                        <div class="grid grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">域名 *</label>
                                <input type="text" id="domain" name="domain" required
                                       class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="example.com">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">目标网址 *</label>
                                <input type="text" id="target_url" name="target_url" required
                                       class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="example.com 或 https://example.com">
                                <p class="text-xs text-gray-500 mt-1">支持自动识别协议，可直接输入域名</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">站点分类</label>
                                <select id="category_id" name="category_id"
                                        class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">选择分类</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 子域名设置 -->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                子域名前缀
                                <span class="text-xs text-gray-500 ml-1">（可选，多个用逗号分隔，@表示裸域名）</span>
                            </label>
                            <input type="text" id="alias_prefixes" name="alias_prefixes"
                                   class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="例如: www,m,wap,@">
                            <p class="text-xs text-gray-500 mt-1">输入子域名前缀，如 www,m,wap，@表示不带www的裸域名</p>
                        </div>
                    </div>
                    
                    <!-- 标签页导航 -->
                    <div class="border-b bg-white">
                        <nav class="flex px-6">
                            <button type="button" onclick="switchTab('seo')" id="tab-seo"
                                    class="px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none">
                                SEO优化
                            </button>
                            <button type="button" onclick="switchTab('basic')" id="tab-basic" 
                                    class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 focus:outline-none">
                                基础配置
                            </button>
                            <button type="button" onclick="switchTab('injection')" id="tab-injection"
                                    class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 focus:outline-none">
                                内容注入
                            </button>
                            <!-- 缓存设置标签已移除 -->
                            <button type="button" onclick="switchTab('security')" id="tab-security"
                                    class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 focus:outline-none">
                                安全设置
                            </button>
                        </nav>
                    </div>
                    
                    <!-- 标签页内容 -->
                    <div class="px-6 py-4">
                        <!-- SEO优化标签页 -->
                        <div id="tab-content-seo">
                            <div class="space-y-4">
                                <h4 class="text-base font-medium text-gray-800 mb-3">首页SEO设置</h4>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">首页标题</label>
                                    <input type="text" id="home_title" name="home_title"
                                           class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="自定义首页标题">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">首页描述</label>
                                    <textarea id="home_description" name="home_description" rows="3"
                                              class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                              placeholder="自定义首页描述"></textarea>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">首页关键词</label>
                                    <input type="text" id="home_keywords" name="home_keywords"
                                           class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="关键词1,关键词2,关键词3（逗号分隔）">
                                </div>
                                
                                <!-- 企业名称配置 -->
                                <div class="border-t pt-4">
                                    <h4 class="text-base font-medium text-gray-800 mb-3">企业名称设置</h4>
                                    
                                    <div class="mb-3">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enable_company_name" name="enable_company_name" 
                                                   onchange="toggleCompanyNameSettings()" class="mr-2">
                                            <span class="text-sm font-medium">启用企业名称</span>
                                        </label>
                                        <p class="text-xs text-gray-500 ml-6 mt-1">
                                            启用后将在首页标题上方显示企业名称
                                        </p>
                                    </div>
                                    
                                    <div id="company-name-settings" class="hidden space-y-3">
                                        <!-- 企业名称库选择 -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">选择企业名称库</label>
                                            <select id="company_library_id" name="company_library_id" 
                                                    onchange="onCompanyLibraryChange()"
                                                    class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="">请选择企业名称库</option>
                                            </select>
                                            <p class="text-xs text-gray-500 mt-1">
                                                新建站点时将从选中的库中随机选择一个企业名称
                                            </p>
                                        </div>
                                        
                                        <!-- 当前企业名称（编辑时显示） -->
                                        <div id="current-company-name-div" class="hidden">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">当前企业名称</label>
                                            <input type="text" id="company_name" name="company_name"
                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                   placeholder="企业名称">
                                            <p class="text-xs text-gray-500 mt-1">
                                                可手动修改企业名称，留空将重新随机选择
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 基础配置标签页 -->
                        <div id="tab-content-basic" class="hidden space-y-4">
                            <!-- 基础设置 -->
                            <div class="space-y-3">
                                <h5 class="text-sm font-medium text-gray-700 border-b pb-1">基础设置</h5>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">站点状态</label>
                                        <select id="status" name="status"
                                                class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="active">活跃</option>
                                            <option value="inactive">未激活</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">缓存深度</label>
                                        <input type="number" id="crawl_depth" name="crawl_depth" min="1" max="10" value="2"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 功能开关 -->
                            <div class="space-y-3">
                                <h5 class="text-sm font-medium text-gray-700 border-b pb-1">功能开关</h5>
                                <div class="flex flex-wrap gap-x-6 gap-y-3">
                                    <!-- 缓存现在永久有效，移除缓存开关 -->
                                    
                                    <label class="flex items-center">
                                        <input type="checkbox" id="enable_preload" name="enable_preload" class="mr-2">
                                        <span class="text-sm">预加载下一层链接</span>
                                    </label>
                                    
                                    <label class="flex items-center">
                                        <input type="checkbox" id="download_external_resources" name="download_external_resources" class="mr-2">
                                        <span class="text-sm">下载外部资源</span>
                                    </label>
                                    
                                    <label class="flex items-center">
                                        <input type="checkbox" id="enable_https_check" name="enable_https_check" class="mr-2">
                                        <span class="text-sm">HTTPS证书检查</span>
                                    </label>
                                    
                                    <label class="flex items-center">
                                        <input type="checkbox" id="enable_traditional_convert" name="enable_traditional_convert" class="mr-2">
                                        <span class="text-sm">简繁转换</span>
                                    </label>
                                    
                                    <label class="flex items-center">
                                        <input type="checkbox" id="enable_analytics" name="enable_analytics" class="mr-2">
                                        <span class="text-sm">启用统计代码</span>
                                    </label>
                                    
                                    <label class="flex items-center">
                                        <input type="checkbox" id="filter_external_links" name="filter_external_links" class="mr-2">
                                        <span class="text-sm">过滤外部链接</span>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- 拼音设置区域 -->
                            <div class="space-y-3">
                                <h5 class="text-sm font-medium text-gray-700 border-b pb-1">拼音标注设置</h5>
                                
                                <!-- 拼音设置模式选择 -->
                                <div class="mb-3">
                                    <select id="pinyin_mode" name="pinyin_mode" class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="togglePinyinMode()">
                                        <option value="global">使用全局设置</option>
                                        <option value="enable">独立开启</option>
                                        <option value="disable">独立关闭</option>
                                    </select>
                                </div>
                                
                                <!-- 站点独立的拼音设置 -->
                                <div id="pinyin_custom_settings" style="display:none;" class="space-y-3 p-3 bg-gray-50 rounded">
                                    
                                    <!-- 拼音特殊字符设置 -->
                                    <div id="pinyin_special_chars_settings" class="ml-6 space-y-3" style="display:none;">
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enable_pinyin_special_chars" name="enable_pinyin_special_chars" class="mr-2">
                                            <span class="text-sm">插入特殊字符</span>
                                        </label>
                                        
                                        <div class="ml-6 space-y-2">
                                            <div class="flex items-center space-x-2">
                                                <label class="text-sm">插入比例：</label>
                                                <input type="number" id="pinyin_special_chars_ratio" name="pinyin_special_chars_ratio" 
                                                       min="0" max="100" value="30" class="w-20 px-2 py-1 border rounded">
                                                <span class="text-sm">%</span>
                                            </div>
                                            
                                            <div class="text-xs text-gray-500 mt-2">
                                                <i class="fas fa-info-circle mr-1"></i>
                                                特殊字符从<a href="/admin/settings/content" class="text-blue-500 underline">全局设置</a>中继承
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 全局设置提示 -->
                                <div id="pinyin_global_hint" class="p-3 bg-blue-50 rounded text-sm text-blue-600">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    使用全局拼音设置，请在 <a href="/admin/settings/content" class="underline">系统设置-内容注入</a> 中配置
                                </div>
                            </div>
                        </div>
                        
                        <!-- 缓存设置标签页已移除，缓存现在永久有效 -->
                        <div id="tab-content-cache-removed" class="hidden space-y-4" style="display:none!important">
                            <div class="grid grid-cols-1 gap-4">
                            <div class="bg-blue-50 border border-blue-200 rounded p-3 text-sm">
                                <p class="font-medium text-blue-800">缓存说明
                                        </p>
                                        <ul class="space-y-1 text-blue-700 ml-4">
                                            <li>• 本地磁盘缓存：存储所有页面和资源，支持大容量存储</li>
                                            <li>• Redis内存缓存：仅缓存首页（/），提供极速访问</li>
                                            <li>• 首页识别：路径为 / 或 /index.html 的请求</li>
                                            <li>• 内页全部使用磁盘缓存，避免内存占用过大</li>
                                        </ul>
                                    </div>
                                    <label class="flex items-center mb-3">
                                        <input type="checkbox" id="use_global_cache" name="use_global_cache" checked class="mr-2" onchange="toggleCacheInputs()">
                                        <span class="text-sm text-gray-600">使用全局缓存设置</span>
                                    </label>
                                    <div id="custom-cache-settings" class="space-y-3 opacity-50">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">站点最大缓存大小（MB）</label>
                                            <input type="number" id="cache_max_size" name="cache_max_size" value="1024" min="100" max="10240" disabled
                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 disabled:text-gray-500">
                                            <p class="text-xs text-gray-500 mt-1">限制该站点的本地磁盘缓存占用空间</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">首页缓存时间（分钟）</label>
                                            <input type="number" id="cache_home_ttl" name="cache_home_ttl" value="30" min="5" max="1440" disabled
                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 disabled:text-gray-500">
                                            <p class="text-xs text-gray-500 mt-1">首页更新频率较高，建议设置较短的缓存时间</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-1">其他页面缓存时间（分钟）</label>
                                            <input type="number" id="cache_other_ttl" name="cache_other_ttl" value="1440" min="30" max="10080" disabled
                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 disabled:text-gray-500">
                                            <p class="text-xs text-gray-500 mt-1">内页更新频率较低，可以设置较长的缓存时间（最长7天）</p>
                                        </div>
                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" id="enable_redis_cache" name="enable_redis_cache" checked disabled class="mr-2 disabled:opacity-50">
                                                <span class="text-sm font-medium text-gray-600">启用Redis缓存加速首页</span>
                                            </label>
                                            <p class="text-xs text-gray-500 mt-1 ml-6">开启后首页将使用Redis内存缓存，访问速度更快</p>
                                        </div>
                                    </div>
                            </div>
                        </div>
                        
                        <!-- 内容注入标签页 -->
                        <div id="tab-content-injection" class="hidden space-y-4">
                                <div class="grid grid-cols-2 gap-6 px-4">
                                    <div class="space-y-3">
                                        <h5 class="text-sm font-medium text-gray-700 border-b pb-1">基础注入</h5>
                                        <!-- 关键词注入设置 -->
                                        <div class="border rounded p-2 bg-gray-50">
                                            <label class="flex items-center mb-2">
                                                <input type="checkbox" id="enable_keyword" name="enable_keyword" onchange="toggleKeywordsGroup()" class="mr-2">
                                                <span class="text-sm font-medium">启用关键词注入</span>
                                            </label>
                                            <div id="keywords-group" class="ml-6 space-y-3 hidden">
                                                <!-- SEO关键词注入设置 -->
                                                <div class="space-y-3 mt-3 p-3 bg-blue-50 rounded">
                                                    <div class="text-sm font-medium text-blue-700 mb-2">SEO关键词注入设置</div>
                                                    
                                                    <!-- 标题设置 -->
                                                    <div class="p-3 bg-white rounded border border-blue-200">
                                                        <div class="flex items-center justify-between mb-2">
                                                            <label class="flex items-center text-sm font-medium text-gray-700">
                                                                <input type="checkbox" id="keyword_inject_title" checked class="mr-2">
                                                                <span>注入到标题</span>
                                                            </label>
                                                        </div>
                                                        <div class="ml-6 space-y-2">
                                                            <div>
                                                                <label class="text-xs text-gray-600">选择标题关键词库:</label>
                                                                <div id="title-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-gray-50 rounded border max-h-20 overflow-y-auto">
                                                                    <div class="text-gray-500 text-xs">加载中...</div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <label class="text-xs text-gray-600">标题模板:</label>
                                                                <input type="text" id="keyword_title_template" placeholder="{keyword1}-{keyword2}-{original}" 
                                                                       value="{keyword1}-{original}" 
                                                                       class="w-full text-xs border border-gray-300 rounded px-2 py-1 mt-1">
                                                                <span class="text-xs text-gray-400">可用变量: {keyword1-5}, {original}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- Meta关键词设置 -->
                                                    <div class="p-3 bg-white rounded border border-blue-200">
                                                        <div class="flex items-center justify-between mb-2">
                                                            <label class="flex items-center text-sm font-medium text-gray-700">
                                                                <input type="checkbox" id="keyword_inject_meta" checked class="mr-2">
                                                                <span>注入到Meta关键词</span>
                                                            </label>
                                                        </div>
                                                        <div class="ml-6 space-y-2">
                                                            <div>
                                                                <label class="text-xs text-gray-600">选择Meta关键词库:</label>
                                                                <div id="meta-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-gray-50 rounded border max-h-20 overflow-y-auto">
                                                                    <div class="text-gray-500 text-xs">加载中...</div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <label class="text-xs text-gray-600">Meta关键词模板:</label>
                                                                <input type="text" id="keyword_meta_template" placeholder="{keyword1},{keyword2},{keyword3}" 
                                                                       value="{keyword1},{keyword2},{keyword3}" 
                                                                       class="w-full text-xs border border-gray-300 rounded px-2 py-1 mt-1">
                                                                <span class="text-xs text-gray-400">可用变量: {keyword1-10}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- 描述设置 -->
                                                    <div class="p-3 bg-white rounded border border-blue-200">
                                                        <div class="flex items-center justify-between mb-2">
                                                            <label class="flex items-center text-sm font-medium text-gray-700">
                                                                <input type="checkbox" id="keyword_inject_desc" checked class="mr-2">
                                                                <span>注入到描述</span>
                                                            </label>
                                                        </div>
                                                        <div class="ml-6 space-y-2">
                                                            <div>
                                                                <label class="text-xs text-gray-600">选择描述关键词库:</label>
                                                                <div id="desc-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-gray-50 rounded border max-h-20 overflow-y-auto">
                                                                    <div class="text-gray-500 text-xs">加载中...</div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <label class="text-xs text-gray-600">描述模板:</label>
                                                                <input type="text" id="keyword_desc_template" placeholder="{keyword1}是专业的{keyword2}服务商" 
                                                                       value="{keyword1}是专业的{keyword2}服务商" 
                                                                       class="w-full text-xs border border-gray-300 rounded px-2 py-1 mt-1">
                                                                <span class="text-xs text-gray-400">可用变量: {keyword1-10}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 正文注入设置 -->
                                                <div class="mt-3">
                                                    <!-- 正文关键词库选择 -->
                                                    <div class="mb-3">
                                                        <label class="text-xs text-gray-600">选择关键词库（正文注入）:</label>
                                                        <div id="keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                            <div class="text-gray-500 text-xs">加载关键词库中...</div>
                                                        </div>
                                                        <textarea id="keywords" name="keywords" class="hidden"></textarea>
                                                    </div>
                                                    
                                                    <label class="text-xs text-gray-600 mb-1 block">正文注入位置:</label>
                                                    <div class="flex flex-wrap gap-x-4 gap-y-2 text-xs">
                                                        <label class="flex items-center">
                                                            <input type="checkbox" id="keyword_inject_body" checked class="mr-1">
                                                            <span>正文段落</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" id="keyword_inject_h1" class="mr-1">
                                                            <span>H1标题</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" id="keyword_inject_h2" class="mr-1">
                                                            <span>H2标题</span>
                                                        </label>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" id="keyword_inject_alt" class="mr-1">
                                                            <span>图片Alt</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                
                                                <!-- 注入参数 -->
                                                <div>
                                                    <label class="text-xs text-gray-600 mb-1 block">控制参数:</label>
                                                    <div class="grid grid-cols-2 gap-2">
                                                        <div>
                                                            <label class="text-xs text-gray-500">每页数量</label>
                                                            <input type="number" id="keyword_max_per_page" value="10" min="1" max="50"
                                                                   class="w-full text-xs border rounded px-1 py-0.5">
                                                        </div>
                                                        <div>
                                                            <label class="text-xs text-gray-500">注入概率(%)</label>
                                                            <input type="number" id="keyword_inject_ratio" value="30" min="1" max="100"
                                                                   class="w-full text-xs border rounded px-1 py-0.5">
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 高级选项折叠 -->
                                                <details class="text-xs">
                                                    <summary class="cursor-pointer text-gray-600 hover:text-gray-800">更多选项...</summary>
                                                    <div class="mt-2 space-y-2">
                                                        <label class="flex items-center">
                                                            <input type="checkbox" id="keyword_inject_hidden" class="mr-1">
                                                            <span>隐藏元素注入</span>
                                                        </label>
                                                        <div class="grid grid-cols-2 gap-2">
                                                            <div>
                                                                <label class="text-xs text-gray-500">最小字数</label>
                                                                <input type="number" id="keyword_min_word_count" value="20" min="10" max="200"
                                                                       class="w-full text-xs border rounded px-1 py-0.5">
                                                            </div>
                                                            <div>
                                                                <label class="text-xs text-gray-500">密度(%)</label>
                                                                <input type="number" id="keyword_density" value="2" min="0" max="10" step="0.5"
                                                                       class="w-full text-xs border rounded px-1 py-0.5">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </details>
                                            </div>
                                        </div>
                                
                                        <!-- 结构注入设置 -->
                                        <div class="border rounded p-2 bg-gray-50">
                                            <label class="flex items-center mb-2">
                                                <input type="checkbox" id="enable_structure" name="enable_structure" onchange="toggleStructureSettings()" class="mr-2">
                                                <span class="text-sm font-medium">启用结构注入</span>
                                            </label>
                                            <div id="structure-settings" class="ml-6 space-y-2 hidden">
                                                <!-- 结构注入关键词库选择 -->
                                                <div>
                                                    <label class="text-xs text-gray-600 block mb-1">选择关键词库:</label>
                                                    <div id="structure-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                        <div class="text-gray-500 text-xs">加载中...</div>
                                                    </div>
                                                </div>
                                                <!-- 注入数量范围 -->
                                                <div class="grid grid-cols-2 gap-2">
                                                    <div>
                                                        <label for="structure_min_per_page" class="text-xs text-gray-600">最少注入数量:</label>
                                                        <input type="number" id="structure_min_per_page" name="structure_min_per_page" value="10" min="1" max="100" class="w-full text-xs border rounded px-1 py-0.5">
                                                    </div>
                                                    <div>
                                                        <label for="structure_max_per_page" class="text-xs text-gray-600">最多注入数量:</label>
                                                        <input type="number" id="structure_max_per_page" name="structure_max_per_page" value="20" min="1" max="100" class="w-full text-xs border rounded px-1 py-0.5">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 伪原创设置 -->
                                        <div class="border rounded p-2 bg-gray-50">
                                            <label class="flex items-center mb-2">
                                                <input type="checkbox" id="enable_pseudo" name="enable_pseudo" onchange="togglePseudoSettings()" class="mr-2">
                                                <span class="text-sm font-medium">启用伪原创（对body内容生效）</span>
                                            </label>
                                            <div id="pseudo-settings" class="ml-6 space-y-2 hidden">
                                                <label class="text-xs text-gray-600">选择伪原创词库:</label>
                                                <div id="pseudo-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                    <div class="text-gray-500 text-xs">加载词库中...</div>
                                                </div>
                                            </div>
                                        </div>
                                </div>
                                
                                <div class="space-y-3">
                                    <h5 class="text-sm font-medium text-gray-700 border-b pb-1">高级注入</h5>
                                        <div class="space-y-2">
                                            <!-- Unicode转码设置 -->
                                            <div class="border rounded p-2 bg-gray-50">
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" id="enable_unicode" name="enable_unicode" class="mr-2" onchange="toggleUnicodeOptions()">
                                                    <span class="text-sm font-medium">启用Unicode转码</span>
                                                </label>
                                                <div id="unicode-options" class="ml-6 space-y-2 hidden">
                                                    <div class="space-y-1">
                                                        <label class="text-xs text-gray-600">应用范围:</label>
                                                        <select id="unicode_scope" name="unicode_scope" class="w-full text-sm border rounded px-2 py-1">
                                                            <option value="homepage">仅首页</option>
                                                            <option value="all">全站</option>
                                                        </select>
                                                    </div>
                                                    <div class="flex flex-wrap gap-x-4 gap-y-2">
                                                        <label class="text-xs text-gray-600">转码选项:</label>
                                                        <label class="inline-flex items-center">
                                                            <input type="checkbox" id="enable_unicode_title" name="enable_unicode_title" class="mr-1">
                                                            <span class="text-xs">标题</span>
                                                        </label>
                                                        <label class="inline-flex items-center">
                                                            <input type="checkbox" id="enable_unicode_keywords" name="enable_unicode_keywords" class="mr-1">
                                                            <span class="text-xs">关键词</span>
                                                        </label>
                                                        <label class="inline-flex items-center">
                                                            <input type="checkbox" id="enable_unicode_desc" name="enable_unicode_desc" class="mr-1">
                                                            <span class="text-xs">描述</span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 随机字符串注入设置 -->
                                        <div class="border rounded p-2 bg-gray-50">
                                            <label class="flex items-center mb-2">
                                                <input type="checkbox" id="enable_random_string" name="enable_random_string" onchange="toggleRandomStringSettings()" class="mr-2">
                                                <span class="text-sm font-medium">启用随机字符串注入</span>
                                            </label>
                                            <div id="random-string-settings" class="ml-6 space-y-2 hidden">
                                                <label class="text-xs text-gray-600">随机字符串长度:</label>
                                                <input type="number" id="random_string_length" name="random_string_length" min="3" max="10" value="4"
                                                       class="w-full text-xs border rounded px-1 py-0.5"
                                                       placeholder="建议4-6位">
                                            </div>
                                        </div>
                                        
                                        <!-- H1标签注入设置（仅首页） -->
                                        <div class="border rounded p-2 bg-gray-50">
                                            <label class="flex items-center mb-2">
                                                <input type="checkbox" id="enable_h1_tag" name="enable_h1_tag" onchange="toggleH1TagSettings()" class="mr-2">
                                                <span class="text-sm font-medium">仅首页插入H1标签</span>
                                            </label>
                                            <div id="h1-tag-settings" class="ml-6 space-y-2 hidden">
                                                <div class="text-xs text-gray-500 mb-2">
                                                    将使用"首页SEO设置"中的标题作为H1标签内容
                                                </div>
                                                <div>
                                                    <label class="text-xs text-gray-600">注入位置:</label>
                                                    <select id="h1_tag_position" name="h1_tag_position"
                                                            class="w-full text-xs border rounded px-1 py-0.5">
                                                        <option value="both">顶部和底部</option>
                                                        <option value="top">仅顶部</option>
                                                        <option value="bottom">仅底部</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 隐藏HTML注入设置 -->
                                        <div class="border rounded p-2 bg-gray-50">
                                            <label class="flex items-center mb-2">
                                                <input type="checkbox" id="enable_hidden_html" name="enable_hidden_html" onchange="toggleHiddenHTMLSettings()" class="mr-2">
                                                <span class="text-sm font-medium">启用隐藏HTML注入</span>
                                            </label>
                                            <div id="hidden-html-settings" class="ml-6 space-y-2 hidden">
                                                <div class="grid grid-cols-2 gap-2">
                                                    <div>
                                                        <label class="text-xs text-gray-600">元素数量:</label>
                                                        <input type="number" id="hidden_html_length" name="hidden_html_length" min="10" max="200" value="50"
                                                               class="w-full text-xs border rounded px-1 py-0.5"
                                                               placeholder="10-200个">
                                                    </div>
                                                    <div>
                                                        <label class="text-xs text-gray-600">注入位置:</label>
                                                        <select id="hidden_html_position" name="hidden_html_position"
                                                                class="w-full text-xs border rounded px-1 py-0.5">
                                                            <option value="both">顶部和底部</option>
                                                            <option value="top">仅顶部</option>
                                                            <option value="bottom">仅底部</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="hidden_html_random_id" name="hidden_html_random_id" checked class="mr-1">
                                                    <span class="text-xs">使用随机ID</span>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <!-- 首页关键词注入设置 -->
                                        <div class="border rounded p-2 bg-gray-50">
                                            <label class="flex items-center mb-2">
                                                <input type="checkbox" id="enable_home_keyword_inject" name="enable_home_keyword_inject" onchange="toggleHomeKeywordSettings()" class="mr-2">
                                                <span class="text-sm font-medium">启用首页关键词注入</span>
                                            </label>
                                            <div id="home-keyword-settings" class="ml-6 space-y-2 hidden">
                                                <div class="grid grid-cols-2 gap-2">
                                                    <div>
                                                        <label class="text-xs text-gray-600">注入数量:</label>
                                                        <input type="number" id="home_keyword_inject_count" name="home_keyword_inject_count" min="1" max="100" value="10"
                                                               class="w-full text-xs border rounded px-1 py-0.5"
                                                               placeholder="1-100个">
                                                    </div>
                                                    <div>
                                                        <label class="flex items-center">
                                                            <input type="checkbox" id="enable_home_keyword_unicode" name="enable_home_keyword_unicode" class="mr-1">
                                                            <span class="text-xs">Unicode转码</span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mt-2">
                                                    <label class="text-xs text-gray-600 block mb-1">选择关键词库:</label>
                                                    <div id="home-keyword-libraries-container" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                        <div class="text-gray-500 text-xs">加载中...</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                </div>
                            </div>
                            
                            <!-- 自定义规则 -->
                            <div class="border-t pt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">自定义替换规则（JSON格式）</label>
                                <textarea id="custom_rules" name="custom_rules" rows="3"
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder='{"old_text": "new_text"}'></textarea>
                            </div>
                        </div>
                        
                        <!-- 安全设置标签页 -->
                        <div id="tab-content-security" class="hidden space-y-4">
                            <div class="grid grid-cols-2 gap-6 px-4">
                                <!-- 左侧列 -->
                                <div class="space-y-4">
                                    <!-- 站点UA判断 -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                            <i class="fas fa-user-shield text-blue-500 mr-2"></i>站点UA判断
                                        </h5>
                                        <p class="text-xs text-gray-500 mb-3">选择此站点的UA判断策略</p>
                                        <select id="ua_check_mode" name="ua_check_mode" class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="toggleUASettingsMode()">
                                            <option value="global">使用全局设置</option>
                                            <option value="enable">独立开启UA判断</option>
                                            <option value="disable">独立关闭UA判断</option>
                                        </select>
                                        <div id="ua-settings" class="hidden mt-3 space-y-2">
                                            <input type="text" id="allowed_ua" name="allowed_ua" 
                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                   placeholder="允许的UA关键词（如：google|baidu）">
                                            <!-- 标签说明 -->
                                            <div class="bg-blue-50 border border-blue-200 rounded p-2">
                                                <p class="text-xs font-semibold text-blue-900 mb-1">支持的标签：</p>
                                                <div class="grid grid-cols-2 gap-1 text-xs text-blue-700">
                                                    <div><code class="bg-white px-1 rounded">{title}</code> - 首页标题</div>
                                                    <div><code class="bg-white px-1 rounded">{description}</code> - 首页描述</div>
                                                    <div><code class="bg-white px-1 rounded">{keywords}</code> - 首页关键词</div>
                                                    <div><code class="bg-white px-1 rounded">{analytics}</code> - 统计JS</div>
                                                    <div><code class="bg-white px-1 rounded">{company}</code> - 企业名称</div>
                                                    <div><code class="bg-white px-1 rounded">{domain}</code> - 当前域名</div>
                                                    <div><code class="bg-white px-1 rounded">{year}</code> - 当前年份</div>
                                                    <div><code class="bg-white px-1 rounded">{date}</code> - 当前日期</div>
                                                </div>
                                            </div>
                                            <textarea id="non_spider_html" name="non_spider_html" rows="4"
                                                      class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                      placeholder="非爬虫访问时显示的HTML（支持上述标签）"></textarea>
                                        </div>
                                    </div>
                                    
                                    <!-- 来源判断设置 -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                            <i class="fas fa-link text-purple-500 mr-2"></i>来源判断
                                        </h5>
                                        <div class="space-y-2">
                                            <select id="referer_check_mode" name="referer_check_mode" class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="toggleRefererSettings()">
                                                <option value="global">使用全局设置</option>
                                                <option value="enable">独立设置</option>
                                                <option value="disable">关闭</option>
                                            </select>
                                            <div id="referer-settings" class="hidden space-y-2 mt-3">
                                                <input type="text" id="allowed_referers" name="allowed_referers" 
                                                       class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                       placeholder="允许的来源域名（如：*.google.com|www.baidu.com）">
                                                <div class="grid grid-cols-2 gap-2">
                                                    <input type="number" id="referer_block_code" name="referer_block_code" 
                                                           class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                           placeholder="状态码（默认403）" min="100" max="599" value="403">
                                                    <span class="text-xs text-gray-500 self-center">拒绝状态码</span>
                                                </div>
                                                <textarea id="referer_block_html" name="referer_block_html" rows="3"
                                                          class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                          placeholder="拒绝时显示的HTML（支持 {domain}, {referer}, {ip} 标签）"></textarea>
                                            </div>
                                            <p class="text-xs text-gray-500">控制允许哪些来源域名访问此站点</p>
                                        </div>
                                    </div>
                                    
                                    <!-- 域名跳转设置 -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                            <i class="fas fa-directions text-green-500 mr-2"></i>域名跳转
                                        </h5>
                                        <div class="space-y-2">
                                            <label class="block text-sm text-gray-700">@ 跳转到 www</label>
                                            <select id="redirect_www" name="redirect_www" class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option value="">使用全局设置</option>
                                                <option value="true">启用</option>
                                                <option value="false">禁用</option>
                                            </select>
                                            <p class="text-xs text-gray-500">当用户访问 example.com 时自动跳转到 www.example.com</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 右侧列 -->
                                <div class="space-y-4">
                                    <!-- 爬虫屏蔽设置 -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                            <i class="fas fa-shield-alt text-red-500 mr-2"></i>爬虫屏蔽设置
                                        </h5>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enable_spider_block" name="enable_spider_block" class="mr-2" onchange="toggleSpiderBlockSettings()">
                                            <span class="text-sm">启用爬虫屏蔽</span>
                                        </label>
                                        <div id="spider-block-config" class="hidden mt-3 space-y-2">
                                            <label class="flex items-center mb-2">
                                                <input type="checkbox" id="use_global_spider_ua" name="use_global_spider_ua" checked class="mr-2" onchange="toggleSpiderUAInput()">
                                                <span class="text-sm text-gray-600">使用全局爬虫UA规则</span>
                                            </label>
                                            <input type="text" id="custom_spider_ua" name="custom_spider_ua" disabled
                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm disabled:bg-gray-100 disabled:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                   placeholder="自定义爬虫UA（如：google|baidu|bing）">
                                            <textarea id="spider_block_403_template" name="spider_block_403_template" rows="3"
                                                      class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                      placeholder="被屏蔽时显示的内容（可选）"></textarea>
                                        </div>
                                    </div>
                                    
                                    <!-- Sitemap设置 -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                            <i class="fas fa-sitemap text-orange-500 mr-2"></i>Sitemap设置
                                        </h5>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="enable_sitemap" name="enable_sitemap" class="mr-2" onchange="toggleSitemapSettings()">
                                            <span class="text-sm">启用Sitemap</span>
                                        </label>
                                        <div id="sitemap-config" class="hidden mt-3 space-y-3">
                                            <div class="grid grid-cols-2 gap-3">
                                                <div>
                                                    <label class="block text-xs text-gray-600 mb-1">更新间隔（分钟）</label>
                                                    <input type="number" id="sitemap_update_interval" name="sitemap_update_interval" value="60"
                                                           class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                           placeholder="60">
                                                </div>
                                                <div>
                                                    <label class="block text-xs text-gray-600 mb-1">优先级</label>
                                                    <input type="number" id="sitemap_priority" name="sitemap_priority" value="0.5" min="0" max="1" step="0.1"
                                                           class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                           placeholder="0.5">
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">更新频率</label>
                                                <select id="sitemap_changefreq" name="sitemap_changefreq"
                                                        class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    <option value="always">always</option>
                                                    <option value="hourly">hourly</option>
                                                    <option value="daily" selected>daily</option>
                                                    <option value="weekly">weekly</option>
                                                    <option value="monthly">monthly</option>
                                                    <option value="yearly">yearly</option>
                                                    <option value="never">never</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-xs text-gray-600 mb-1">最大URL数量</label>
                                                <input type="number" id="sitemap_max_urls" name="sitemap_max_urls" value="50000"
                                                       class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                       placeholder="50000" min="100" max="50000">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                </div>
                
                <!-- 底部按钮（固定在模态框底部） -->
                <div class="px-6 py-4 border-t bg-gray-50 rounded-b-lg">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            <!-- 左侧可以添加提示信息 -->
                        </div>
                        <div class="flex space-x-3">
                            <button type="button" onclick="hideModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 bg-white transition-colors">
                                取消
                            </button>
                            <button type="submit" form="site-form"
                                    class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                保存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        // 版本号：2.0.0 - 修复批量添加拼音设置
        const SITES_JS_VERSION = '2.0.0';
        
        // 从 localStorage 读取保存的页码和页面大小
        let currentPage = parseInt(localStorage.getItem('sites_current_page')) || 1;
        let pageSize = parseInt(localStorage.getItem('sites_page_size')) || 20;
        let totalItems = 0;
        let selectedSites = new Set();
        let currentSortColumn = localStorage.getItem('sites_sort_column') || '';
        let currentSortOrder = localStorage.getItem('sites_sort_order') || 'asc';

        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 5000);

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 显示/隐藏加载遮罩
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
        }
        
        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }

        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.site-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                const row = checkbox.closest('tr');
                if (selectAll.checked) {
                    selectedSites.add(checkbox.value);
                    row.classList.add('bg-blue-50');
                } else {
                    selectedSites.delete(checkbox.value);
                    row.classList.remove('bg-blue-50');
                }
            });
            
            updateBatchActions();
        }
        
        // 单个站点选择变化
        function onSiteCheckChange() {
            const checkboxes = document.querySelectorAll('.site-checkbox');
            selectedSites.clear();
            
            checkboxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                if (checkbox.checked) {
                    selectedSites.add(checkbox.value);
                    row.classList.add('bg-blue-50');
                } else {
                    row.classList.remove('bg-blue-50');
                }
            });
            
            // 更新全选框状态
            const selectAll = document.getElementById('select-all');
            const allChecked = checkboxes.length > 0 && Array.from(checkboxes).every(cb => cb.checked);
            const someChecked = Array.from(checkboxes).some(cb => cb.checked);
            
            selectAll.checked = allChecked;
            selectAll.indeterminate = someChecked && !allChecked;
            
            updateBatchActions();
        }
        
        // 更新批量操作栏
        function updateBatchActions() {
            const batchActions = document.getElementById('batch-actions');
            const selectedCount = document.getElementById('selected-count');
            
            if (selectedSites.size > 0) {
                batchActions.classList.remove('hidden');
                selectedCount.textContent = selectedSites.size;
            } else {
                batchActions.classList.add('hidden');
            }
        }
        
        // 清除选择
        function clearSelection() {
            selectedSites.clear();
            document.getElementById('select-all').checked = false;
            document.getElementById('select-all').indeterminate = false;
            document.querySelectorAll('.site-checkbox').forEach(cb => {
                cb.checked = false;
                cb.closest('tr').classList.remove('bg-blue-50');
            });
            updateBatchActions();
        }
        
        // 复制选中的站点域名
        function copySelectedDomains() {
            if (selectedSites.size === 0) {
                showToast('请先选择要复制的站点', 'warning');
                return;
            }
            
            // 收集选中站点的域名（只获取主域名，不包含别名）
            const domains = [];
            selectedSites.forEach(siteId => {
                // 从表格中获取站点域名
                const row = document.querySelector(`input[value="${siteId}"]`)?.closest('tr');
                if (row) {
                    const domainCell = row.querySelector('td:nth-child(2)');
                    if (domainCell) {
                        // 获取主域名文本，不包含别名
                        const domainDiv = domainCell.querySelector('.text-sm.font-medium.text-gray-900');
                        if (domainDiv) {
                            // 获取第一个文本节点（主域名），忽略子元素
                            const domainText = domainDiv.childNodes[0];
                            if (domainText && domainText.nodeType === Node.TEXT_NODE) {
                                const domain = domainText.textContent.trim();
                                if (domain) {
                                    domains.push(domain);
                                }
                            }
                        }
                    }
                }
            });
            
            if (domains.length === 0) {
                showToast('未能获取站点信息', 'error');
                return;
            }
            
            // 将域名列表复制到剪贴板（每行一个）
            const text = domains.join('\n');
            
            // 创建临时textarea元素用于复制
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.opacity = '0';
            document.body.appendChild(textarea);
            textarea.select();
            
            try {
                document.execCommand('copy');
                showToast(`已复制 ${domains.length} 个域名到剪贴板`, 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }
            
            document.body.removeChild(textarea);
        }

        // 批量打开站点
        window.batchOpenSites = function() {
            if (selectedSites.size === 0) {
                showToast('请先选择要打开的站点', 'warning');
                return;
            }

            let interval = 3; // 默认间隔3秒

            // 如果选择多个站点，则弹出对话框询问间隔时间
            if (selectedSites.size > 1) {
                const intervalInput = prompt(
                    `您选择了 ${selectedSites.size} 个站点\n` +
                    '请输入打开间隔时间（秒）：\n' +
                    '建议：1-3秒较为合适，避免浏览器阻止',
                    '3'  // 默认3秒
                );

                // 用户取消
                if (intervalInput === null) {
                    return;
                }

                // 验证输入
                const parsedInterval = parseFloat(intervalInput);
                if (isNaN(parsedInterval) || parsedInterval < 0.1 || parsedInterval > 60) {
                    showToast('请输入有效的间隔时间（0.1-60秒）', 'error');
                    return;
                }
                interval = parsedInterval;
            }


            // 计算总时间
            const totalTime = (selectedSites.size * interval).toFixed(1);
            const maxOpen = 10;
            
            // 如果站点数量较多，显示提醒
            if (selectedSites.size > maxOpen) {
                showToast(`开始以 ${interval} 秒间隔打开 ${selectedSites.size} 个站点（预计 ${totalTime} 秒）`, 'info');
            }
            
            // 收集选中站点的域名（只获取主域名）
            const domains = [];
            selectedSites.forEach(siteId => {
                // 从表格中获取站点域名
                const row = document.querySelector(`input[value="${siteId}"]`)?.closest('tr');
                if (row) {
                    const domainCell = row.querySelector('td:nth-child(2)');
                    if (domainCell) {
                        // 获取主域名文本，不包含别名
                        const domainDiv = domainCell.querySelector('.text-sm.font-medium.text-gray-900');
                        if (domainDiv) {
                            // 获取第一个文本节点（主域名），忽略子元素
                            const domainText = domainDiv.childNodes[0];
                            if (domainText && domainText.nodeType === Node.TEXT_NODE) {
                                const domain = domainText.textContent.trim();
                                if (domain) {
                                    domains.push(domain);
                                }
                            }
                        }
                    }
                }
            });
            
            if (domains.length === 0) {
                showToast('未找到有效的域名', 'error');
                return;
            }
            
            // 批量打开域名（只打开www版本）
            let openedCount = 0;
            const urlsToOpen = [];
            
            // 处理每个域名
            domains.forEach(domain => {
                // 清理域名中的空格和特殊字符
                let cleanDomain = domain.trim().replace(/\s+/g, '');
                
                // 跳过空域名
                if (!cleanDomain) return; 
                
                // 统一添加www版本
                if (!cleanDomain.toLowerCase().startsWith('www.')) {
                    // 如果不是www开头，添加www前缀
                    urlsToOpen.push({
                        domain: 'www.' + cleanDomain,
                        type: 'www'
                    });
                } else {
                    // 如果已经是www开头，直接使用
                    urlsToOpen.push({
                        domain: cleanDomain,
                        type: 'www-already'
                    });
                }
            });
            
            // 显示将要打开的域名信息
            console.log('=== 批量打开站点 ===');
            console.log('选中的域名:', domains);
            console.log('准备打开的URL数量:', urlsToOpen.length);
            urlsToOpen.forEach((item, i) => {
                console.log(`${i + 1}. ${item.domain} (${item.type})`);
            });
            
            // 如果没有URL要打开，直接返回
            if (urlsToOpen.length === 0) {
                showToast('没有可打开的域名', 'warning');
                return;
            }
            
            const totalToOpen = urlsToOpen.length;
            
            // 显示确认信息
            if (totalToOpen > 1) {
                showToast(`正在以 ${interval} 秒间隔打开 ${totalToOpen} 个URL...`, 'info');
            }
            
            // 将间隔时间转换为毫秒
            const intervalMs = interval * 1000;
            
            // 批量打开URL
            urlsToOpen.forEach((item, index) => {
                // 添加延迟，使用用户指定的间隔时间
                setTimeout(() => {
                    // 确保域名包含协议
                    let url = item.domain;
                    if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = 'http://' + url;
                    }
                    
                    console.log(`正在打开 [${index + 1}/${totalToOpen}]:`, url);
                    
                    try {
                        // 在新窗口打开
                        const newWindow = window.open(url, '_blank');
                        if (newWindow) {
                            openedCount++;
                            console.log(`✓ 成功打开: ${url}`);
                            
                            // 显示进度
                            if (totalToOpen > 5 && openedCount % 5 === 0) {
                                showToast(`已打开 ${openedCount}/${totalToOpen} 个站点...`, 'info');
                            }
                        } else {
                            console.log(`✗ 打开失败: ${url} (可能被浏览器拦截)`);
                        }
                    } catch (e) {
                        console.error(`打开URL出错: ${url}`, e);
                    }
                    
                    // 最后一个打开完成后显示提示
                    if (index === totalToOpen - 1) {
                        setTimeout(() => {
                            showToast(`已打开 ${openedCount}/${totalToOpen} 个URL (${domains.length} 个站点)`, 
                                      openedCount === totalToOpen ? 'success' : 'warning');
                            console.log(`=== 批量打开完成: ${openedCount}/${totalToOpen} ===`);
                        }, 100);
                    }
                }, index * intervalMs); // 使用用户指定的间隔时间（毫秒）
            });
        }
        
        // 复制选中的目标站地址
        function copySelectedTargets() {
            if (selectedSites.size === 0) {
                showToast('请先选择要复制的站点', 'warning');
                return;
            }
            
            // 收集选中站点的目标站地址
            const targets = [];
            selectedSites.forEach(siteId => {
                // 从表格中获取目标站地址
                const row = document.querySelector(`input[value="${siteId}"]`)?.closest('tr');
                if (row) {
                    // 第4列是目标站地址（第1列复选框，第2列域名，第3列分类，第4列目标站）
                    const targetCell = row.querySelector('td:nth-child(4)');
                    if (targetCell) {
                        // 尝试获取链接的href，如果没有链接则获取文本
                        const targetLink = targetCell.querySelector('a');
                        const targetUrl = targetLink ? targetLink.href : targetCell.textContent.trim();
                        if (targetUrl && targetUrl !== '-') {
                            targets.push(targetUrl);
                        }
                    }
                }
            });
            
            if (targets.length === 0) {
                showToast('选中的站点没有目标站地址', 'warning');
                return;
            }
            
            // 将目标站列表复制到剪贴板（每行一个）
            const text = targets.join('\n');
            
            // 创建临时textarea元素用于复制
            const textarea = document.createElement('textarea');
            textarea.value = text;
            textarea.style.position = 'fixed';
            textarea.style.opacity = '0';
            document.body.appendChild(textarea);
            textarea.select();
            
            try {
                document.execCommand('copy');
                showToast(`已复制 ${targets.length} 个目标站地址到剪贴板`, 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }
            
            document.body.removeChild(textarea);
        }
        
        // 批量删除
        async function batchDelete() {
            if (selectedSites.size === 0) return;
            
            if (!confirm(`确定要删除选中的 ${selectedSites.size} 个站点吗？\n\n此操作将同时清空这些站点的所有缓存！`)) {
                return;
            }
            
            showLoading();
            
            try {
                const res = await fetch('/api/v1/sites/batch', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ids: Array.from(selectedSites).map(id => parseInt(id))
                    })
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showToast(data.message, 'success');
                    clearSelection();
                    loadSites();
                } else {
                    showToast(data.error || '删除失败', 'error');
                }
            } catch (error) {
                showToast('删除失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // 批量清除缓存
        async function batchClearCache() {
            if (selectedSites.size === 0) {
                showToast('请先选择要清除缓存的站点', 'warning');
                return;
            }
            
            if (!confirm(`确定要清除选中的 ${selectedSites.size} 个站点的缓存吗？`)) {
                return;
            }
            
            showLoading();
            let successCount = 0;
            let failCount = 0;
            let totalDeleted = 0;
            
            try {
                // 获取选中站点的信息
                const siteIds = Array.from(selectedSites);
                const sites = [];
                
                // 从表格中获取站点域名
                for (const siteId of siteIds) {
                    const row = document.querySelector(`input.site-checkbox[value="${siteId}"]`)?.closest('tr');
                    if (row) {
                        const domainCell = row.querySelector('td:nth-child(2)');
                        if (domainCell) {
                            const domainElement = domainCell.querySelector('.text-sm.font-medium');
                            if (domainElement) {
                                // 只获取第一个文本节点（主域名），忽略子元素内容
                                let domain = '';
                                for (const node of domainElement.childNodes) {
                                    if (node.nodeType === Node.TEXT_NODE) {
                                        domain = node.textContent.trim();
                                        if (domain) break;
                                    }
                                }
                                if (domain) {
                                    sites.push({ id: siteId, domain: domain });
                                }
                            }
                        }
                    }
                }
                
                if (sites.length === 0) {
                    showToast('未能获取站点信息', 'error');
                    return;
                }
                
                // 并行清除所有站点的缓存
                const promises = sites.map(async (site) => {
                    try {
                        const res = await fetch(`/api/v1/cache/site/${site.domain}`, {
                            method: 'DELETE'
                        });
                        const data = await res.json();
                        
                        if (data.success) {
                            successCount++;
                            totalDeleted += (data.data?.deleted_count || 0);
                            return { success: true, domain: site.domain, count: data.data?.deleted_count || 0 };
                        } else {
                            failCount++;
                            return { success: false, domain: site.domain, error: data.error };
                        }
                    } catch (error) {
                        failCount++;
                        return { success: false, domain: site.domain, error: error.message };
                    }
                });
                
                const results = await Promise.all(promises);
                
                // 显示结果
                if (successCount > 0) {
                    let message = `成功清除 ${successCount} 个站点的缓存`;
                    if (totalDeleted > 0) {
                        message += `，共删除 ${totalDeleted} 个文件`;
                    }
                    showToast(message, 'success');
                    
                    // 刷新列表以更新缓存状态
                    loadSites();
                }
                
                if (failCount > 0) {
                    const failedDomains = results
                        .filter(r => !r.success)
                        .map(r => r.domain)
                        .join(', ');
                    showToast(`${failCount} 个站点清除失败: ${failedDomains}`, 'error');
                }
                
            } catch (error) {
                showToast('批量清除缓存失败: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }
        
        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN');
        }

        // 获取CSRF Token
        function getCSRFToken() {
            // 从meta标签获取
            const metaToken = document.querySelector('meta[name="csrf-token"]')?.content;
            if (metaToken) return metaToken;
            
            // 从localStorage获取
            const localToken = localStorage.getItem('csrf_token');
            if (localToken) return localToken;
            
            // 从cookie中获取
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrf_token') {
                    return decodeURIComponent(value);
                }
            }
            return '';
        }

        // 全局设置缓存
        let globalSettings = null;
        
        // 获取全局设置
        async function loadGlobalSettings() {
            if (!globalSettings) {
                try {
                    const res = await fetch('/api/v1/system/settings');
                    const data = await res.json();
                    if (data.success) {
                        globalSettings = data.data;
                    }
                } catch (error) {
                    console.error('加载全局设置失败:', error);
                }
            }
            return globalSettings;
        }
        
        // 获取全局UA检查设置
        function getGlobalUACheck() {
            return globalSettings?.enable_global_ua_check || false;
        }
        
        // 获取全局来源判断设置
        function getGlobalRefererCheck() {
            return globalSettings?.enable_global_referer_check || false;
        }
        
        // 获取全局WWW跳转设置
        function getGlobalRedirectWWW() {
            return globalSettings?.global_redirect_www || false;
        }
        
        // 加载站点列表
        async function loadSites() {
            try {
                const categoryId = document.getElementById('category-filter').value;
                const searchInput = document.getElementById('search-input').value.trim();
                
                // 处理批量搜索，将多行转换为数组
                const keywords = searchInput ? searchInput.split('\n').map(s => s.trim()).filter(s => s) : [];
                
                let res, data;
                
                // 如果有搜索关键词，使用POST请求
                if (keywords.length > 0) {
                    const requestBody = {
                        keywords: keywords,
                        page: currentPage,
                        page_size: pageSize,
                        category_id: categoryId ? parseInt(categoryId) : 0,
                        sort_by: currentSortColumn || 'id',
                        sort_order: currentSortOrder || 'desc'
                    };
                    
                    res = await fetch('/api/v1/sites/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestBody)
                    });
                    data = await res.json();
                } else {
                    // 没有搜索关键词，使用GET请求
                    const params = new URLSearchParams({
                        page: currentPage,
                        page_size: pageSize
                    });
                    
                    if (categoryId) {
                        params.append('category_id', categoryId);
                    }
                    
                    // 添加排序参数
                    if (currentSortColumn) {
                        params.append('sort_by', currentSortColumn);
                        params.append('sort_order', currentSortOrder);
                    }
                    
                    res = await fetch(`/api/v1/sites?${params}`);
                    data = await res.json();
                }
                
                if (data.success) {
                    renderSites(data.data.sites);
                    updatePagination(data.data.total);
                } else {
                    showToast(data.error || '加载失败', 'error');
                }
            } catch (error) {
                showToast('加载失败: ' + error.message, 'error');
            }
        }

        // 渲染站点列表
        function renderSites(sites) {
            const tbody = document.getElementById('sites-tbody');
            
            if (!sites || sites.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="px-6 py-12 text-center text-gray-500">
                            暂无数据
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = sites.map(site => `
                <tr class="hover:bg-gray-50 cursor-pointer" onclick="toggleRowCheckbox(event, ${site.id})">
                    <td class="px-6 py-3 whitespace-nowrap" onclick="event.stopPropagation()">
                        <input type="checkbox" 
                               class="site-checkbox w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                               value="${site.id}" 
                               onchange="onSiteCheckChange()">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">
                            ${site.domain}
                            ${site.aliases && site.aliases.length > 0 ? `
                                <div class="mt-1 text-xs text-gray-500">
                                    <i class="fas fa-link mr-1"></i>
                                    ${site.aliases.length == 1 ? 
                                        site.aliases[0].alias_domain :
                                        `<span class="alias-list" data-site-id="${site.id}">
                                            <span class="alias-preview">${site.aliases[0].alias_domain}
                                                <a href="javascript:void(0)" onclick="toggleAliases(${site.id})" class="text-blue-500 hover:text-blue-700 ml-1">
                                                    +${site.aliases.length - 1}个
                                                </a>
                                            </span>
                                            <span class="alias-full hidden">${site.aliases.map(alias => alias.alias_domain).join(', ')}
                                                <a href="javascript:void(0)" onclick="toggleAliases(${site.id})" class="text-blue-500 hover:text-blue-700 ml-1">
                                                    收起
                                                </a>
                                            </span>
                                        </span>`
                                    }
                                </div>
                            ` : ''}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${site.category ? `<span class="inline-block px-2 py-1 text-xs rounded cursor-pointer hover:opacity-80" 
                            style="background-color: ${site.category.color}20; color: ${site.category.color};"
                            onclick="filterByCategory(${site.category.id})"
                            title="点击筛选该分类">
                            ${site.category.icon ? `<i class="${site.category.icon} mr-1"></i>` : ''}
                            ${site.category.name}
                        </span>` : '<span class="text-xs text-gray-400">未分类</span>'}
                    </td>
                    <td class="px-6 py-4">
                        <a href="${site.target_url}" target="_blank" class="text-sm text-blue-600 hover:text-blue-800">
                            ${site.target_url}
                        </a>
                    </td>
                    <td class="px-6 py-4">
                        ${generateFeatureTags(site)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${getCacheStatusBadge(site.cache_status, site.cache_error)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${site.sitemap_count || 0} 个
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${site.count_404 || 0} 个
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${formatTime(site.updated_at)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm" onclick="event.stopPropagation()">
                        <div class="flex space-x-2">
                            <button onclick="editSite(${site.id})" class="text-blue-600 hover:text-blue-800" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="clearSiteCache(${site.id}, '${site.domain}')" class="text-yellow-600 hover:text-yellow-800" title="清空缓存">
                                <i class="fas fa-broom"></i>
                            </button>
                            <button onclick="deleteSite(${site.id})" class="text-red-600 hover:text-red-800" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            // Re-apply selection state after rendering
            document.querySelectorAll('.site-checkbox').forEach(checkbox => {
                if (selectedSites.has(checkbox.value)) {
                    checkbox.checked = true;
                    checkbox.closest('tr').classList.add('bg-blue-50');
                }
            });
            onSiteCheckChange();
        }

        // 更新分页
        function updatePagination(total) {
            totalItems = total;
            const totalPages = Math.ceil(total / pageSize);
            const startItem = (currentPage - 1) * pageSize + 1;
            const endItem = Math.min(currentPage * pageSize, total);
            
            document.getElementById('start-item').textContent = startItem;
            document.getElementById('end-item').textContent = endItem;
            document.getElementById('total-items').textContent = total;
            
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let html = '<div class="flex items-center">';
            
            // 上一页
            if (currentPage > 1) {
                html += `<button onclick="changePage(${currentPage - 1})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               <i class="fas fa-chevron-left mr-1"></i>上一页
                         </button>`;
            } else {
                html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-l-md cursor-not-allowed">
                               <i class="fas fa-chevron-left mr-1"></i>上一页
                         </span>`;
            }
            
            // 计算显示的页码范围
            const maxVisible = 5; // 最多显示5个页码，让界面更紧凑
            let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let endPage = Math.min(totalPages, startPage + maxVisible - 1);
            
            // 调整起始页，确保显示足够的页码
            if (endPage - startPage + 1 < maxVisible) {
                startPage = Math.max(1, endPage - maxVisible + 1);
            }
            
            // 如果不是从第1页开始，显示第1页和省略号
            if (startPage > 1) {
                html += `<button onclick="changePage(1)" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               1
                         </button>`;
                if (startPage > 2) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border-t border-b border-gray-300">
                                   <i class="fas fa-ellipsis-h"></i>
                             </span>`;
                }
            }
            
            // 显示页码
            for (let i = startPage; i <= endPage; i++) {
                if (i === currentPage) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 border-t border-b border-blue-600">
                                   ${i}
                             </span>`;
                } else {
                    html += `<button onclick="changePage(${i})" 
                                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                                   ${i}
                             </button>`;
                }
            }
            
            // 如果不是到最后一页，显示省略号和最后一页
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-white border-t border-b border-gray-300">
                                   <i class="fas fa-ellipsis-h"></i>
                             </span>`;
                }
                html += `<button onclick="changePage(${totalPages})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               ${totalPages}
                         </button>`;
            }
            
            // 下一页
            if (currentPage < totalPages) {
                html += `<button onclick="changePage(${currentPage + 1})" 
                               class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 transition-colors">
                               下一页<i class="fas fa-chevron-right ml-1"></i>
                         </button>`;
            } else {
                html += `<span class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-r-md cursor-not-allowed">
                               下一页<i class="fas fa-chevron-right ml-1"></i>
                         </span>`;
            }
            
            html += '</div>';
            
            pagination.innerHTML = html;
        }

        // 切换页面
        function changePage(page) {
            currentPage = page;
            // 保存到 localStorage
            localStorage.setItem('sites_current_page', currentPage);
            loadSites();
        }
        
        // 改变每页显示数量
        function changePageSize() {
            pageSize = parseInt(document.getElementById('page-size').value);
            currentPage = 1; // 重置到第一页
            // 保存到 localStorage
            localStorage.setItem('sites_page_size', pageSize);
            localStorage.setItem('sites_current_page', currentPage);
            loadSites();
        }

        // 搜索处理
        // 调整搜索框高度
        function adjustSearchHeight(element) {
            // 重置高度以获取正确的scrollHeight
            element.style.height = '40px';
            // 计算内容高度
            const scrollHeight = element.scrollHeight;
            // 设置新高度，最小40px，最大120px
            const newHeight = Math.min(Math.max(scrollHeight, 40), 120);
            element.style.height = newHeight + 'px';

            // 仅当高度达到最大值且内容仍然溢出时才显示滚动条
            if (newHeight >= 120) {
                element.style.overflowY = 'auto';
            } else {
                element.style.overflowY = 'hidden';
            }

            if (element.parentElement) {
                element.parentElement.style.height = newHeight + 'px';
            }
        }
        
        // 处理搜索框的键盘事件
        function handleSearchKeydown(event) {
            // 检测是否按下 Ctrl+Enter 或 Cmd+Enter
            if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
                event.preventDefault(); // 阻止默认行为
                executeSearch(); // 执行搜索
            }
        }
        
        // 显示搜索下拉框
        function showSearchDropdown() {
            const dropdown = document.getElementById('search-dropdown');
            const searchInput = document.getElementById('search-input');
            const value = searchInput.value.trim();
            
            // 如果有内容或者用户点击了搜索框，显示下拉框
            if (dropdown) {
                dropdown.classList.remove('hidden');
                // 可以在这里加载动态搜索建议
                // loadSearchSuggestions(value);
            }
        }
        
        // 隐藏搜索下拉框
        function hideSearchDropdown(event) {
            // 延迟隐藏，让点击事件先执行
            setTimeout(() => {
                const dropdown = document.getElementById('search-dropdown');
                if (dropdown) {
                    dropdown.classList.add('hidden');
                }
            }, 200);
        }
        
        // 插入搜索建议到搜索框
        function insertSearchSuggestion(text) {
            const searchInput = document.getElementById('search-input');
            const currentValue = searchInput.value.trim();
            const lines = currentValue ? currentValue.split('\n') : [];
            
            // 如果当前没有内容或最后一行为空，直接插入
            if (!currentValue || lines[lines.length - 1] === '') {
                searchInput.value = currentValue ? currentValue + text : text;
            } else {
                // 否则新起一行插入
                searchInput.value = currentValue + '\n' + text;
            }
            
            adjustSearchHeight(searchInput);
            hideSearchDropdown();
            searchInput.focus();
        }
        
        // 执行搜索
        function executeSearch() {
            const searchInput = document.getElementById('search-input');
            const keyword = searchInput.value.trim();
            if (!keyword) {
                return; // 空内容不搜索
            }
            currentSearchKeyword = keyword;
            currentPage = 1;
            // 搜索时重置页码
            localStorage.setItem('sites_current_page', currentPage);
            loadSites();
            updateClearButton();
        }
        
        // 切换关键词组显示
        function toggleKeywordsGroup() {
            const checkbox = document.getElementById('enable_keyword');
            const group = document.getElementById('keywords-group');
            if (checkbox.checked) {
                group.classList.remove('hidden');
                loadKeywordLibraries(); // 加载关键词库
            } else {
                group.classList.add('hidden');
            }
        }
        
        // 切换伪原创设置显示
        function togglePseudoSettings() {
            const checkbox = document.getElementById('enable_pseudo');
            const settings = document.getElementById('pseudo-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
                loadPseudoLibraries();
            } else {
                settings.classList.add('hidden');
            }
        }

        function toggleStructureSettings() {
            const checkbox = document.getElementById('enable_structure');
            const settings = document.getElementById('structure-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
                loadStructureKeywordLibraries();
            } else {
                settings.classList.add('hidden');
            }
        }
        
        // 切换企业名称设置
        function toggleCompanyNameSettings() {
            const checkbox = document.getElementById('enable_company_name');
            const settings = document.getElementById('company-name-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
                loadCompanyLibraries();
            } else {
                settings.classList.add('hidden');
            }
        }
        
        // 加载企业名称库列表
        async function loadCompanyLibraries() {
            try {
                const response = await fetch('/api/v1/company/libraries');
                const result = await response.json();
                
                if (result.success) {
                    const select = document.getElementById('company_library_id');
                    const currentValue = select.value;
                    
                    select.innerHTML = '<option value="">请选择企业名称库</option>';
                    result.data.forEach(library => {
                        const option = document.createElement('option');
                        option.value = library.id;
                        option.textContent = `${library.name} (${library.company_count}个名称)`;
                        option.setAttribute('data-count', library.company_count || 0);
                        if (library.id == currentValue) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载企业名称库失败:', error);
            }
        }
        
        // 企业库选择变化处理（单个站点添加/编辑）
        async function onCompanyLibraryChange() {
            const select = document.getElementById('company_library_id');
            const nameInput = document.getElementById('company_name');
            
            if (select.value) {
                // 选择了企业库，获取随机企业名称
                const selectedOption = select.options[select.selectedIndex];
                const count = selectedOption.getAttribute('data-count');
                
                if (count && count > 0) {
                    try {
                        // 从后端获取一个随机企业名称
                        const response = await fetch(`/api/v1/company/libraries/${select.value}/random-name`);
                        if (response.ok) {
                            const result = await response.json();
                            if (result.success && result.data) {
                                nameInput.value = result.data.name;
                                showToast(`已随机分配企业名称: ${result.data.name}`, 'info');
                            }
                        } else {
                            // 如果API不存在，清空输入框让用户手动输入
                            nameInput.value = '';
                            nameInput.placeholder = '将从企业库中随机分配';
                        }
                    } catch (error) {
                        console.error('获取随机企业名称失败:', error);
                        nameInput.value = '';
                        nameInput.placeholder = '将从企业库中随机分配';
                    }
                } else {
                    nameInput.value = '';
                    nameInput.placeholder = '企业库中暂无企业名称';
                }
                
                nameInput.disabled = true;
            } else {
                // 未选择企业库，允许直接输入
                nameInput.value = '';
                nameInput.placeholder = '请输入企业名称';
                nameInput.disabled = false;
            }
        }

        // 加载结构注入关键词库
        async function loadStructureKeywordLibraries() {
            try {
                const response = await fetch('/api/v1/keywords/libraries');
                const data = await response.json();
                if (data.success && data.data) {
                    const container = document.getElementById('structure-keyword-libraries');
                    if (!container) return;

                    if (data.data.length === 0) {
                        container.innerHTML = '<div class="text-gray-500 text-xs">暂无关键词库</div>';
                        return;
                    }

                    container.innerHTML = data.data.map(lib => `
                        <label class="inline-flex items-center space-x-2">
                            <input type="checkbox" name="structure-keyword-library" value="${lib.id}" class="mr-1">
                            <span class="text-xs">${lib.name}</span>
                        </label>
                    `).join('');
                }
            } catch (error) {
                console.error('加载结构关键词库失败:', error);
            }
        }

        // 获取选中的结构注入词库ID
        function getSelectedStructureLibraries() {
            const checkboxes = document.querySelectorAll('input[name="structure-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }

        function toggleBatchStructureSettings() {
            const checkbox = document.getElementById('batch-enable-structure');
            const settings = document.getElementById('batch-structure-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
                loadBatchStructureKeywordLibraries();
            } else {
                settings.classList.add('hidden');
            }
        }

        // 加载批量编辑的结构注入关键词库
        async function loadBatchStructureKeywordLibraries() {
            try {
                const response = await fetch('/api/v1/keywords/libraries');
                const data = await response.json();
                
                if (data.success && data.data && data.data.length > 0) {
                    const select = document.getElementById('batch-structure-library-select');
                    if (!select) {
                        console.error('找不到批量编辑结构注入下拉框元素');
                        return;
                    }
                    
                    // 清空现有选项（保留第一个默认选项）
                    while (select.options.length > 1) {
                        select.remove(1);
                    }
                    
                    // 并行获取所有词库的关键词数量
                    const detailPromises = data.data.map(async lib => {
                        try {
                            const keywordsResponse = await fetch(`/api/v1/keywords/libraries/${lib.id}/keywords`);
                            const keywordsData = await keywordsResponse.json();
                            // API返回的是 data.items 数组
                            const count = keywordsData.success && keywordsData.data && keywordsData.data.items 
                                ? keywordsData.data.items.length 
                                : 0;
                            return {
                                id: lib.id,
                                name: lib.name,
                                count: count
                            };
                        } catch (err) {
                            console.error(`获取词库 ${lib.id} 关键词失败:`, err);
                            return { id: lib.id, name: lib.name, count: 0 };
                        }
                    });
                    
                    const librariesWithCount = await Promise.all(detailPromises);
                    
                    // 添加到下拉框
                    librariesWithCount.forEach(lib => {
                        const option = document.createElement('option');
                        option.value = lib.id;
                        option.dataset.name = lib.name;
                        option.dataset.count = String(lib.count); // 确保是字符串
                        option.textContent = `${lib.name} (${lib.count}个词)`;
                        select.appendChild(option);
                    });
                    
                    console.log(`加载了 ${librariesWithCount.length} 个关键词库到批量编辑结构注入下拉框`);
                }
            } catch (error) {
                console.error('批量编辑加载关键词库失败:', error);
            }
        }

        // 添加批量编辑的结构注入词库
        function addBatchStructureLibrary(select) {
            const selectedOption = select.options[select.selectedIndex];
            if (!selectedOption || !selectedOption.value) return;
            
            const libId = selectedOption.value;
            const libName = selectedOption.dataset.name || selectedOption.textContent.split(' (')[0];
            const libCount = selectedOption.dataset.count || '0';
            
            // 检查是否已经添加
            if (document.querySelector(`#batch-structure-selected-libraries [data-lib-id="${libId}"]`)) {
                select.value = '';
                return;
            }
            
            // 创建标签
            const tag = document.createElement('span');
            tag.className = 'inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded';
            tag.dataset.libId = libId;
            tag.innerHTML = `
                ${libName} (${libCount}个词)
                <button type="button" onclick="removeBatchStructureLibrary(${libId})" class="ml-1 hover:text-blue-600">
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            `;
            
            document.getElementById('batch-structure-selected-libraries').appendChild(tag);
            select.value = '';
        }

        // 移除批量编辑的结构注入词库
        function removeBatchStructureLibrary(libId) {
            const tag = document.querySelector(`#batch-structure-selected-libraries [data-lib-id="${libId}"]`);
            if (tag) {
                tag.remove();
            }
        }

        // 获取批量编辑选中的结构注入词库ID
        function getBatchSelectedStructureLibraries() {
            const tags = document.querySelectorAll('#batch-structure-selected-libraries [data-lib-id]');
            return Array.from(tags).map(tag => parseInt(tag.dataset.libId));
        }
        
        // 切换随机字符串设置显示
        function toggleRandomStringSettings() {
            const checkbox = document.getElementById('enable_random_string');
            const settings = document.getElementById('random-string-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
            } else {
                settings.classList.add('hidden');
            }
        }
        
        // 切换H1标签设置显示
        function toggleH1TagSettings() {
            const checkbox = document.getElementById('enable_h1_tag');
            const settings = document.getElementById('h1-tag-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
            } else {
                settings.classList.add('hidden');
            }
        }
        
        // 切换隐藏HTML设置显示
        function toggleHiddenHTMLSettings() {
            const checkbox = document.getElementById('enable_hidden_html');
            const settings = document.getElementById('hidden-html-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
            } else {
                settings.classList.add('hidden');
            }
        }

        // 切换首页关键词注入设置
        function toggleHomeKeywordSettings() {
            const checkbox = document.getElementById('enable_home_keyword_inject');
            const settings = document.getElementById('home-keyword-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
                // 加载关键词库列表
                loadKeywordLibrariesForHome();
            } else {
                settings.classList.add('hidden');
            }
        }

        // 加载关键词库列表（用于首页关键词注入）
        async function loadKeywordLibrariesForHome() {
            try {
                const response = await fetch('/api/v1/keywords/libraries');
                const result = await response.json();
                
                if (result.success && result.data) {
                    const container = document.getElementById('home-keyword-libraries-container');
                    if (!container) return;

                    if (result.data.length === 0) {
                        container.innerHTML = '<div class="text-gray-500 text-xs">暂无关键词库</div>';
                        return;
                    }

                    container.innerHTML = result.data.map(lib => `
                        <label class="inline-flex items-center space-x-2">
                            <input type="checkbox" name="home-keyword-library" value="${lib.id}" class="mr-1">
                            <span class="text-xs">${lib.name}</span>
                        </label>
                    `).join('');
                }
            } catch (error) {
                console.error('加载首页关键词库失败:', error);
                showToast('加载首页关键词库失败', 'error');
            }
        }

        // 获取选中的首页关键词库ID列表
        function getSelectedHomeKeywordLibraryIDs() {
            const checkboxes = document.querySelectorAll('input[name="home-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('[id^="tab-content-"]').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 重置所有标签按钮样式
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
                tab.classList.add('text-gray-600');
            });
            
            // 显示选中的标签内容
            document.getElementById(`tab-content-${tabName}`).classList.remove('hidden');
            
            // 高亮选中的标签按钮
            const activeTab = document.getElementById(`tab-${tabName}`);
            activeTab.classList.remove('text-gray-600');
            activeTab.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
        }
        
        // 切换UA设置显示
        function toggleUASettingsMode() {
            const select = document.getElementById('ua_check_mode');
            const settings = document.getElementById('ua-settings');
            if (select && settings) {
                if (select.value === 'enable') {
                    settings.classList.remove('hidden');
                } else {
                    settings.classList.add('hidden');
                }
            }
        }

        function toggleEditUASettingsMode() {
            const select = document.getElementById('edit-ua_check_mode');
            const settings = document.getElementById('edit-ua-settings');
            if (select && settings) {
                if (select.value === 'enable') {
                    settings.classList.remove('hidden');
                } else {
                    settings.classList.add('hidden');
                }
            }
        }
        
        // 切换来源判断设置显示
        function toggleRefererSettings() {
            const select = document.getElementById('referer_check_mode');
            const settings = document.getElementById('referer-settings');
            if (select && settings) {
                if (select.value === 'enable') {
                    settings.classList.remove('hidden');
                } else {
                    settings.classList.add('hidden');
                }
            }
        }
        
        // 切换蜘蛛屏蔽设置
        function toggleSpiderBlockSettings() {
            const checkbox = document.getElementById('enable_spider_block');
            const config = document.getElementById('spider-block-config');
            
            if (checkbox.checked) {
                config.classList.remove('hidden');
            } else {
                config.classList.add('hidden');
            }
        }
        
        // 切换Sitemap设置
        function toggleSitemapSettings() {
            const checkbox = document.getElementById('enable_sitemap');
            const config = document.getElementById('sitemap-config');
            
            if (checkbox && config) {
                if (checkbox.checked) {
                    config.classList.remove('hidden');
                } else {
                    config.classList.add('hidden');
                }
            }
        }
        
        
        // 切换拼音模式（全局/开启/关闭）
        function togglePinyinMode() {
            const mode = document.getElementById('pinyin_mode').value;
            const customSettings = document.getElementById('pinyin_custom_settings');
            const globalHint = document.getElementById('pinyin_global_hint');
            const specialCharsSettings = document.getElementById('pinyin_special_chars_settings');
            
            if (mode === 'enable') {
                // 独立开启 - 显示特殊字符设置
                customSettings.style.display = 'block';
                globalHint.style.display = 'none';
                if (specialCharsSettings) {
                    specialCharsSettings.style.display = 'block';
                }
            } else if (mode === 'disable') {
                // 独立关闭 - 隐藏所有设置
                customSettings.style.display = 'none';
                globalHint.style.display = 'none';
            } else {
                // 使用全局设置
                customSettings.style.display = 'none';
                globalHint.style.display = 'block';
            }
        }
        
        // 批量添加的拼音模式切换
        function toggleBatchPinyinMode() {
            const mode = document.getElementById('batch-pinyin-mode').value;
            const customSettings = document.getElementById('batch-pinyin-custom-settings');
            const globalHint = document.getElementById('batch-pinyin-global-hint');
            const specialCharsSettings = document.getElementById('batch-pinyin-special-chars-settings');
            
            if (mode === 'enable') {
                // 独立开启 - 显示特殊字符设置
                customSettings.style.display = 'block';
                globalHint.style.display = 'none';
                if (specialCharsSettings) {
                    specialCharsSettings.style.display = 'block';
                }
            } else if (mode === 'disable') {
                // 独立关闭 - 隐藏所有设置
                customSettings.style.display = 'none';
                globalHint.style.display = 'none';
            } else {
                // 使用全局设置
                customSettings.style.display = 'none';
                globalHint.style.display = 'block';
            }
        }
        
        // 切换蜘蛛UA输入框
        function toggleSpiderUAInput() {
            const checkbox = document.getElementById('use_global_spider_ua');
            const input = document.getElementById('custom_spider_ua');
            
            if (checkbox.checked) {
                input.disabled = true;
            } else {
                input.disabled = false;
            }
        }
        
        // 切换缓存输入框
        function toggleCacheInputs() {
            const checkbox = document.getElementById('use_global_cache');
            const customSettings = document.getElementById('custom-cache-settings');
            const inputs = customSettings.querySelectorAll('input');
            
            if (checkbox.checked) {
                customSettings.classList.add('opacity-50');
                inputs.forEach(input => input.disabled = true);
            } else {
                customSettings.classList.remove('opacity-50');
                inputs.forEach(input => input.disabled = false);
            }
        }
        
        // 切换Unicode选项显示
        function toggleUnicodeOptions() {
            const checkbox = document.getElementById('enable_unicode');
            const options = document.getElementById('unicode-options');
            
            if (!checkbox || !options) return;
            
            if (checkbox.checked) {
                options.classList.remove('hidden');
                // 如果之前没有选中任何项，默认全选
                const titleElem = document.getElementById('enable_unicode_title');
                const keywordsElem = document.getElementById('enable_unicode_keywords');
                const descElem = document.getElementById('enable_unicode_desc');
                
                if (titleElem && keywordsElem && descElem) {
                    const titleChecked = titleElem.checked;
                    const keywordsChecked = keywordsElem.checked;
                    const descChecked = descElem.checked;
                    
                    if (!titleChecked && !keywordsChecked && !descChecked) {
                        titleElem.checked = true;
                        keywordsElem.checked = true;
                        descElem.checked = true;
                    }
                }
            } else {
                options.classList.add('hidden');
                // 取消勾选时，清空所有子选项
                const titleElem = document.getElementById('enable_unicode_title');
                const keywordsElem = document.getElementById('enable_unicode_keywords');
                const descElem = document.getElementById('enable_unicode_desc');
                
                if (titleElem) titleElem.checked = false;
                if (keywordsElem) keywordsElem.checked = false;
                if (descElem) descElem.checked = false;
            }
        }
        
        // 切换批量添加的首页关键词注入设置
        function toggleBatchHomeKeywordSettings() {
            const checkbox = document.getElementById('batch-enable-home-keyword-inject');
            const settings = document.getElementById('batch-home-keyword-settings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
                // 加载关键词库列表
                loadKeywordLibrariesForBatchHome();
            } else {
                settings.classList.add('hidden');
            }
        }

        // 加载批量添加的首页关键词库列表
        function loadKeywordLibrariesForBatchHome() {
            fetch('/api/v1/keywords/libraries')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const select = document.getElementById('batch-home-keyword-library-select');
                        select.innerHTML = '<option value="">请选择关键词库...</option>';
                        data.data.forEach(lib => {
                            select.innerHTML += `<option value="${lib.id}">${lib.name} (${lib.type})</option>`;
                        });
                    }
                });
        }

        // 添加批量首页关键词库
        function addBatchHomeKeywordLibrary() {
            const select = document.getElementById('batch-home-keyword-library-select');
            const selectedId = select.value;
            const selectedText = select.options[select.selectedIndex].text;
            
            if (!selectedId) {
                showToast('请先选择关键词库', 'error');
                return;
            }
            
            // 检查是否已经添加过
            const container = document.getElementById('batch-selected-home-keyword-libraries');
            if (container.querySelector(`[data-library-id="${selectedId}"]`)) {
                showToast('该关键词库已经添加', 'warning');
                return;
            }
            
            // 添加到列表
            const libraryItem = document.createElement('div');
            libraryItem.className = 'library-item flex items-center justify-between bg-white border rounded px-2 py-1';
            libraryItem.setAttribute('data-library-id', selectedId);
            libraryItem.innerHTML = `
                <span class="text-xs">${selectedText}</span>
                <button type="button" onclick="removeBatchHomeKeywordLibrary(this)" class="text-red-500 hover:text-red-700">
                    <i class="fas fa-times text-xs"></i>
                </button>
            `;
            container.appendChild(libraryItem);
            
            // 重置选择框
            select.value = '';
        }

        // 移除批量首页关键词库
        function removeBatchHomeKeywordLibrary(button) {
            button.parentElement.remove();
        }
        
        // 获取批量添加选中的首页关键词库
        function getSelectedBatchHomeKeywordLibraries() {
            const container = document.getElementById('batch-selected-home-keyword-libraries');
            if (!container) return [];
            const libraries = container.querySelectorAll('.library-item');
            return Array.from(libraries).map(item => parseInt(item.dataset.libraryId)).filter(id => !isNaN(id));
        }
        
        // 获取批量添加选中的关键词库
        function getSelectedBatchKeywordLibraries() {
            const checkboxes = document.querySelectorAll('input[name="batch-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 获取批量添加选中的标题关键词库
        function getSelectedBatchTitleKeywordLibraries() {
            const checkboxes = document.querySelectorAll('input[name="batch-title-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 获取批量添加选中的Meta关键词库
        function getSelectedBatchMetaKeywordLibraries() {
            const checkboxes = document.querySelectorAll('input[name="batch-meta-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 获取批量添加选中的描述关键词库
        function getSelectedBatchDescKeywordLibraries() {
            const checkboxes = document.querySelectorAll('input[name="batch-desc-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 获取批量添加选中的伪原创库
        function getSelectedBatchPseudoLibraries() {
            const checkboxes = document.querySelectorAll('input[name="batch-pseudo-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }

        // 切换批量添加的Unicode选项显示
        function toggleBatchUnicodeOptions() {
            const checkbox = document.getElementById('batch-enable-unicode');
            const options = document.getElementById('batch-unicode-options');
            
            if (checkbox.checked) {
                options.classList.remove('hidden');
                // 如果之前没有选中任何项，默认全选
                const titleChecked = document.getElementById('batch-enable-unicode-title').checked;
                const keywordsChecked = document.getElementById('batch-enable-unicode-keywords').checked;
                const descChecked = document.getElementById('batch-enable-unicode-desc').checked;
                
                if (!titleChecked && !keywordsChecked && !descChecked) {
                    document.getElementById('batch-enable-unicode-title').checked = true;
                    document.getElementById('batch-enable-unicode-keywords').checked = true;
                    document.getElementById('batch-enable-unicode-desc').checked = true;
                }
            } else {
                options.classList.add('hidden');
                // 取消勾选时，清空所有子选项
                document.getElementById('batch-enable-unicode-title').checked = false;
                document.getElementById('batch-enable-unicode-keywords').checked = false;
                document.getElementById('batch-enable-unicode-desc').checked = false;
            }
        }
        
        // 加载关键词库列表
        async function loadKeywordLibraries() {
            try {
                const res = await fetch('/api/v1/keywords/libraries');
                const data = await res.json();
                
                if (data.success) {
                    renderKeywordLibraries(data.data);
                    // 同时渲染到三个独立选择器
                    renderTitleKeywordLibraries(data.data);
                    renderMetaKeywordLibraries(data.data);
                    renderDescKeywordLibraries(data.data);
                }
            } catch (error) {
                console.error('加载关键词库失败:', error);
            }
        }
        
        // 加载伪原创词库列表
        async function loadPseudoLibraries() {
            try {
                const res = await fetch('/api/v1/pseudo/libraries');
                const data = await res.json();
                
                if (data.success) {
                    renderPseudoLibraries(data.data);
                }
            } catch (error) {
                console.error('加载伪原创词库失败:', error);
            }
        }
        
        // 渲染关键词库列表
        function renderKeywordLibraries(libraries) {
            const container = document.getElementById('keyword-libraries');
            
            if (!libraries || libraries.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-xs">暂无关键词库，请先创建词库</div>';
                return;
            }
            
            container.innerHTML = libraries.map(lib => `
                <label class="inline-flex items-center space-x-2">
                    <input type="checkbox" name="keyword-library" value="${lib.id}" class="mr-2" onchange="updateSelectedKeywords()">
                    <span class="text-sm">${lib.name}</span>
                    <span class="text-xs text-gray-500">${lib.description || ''}</span>
                </label>
            `).join('');
        }
        
        // 渲染标题关键词库列表
        function renderTitleKeywordLibraries(libraries) {
            const container = document.getElementById('title-keyword-libraries');
            
            if (!libraries || libraries.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-xs">暂无关键词库</div>';
                return;
            }
            
            container.innerHTML = libraries.map(lib => `
                <label class="inline-flex items-center space-x-2">
                    <input type="checkbox" name="title-keyword-library" value="${lib.id}" class="mr-1">
                    <span class="text-xs">${lib.name}</span>
                </label>
            `).join('');
        }
        
        // 渲染Meta关键词库列表
        function renderMetaKeywordLibraries(libraries) {
            const container = document.getElementById('meta-keyword-libraries');
            
            if (!libraries || libraries.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-xs">暂无关键词库</div>';
                return;
            }
            
            container.innerHTML = libraries.map(lib => `
                <label class="inline-flex items-center space-x-2">
                    <input type="checkbox" name="meta-keyword-library" value="${lib.id}" class="mr-1">
                    <span class="text-xs">${lib.name}</span>
                </label>
            `).join('');
        }
        
        // 渲染描述关键词库列表
        function renderDescKeywordLibraries(libraries) {
            const container = document.getElementById('desc-keyword-libraries');
            
            if (!libraries || libraries.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-xs">暂无关键词库</div>';
                return;
            }
            
            container.innerHTML = libraries.map(lib => `
                <label class="inline-flex items-center space-x-2">
                    <input type="checkbox" name="desc-keyword-library" value="${lib.id}" class="mr-1">
                    <span class="text-xs">${lib.name}</span>
                </label>
            `).join('');
        }
        
        // 渲染伪原创词库列表
        function renderPseudoLibraries(libraries) {
            const container = document.getElementById('pseudo-libraries');
            
            if (!libraries || libraries.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-sm">暂无伪原创词库，请先创建词库</div>';
                return;
            }
            
            container.innerHTML = libraries.map(lib => `
                <label class="inline-flex items-center space-x-2">
                    <input type="checkbox" name="pseudo-library" value="${lib.id}" class="mr-2">
                    <span class="text-sm">${lib.name}</span>
                    <span class="text-xs text-gray-500">${lib.description || ''}</span>
                </label>
            `).join('');
        }
        
        // 获取选中的关键词库
        function getSelectedKeywordLibraries() {
            const checkboxes = document.querySelectorAll('input[name="keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 获取选中的标题关键词库
        function getSelectedTitleKeywordLibraries() {
            const checkboxes = document.querySelectorAll('input[name="title-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 获取选中的Meta关键词库
        function getSelectedMetaKeywordLibraries() {
            const checkboxes = document.querySelectorAll('input[name="meta-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 获取选中的描述关键词库
        function getSelectedDescKeywordLibraries() {
            const checkboxes = document.querySelectorAll('input[name="desc-keyword-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }
        
        // 更新选中的关键词（当关键词库选择变化时）
        async function updateSelectedKeywords() {
            const selectedLibraryIds = getSelectedKeywordLibraries();
            // 这里可以将选中的库ID存储，后端会根据库ID获取所有关键词
            // 暂时将库ID存储在隐藏的keywords字段中
            document.getElementById('keywords').value = JSON.stringify(selectedLibraryIds);
        }
        
        // 获取选中的伪原创词库
        function getSelectedPseudoLibraries() {
            const checkboxes = document.querySelectorAll('input[name="pseudo-library"]:checked');
            return Array.from(checkboxes).map(cb => parseInt(cb.value));
        }

        // 显示添加模态框
        function showAddModal() {
            document.getElementById('modal-title').textContent = '添加站点';
            document.getElementById('site-form').reset();
            document.getElementById('site-id').value = '';
            // 添加时确保域名可编辑
            document.getElementById('domain').disabled = false;
            
            // 重置所有复选框和配置
            resetFormToDefaults();
            
            // 加载分类列表
            loadCategories();
            
            // 加载关键词库（确保在添加新站点时关键词库可选）
            if (document.getElementById('enable_keyword').checked) {
                loadKeywordLibraries();
            }
            
            // 加载首页关键词库
            if (document.getElementById('enable_home_keyword_inject').checked) {
                loadKeywordLibrariesForHome();
            }
            
            // 默认显示SEO优化标签页
            switchTab('seo');
            
            // 显示模态框
            document.getElementById('site-modal').classList.remove('hidden');
        }
        
        // 重置表单到默认状态
        function resetFormToDefaults() {
            // 基础配置默认值
            // 缓存现在永久有效，无需设置
            // document.getElementById('enable_cache').checked = true;
            document.getElementById('enable_preload').checked = false;
            document.getElementById('download_external_resources').checked = false;
            document.getElementById('enable_https_check').checked = false;
            document.getElementById('enable_traditional_convert').checked = false;
            
            // UA判断默认关闭
            // UA判断重置
            const uaCheckMode = document.getElementById('ua_check_mode');
            if (uaCheckMode) {
                uaCheckMode.value = 'global';
                document.getElementById('ua-settings').classList.add('hidden');
            }
            document.getElementById('allowed_ua').value = '';
            document.getElementById('non_spider_html').value = '';
            
            // 来源判断默认使用全局设置
            const refererCheckMode = document.getElementById('referer_check_mode');
            if (refererCheckMode) {
                refererCheckMode.value = 'global';
                document.getElementById('referer-settings').classList.add('hidden');
            }
            document.getElementById('allowed_referers').value = '';
            document.getElementById('referer_block_code').value = '403';
            document.getElementById('referer_block_html').value = '';
            
            // 域名跳转默认不设置
            document.getElementById('redirect_www').value = '';
            
            // 蜘蛛屏蔽默认关闭
            document.getElementById('enable_spider_block').checked = false;
            document.getElementById('spider-block-config').classList.add('hidden');
            document.getElementById('use_global_spider_ua').checked = true;
            document.getElementById('custom_spider_ua').value = '';
            document.getElementById('custom_spider_ua').disabled = true;
            document.getElementById('spider_block_403_template').value = '';
            
            // Sitemap默认关闭
            document.getElementById('enable_sitemap').checked = false;
            document.getElementById('sitemap-config').classList.add('hidden');
            document.getElementById('sitemap_update_interval').value = 60;
            document.getElementById('sitemap_changefreq').value = 'daily';
            document.getElementById('sitemap_priority').value = 0.5;
            document.getElementById('sitemap_max_urls').value = 50000;
            
            // 缓存默认使用全局设置
            document.getElementById('use_global_cache').checked = true;
            document.getElementById('cache_max_size').value = 1024;
            document.getElementById('cache_home_ttl').value = 30;
            document.getElementById('cache_other_ttl').value = 1440;
            document.getElementById('enable_redis_cache').checked = true;
            toggleCacheInputs();
            
            // 内容注入默认关闭
            document.getElementById('enable_keyword').checked = false;
            document.getElementById('enable_structure').checked = false;
            document.getElementById('enable_pseudo').checked = false;
            document.getElementById('filter_external_links').checked = false;
            document.getElementById('home_title').value = '';
            document.getElementById('home_description').value = '';
            document.getElementById('home_keywords').value = '';
            document.getElementById('enable_unicode').checked = false;
            document.getElementById('enable_random_string').checked = false;
            document.getElementById('random_string_length').value = 4;
            // 拼音设置使用 pinyin_mode 下拉选择，而不是 checkbox
            const pinyinMode = document.getElementById('pinyin_mode');
            if (pinyinMode) {
                pinyinMode.value = 'global';
                togglePinyinMode(); // 调用切换函数更新UI状态
            }
            document.getElementById('enable_h1_tag').checked = false;
            document.getElementById('h1_tag_position').value = 'both';
            document.getElementById('enable_hidden_html').checked = false;
            document.getElementById('hidden_html_length').value = 50;
            document.getElementById('hidden_html_random_id').checked = false;
            document.getElementById('hidden_html_position').value = 'body';
            
            // 隐藏所有子配置面板
            const hiddenPanels = [
                'keyword-settings', 'structure-settings', 'pseudo-settings',
                'unicode-options', 'random-string-options', 'hidden-html-options'
            ];
            hiddenPanels.forEach(id => {
                const panel = document.getElementById(id);
                if (panel) panel.classList.add('hidden');
            });
        }
        
        // 显示添加单个站点模态框（新增函数）
        function showAddSingleSiteModal() {
            showAddModal();
            // 重置所有子设置区域的显示状态
            document.getElementById('keywords-group').classList.add('hidden');
            document.getElementById('pseudo-settings').classList.add('hidden');
            document.getElementById('random-string-settings').classList.add('hidden');
            document.getElementById('hidden-html-settings').classList.add('hidden');
            const uaSettings = document.getElementById('ua-settings');
            if (uaSettings) uaSettings.classList.add('hidden');
            const spiderBlockConfig = document.getElementById('spider-block-config');
            if (spiderBlockConfig) spiderBlockConfig.classList.add('hidden');
            
            // 切换到基础配置标签页
            switchTab('basic');
            
            document.getElementById('site-modal').classList.remove('hidden');
        }

        // 隐藏模态框
        function hideModal() {
            document.getElementById('site-modal').classList.add('hidden');
        }

        // 编辑站点
        async function editSite(id) {
            try {
                const res = await fetch(`/api/v1/sites/${id}`);
                const data = await res.json();
                
                if (data.success) {
                    const site = data.data;
                    document.getElementById('modal-title').textContent = '编辑站点';
                    document.getElementById('site-id').value = site.id;
                    document.getElementById('domain').value = site.domain;
                    // 编辑时允许修改域名
                    document.getElementById('domain').disabled = false;
                    document.getElementById('target_url').value = site.target_url;
                    document.getElementById('status').value = site.status;
                    document.getElementById('crawl_depth').value = site.crawl_depth || 2;
                    
                    // 设置子域名前缀
                    if (site.aliases && site.aliases.length > 0) {
                        const prefixes = site.aliases.map(alias => {
                            // 从alias_domain中提取前缀
                            const mainDomain = site.domain;
                            const aliasDomain = alias.alias_domain;
                            if (aliasDomain === mainDomain) {
                                return '@'; // 裸域名
                            } else if (aliasDomain.endsWith('.' + mainDomain)) {
                                return aliasDomain.substring(0, aliasDomain.length - mainDomain.length - 1);
                            }
                            return '';
                        }).filter(p => p);
                        document.getElementById('alias_prefixes').value = prefixes.join(',');
                    } else {
                        document.getElementById('alias_prefixes').value = '';
                    }
                    
                    // 加载分类并设置选中值
                    await loadCategories();
                    const categorySelect = document.getElementById('category_id');
                    if (categorySelect && site.category_id) {
                        categorySelect.value = site.category_id;
                    }
                    
                    // 基础配置
                    // 缓存现在永久有效，无需设置
                    // const enableCacheCheckbox = document.getElementById('enable_cache');
                    // if (enableCacheCheckbox) enableCacheCheckbox.checked = site.enable_cache !== false;
                    
                    const enablePreloadCheckbox = document.getElementById('enable_preload');
                    if (enablePreloadCheckbox) enablePreloadCheckbox.checked = site.enable_preload || false;
                    
                    const downloadExternalCheckbox = document.getElementById('download_external_resources');
                    if (downloadExternalCheckbox) downloadExternalCheckbox.checked = site.download_external_resources || false;
                    
                    const enableHttpsCheckbox = document.getElementById('enable_https_check');
                    if (enableHttpsCheckbox) enableHttpsCheckbox.checked = site.enable_https_check || false;
                    
                    const enableTraditionalCheckbox = document.getElementById('enable_traditional_convert');
                    if (enableTraditionalCheckbox) enableTraditionalCheckbox.checked = site.enable_traditional_convert || false;
                    
                    // UA判断设置（三态控制）
                    const uaCheckMode = document.getElementById('ua_check_mode');
                    if (uaCheckMode) {
                        // 判断使用哪种模式
                        console.log('UA Check Settings:', {
                            use_global_ua_check: site.use_global_ua_check,
                            enable_ua_check: site.enable_ua_check
                        });
                        
                        if (site.use_global_ua_check === false) {
                            // 站点独立控制
                            if (site.enable_ua_check === true) {
                                uaCheckMode.value = 'enable';
                                const uaSettings = document.getElementById('ua-settings');
                                if (uaSettings) uaSettings.classList.remove('hidden');
                            } else {
                                uaCheckMode.value = 'disable';
                            }
                        } else {
                            // use_global_ua_check为true或null时，使用全局设置
                            uaCheckMode.value = 'global';
                        }
                        
                        // 触发onChange事件以更新UI
                        toggleUASettingsMode();
                    }
                    const allowedUAInput = document.getElementById('allowed_ua');
                    if (allowedUAInput) allowedUAInput.value = site.allowed_ua || '';
                    const nonSpiderHTMLTextarea = document.getElementById('non_spider_html');
                    if (nonSpiderHTMLTextarea) nonSpiderHTMLTextarea.value = site.non_spider_html || '';
                    
                    // 来源判断设置
                    const refererCheckMode = document.getElementById('referer_check_mode');
                    if (refererCheckMode) {
                        if (site.use_global_referer_check === false) {
                            if (site.enable_referer_check === true) {
                                refererCheckMode.value = 'enable';
                                const refererSettings = document.getElementById('referer-settings');
                                if (refererSettings) refererSettings.classList.remove('hidden');
                            } else {
                                refererCheckMode.value = 'disable';
                            }
                        } else {
                            refererCheckMode.value = 'global';
                        }
                        toggleRefererSettings();
                    }
                    const allowedReferersInput = document.getElementById('allowed_referers');
                    if (allowedReferersInput) allowedReferersInput.value = site.allowed_referers || '';
                    const refererBlockCodeInput = document.getElementById('referer_block_code');
                    if (refererBlockCodeInput) refererBlockCodeInput.value = site.referer_block_code || 403;
                    const refererBlockHTMLTextarea = document.getElementById('referer_block_html');
                    if (refererBlockHTMLTextarea) refererBlockHTMLTextarea.value = site.referer_block_html || '';
                    
                    // 域名跳转设置
                    const redirectWWWSelect = document.getElementById('redirect_www');
                    if (redirectWWWSelect) {
                        if (site.redirect_www === true) {
                            redirectWWWSelect.value = 'true';
                        } else if (site.redirect_www === false) {
                            redirectWWWSelect.value = 'false';
                        } else {
                            redirectWWWSelect.value = '';
                        }
                    }
                    
                    // 蜘蛛屏蔽设置
                    const enableSpiderBlockCheckbox = document.getElementById('enable_spider_block');
                    if (enableSpiderBlockCheckbox) {
                        enableSpiderBlockCheckbox.checked = site.enable_spider_block || false;
                        if (site.enable_spider_block) {
                            const spiderBlockConfig = document.getElementById('spider-block-config');
                            if (spiderBlockConfig) spiderBlockConfig.classList.remove('hidden');
                        }
                    }
                    
                    const useGlobalSpiderUACheckbox = document.getElementById('use_global_spider_ua');
                    if (useGlobalSpiderUACheckbox) useGlobalSpiderUACheckbox.checked = site.use_global_spider_ua || false;
                    const spiderUAInput = document.getElementById('custom_spider_ua');
                    if (spiderUAInput) {
                        spiderUAInput.value = site.custom_spider_ua || '';
                        spiderUAInput.disabled = site.use_global_spider_ua || false;
                    }
                    const spiderBlock403Textarea = document.getElementById('spider_block_403_template');
                    if (spiderBlock403Textarea) spiderBlock403Textarea.value = site.spider_block_403_template || '';
                    
                    // Sitemap设置
                    const enableSitemapCheckbox = document.getElementById('enable_sitemap');
                    if (enableSitemapCheckbox) {
                        enableSitemapCheckbox.checked = site.enable_sitemap || false;
                        if (site.enable_sitemap) {
                            const sitemapConfig = document.getElementById('sitemap-config');
                            if (sitemapConfig) sitemapConfig.classList.remove('hidden');
                        }
                    }
                    const sitemapUpdateInterval = document.getElementById('sitemap_update_interval');
                    if (sitemapUpdateInterval) sitemapUpdateInterval.value = site.sitemap_update_interval || 60;
                    const sitemapChangefreq = document.getElementById('sitemap_changefreq');
                    if (sitemapChangefreq) sitemapChangefreq.value = site.sitemap_changefreq || 'daily';
                    const sitemapPriority = document.getElementById('sitemap_priority');
                    if (sitemapPriority) sitemapPriority.value = site.sitemap_priority || 0.5;
                    const sitemapMaxUrls = document.getElementById('sitemap_max_urls');
                    if (sitemapMaxUrls) sitemapMaxUrls.value = site.sitemap_max_urls || 50000;
                    
                    // 缓存设置
                    const useGlobalCache = site.use_global_cache !== false; // 默认使用全局设置
                    document.getElementById('use_global_cache').checked = useGlobalCache;
                    document.getElementById('cache_max_size').value = site.cache_max_size || 1024;
                    document.getElementById('cache_home_ttl').value = site.cache_home_ttl || 30;
                    document.getElementById('cache_other_ttl').value = site.cache_other_ttl || 1440;
                    document.getElementById('enable_redis_cache').checked = site.enable_redis_cache !== false; // 默认开启
                    toggleCacheInputs(); // 更新输入框状态
                    
                    // 统计设置 - 复选框控制是否使用全局统计代码
                    const enableAnalytics = document.getElementById('enable_analytics');
                    if (enableAnalytics) {
                        // use_global_analytics为null或true时勾选，false时不勾选
                        enableAnalytics.checked = site.use_global_analytics !== false;
                    }
                    
                    // 设置内容注入配置
                    if (site.inject_config) {
                        document.getElementById('enable_keyword').checked = site.inject_config.enable_keyword || false;
                        document.getElementById('enable_structure').checked = site.inject_config.enable_structure || false;
                        // 设置结构注入配置
                        if (site.inject_config.enable_structure) {
                            toggleStructureSettings();
                            // 设置最小最大注入数量
                            document.getElementById('structure_min_per_page').value = site.inject_config.structure_min_per_page || 10;
                            document.getElementById('structure_max_per_page').value = site.inject_config.structure_max_per_page || 20;
                            // 加载已选择的词库
                            if (site.inject_config.structure_library_ids && site.inject_config.structure_library_ids.length > 0) {
                                loadStructureKeywordLibraries().then(() => {
                                    site.inject_config.structure_library_ids.forEach(id => {
                                        const checkbox = document.querySelector(`input[name="structure-keyword-library"][value="${id}"]`);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }
                                    });
                                });
                            }
                        }
                        document.getElementById('enable_pseudo').checked = site.inject_config.enable_pseudo || false;
                        document.getElementById('filter_external_links').checked = site.inject_config.filter_external_links || false;
                        document.getElementById('home_title').value = site.inject_config.home_title || '';
                        document.getElementById('home_description').value = site.inject_config.home_description || '';
                        document.getElementById('home_keywords').value = site.inject_config.home_keywords || '';
                        // 设置Unicode主开关 - 根据实际的enable_unicode值
                        document.getElementById('enable_unicode').checked = site.inject_config.enable_unicode === true;
                        
                        // 如果Unicode开关打开，显示详细选项
                        if (site.inject_config.enable_unicode === true) {
                            const unicodeOptions = document.getElementById('unicode-options');
                            if (unicodeOptions) {
                                unicodeOptions.classList.remove('hidden');
                            }
                            
                            // 设置Unicode转码细粒度控制
                            const unicodeScope = document.getElementById('unicode_scope');
                            if (unicodeScope) {
                                unicodeScope.value = site.inject_config.unicode_scope || 'homepage';
                            }
                            
                            const unicodeTitle = document.getElementById('enable_unicode_title');
                            if (unicodeTitle) {
                                unicodeTitle.checked = site.inject_config.enable_unicode_title === true;
                            }
                            
                            const unicodeKeywords = document.getElementById('enable_unicode_keywords');
                            if (unicodeKeywords) {
                                unicodeKeywords.checked = site.inject_config.enable_unicode_keywords === true;
                            }
                            
                            const unicodeDesc = document.getElementById('enable_unicode_desc');
                            if (unicodeDesc) {
                                unicodeDesc.checked = site.inject_config.enable_unicode_desc === true;
                            }
                        }
                        
                        document.getElementById('enable_random_string').checked = site.inject_config.enable_random_string || false;
                        document.getElementById('random_string_length').value = site.inject_config.random_string_length || 4;
                        
                        // 拼音设置模式
                        const pinyinMode = document.getElementById('pinyin_mode');
                        if (pinyinMode) {
                            // 根据use_global_pinyin判断模式
                            // null = 使用全局设置
                            // true = 独立开启
                            // false = 独立关闭
                            if (site.inject_config.use_global_pinyin === null || site.inject_config.use_global_pinyin === undefined) {
                                // 使用全局设置
                                pinyinMode.value = 'global';
                                document.getElementById('pinyin_custom_settings').style.display = 'none';
                                document.getElementById('pinyin_global_hint').style.display = 'block';
                            } else if (site.inject_config.use_global_pinyin === true) {
                                // 独立开启
                                pinyinMode.value = 'enable';
                                document.getElementById('pinyin_custom_settings').style.display = 'block';
                                document.getElementById('pinyin_special_chars_settings').style.display = 'block';
                                document.getElementById('pinyin_global_hint').style.display = 'none';
                            } else {
                                // 独立关闭 (use_global_pinyin === false)
                                pinyinMode.value = 'disable';
                                document.getElementById('pinyin_custom_settings').style.display = 'none';
                                document.getElementById('pinyin_global_hint').style.display = 'none';
                            }
                        }
                        document.getElementById('enable_pinyin_special_chars').checked = site.inject_config.enable_pinyin_special_chars || false;
                        document.getElementById('pinyin_special_chars_ratio').value = (site.inject_config.pinyin_special_chars_ratio * 100) || 30;
                        // 特殊字符列表从全局设置继承，不需要在站点级别设置
                        
                        document.getElementById('enable_h1_tag').checked = site.inject_config.enable_h1_tag || false;
                        document.getElementById('h1_tag_position').value = site.inject_config.h1_tag_position || 'both';
                        document.getElementById('enable_hidden_html').checked = site.inject_config.enable_hidden_html || false;
                        document.getElementById('hidden_html_length').value = site.inject_config.hidden_html_length || 50;
                        document.getElementById('hidden_html_random_id').checked = site.inject_config.hidden_html_random_id !== false;
                        document.getElementById('hidden_html_position').value = site.inject_config.hidden_html_position || 'both';
                        
                        // 首页关键词注入设置
                        document.getElementById('enable_home_keyword_inject').checked = site.inject_config.enable_home_keyword_inject || false;
                        document.getElementById('home_keyword_inject_count').value = site.inject_config.home_keyword_inject_count || 10;
                        document.getElementById('enable_home_keyword_unicode').checked = site.inject_config.enable_home_keyword_unicode || false;
                        
                        document.getElementById('keywords').value = (site.inject_config.keywords || []).join('\n');
                        
                        // 加载关键词注入详细配置
                        const setElementValue = (id, value, isChecked = false) => {
                            const element = document.getElementById(id);
                            if (element) {
                                if (isChecked) {
                                    element.checked = value;
                                } else {
                                    element.value = value;
                                }
                            }
                        };
                        
                        setElementValue('keyword_inject_title', site.inject_config.keyword_inject_title !== false, true);
                        setElementValue('keyword_inject_meta', site.inject_config.keyword_inject_meta !== false, true);
                        setElementValue('keyword_inject_desc', site.inject_config.keyword_inject_desc !== false, true);
                        setElementValue('keyword_inject_body', site.inject_config.keyword_inject_body !== false, true);
                        setElementValue('keyword_inject_h1', site.inject_config.keyword_inject_h1 || false, true);
                        setElementValue('keyword_inject_h2', site.inject_config.keyword_inject_h2 || false, true);
                        setElementValue('keyword_inject_alt', site.inject_config.keyword_inject_alt || false, true);
                        setElementValue('keyword_inject_hidden', site.inject_config.keyword_inject_hidden || false, true);
                        setElementValue('keyword_max_per_page', site.inject_config.keyword_max_per_page || 10);
                        // 注入概率从小数转换为百分比显示（如0.3转为30）
                        const ratioValue = site.inject_config.keyword_inject_ratio ? 
                            (site.inject_config.keyword_inject_ratio < 1 ? site.inject_config.keyword_inject_ratio * 100 : site.inject_config.keyword_inject_ratio) : 30;
                        setElementValue('keyword_inject_ratio', ratioValue);
                        setElementValue('keyword_min_word_count', site.inject_config.keyword_min_word_count || 20);
                        setElementValue('keyword_density', site.inject_config.keyword_density || 2);
                        
                        // 加载关键词模板配置
                        setElementValue('keyword_title_template', site.inject_config.keyword_title_template || '{keyword1}-{original}');
                        setElementValue('keyword_meta_template', site.inject_config.keyword_meta_template || '{keyword1},{keyword2},{keyword3}');
                        setElementValue('keyword_desc_template', site.inject_config.keyword_desc_template || '{keyword1}是专业的{keyword2}服务商');
                        
                        // 显示/隐藏相关设置区域
                        if (site.inject_config.enable_keyword) {
                            document.getElementById('keywords-group').classList.remove('hidden');
                        } else {
                            document.getElementById('keywords-group').classList.add('hidden');
                        }
                        if (site.inject_config.enable_random_string) {
                            document.getElementById('random-string-settings').classList.remove('hidden');
                        }
                        if (site.inject_config.enable_h1_tag) {
                            document.getElementById('h1-tag-settings').classList.remove('hidden');
                        }
                        if (site.inject_config.enable_hidden_html) {
                            document.getElementById('hidden-html-settings').classList.remove('hidden');
                        }
                        if (site.inject_config.enable_home_keyword_inject) {
                            document.getElementById('home-keyword-settings').classList.remove('hidden');
                            // 加载关键词库并设置已选择的
                            loadKeywordLibrariesForHome().then(() => {
                                if (site.inject_config.home_keyword_library_ids && site.inject_config.home_keyword_library_ids.length > 0) {
                                    site.inject_config.home_keyword_library_ids.forEach(id => {
                                        const checkbox = document.querySelector(`input[name="home-keyword-library"][value="${id}"]`);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }
                                    });
                                }
                            });
                        }
                        
                        // 处理企业名称设置
                        if (site.enable_company_name) {
                            document.getElementById('enable_company_name').checked = true;
                            document.getElementById('company-name-settings').classList.remove('hidden');
                            loadCompanyLibraries().then(() => {
                                if (site.company_library_id) {
                                    document.getElementById('company_library_id').value = site.company_library_id;
                                }
                            });
                            if (site.company_name) {
                                document.getElementById('current-company-name-div').classList.remove('hidden');
                                document.getElementById('company_name').value = site.company_name;
                            }
                        }
                        
                        // 处理伪原创设置
                        if (site.inject_config.enable_pseudo) {
                            document.getElementById('pseudo-settings').classList.remove('hidden');
                            loadPseudoLibraries().then(() => {
                                // 设置已选中的词库
                                if (site.inject_config.pseudo_library_ids) {
                                    site.inject_config.pseudo_library_ids.forEach(id => {
                                        const checkbox = document.querySelector(`input[name="pseudo-library"][value="${id}"]`);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }
                                    });
                                }
                            });
                        }
                        
                        // 处理关键词库设置
                        if (site.inject_config.enable_keyword) {
                            loadKeywordLibraries().then(() => {
                                // 设置已选中的关键词库
                                if (site.inject_config.keyword_library_ids) {
                                    site.inject_config.keyword_library_ids.forEach(id => {
                                        const checkbox = document.querySelector(`input[name="keyword-library"][value="${id}"]`);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }
                                    });
                                }
                                
                                // 设置独立的关键词库
                                if (site.inject_config.title_keyword_library_ids) {
                                    site.inject_config.title_keyword_library_ids.forEach(id => {
                                        const checkbox = document.querySelector(`input[name="title-keyword-library"][value="${id}"]`);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }
                                    });
                                }
                                
                                if (site.inject_config.meta_keyword_library_ids) {
                                    site.inject_config.meta_keyword_library_ids.forEach(id => {
                                        const checkbox = document.querySelector(`input[name="meta-keyword-library"][value="${id}"]`);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }
                                    });
                                }
                                
                                if (site.inject_config.desc_keyword_library_ids) {
                                    site.inject_config.desc_keyword_library_ids.forEach(id => {
                                        const checkbox = document.querySelector(`input[name="desc-keyword-library"][value="${id}"]`);
                                        if (checkbox) {
                                            checkbox.checked = true;
                                        }
                                    });
                                }
                            });
                        }
                    }
                    
                    if (site.custom_rules) {
                        document.getElementById('custom_rules').value = JSON.stringify(site.custom_rules, null, 2);
                    }
                    
                    // 切换到SEO优化标签页
                    switchTab('seo');
                    
                    document.getElementById('site-modal').classList.remove('hidden');
                } else {
                    showToast(data.error || '加载失败', 'error');
                }
            } catch (error) {
                showToast('加载失败: ' + error.message, 'error');
            }
        }

        // 提交表单
        document.getElementById('site-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const id = document.getElementById('site-id').value;
            const formData = {
                domain: document.getElementById('domain').value,
                target_url: document.getElementById('target_url').value,
                category_id: document.getElementById('category_id').value ? parseInt(document.getElementById('category_id').value) : null,
                crawl_depth: parseInt(document.getElementById('crawl_depth').value),
                status: document.getElementById('status').value,
                // 子域名前缀
                alias_prefixes: document.getElementById('alias_prefixes').value ? 
                    document.getElementById('alias_prefixes').value.split(',').map(s => s.trim()).filter(s => s) : [],
                enable_cache: true, // 缓存永久有效
                enable_preload: document.getElementById('enable_preload').checked,
                download_external_resources: document.getElementById('download_external_resources').checked,
                enable_https_check: document.getElementById('enable_https_check').checked,
                enable_traditional_convert: document.getElementById('enable_traditional_convert').checked,
                // UA判断三态控制
                use_global_ua_check: document.getElementById('ua_check_mode')?.value === 'global' ? true : false,
                enable_ua_check: document.getElementById('ua_check_mode')?.value === 'enable',
                // 当使用全局设置时，清空allowed_ua和non_spider_html
                allowed_ua: document.getElementById('ua_check_mode')?.value === 'global' ? '' : (document.getElementById('allowed_ua')?.value || ''),
                non_spider_html: document.getElementById('ua_check_mode')?.value === 'global' ? '' : (document.getElementById('non_spider_html')?.value || ''),
                // 来源判断设置
                // global = 使用全局设置(use_global_referer_check=true)
                // enable = 独立设置(use_global_referer_check=false, enable_referer_check=true)
                // disable = 关闭(use_global_referer_check=false, enable_referer_check=false)
                use_global_referer_check: document.getElementById('referer_check_mode')?.value === 'global',
                enable_referer_check: document.getElementById('referer_check_mode')?.value === 'enable',
                allowed_referers: document.getElementById('referer_check_mode')?.value === 'enable' ? (document.getElementById('allowed_referers')?.value || '') : '',
                referer_block_code: document.getElementById('referer_check_mode')?.value === 'enable' ? (parseInt(document.getElementById('referer_block_code')?.value) || 403) : 403,
                referer_block_html: document.getElementById('referer_check_mode')?.value === 'enable' ? (document.getElementById('referer_block_html')?.value || '') : '',
                redirect_www: document.getElementById('redirect_www').value === 'true' ? true : (document.getElementById('redirect_www').value === 'false' ? false : null),
                enable_spider_block: document.getElementById('enable_spider_block').checked,
                use_global_spider_ua: document.getElementById('use_global_spider_ua').checked,
                custom_spider_ua: document.getElementById('custom_spider_ua').value,
                spider_block_403_template: document.getElementById('spider_block_403_template').value,
                enable_company_name: document.getElementById('enable_company_name').checked,
                company_library_id: document.getElementById('enable_company_name').checked ? parseInt(document.getElementById('company_library_id').value) || 0 : 0,
                company_name: document.getElementById('company_name') ? document.getElementById('company_name').value : '',
                enable_sitemap: document.getElementById('enable_sitemap').checked,
                sitemap_update_interval: parseInt(document.getElementById('sitemap_update_interval').value) || 60,
                sitemap_changefreq: document.getElementById('sitemap_changefreq').value,
                sitemap_priority: parseFloat(document.getElementById('sitemap_priority').value) || 0.5,
                sitemap_max_urls: parseInt(document.getElementById('sitemap_max_urls').value) || 50000,
                use_global_cache: document.getElementById('use_global_cache').checked,
                cache_max_size: parseInt(document.getElementById('cache_max_size').value),
                cache_home_ttl: parseInt(document.getElementById('cache_home_ttl').value),
                cache_other_ttl: parseInt(document.getElementById('cache_other_ttl').value),
                enable_redis_cache: document.getElementById('enable_redis_cache').checked,
                // 统计设置 - 复选框控制是否使用全局统计代码
                use_global_analytics: document.getElementById('enable_analytics').checked ? null : false,
                inject_config: {
                    enable_keyword: document.getElementById('enable_keyword').checked,
                    enable_structure: document.getElementById('enable_structure').checked,
                    structure_library_ids: document.getElementById('enable_structure').checked ? getSelectedStructureLibraries() : [],
                    structure_min_per_page: parseInt(document.getElementById('structure_min_per_page').value) || 10,
                    structure_max_per_page: parseInt(document.getElementById('structure_max_per_page').value) || 20,
                    enable_pseudo: document.getElementById('enable_pseudo').checked,
                    filter_external_links: document.getElementById('filter_external_links').checked,
                    home_title: document.getElementById('home_title').value,
                    home_description: document.getElementById('home_description').value,
                    home_keywords: document.getElementById('home_keywords').value,
                    enable_unicode: document.getElementById('enable_unicode').checked,
                    unicode_scope: document.getElementById('enable_unicode').checked && document.getElementById('unicode_scope') ? document.getElementById('unicode_scope').value : '',
                    enable_unicode_title: document.getElementById('enable_unicode').checked && document.getElementById('enable_unicode_title') ? document.getElementById('enable_unicode_title').checked : false,
                    enable_unicode_keywords: document.getElementById('enable_unicode').checked && document.getElementById('enable_unicode_keywords') ? document.getElementById('enable_unicode_keywords').checked : false,
                    enable_unicode_desc: document.getElementById('enable_unicode').checked && document.getElementById('enable_unicode_desc') ? document.getElementById('enable_unicode_desc').checked : false,
                    enable_random_string: document.getElementById('enable_random_string').checked,
                    random_string_length: parseInt(document.getElementById('random_string_length').value) || 4,
                    // 拼音设置
                    use_global_pinyin: document.getElementById('pinyin_mode').value === 'global' ? null : 
                                      (document.getElementById('pinyin_mode').value === 'enable' ? true : false),
                    enable_pinyin: document.getElementById('pinyin_mode').value === 'enable',
                    enable_pinyin_special_chars: document.getElementById('pinyin_mode').value === 'enable' ? document.getElementById('enable_pinyin_special_chars').checked : false,
                    pinyin_special_chars_ratio: document.getElementById('pinyin_mode').value === 'enable' ? (parseFloat(document.getElementById('pinyin_special_chars_ratio').value) / 100 || 0.3) : 0,
                    // pinyin_special_chars 从全局设置继承，不需要在站点级别设置
                    enable_h1_tag: document.getElementById('enable_h1_tag').checked,
                    h1_tag_position: document.getElementById('h1_tag_position').value,
                    enable_hidden_html: document.getElementById('enable_hidden_html').checked,
                    hidden_html_length: parseInt(document.getElementById('hidden_html_length').value) || 50,
                    hidden_html_random_id: document.getElementById('hidden_html_random_id').checked,
                    hidden_html_position: document.getElementById('hidden_html_position').value,
                    // 首页关键词注入
                    enable_home_keyword_inject: document.getElementById('enable_home_keyword_inject').checked,
                    home_keyword_library_ids: getSelectedHomeKeywordLibraryIDs(),
                    home_keyword_inject_count: parseInt(document.getElementById('home_keyword_inject_count').value) || 10,
                    enable_home_keyword_unicode: document.getElementById('enable_home_keyword_unicode').checked,
                    pseudo_library_ids: getSelectedPseudoLibraries(),
                    keyword_library_ids: getSelectedKeywordLibraries(),  // 获取选中的关键词库
                    title_keyword_library_ids: getSelectedTitleKeywordLibraries(),  // 标题关键词库
                    meta_keyword_library_ids: getSelectedMetaKeywordLibraries(),   // Meta关键词库
                    desc_keyword_library_ids: getSelectedDescKeywordLibraries(),   // 描述关键词库
                    // 新增关键词注入详细配置
                    keyword_inject_title: document.getElementById('keyword_inject_title') ? document.getElementById('keyword_inject_title').checked : false,
                    keyword_inject_meta: document.getElementById('keyword_inject_meta') ? document.getElementById('keyword_inject_meta').checked : false,
                    keyword_inject_desc: document.getElementById('keyword_inject_desc') ? document.getElementById('keyword_inject_desc').checked : false,
                    keyword_inject_body: document.getElementById('keyword_inject_body') ? document.getElementById('keyword_inject_body').checked : false,
                    keyword_inject_h1: document.getElementById('keyword_inject_h1') ? document.getElementById('keyword_inject_h1').checked : false,
                    keyword_inject_h2: document.getElementById('keyword_inject_h2') ? document.getElementById('keyword_inject_h2').checked : false,
                    keyword_inject_alt: document.getElementById('keyword_inject_alt') ? document.getElementById('keyword_inject_alt').checked : false,
                    keyword_inject_hidden: document.getElementById('keyword_inject_hidden') ? document.getElementById('keyword_inject_hidden').checked : false,
                    keyword_max_per_page: document.getElementById('keyword_max_per_page') ? parseInt(document.getElementById('keyword_max_per_page').value) || 10 : 10,
                    keyword_inject_ratio: document.getElementById('keyword_inject_ratio') ? parseFloat(document.getElementById('keyword_inject_ratio').value) / 100 || 0.3 : 0.3,
                    keyword_min_word_count: document.getElementById('keyword_min_word_count') ? parseInt(document.getElementById('keyword_min_word_count').value) || 20 : 20,
                    keyword_density: document.getElementById('keyword_density') ? parseFloat(document.getElementById('keyword_density').value) || 2 : 2,
                    // 关键词模板配置
                    keyword_title_template: document.getElementById('keyword_title_template') ? document.getElementById('keyword_title_template').value || '{keyword1}-{original}' : '{keyword1}-{original}',
                    keyword_meta_template: document.getElementById('keyword_meta_template') ? document.getElementById('keyword_meta_template').value || '{keyword1},{keyword2},{keyword3}' : '{keyword1},{keyword2},{keyword3}',
                    keyword_desc_template: document.getElementById('keyword_desc_template') ? document.getElementById('keyword_desc_template').value || '{keyword1}是专业的{keyword2}服务商' : '{keyword1}是专业的{keyword2}服务商',
                }
            };
            
            const customRules = document.getElementById('custom_rules').value;
            if (customRules) {
                try {
                    formData.custom_rules = JSON.parse(customRules);
                } catch (e) {
                    showToast('自定义规则格式错误', 'error');
                    return;
                }
            }
            
            try {
                showLoading();
                const url = id ? `/api/v1/sites/${id}` : '/api/v1/sites';
                const method = id ? 'PUT' : 'POST';
                
                const res = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await res.json();
                hideLoading();
                
                if (data.success) {
                    showToast(id ? '更新成功' : '添加成功', 'success');
                    hideModal();
                    loadSites();
                } else {
                    showToast(data.error || '操作失败', 'error');
                }
            } catch (error) {
                hideLoading();
                showToast('操作失败: ' + error.message, 'error');
            }
        });

        // 删除站点
        async function deleteSite(id) {
            if (!confirm('确定要删除这个站点吗？')) return;
            
            try {
                const res = await fetch(`/api/v1/sites/${id}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showToast('删除成功', 'success');
                    loadSites();
                } else {
                    showToast(data.error || '删除失败', 'error');
                }
            } catch (error) {
                showToast('删除失败: ' + error.message, 'error');
            }
        }
        
        // 清空站点缓存
        async function clearSiteCache(id, domain) {
            if (!confirm(`确定要清空站点 ${domain} 的缓存吗？`)) return;
            
            try {
                const res = await fetch(`/api/v1/cache/site/${domain}`, {
                    method: 'DELETE'
                });
                const data = await res.json();
                
                if (data.success) {
                    const fileCount = data.data.deleted_count || 0;
                    const sitemapCount = data.data.sitemap_deleted_count || 0;
                    let message = `站点 ${domain} 缓存清空成功，共删除 ${fileCount} 个文件`;
                    if (sitemapCount > 0) {
                        message += `，清空 ${sitemapCount} 个sitemap条目`;
                    }
                    showToast(message, 'success');
                    // 刷新列表以更新sitemap数量
                    loadSites();
                } else {
                    showToast(data.error || '清空缓存失败', 'error');
                }
            } catch (error) {
                showToast('清空缓存失败: ' + error.message, 'error');
            }
        }

        // 刷新列表
        function refreshSites() {
            loadSites();
            showToast('列表已刷新', 'success');
        }

        // 获取状态样式
        function getStatusClass(status) {
            const classes = {
                'active': 'bg-green-100 text-green-800',
                'inactive': 'bg-gray-100 text-gray-800',
                'error': 'bg-red-100 text-red-800'
            };
            return classes[status] || 'bg-gray-100 text-gray-800';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'active': '活跃',
                'inactive': '未激活',
                'error': '错误'
            };
            return texts[status] || status;
        }

        // 生成功能标签（可收起）
        function generateFeatureTags(site) {
            const tags = [];
            
            // 收集所有功能标签（Sitemap优先显示）
            if (site.enable_sitemap) tags.push({ name: 'Sitemap', class: 'bg-pink-100 text-pink-800' });
            if (site.enable_cache) tags.push({ name: '缓存', class: 'bg-blue-100 text-blue-800' });
            if (site.enable_redis_cache) tags.push({ name: 'Redis', class: 'bg-red-100 text-red-800' });
            if (site.enable_preload) tags.push({ name: '预加载', class: 'bg-purple-100 text-purple-800' });
            if (site.enable_traditional_convert) tags.push({ name: '繁简转换', class: 'bg-indigo-100 text-indigo-800' });
            if (site.enable_https_check) tags.push({ name: 'HTTPS检查', class: 'bg-green-100 text-green-800' });
            if (site.enable_analytics) tags.push({ name: '统计', class: 'bg-yellow-100 text-yellow-800' });
            if (site.enable_company_name) tags.push({ name: '企业名', class: 'bg-orange-100 text-orange-800' });
            if (site.enable_ua_check) tags.push({ name: 'UA判断', class: 'bg-teal-100 text-teal-800' });
            if (site.enable_referer_check) tags.push({ name: '来源判断', class: 'bg-cyan-100 text-cyan-800' });
            if (site.enable_spider_block) tags.push({ name: '爬虫屏蔽', class: 'bg-gray-100 text-gray-800' });
            
            // 注入功能
            if (site.inject_config) {
                if (site.inject_config.enable_keyword) tags.push({ name: '关键词', class: 'bg-lime-100 text-lime-800' });
                if (site.inject_config.enable_structure) tags.push({ name: '结构注入', class: 'bg-amber-100 text-amber-800' });
                if (site.inject_config.enable_pseudo) tags.push({ name: '伪原创', class: 'bg-emerald-100 text-emerald-800' });
                if (site.inject_config.enable_unicode) tags.push({ name: 'Unicode', class: 'bg-violet-100 text-violet-800' });
                if (site.inject_config.enable_pinyin) tags.push({ name: '拼音', class: 'bg-fuchsia-100 text-fuchsia-800' });
                if (site.inject_config.enable_random_string) tags.push({ name: '随机串', class: 'bg-rose-100 text-rose-800' });
                if (site.inject_config.enable_hidden_html) tags.push({ name: '隐藏HTML', class: 'bg-slate-100 text-slate-800' });
            }
            
            if (tags.length === 0) {
                return '<span class="text-xs text-gray-400">无功能</span>';
            }
            
            // 如果只有1个标签，直接显示
            if (tags.length === 1) {
                return `<span class="inline-block px-2 py-1 text-xs rounded ${tags[0].class}">${tags[0].name}</span>`;
            }
            
            // 标签超过1个，只显示第1个并添加展开/收起功能
            const visibleTags = tags.slice(0, 1);
            const hiddenTags = tags.slice(1);
            
            return `
                <div class="feature-tags" data-site-id="${site.id}">
                    <span class="tags-preview">
                        ${visibleTags.map(tag => 
                            `<span class="inline-block px-2 py-1 text-xs rounded ${tag.class} mr-1 mb-1">${tag.name}</span>`
                        ).join('')}
                        <a href="javascript:void(0)" onclick="toggleFeatureTags(${site.id})" class="text-blue-500 hover:text-blue-700 text-xs">
                            +${hiddenTags.length}个
                        </a>
                    </span>
                    <span class="tags-full hidden">
                        ${tags.map(tag => 
                            `<span class="inline-block px-2 py-1 text-xs rounded ${tag.class} mr-1 mb-1">${tag.name}</span>`
                        ).join('')}
                        <a href="javascript:void(0)" onclick="toggleFeatureTags(${site.id})" class="text-blue-500 hover:text-blue-700 text-xs">
                            收起
                        </a>
                    </span>
                </div>
            `;
        }

        // 切换子域名显示
        function toggleAliases(siteId) {
            const aliasContainer = document.querySelector(`.alias-list[data-site-id="${siteId}"]`);
            if (aliasContainer) {
                const preview = aliasContainer.querySelector('.alias-preview');
                const full = aliasContainer.querySelector('.alias-full');
                
                if (preview.classList.contains('hidden')) {
                    preview.classList.remove('hidden');
                    full.classList.add('hidden');
                } else {
                    preview.classList.add('hidden');
                    full.classList.remove('hidden');
                }
            }
        }

        // 切换功能标签显示
        function toggleFeatureTags(siteId) {
            const tagsContainer = document.querySelector(`.feature-tags[data-site-id="${siteId}"]`);
            if (tagsContainer) {
                const preview = tagsContainer.querySelector('.tags-preview');
                const full = tagsContainer.querySelector('.tags-full');
                
                if (preview.classList.contains('hidden')) {
                    preview.classList.remove('hidden');
                    full.classList.add('hidden');
                } else {
                    preview.classList.add('hidden');
                    full.classList.remove('hidden');
                }
            }
        }

        // 点击行切换复选框
        function toggleRowCheckbox(event, siteId) {
            // 如果点击的是链接或按钮，不处理
            if (event.target.tagName === 'A' || event.target.tagName === 'BUTTON' || event.target.tagName === 'I') {
                return;
            }
            
            // 查找对应的复选框
            const checkbox = document.querySelector(`.site-checkbox[value="${siteId}"]`);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                onSiteCheckChange();
            }
        }
        
        // 获取缓存状态徽章
        function getCacheStatusBadge(status, error) {
            if (!status) status = 'pending';
            
            const statusConfig = {
                'pending': {
                    class: 'bg-yellow-100 text-yellow-800',
                    icon: 'fas fa-clock',
                    text: '待获取'
                },
                'success': {
                    class: 'bg-green-100 text-green-800',
                    icon: 'fas fa-check-circle',
                    text: '成功'
                },
                'failed': {
                    class: 'bg-red-100 text-red-800',
                    icon: 'fas fa-times-circle',
                    text: '失败'
                },
                'updating': {
                    class: 'bg-blue-100 text-blue-800',
                    icon: 'fas fa-sync-alt',
                    text: '更新中'
                }
            };
            
            const config = statusConfig[status] || statusConfig['pending'];
            const errorTitle = error ? ` title="${error}"` : '';
            
            return `<span class="inline-flex items-center px-2 py-1 text-xs rounded ${config.class}" ${errorTitle}>
                <i class="${config.icon} mr-1"></i>
                ${config.text}
            </span>`;
        }
        
        // 排序表格
        function sortTable(column) {
            // 如果点击同一列，切换排序方向
            if (currentSortColumn === column) {
                currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                currentSortColumn = column;
                currentSortOrder = 'desc'; // 默认降序
            }
            
            // 保存排序设置到 localStorage
            localStorage.setItem('sites_sort_column', currentSortColumn);
            localStorage.setItem('sites_sort_order', currentSortOrder);
            
            // 更新所有排序图标
            updateSortIcons();
            
            // 重新加载数据（后端会处理排序）
            loadSites();
        }
        
        // 更新排序图标
        function updateSortIcons() {
            // 重置所有图标
            const sortableColumns = ['cache_status', 'sitemap_count', 'count_404'];
            sortableColumns.forEach(col => {
                const icon = document.getElementById(`sort-icon-${col}`);
                if (icon) {
                    icon.className = 'fas fa-sort text-gray-400';
                }
            });
            
            // 设置当前排序列的图标
            if (currentSortColumn) {
                const icon = document.getElementById(`sort-icon-${currentSortColumn}`);
                if (icon) {
                    if (currentSortOrder === 'asc') {
                        icon.className = 'fas fa-sort-up text-blue-500';
                    } else {
                        icon.className = 'fas fa-sort-down text-blue-500';
                    }
                }
            }
        }

        
        
        // 加载分类列表
        async function loadCategories(specificSelectId = null) {
            try {
                const response = await fetchWithAuth('/api/v1/site-categories');
                const data = await response.json();
                
                if (data.success) {
                    const categories = data.data || [];
                    
                    // 如果指定了特定的选择器ID，只更新该选择器
                    if (specificSelectId) {
                        const select = document.getElementById(specificSelectId);
                        if (select) {
                            const currentValue = select.value;
                            select.innerHTML = '<option value="">选择分类</option>';
                            categories.forEach(category => {
                                const option = document.createElement('option');
                                option.value = category.id;
                                option.textContent = category.name;
                                select.appendChild(option);
                            });
                            if (currentValue) {
                                select.value = currentValue;
                            }
                        }
                        return;
                    }
                    
                    // 更新筛选下拉框
                    const filterSelect = document.getElementById('category-filter');
                    if (filterSelect) {
                        const currentFilterValue = filterSelect.value;
                        filterSelect.innerHTML = '<option value="">全部分类</option>';
                        categories.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category.id;
                            option.textContent = category.name;
                            if (category.color) {
                                option.style.color = category.color;
                            }
                            filterSelect.appendChild(option);
                        });
                        if (currentFilterValue) {
                            filterSelect.value = currentFilterValue;
                        }
                    }
                    
                    // 更新所有分类选择框（包括单个添加和批量添加）
                    const selects = document.querySelectorAll('select[name="category_id"], #category_id, #batch-category-id');
                    selects.forEach(select => {
                        // 保存当前选中的值
                        const currentValue = select.value;
                        
                        // 清空并重新填充选项
                        select.innerHTML = '<option value="">选择分类</option>';
                        categories.forEach(category => {
                            const option = document.createElement('option');
                            option.value = category.id;
                            option.textContent = category.name;
                            select.appendChild(option);
                        });
                        
                        // 恢复选中值
                        if (currentValue) {
                            select.value = currentValue;
                        }
                    });
                }
            } catch (error) {
                console.error('加载分类失败:', error);
            }
        }
        
        // 处理搜索
        // 这个函数已经在上面定义过，这里是重复的，可以删除
        
        // 按分类筛选
        function filterByCategory(categoryId) {
            const filterSelect = document.getElementById('category-filter');
            if (filterSelect) {
                filterSelect.value = categoryId;
                currentPage = 1; // 重置到第一页
                loadSites();
                updateClearButton();
            }
        }
        
        // 清除所有筛选
        function clearFilters() {
            document.getElementById('category-filter').value = '';
            const searchInput = document.getElementById('search-input');
            searchInput.value = '';
            searchInput.style.height = '40px';
            currentPage = 1; // 重置到第一页
            // 清除筛选时重置页码
            localStorage.setItem('sites_current_page', currentPage);
            loadSites();
            updateClearButton();
        }
        
        // 更新清除按钮的显示状态
        function updateClearButton() {
            const categoryFilter = document.getElementById('category-filter').value;
            const searchInput = document.getElementById('search-input').value.trim();
            const clearButton = document.getElementById('clear-filters');
            
            // 如果有任何筛选条件，显示清除按钮
            if (categoryFilter || searchInput) {
                clearButton.classList.remove('hidden');
                clearButton.classList.add('flex');
            } else {
                clearButton.classList.add('hidden');
                clearButton.classList.remove('flex');
            }
        }
        
        // 切换UA设置显示
        function toggleUASettingsMode() {
            const select = document.getElementById('ua_check_mode');
            const settings = document.getElementById('ua-settings');
            if (select && settings) {
                if (select.value === 'enable') {
                    settings.classList.remove('hidden');
                } else {
                    settings.classList.add('hidden');
                }
            }
        }
        
        // 批量设置相关功能
        function showBatchSettings() {
            const checkedBoxes = document.querySelectorAll('.site-checkbox:checked');
            if (checkedBoxes.length === 0) {
                showToast('请至少选择一个站点', 'warning');
                return;
            }
            document.getElementById('batch-settings-modal').classList.remove('hidden');
        }
        
        function hideBatchSettings() {
            document.getElementById('batch-settings-modal').classList.add('hidden');
        }
        
        async function applyBatchSettings() {
            const checkedBoxes = document.querySelectorAll('.site-checkbox:checked');
            if (checkedBoxes.length === 0) {
                showToast('请至少选择一个站点', 'warning');
                return;
            }
            
            const siteIds = Array.from(checkedBoxes).map(cb => parseInt(cb.value));
            const settings = {};
            
            // 确保模态框是显示的
            const modal = document.getElementById('batch-settings-modal');
            if (!modal || modal.classList.contains('hidden')) {
                console.error('模态框未显示');
                showToast('请先打开批量设置弹出层', 'error');
                return;
            }
            
            // 等待一小段时间确保表单完全渲染
            await new Promise(resolve => setTimeout(resolve, 200));
            
            // 直接在模态框内查找所有设置项，不依赖表单元素
            const settingInputs = modal.querySelectorAll('[name^="batch_"]');
            console.log('找到设置项数量:', settingInputs.length);
            
            settingInputs.forEach(input => {
                if (!input.checked) return; // 只处理选中的设置项
                
                const settingName = input.name.replace('batch_', '');
                const valueInput = modal.querySelector(`[name="${settingName}_value"]`);
                
                if (valueInput) {
                    if (valueInput.type === 'checkbox') {
                        settings[settingName] = valueInput.checked;
                    } else if (valueInput.tagName === 'SELECT') {
                        const value = valueInput.value;
                        if (value === 'true') {
                            settings[settingName] = true;
                        } else if (value === 'false') {
                            settings[settingName] = false;
                        } else if (value === '') {
                            settings[settingName] = null; // 使用全局设置
                        } else {
                            settings[settingName] = value;
                        }
                    } else {
                        settings[settingName] = valueInput.value;
                    }
                }
            });
            
            // 检查是否有设置项被收集
            if (Object.keys(settings).length === 0) {
                showToast('请至少选择一个配置项', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/v1/sites/batch-update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    },
                    body: JSON.stringify({
                        site_ids: siteIds,
                        settings: settings
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    showToast(`成功更新 ${siteIds.length} 个站点的设置`, 'success');
                    hideBatchSettings();
                    loadSites(); // 刷新列表
                    selectAll(false); // 取消全选
                } else {
                    showToast(`批量更新失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                console.error('批量设置错误:', error);
                showToast('批量更新失败: 网络错误', 'error');
            }
        }
        
        // 批量设置项切换
        function toggleBatchSetting(settingName) {
            const checkbox = document.querySelector(`[name="batch_${settingName}"]`);
            const valueControls = document.querySelector(`#${settingName}_controls`);
            
            if (checkbox.checked) {
                valueControls.classList.remove('hidden');
                valueControls.classList.add('flex');
            } else {
                valueControls.classList.add('hidden');
                valueControls.classList.remove('flex');
            }
        }

        // 选择所有站点或取消选择
        function selectAll(checked) {
            const selectAllCheckbox = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.site-checkbox');
            
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checked;
            }
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;
                if (checked) {
                    selectedSites.add(checkbox.value);
                } else {
                    selectedSites.delete(checkbox.value);
                }
            });
            
            updateBatchActions();
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', async function() {
            await loadGlobalSettings();  // 先加载全局设置
            
            // 恢复页面大小选择器的值
            const pageSizeSelect = document.getElementById('page-size');
            if (pageSizeSelect) {
                pageSizeSelect.value = pageSize;
            }
            
            // 如果有排序设置，更新排序图标
            if (currentSortColumn) {
                updateSortIcons();
            }
            
            loadSites();
            loadCategories();
        });
    </script>
    
    <!-- 批量设置弹出层 -->
    <div id="batch-settings-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
        <div class="relative top-10 mx-auto p-6 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4 pb-4 border-b">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-cogs text-blue-500 mr-2"></i>批量设置
                </h3>
                <button onclick="hideBatchSettings()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                </button>
            </div>
            
            <form id="batch-settings-form" class="space-y-6">
                <!-- 分区一：基础与访问控制 -->
                <div>
                    <h4 class="text-md font-semibold text-gray-800 border-b pb-2 mb-4">基础与访问控制</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                        <!-- 站点状态 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_status" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_status" id="batch_status" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('status')">
                                站点状态
                            </label>
                            <div id="status_controls" class="hidden items-center">
                                <select name="status_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="active">活跃</option>
                                    <option value="inactive">未激活</option>
                                </select>
                            </div>
                        </div>
                        <!-- UA判断 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_use_global_ua_check" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_use_global_ua_check" id="batch_use_global_ua_check" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('use_global_ua_check')">
                                UA判断
                            </label>
                            <div id="use_global_ua_check_controls" class="hidden items-center">
                                <select name="use_global_ua_check_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">使用全局</option>
                                    <option value="false">独立关闭</option>
                                </select>
                            </div>
                        </div>
                        <!-- 来源判断 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_use_global_referer_check" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_use_global_referer_check" id="batch_use_global_referer_check" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('use_global_referer_check')">
                                来源判断
                            </label>
                            <div id="use_global_referer_check_controls" class="hidden items-center">
                                <select name="use_global_referer_check_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">使用全局</option>
                                    <option value="false">独立关闭</option>
                                </select>
                            </div>
                        </div>
                        <!-- 蜘蛛屏蔽 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_enable_spider_block" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_enable_spider_block" id="batch_enable_spider_block" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('enable_spider_block')">
                                蜘蛛屏蔽
                            </label>
                            <div id="enable_spider_block_controls" class="hidden items-center">
                                <select name="enable_spider_block_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分区二：内容注入 -->
                <div>
                    <h4 class="text-md font-semibold text-gray-800 border-b pb-2 mb-4">内容注入</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                        <!-- 关键词注入 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_enable_keyword" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_enable_keyword" id="batch_enable_keyword" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('enable_keyword')">
                                关键词注入
                            </label>
                            <div id="enable_keyword_controls" class="hidden items-center">
                                <select name="enable_keyword_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                        <!-- 伪原创 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_enable_pseudo" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_enable_pseudo" id="batch_enable_pseudo" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('enable_pseudo')">
                                伪原创
                            </label>
                            <div id="enable_pseudo_controls" class="hidden items-center">
                                <select name="enable_pseudo_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                        <!-- 结构注入 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_enable_structure" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_enable_structure" id="batch_enable_structure" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('enable_structure')">
                                结构注入
                            </label>
                            <div id="enable_structure_controls" class="hidden items-center">
                                <select name="enable_structure_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                        <!-- Unicode编码 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_enable_unicode" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_enable_unicode" id="batch_enable_unicode" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('enable_unicode')">
                                Unicode编码
                            </label>
                            <div id="enable_unicode_controls" class="hidden items-center">
                                <select name="enable_unicode_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分区三：SEO与站点功能 -->
                <div>
                    <h4 class="text-md font-semibold text-gray-800 border-b pb-2 mb-4">SEO与站点功能</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                        <!-- 域名跳转 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_redirect_www" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_redirect_www" id="batch_redirect_www" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('redirect_www')">
                                域名跳转(@→www)
                            </label>
                            <div id="redirect_www_controls" class="hidden items-center">
                                <select name="redirect_www_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">使用全局</option>
                                    <option value="true">开启跳转</option>
                                    <option value="false">关闭跳转</option>
                                </select>
                            </div>
                        </div>
                        <!-- Sitemap -->
                        <div class="flex items-center justify-between">
                            <label for="batch_enable_sitemap" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_enable_sitemap" id="batch_enable_sitemap" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('enable_sitemap')">
                                Sitemap
                            </label>
                            <div id="enable_sitemap_controls" class="hidden items-center">
                                <select name="enable_sitemap_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">启用</option>
                                    <option value="false">禁用</option>
                                </select>
                            </div>
                        </div>
                        <!-- 统计 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_use_global_analytics" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_use_global_analytics" id="batch_use_global_analytics" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('use_global_analytics')">
                                统计
                            </label>
                            <div id="use_global_analytics_controls" class="hidden items-center">
                                <select name="use_global_analytics_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">使用全局</option>
                                    <option value="false">独立关闭</option>
                                </select>
                            </div>
                        </div>
                        <!-- 缓存 -->
                        <div class="flex items-center justify-between">
                            <label for="batch_use_global_cache" class="text-sm text-gray-700 flex items-center">
                                <input type="checkbox" name="batch_use_global_cache" id="batch_use_global_cache" class="mr-3 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" onchange="toggleBatchSetting('use_global_cache')">
                                缓存
                            </label>
                            <div id="use_global_cache_controls" class="hidden items-center">
                                <select name="use_global_cache_value" class="text-sm border border-gray-300 rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="true">使用全局</option>
                                    <option value="false">独立关闭</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6 pt-6 border-t">
                    <button type="button" onclick="hideBatchSettings()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors">
                        取消
                    </button>
                    <button type="button" onclick="applyBatchSettings()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        应用设置
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
