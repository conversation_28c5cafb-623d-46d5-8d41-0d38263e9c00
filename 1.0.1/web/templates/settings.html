<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* 开关按钮样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
        }
        
        input:checked + .slider {
            background-color: #3B82F6;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .slider.round {
            border-radius: 24px;
        }
        
        .slider.round:before {
            border-radius: 50%;
        }
        
        /* 确保保存按钮容器始终可见 */
        .settings-panel-footer {
            background-color: white;
            border-top: 1px solid #e5e7eb;
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
        }
        
        /* 面板容器样式 */
        .settings-panel {
            min-height: 100px;
        }
        
        /* 确保主容器使用flex布局 */
        .main-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        /* Tab导航滚动条样式 */
        .tab-container::-webkit-scrollbar {
            height: 4px;
        }
        
        .tab-container::-webkit-scrollbar-track {
            background: #f3f4f6;
        }
        
        .tab-container::-webkit-scrollbar-thumb {
            background: #9ca3af;
            border-radius: 2px;
        }
        
        .tab-container::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }
        
        /* 确保tab按钮不会被压缩 */
        .tab-button {
            white-space: nowrap;
        }
        
        /* 左侧导航样式 */
        .settings-sidebar {
            width: 240px;
            background: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
        }
        
        .settings-nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #4b5563;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
            font-size: 15px;  /* 统一设置为较大字体 */
        }
        
        .settings-nav-item:hover {
            background-color: #f9fafb;
            color: #1f2937;
        }
        
        .settings-nav-item.active {
            background-color: #eff6ff;
            color: #2563eb;
            border-left-color: #2563eb;
            font-weight: 500;
        }
        
        .settings-nav-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .settings-content {
            flex: 1;
            overflow-y: auto;
            background-color: #f9fafb;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">系统设置</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 - 改为flex布局 -->
            <div class="flex flex-1 overflow-hidden">
                <!-- 左侧导航栏 -->
                <div class="settings-sidebar">
                    <div class="p-4 border-b">
                        <h2 class="text-lg font-semibold text-gray-800">系统设置</h2>
                    </div>
                    <nav class="py-2">
                        <a href="javascript:void(0)" onclick="switchTab('general')" class="settings-nav-item active" data-tab="general">
                            <i class="fas fa-cog"></i>
                            <span>基础设置</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('crawler')" class="settings-nav-item" data-tab="crawler">
                            <i class="fas fa-globe"></i>
                            <span>请求设置</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('ua-check')" class="settings-nav-item" data-tab="ua-check">
                            <i class="fas fa-user-shield"></i>
                            <span>UA判断设置</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('referer-check')" class="settings-nav-item" data-tab="referer-check">
                            <i class="fas fa-link"></i>
                            <span>来源判断</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('performance')" class="settings-nav-item" data-tab="performance">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>性能配置</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('analytics')" class="settings-nav-item" data-tab="analytics">
                            <i class="fas fa-chart-line"></i>
                            <span>统计设置</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('sitemap')" class="settings-nav-item" data-tab="sitemap">
                            <i class="fas fa-sitemap"></i>
                            <span>站点地图</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('system')" class="settings-nav-item" data-tab="system">
                            <i class="fas fa-info-circle"></i>
                            <span>系统信息</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('error-pages')" class="settings-nav-item" data-tab="error-pages">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>错误页面</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchTab('weight-monitor')" class="settings-nav-item" data-tab="weight-monitor">
                            <i class="fas fa-weight"></i>
                            <span>权重监测</span>
                        </a>
                    </nav>
                </div>
                
                <!-- 右侧内容区 -->
                <div class="settings-content">
                    <div class="p-8">
                        <!-- 设置面板容器 -->
                        <div class="bg-white rounded-lg shadow overflow-hidden flex flex-col" style="min-height: calc(100vh - 200px);">
                            <!-- 面板内容区域（可滚动） -->
                            <div class="flex-1 overflow-y-auto">
                    <!-- 基础设置面板 -->
                    <div class="settings-panel p-6" id="general-panel">
                        <!-- 内容将通过Ajax动态加载 -->
                        <div id="basic-content-container">
                            <div class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-3xl text-gray-400"></i>
                                <p class="mt-2 text-gray-500">加载中...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 请求设置面板 -->
                    <div class="settings-panel p-6 hidden" id="crawler-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">请求设置</h3>
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-network-wired mr-1"></i>HTTP请求并发数
                                </label>
                                <input type="number" id="crawler-concurrency" value="10" min="1" max="50"
                                       class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="mt-1 text-sm text-gray-600">控制爬虫抓取目标站点时的并发连接数</p>
                                <p class="mt-1 text-xs text-gray-500">⚠️ 注意：这里配置的是对目标站点的HTTP请求并发，不是任务处理并发</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">HTTP客户端超时（毫秒）</label>
                                <input type="number" id="http-client-timeout-main" value="30000" min="5000" max="120000" step="1000"
                                       class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="mt-1 text-sm text-gray-600">HTTP客户端请求超时时间（默认30000ms = 30秒）</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">代理请求超时（毫秒）</label>
                                <input type="number" id="proxy-request-timeout-main" value="60000" min="10000" max="300000" step="1000"
                                       class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="mt-1 text-sm text-gray-600">代理转发请求的超时时间（默认60000ms = 60秒）</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">User-Agent列表（随机使用）</label>
                                <textarea id="user-agent-list" rows="8"
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="每行一个User-Agent，访问时随机选择使用">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0
Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15
Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36</textarea>
                                <p class="mt-1 text-sm text-gray-600">每行输入一个User-Agent，系统会在请求时随机选择使用</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 缓存超时设置面板已移除 -->
                    <div class="settings-panel p-6 hidden" id="cache-timeout-panel-removed" style="display:none!important">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">缓存超时设置</h3>
                        <div class="space-y-6">
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <p class="font-medium text-yellow-800 mb-2">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>超时配置说明：
                                </p>
                                <ul class="space-y-1 text-sm text-yellow-700">
                                    <li>• 所有时间单位为毫秒（1秒 = 1000毫秒）</li>
                                    <li>• 超时时间过短可能导致频繁失败</li>
                                    <li>• 超时时间过长可能影响响应速度</li>
                                    <li>• 修改后需要重启服务才能生效</li>
                                </ul>
                            </div>
                            
                            <!-- Redis超时配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">Redis缓存超时</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">连接超时</label>
                                        <input type="number" id="redis-connect-timeout" min="1000" max="30000" step="1000"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="5000">
                                        <p class="mt-1 text-xs text-gray-600">默认5000ms（5秒）</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">读取超时</label>
                                        <input type="number" id="redis-read-timeout" min="500" max="10000" step="500"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="2000">
                                        <p class="mt-1 text-xs text-gray-600">默认2000ms（2秒）</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">写入超时</label>
                                        <input type="number" id="redis-write-timeout" min="500" max="10000" step="500"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="3000">
                                        <p class="mt-1 text-xs text-gray-600">默认3000ms（3秒）</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">连接池超时</label>
                                        <input type="number" id="redis-pool-timeout" min="1000" max="10000" step="1000"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="4000">
                                        <p class="mt-1 text-xs text-gray-600">默认4000ms（4秒）</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">最大连接数</label>
                                        <input type="number" id="redis-max-pool-size" min="10" max="200" step="10"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="30">
                                        <p class="mt-1 text-xs text-gray-600">默认30个连接</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 文件缓存超时配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">文件缓存超时</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">文件读取超时</label>
                                        <input type="number" id="file-read-timeout" min="1000" max="30000" step="1000"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="5000">
                                        <p class="mt-1 text-xs text-gray-600">默认5000ms（5秒）</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">文件写入超时</label>
                                        <input type="number" id="file-write-timeout" min="1000" max="60000" step="1000"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="10000">
                                        <p class="mt-1 text-xs text-gray-600">默认10000ms（10秒）</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">异步队列大小</label>
                                        <input type="number" id="async-queue-size" min="1000" max="100000" step="1000"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="10000">
                                        <p class="mt-1 text-xs text-gray-600">默认10000个任务</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">异步工作协程数</label>
                                        <input type="number" id="async-worker-count" min="1" max="50" step="1"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="10">
                                        <p class="mt-1 text-xs text-gray-600">默认10个协程</p>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                    
                    <!-- UA判断设置面板 -->
                    <div class="settings-panel p-6 hidden" id="ua-check-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">全局UA判断默认设置</h3>
                        
                        <!-- 紧凑的提示信息 -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2.5 mb-4 flex items-start">
                            <i class="fas fa-info-circle text-yellow-600 mt-0.5 mr-2"></i>
                            <p class="text-xs text-yellow-800">
                                这里是全局默认设置，新建站点时会使用这些默认值。每个站点可以在"站点管理"中单独配置UA判断规则。
                            </p>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- 开关设置 - 使用更紧凑的布局 -->
                            <div class="grid grid-cols-2 gap-4">
                                <!-- 全局UA判断开关 -->
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-800">启用全局UA判断</h4>
                                            <p class="text-xs text-gray-600 mt-0.5">所有站点将默认使用UA判断规则（站点可单独设置）</p>
                                        </div>
                                        <label class="switch switch-sm">
                                            <input type="checkbox" id="enable-global-ua-check">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- 域名跳转设置 -->
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-800">全局 @ 跳转到 www</h4>
                                            <p class="text-xs text-gray-600 mt-0.5">所有根域名访问将自动跳转到 www 子域名</p>
                                        </div>
                                        <label class="switch switch-sm">
                                            <input type="checkbox" id="global-redirect-www">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- UA判断规则说明 - 更紧凑的版本 -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                                    <div class="flex-1">
                                        <p class="text-xs font-medium text-blue-800 mb-1">UA判断规则说明：</p>
                                        <ul class="text-xs text-blue-700 space-y-0.5">
                                            <li>• 全局开关关闭时，不进行UA判断</li>
                                            <li>• 全局开关开启且设置了关键词后，只有匹配的UA才能访问正常内容</li>
                                            <li>• 未匹配的访问将显示自定义页面</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">允许的UA关键词</label>
                                <textarea id="default-allowed-ua" rows="4" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="输入允许访问的UA关键词，用 | 分隔，例如：
googlebot|baiduspider|bingbot|yandexbot
留空则不进行UA判断（允许所有访问）"></textarea>
                                <p class="mt-1 text-sm text-gray-600">设置允许访问的User-Agent关键词，多个关键词用 | 分隔，不区分大小写。<strong>留空表示不进行UA判断</strong></p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">非爬虫访问页面</label>
                                
                                <!-- 标签说明 -->
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                                    <h5 class="font-semibold text-blue-900 mb-2">
                                        <i class="fas fa-tags mr-1"></i>支持的自定义标签
                                    </h5>
                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                        <div class="space-y-1">
                                            <div><code class="bg-white px-1 py-0.5 rounded">{title}</code> - 站点首页标题</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{description}</code> - 站点首页描述</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{keywords}</code> - 站点首页关键词</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{analytics}</code> - 统计JS引用</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{company}</code> - 企业名称</div>
                                        </div>
                                        <div class="space-y-1">
                                            <div><code class="bg-white px-1 py-0.5 rounded">{domain}</code> - 当前域名</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{year}</code> - 当前年份</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{date}</code> - 当前日期</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{time}</code> - 当前时间</div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-xs text-blue-700">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        提示：{analytics}会自动生成统计JS文件引用，如：&lt;script src="/xxxxx.js"&gt;&lt;/script&gt;
                                    </div>
                                </div>
                                
                                <textarea id="default-non-spider-html" rows="15" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="在此输入自定义HTML模板..."></textarea>
                                <p class="mt-1 text-sm text-gray-600">当UA不匹配时显示的HTML页面（仅在设置了UA关键词时生效）</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 来源判断设置面板 -->
                    <div class="settings-panel p-6 hidden" id="referer-check-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">全局来源判断设置</h3>
                        
                        <!-- 紧凑的提示信息 -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2.5 mb-4 flex items-start">
                            <i class="fas fa-info-circle text-yellow-600 mt-0.5 mr-2"></i>
                            <p class="text-xs text-yellow-800">
                                这里是全局默认设置，新建站点时会使用这些默认值。每个站点可以在"站点管理"中单独配置来源判断规则。
                            </p>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- 开关设置 - 使用更紧凑的布局 -->
                            <div class="border border-gray-200 rounded-lg p-3">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-800">启用全局来源判断</h4>
                                        <p class="text-xs text-gray-600 mt-0.5">所有站点将默认进行来源检查（站点可单独设置）</p>
                                    </div>
                                    <label class="switch switch-sm">
                                        <input type="checkbox" id="enable-global-referer-check">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- 来源判断规则说明 - 更紧凑的版本 -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                                    <div class="flex-1">
                                        <p class="text-xs font-medium text-blue-800 mb-1">来源判断规则说明：</p>
                                        <ul class="text-xs text-blue-700 space-y-0.5">
                                            <li>• 全局开关关闭时，不进行来源判断</li>
                                            <li>• 全局开关开启后，只有来自白名单域名的访问才被允许</li>
                                            <li>• 支持通配符匹配，如 *.google.com 匹配所有谷歌子域名</li>
                                            <li>• 空Referer的直接访问默认被允许</li>
                                            <li>• 非白名单访问将返回自定义状态码和页面</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">允许的来源域名</label>
                                <textarea id="global-allowed-referers" rows="4" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="输入允许访问的来源域名，用 | 分隔，例如：
www.baidu.com|*.google.com|www.so.com|*.bing.com
留空则允许所有来源（包括空Referer）"></textarea>
                                <p class="mt-1 text-sm text-gray-600">设置允许访问的来源域名，多个域名用 | 分隔，支持通配符(*)。<strong>留空表示不进行来源判断</strong></p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">拒绝状态码</label>
                                <input type="number" id="global-referer-block-code" 
                                       class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="403" min="100" max="599" value="403">
                                <p class="mt-1 text-sm text-gray-600">当来源不在白名单时返回的HTTP状态码（100-599）</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">拒绝访问页面</label>
                                
                                <!-- 标签说明 -->
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                                    <h5 class="font-semibold text-blue-900 mb-2">
                                        <i class="fas fa-tags mr-1"></i>支持的自定义标签
                                    </h5>
                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                        <div class="space-y-1">
                                            <div><code class="bg-white px-1 py-0.5 rounded">{domain}</code> - 当前域名</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{referer}</code> - 访问来源</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{ip}</code> - 访问者IP</div>
                                        </div>
                                        <div class="space-y-1">
                                            <div><code class="bg-white px-1 py-0.5 rounded">{year}</code> - 当前年份</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{date}</code> - 当前日期</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{time}</code> - 当前时间</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <textarea id="global-referer-block-html" rows="15" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="在此输入自定义HTML模板..."></textarea>
                                <p class="mt-1 text-sm text-gray-600">当来源不在白名单时显示的HTML页面</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 缓存设置面板已移除 -->
                    
                    <!-- 性能配置面板 -->
                    <div class="settings-panel p-6 hidden" id="performance-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">性能配置</h3>
                        
                        <!-- 性能优化提示 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <p class="font-medium text-blue-800 mb-2">
                                <i class="fas fa-info-circle mr-2"></i>性能优化建议：
                            </p>
                            <ul class="text-sm text-blue-700 ml-4 space-y-1">
                                <li>• 200个站点并发：使用默认推荐值</li>
                                <li>• 500个站点并发：数据库连接数设为400，HTTP连接数设为500</li>
                                <li>• 修改后需重启服务生效</li>
                                <li>• 建议配合8核16GB以上硬件配置</li>
                            </ul>
                        </div>
                        
                        <div class="space-y-6">
                            <!-- 数据库连接池配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">
                                    <i class="fas fa-database mr-2"></i>数据库连接池配置
                                </h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最大连接数</label>
                                        <input type="number" id="db_max_open_conns" name="db_max_open_conns" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐200">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：25，推荐值：200-400</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最大空闲连接数</label>
                                        <input type="number" id="db_max_idle_conns" name="db_max_idle_conns"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐100">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：10，推荐值：100-200</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">连接最大生命周期（秒）</label>
                                        <input type="number" id="db_conn_max_lifetime" name="db_conn_max_lifetime"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="600">
                                        <p class="text-xs text-gray-500 mt-1">推荐值：600秒（10分钟）</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">从库最大连接数</label>
                                        <input type="number" id="db_slave_max_open_conns" name="db_slave_max_open_conns"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐100">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：15，推荐值：100</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- HTTP客户端配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">
                                    <i class="fas fa-globe mr-2"></i>HTTP客户端配置
                                </h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最大空闲连接数</label>
                                        <input type="number" id="http_max_idle_conns" name="http_max_idle_conns"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐300">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：50，推荐值：300-500</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">每主机最大空闲连接</label>
                                        <input type="number" id="http_max_idle_conns_per_host" name="http_max_idle_conns_per_host"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐50">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：5，推荐值：50-100</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">每主机最大连接数</label>
                                        <input type="number" id="http_max_conns_per_host" name="http_max_conns_per_host"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐100">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：10，推荐值：100-200</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">空闲连接超时（秒）</label>
                                        <input type="number" id="http_idle_conn_timeout" name="http_idle_conn_timeout"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="90">
                                        <p class="text-xs text-gray-500 mt-1">推荐值：90秒</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 并发控制配置 -->
                            <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                                <h4 class="font-medium text-blue-800 mb-4">
                                    <i class="fas fa-layer-group mr-2"></i>并发控制配置
                                </h4>
                                <div class="bg-yellow-50 border border-yellow-200 rounded p-3 mb-4">
                                    <p class="text-sm text-yellow-800">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        <strong>重要说明：</strong>系统有两个不同的并发概念
                                    </p>
                                    <ul class="text-xs text-yellow-700 mt-2 ml-5 list-disc">
                                        <li><strong>任务处理并发</strong>：同时处理多少个用户访问请求</li>
                                        <li><strong>HTTP请求并发</strong>：抓取目标站点时的并发连接数</li>
                                    </ul>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-users mr-1"></i>任务处理并发数
                                        </label>
                                        <input type="number" id="scheduler_max_workers" name="scheduler_max_workers"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="默认200">
                                        <p class="text-xs text-gray-600 mt-1">同时处理用户请求的工作协程数</p>
                                        <p class="text-xs text-green-600 mt-1">✔ 200站点：300 | 500站点：500</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-network-wired mr-1"></i>HTTP请求并发数
                                        </label>
                                        <input type="number" id="crawler-concurrency-perf" value="10" min="1" max="50"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="默认10">
                                        <p class="text-xs text-gray-600 mt-1">抓取目标站点时的并发连接数</p>
                                        <p class="text-xs text-orange-600 mt-1">⚠️ 过高可能被目标站点封禁</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-inbox mr-1"></i>任务队列大小
                                        </label>
                                        <input type="number" id="scheduler_queue_size" name="scheduler_queue_size"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="默认5000">
                                        <p class="text-xs text-gray-600 mt-1">缓冲队列的最大容量</p>
                                        <p class="text-xs text-green-600 mt-1">✔ 200站点：10000 | 500站点：20000</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            <i class="fas fa-tachometer-alt mr-1"></i>速率限制（QPS）
                                        </label>
                                        <input type="number" id="crawler_rate_limit" name="crawler_rate_limit"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="默认2000">
                                        <p class="text-xs text-gray-600 mt-1">每秒最大请求数限制</p>
                                        <p class="text-xs text-green-600 mt-1">✔ 200站点：3000 | 500站点：5000</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 缓存锁配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">
                                    <i class="fas fa-lock mr-2"></i>缓存锁配置
                                </h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">缓存锁超时（毫秒）</label>
                                        <input type="number" id="cache_lock_timeout" name="cache_lock_timeout"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="推荐2000">
                                        <p class="text-xs text-gray-500 mt-1">原硬编码值：100，推荐值：2000-3000</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">缓存锁重试间隔（毫秒）</label>
                                        <input type="number" id="cache_lock_retry_interval" name="cache_lock_retry_interval"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="100">
                                        <p class="text-xs text-gray-500 mt-1">推荐值：100毫秒</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 快速配置按钮 -->
                            <div class="flex justify-center space-x-4 pt-4">
                                <button onclick="applyPerformancePreset('default')" 
                                        class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                                    <i class="fas fa-undo mr-2"></i>恢复默认值
                                </button>
                                <button onclick="applyPerformancePreset('200sites')" 
                                        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-rocket mr-2"></i>应用200站点优化
                                </button>
                                <button onclick="applyPerformancePreset('500sites')" 
                                        class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-fire mr-2"></i>应用500站点优化
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计设置面板 -->
                    <div class="settings-panel p-6 hidden" id="analytics-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">统计设置</h3>
                        <div class="space-y-6">
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="analytics-enabled" class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    <span class="text-sm font-medium text-gray-700">启用统计功能</span>
                                </label>
                                <p class="mt-1 text-sm text-gray-600 ml-6">启用后，将在所有镜像页面插入统计代码</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">统计代码</label>
                                <textarea id="analytics-code" rows="10" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="请输入您的统计代码，例如Google Analytics、百度统计等"></textarea>
                                <p class="mt-1 text-sm text-gray-600">支持各种统计代码，将自动插入到页面中</p>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <button onclick="refreshAnalyticsJS()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center">
                                    <i class="fas fa-sync-alt mr-2"></i>
                                    刷新所有站点统计JS
                                </button>
                                <span class="text-sm text-gray-600" id="analytics-refresh-status"></span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 站点地图面板 -->
                    <div class="settings-panel p-6 hidden" id="sitemap-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">站点地图设置</h3>
                        <div class="space-y-6">
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="sitemap-auto-refresh" class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    <span class="text-sm font-medium text-gray-700">启用自动刷新</span>
                                </label>
                                <p class="mt-1 text-sm text-gray-600 ml-6">启用后，系统将根据设定间隔自动刷新所有站点地图</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">刷新间隔（分钟）</label>
                                <input type="number" id="sitemap-refresh-interval" min="60" max="10080" value="720"
                                       class="w-32 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="mt-1 text-sm text-gray-600">建议设置为720分钟（12小时），最小60分钟，最大10080分钟（7天）</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">上次刷新时间</label>
                                <p class="text-sm text-gray-600" id="last-sitemap-refresh-time">尚未刷新</p>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <div class="flex gap-2">
                                    <button onclick="refreshAllSitemaps()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                                        <i class="fas fa-sitemap mr-2"></i>
                                        立即刷新所有站点地图
                                    </button>
                                    <button onclick="clearSitemapCache()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                                        <i class="fas fa-trash mr-2"></i>
                                        清空站点地图缓存
                                    </button>
                                </div>
                                <span class="text-sm text-gray-600" id="sitemap-refresh-status"></span>
                            </div>
                            
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h4 class="font-medium text-blue-800 mb-2">站点地图统计</h4>
                                <div class="grid grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <label class="text-gray-600">已启用站点地图</label>
                                        <p class="font-semibold text-blue-600" id="sitemap-enabled-count">0</p>
                                    </div>
                                    <div>
                                        <label class="text-gray-600">总URL数量</label>
                                        <p class="font-semibold text-blue-600" id="sitemap-total-urls">0</p>
                                    </div>
                                    <div>
                                        <label class="text-gray-600">待刷新数量</label>
                                        <p class="font-semibold text-orange-600" id="sitemap-pending-count">0</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 系统信息面板 -->
                    <div class="settings-panel p-6 hidden" id="system-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">系统信息</h3>
                        
                        <!-- 基础信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-600 mb-1">版本</label>
                                <p class="text-lg font-semibold text-gray-800" id="system-version">1.0.4</p>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-600 mb-1">Go版本</label>
                                <p class="text-lg font-semibold text-gray-800" id="go-version">1.21</p>
                            </div>
                        </div>
                        
                        <!-- 数据库状态 -->
                        <div class="border border-gray-200 rounded-lg p-4 mb-6">
                            <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                                <i class="fas fa-database mr-2 text-blue-600"></i>
                                数据库状态
                            </h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">连接状态</label>
                                    <p class="font-semibold" id="db-status">
                                        <span class="text-gray-400">加载中...</span>
                                    </p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">数据库类型</label>
                                    <p class="font-semibold" id="db-type">PostgreSQL</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">数据库大小</label>
                                    <p class="font-semibold" id="db-size">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">表数量</label>
                                    <p class="font-semibold" id="db-tables">-</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">连接池状态</label>
                                    <p class="text-sm" id="db-connections">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">活动连接</label>
                                    <p class="text-sm" id="db-active">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">空闲连接</label>
                                    <p class="text-sm" id="db-idle">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">活动查询</label>
                                    <p class="text-sm" id="db-queries">-</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Redis状态 -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                                <i class="fas fa-memory mr-2 text-red-600"></i>
                                Redis状态
                            </h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">连接状态</label>
                                    <p class="font-semibold" id="redis-status">
                                        <span class="text-gray-400">加载中...</span>
                                    </p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">版本</label>
                                    <p class="font-semibold" id="redis-version">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">内存使用</label>
                                    <p class="font-semibold" id="redis-memory">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">键数量</label>
                                    <p class="font-semibold" id="redis-keys">-</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">命中率</label>
                                    <p class="text-sm" id="redis-hit-rate">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">连接客户端</label>
                                    <p class="text-sm" id="redis-clients">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">每秒操作</label>
                                    <p class="text-sm" id="redis-ops">-</p>
                                </div>
                                <div class="bg-white p-3 rounded border">
                                    <label class="block text-xs text-gray-600 mb-1">运行时间</label>
                                    <p class="text-sm" id="redis-uptime">-</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 错误页面设置面板 -->
                    <div class="settings-panel p-6 hidden" id="error-pages-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">错误页面设置</h3>
                        <div class="space-y-6">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                                <p class="text-sm font-medium text-blue-800 mb-1.5">
                                    <i class="fas fa-info-circle mr-1 text-xs"></i>错误页面说明
                                </p>
                                <ul class="space-y-0.5 text-xs text-blue-700">
                                    <li class="flex items-start">
                                        <span class="mr-1 text-xs">•</span>
                                        <span>站点未配置页面：当访问未在系统中配置的域名时显示</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="mr-1 text-xs">•</span>
                                        <span>404页面：当目标站点返回404时显示，并会缓存该状态</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="mr-1 text-xs">•</span>
                                        <span>支持自定义HTML，可包含CSS和JavaScript</span>
                                    </li>
                                    <li class="flex items-start">
                                        <span class="mr-1 text-xs">•</span>
                                        <span>留空则使用系统默认页面</span>
                                    </li>
                                </ul>
                            </div>
                            
                            <!-- 404页面设置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">404页面设置</h4>
                                
                                <div class="mb-4">
                                    <label class="flex items-center cursor-pointer">
                                        <input type="checkbox" id="enable-404-cache" class="mr-2">
                                        <span class="text-sm font-medium text-gray-700">启用404缓存</span>
                                    </label>
                                    <p class="text-xs text-gray-600 mt-1">当目标站点返回404时，缓存该状态避免重复请求</p>
                                </div>
                                
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">404缓存时间（秒）</label>
                                    <input type="number" id="cache-404-ttl" value="86400" min="60" max="604800"
                                           class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <p class="mt-1 text-sm text-gray-600">404状态缓存时间，默认86400秒（24小时）</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">自定义404页面</label>
                                    <textarea id="default-404-html" rows="15" 
                                              class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                              placeholder="在此输入自定义HTML模板..."
<html>
<head>
    <meta charset='UTF-8'>
    <title>404 - 页面未找到</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script></head>
<body>
    <h1>404</h1>
    <p>页面未找到</p>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>"></textarea>
                                    <p class="mt-1 text-sm text-gray-600">当目标站点返回404时显示的HTML页面</p>
                                </div>
                            </div>
                            
                            <!-- 站点未配置页面 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">站点未配置页面</h4>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">自定义页面</label>
                                <textarea id="site-not-found-html" rows="20" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="在此输入自定义HTML模板..."
<html>
<head>
    <meta charset='UTF-8'>
    <title>站点未配置</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            text-align: center; 
            padding: 50px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: rgba(255, 255, 255, 0.95); 
            padding: 40px; 
            border-radius: 20px; 
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            color: #333;
        }
        h1 { 
            color: #764ba2; 
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        p { 
            color: #666; 
            line-height: 1.8;
            font-size: 1.1em;
        }
        .domain { 
            color: #e74c3c; 
            font-weight: bold;
            font-size: 1.2em;
        }
        .icon {
            font-size: 5em;
            margin-bottom: 20px;
            color: #764ba2;
        }
    </style>
</head>
<body>
    <div class='container'>
        <div class='icon'>⚠️</div>
        <h1>站点未配置</h1>
        <p>您访问的域名尚未在系统中配置</p>
        <p>请联系网站管理员</p>
    </div>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>"></textarea>
                                <p class="mt-1 text-sm text-gray-600">自定义未配置站点的404页面，支持完整的HTML</p>
                                </div>
                            </div>
                            
                            <div class="flex space-x-4">
                                <button onclick="previewSiteNotFound()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center">
                                    <i class="fas fa-eye mr-2"></i>
                                    预览页面
                                </button>
                                <button onclick="resetSiteNotFound()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg flex items-center">
                                    <i class="fas fa-undo mr-2"></i>
                                    恢复默认
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 权重监测设置面板 -->
                    <div class="settings-panel p-6 hidden" id="weight-monitor-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">权重监测设置</h3>
                        <div class="space-y-6">
                            <!-- 功能开关 -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="font-medium text-blue-800">权重监测功能</h4>
                                        <p class="text-sm text-blue-600 mt-1">自动获取所有站点域名的百度权重和流量数据</p>
                                    </div>
                                    <label class="switch">
                                        <input type="checkbox" id="weight-monitor-enabled">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                                <p class="text-xs text-blue-700">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    开启后将按照配置定时获取所有活跃站点的权重信息
                                </p>
                            </div>
                            
                            <!-- API配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">爱站API配置</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">API密钥</label>
                                        <input type="text" id="weight-api-key" 
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                                               placeholder="请输入爱站API密钥">
                                        <p class="mt-1 text-sm text-gray-600">
                                            从 <a href="https://apistore.aizhan.com" target="_blank" class="text-blue-600 hover:underline">爱站API商店</a> 获取密钥
                                        </p>
                                    </div>
                                    
                                    <div class="flex space-x-2">
                                        <button onclick="testAPIKey()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm">
                                            <i class="fas fa-check-circle mr-2"></i>验证密钥
                                        </button>
                                        <button onclick="window.open('https://apistore.aizhan.com/detail/23/', '_blank')" 
                                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                            <i class="fas fa-external-link-alt mr-2"></i>查看API文档
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 调度配置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">调度配置</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">检查间隔（分钟）</label>
                                        <input type="number" id="weight-check-interval" value="60" min="10" max="1440"
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-600">多久执行一次权重检查</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">批次大小</label>
                                        <input type="number" id="weight-batch-size" value="5" min="1" max="20"
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-600">一次请求获取几个域名</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">批次间隔（秒）</label>
                                        <input type="number" id="weight-batch-delay" value="5" min="1" max="60"
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-600">批次之间的等待时间</p>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">循环等待（小时）</label>
                                        <input type="number" id="weight-cycle-wait" value="24" min="1" max="168"
                                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-600">完成后等待下次循环</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 状态信息 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4">运行状态</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">服务状态</span>
                                        <span id="weight-service-status" class="text-sm font-medium text-gray-800">未启动</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">最后检查时间</span>
                                        <span id="weight-last-check" class="text-sm font-medium text-gray-800">-</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">下次检查时间</span>
                                        <span id="weight-next-check" class="text-sm font-medium text-gray-800">-</span>
                                    </div>
                                </div>
                                
                                <div class="mt-4 flex space-x-2">
                                    <button onclick="manualCheckWeights()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                                        <i class="fas fa-play mr-2"></i>立即执行
                                    </button>
                                    <button onclick="viewWeightHistory()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                        <i class="fas fa-chart-line mr-2"></i>查看统计
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 说明信息 -->
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <p class="font-medium text-yellow-800 mb-2">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>注意事项：
                                </p>
                                <ul class="space-y-1 text-sm text-yellow-700">
                                    <li>• 监测的是站点配置的域名，不是目标URL</li>
                                    <li>• API调用会产生费用，请合理设置检查频率</li>
                                    <li>• 批次大小建议不超过10个，避免API限流</li>
                                    <li>• 修改配置后需要保存并重启服务才能生效</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                            </div> <!-- 关闭可滚动容器 -->
                            
                            <!-- 保存按钮（固定在底部） -->
                            <div class="settings-panel-footer flex-shrink-0" id="save-button-container">
                                <div class="flex justify-end p-6 border-t">
                                    <button onclick="saveSettings()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg flex items-center">
                                        <i class="fas fa-save mr-2"></i>
                                        保存设置
                                    </button>
                                </div>
                            </div>
                        </div> <!-- 关闭设置面板容器 -->
                    </div> <!-- 关闭p-8 -->
                </div> <!-- 关闭settings-content -->
            </div> <!-- 关闭flex容器 -->
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        let currentTab = 'general';
        
        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 1000);
        
        // 加载基础设置内容
        async function loadBasicSettings() {
            const container = document.getElementById('basic-content-container');
            if (!container) {
                console.error('未找到basic-content-container');
                return;
            }
            
            console.log('开始加载基础设置...');
            
            try {
                const response = await fetch('/admin/settings/partial/basic');
                console.log('请求响应状态:', response.status);
                
                if (response.ok) {
                    const html = await response.text();
                    console.log('收到HTML内容长度:', html.length);
                    container.innerHTML = html;
                    
                    // 查找并执行内嵌的脚本
                    const scripts = container.querySelectorAll('script');
                    console.log('找到脚本数量:', scripts.length);
                    
                    scripts.forEach((script, index) => {
                        console.log(`执行第${index + 1}个脚本`);
                        const newScript = document.createElement('script');
                        newScript.textContent = script.textContent;
                        document.body.appendChild(newScript);
                        document.body.removeChild(newScript);
                    });
                    
                    // 加载设置数据
                    console.log('加载设置数据...');
                    loadSystemSettings();
                } else {
                    console.error('加载失败，状态码:', response.status);
                    container.innerHTML = '<div class="text-red-500 p-4">加载失败，请刷新页面重试</div>';
                }
            } catch (error) {
                console.error('加载基础设置失败:', error);
                container.innerHTML = '<div class="text-red-500 p-4">加载失败，请刷新页面重试</div>';
            }
        }
        
        // 切换选项卡
        function switchTab(tab) {
            currentTab = tab;
            console.log('切换到标签:', tab);
            
            // 更新左侧导航栏状态
            document.querySelectorAll('.settings-nav-item').forEach(item => {
                if (item.dataset.tab === tab) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
            
            // 显示对应的面板
            document.querySelectorAll('.settings-panel').forEach(panel => {
                console.log('隐藏面板:', panel.id);
                panel.classList.add('hidden');
            });
            
            const targetPanel = document.getElementById(`${tab}-panel`);
            if (targetPanel) {
                console.log('显示面板:', targetPanel.id);
                targetPanel.classList.remove('hidden');
                
                // 如果是基础设置，动态加载内容
                if (tab === 'general') {
                    loadBasicSettings();
                }
            } else {
                console.error('未找到面板:', `${tab}-panel`);
            }
            
            // 系统信息面板不显示保存按钮，其他面板都显示
            const saveContainer = document.getElementById('save-button-container');
            if (saveContainer) {
                if (tab === 'system') {
                    saveContainer.style.display = 'none';
                    // 切换到系统信息时加载最新数据
                    loadSystemInfo();
                } else if (tab === 'sitemap') {
                    // 切换到站点地图时加载统计
                    saveContainer.style.display = 'block';
                    loadSitemapStats();
                } else if (tab === 'weight-monitor') {
                    // 切换到权重监测时加载配置
                    console.log('加载权重监测配置...');
                    saveContainer.style.display = 'block';
                    saveContainer.style.visibility = 'visible';
                    saveContainer.style.opacity = '1';
                    loadWeightMonitorConfig();
                } else {
                    // 确保保存按钮可见
                    saveContainer.style.display = 'block';
                    saveContainer.style.visibility = 'visible';
                    saveContainer.style.opacity = '1';
                }
            } else {
                console.error('保存按钮容器未找到！');
            }
        }
        
        // 预览站点未配置页面
        function previewSiteNotFound() {
            const html = document.getElementById('site-not-found-html').value || getDefaultSiteNotFoundHTML();
            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(html);
            previewWindow.document.close();
        }
        
        // 恢复默认站点未配置页面
        function resetSiteNotFound() {
            if (confirm('确定要恢复默认的404页面吗？')) {
                document.getElementById('site-not-found-html').value = '';
                showToast('已恢复默认设置', 'success');
            }
        }
        
        // 获取默认的站点未配置HTML
        function getDefaultSiteNotFoundHTML() {
            return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>站点未配置</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; margin-bottom: 20px; }
        p { color: #666; line-height: 1.6; }
        .domain { color: #e74c3c; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>站点未配置</h1>
        <p>该域名尚未在系统中配置。</p>
        <p>如果您是网站管理员，请登录后台添加此站点。</p>
    </div>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>`;
        }
        
        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-2 transform transition-all duration-300 translate-x-full`;
            toast.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : type === 'warning' ? 'fa-exclamation-circle' : 'fa-info-circle'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }
        
        // 加载系统信息
        async function loadSystemInfo() {
            try {
                const res = await fetch('/api/v1/system/info');
                const data = await res.json();
                
                if (data.success) {
                    // 基础信息
                    document.getElementById('system-version').textContent = data.data.app_version || '1.0.4';
                    document.getElementById('go-version').textContent = data.data.go_version || '1.21';
                    
                    // 数据库状态
                    if (data.data.database) {
                        const db = data.data.database;
                        
                        // 连接状态
                        const dbStatusEl = document.getElementById('db-status');
                        if (db.connected) {
                            dbStatusEl.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>正常</span>';
                        } else {
                            dbStatusEl.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>断开</span>';
                        }
                        
                        // 数据库信息
                        document.getElementById('db-type').textContent = db.type || 'PostgreSQL';
                        document.getElementById('db-size').textContent = db.size || '-';
                        document.getElementById('db-tables').textContent = db.table_count || '-';
                        
                        // 连接池信息
                        if (db.stats) {
                            document.getElementById('db-connections').textContent = db.stats.open_connections || '0';
                            document.getElementById('db-active').textContent = db.stats.in_use || '0';
                            document.getElementById('db-idle').textContent = db.stats.idle || '0';
                        }
                        document.getElementById('db-queries').textContent = db.active_queries || '0';
                    }
                    
                    // Redis状态
                    if (data.data.redis) {
                        const redis = data.data.redis;
                        
                        // 连接状态
                        const redisStatusEl = document.getElementById('redis-status');
                        if (redis.connected) {
                            redisStatusEl.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>正常</span>';
                        } else {
                            redisStatusEl.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>断开</span>';
                        }
                        
                        // Redis信息
                        if (redis.stats) {
                            document.getElementById('redis-version').textContent = redis.stats.version || '-';
                            document.getElementById('redis-memory').textContent = redis.stats.used_memory_human || '-';
                            document.getElementById('redis-hit-rate').textContent = redis.stats.hit_rate || '-';
                            document.getElementById('redis-clients').textContent = redis.stats.connected_clients || '0';
                            document.getElementById('redis-ops').textContent = redis.stats.instantaneous_ops_per_sec || '0';
                            
                            // 格式化运行时间
                            const uptime = parseInt(redis.stats.uptime_in_seconds) || 0;
                            const days = Math.floor(uptime / 86400);
                            const hours = Math.floor((uptime % 86400) / 3600);
                            document.getElementById('redis-uptime').textContent = days > 0 ? `${days}天${hours}小时` : `${hours}小时`;
                        }
                        
                        document.getElementById('redis-keys').textContent = redis.db_size || '0';
                    }
                }
            } catch (error) {
                console.error('加载系统信息失败:', error);
                // 显示错误状态
                document.getElementById('db-status').innerHTML = '<span class="text-gray-500">加载失败</span>';
                document.getElementById('redis-status').innerHTML = '<span class="text-gray-500">加载失败</span>';
            }
        }
        
        // 加载系统设置
        async function loadSystemSettings() {
            try {
                const res = await fetch('/api/v1/system/settings');
                const data = await res.json();
                
                if (data.success && data.data) {
                    
                    // 验证码配置（这些元素可能在动态加载的basic.html中）
                    const enableCaptcha = document.getElementById('enable-captcha');
                    const captchaLength = document.getElementById('captcha-length');
                    const captchaExpiry = document.getElementById('captcha-expiry');
                    
                    if (enableCaptcha) enableCaptcha.checked = data.data.enable_captcha || false;
                    if (captchaLength) captchaLength.value = data.data.captcha_length || 4;
                    if (captchaExpiry) captchaExpiry.value = data.data.captcha_expiry || 300;
                    
                    // CSRF配置
                    const enableCSRF = document.getElementById('enable-csrf');
                    if (enableCSRF) enableCSRF.checked = data.data.enable_csrf || false;
                    
                    // 日志配置
                    document.getElementById('log_enabled').checked = data.data.log_enabled !== false;
                    document.getElementById('log-level').value = data.data.log_level || 'info';
                    document.getElementById('log_storage').value = data.data.log_storage || 'file';
                    document.getElementById('log_retention_days').value = data.data.log_retention_days || 7;
                    document.getElementById('log_max_size').value = data.data.log_max_size || 100;
                    document.getElementById('log_max_backups').value = data.data.log_max_backups || 10;
                    document.getElementById('log_access_enabled').checked = data.data.log_access_enabled !== false;
                    document.getElementById('log_error_enabled').checked = data.data.log_error_enabled !== false;
                    
                    // 请求设置
                    document.getElementById('crawler-concurrency').value = data.data.crawler_concurrency || 10;
                    // 同步到性能配置面板的HTTP请求并发数
                    document.getElementById('crawler-concurrency-perf').value = data.data.crawler_concurrency || 10;
                    document.getElementById('http-client-timeout-main').value = data.data.http_client_timeout || 30000;
                    document.getElementById('proxy-request-timeout-main').value = data.data.proxy_request_timeout || 60000;
                    document.getElementById('user-agent-list').value = data.data.user_agent_list || document.getElementById('user-agent-list').placeholder;
                    
                    // UA判断设置
                    document.getElementById('enable-global-ua-check').checked = data.data.enable_global_ua_check || false;
                    document.getElementById('default-allowed-ua').value = data.data.default_allowed_ua || '';
                    document.getElementById('default-non-spider-html').value = data.data.default_non_spider_html || '';
                    document.getElementById('global-redirect-www').checked = data.data.global_redirect_www || false;
                    
                    // 来源判断设置
                    document.getElementById('enable-global-referer-check').checked = data.data.enable_global_referer_check || false;
                    document.getElementById('global-allowed-referers').value = data.data.global_allowed_referers || '';
                    document.getElementById('global-referer-block-code').value = data.data.global_referer_block_code || 403;
                    document.getElementById('global-referer-block-html').value = data.data.global_referer_block_html || '';
                    
                    // 缓存默认设置
                    document.getElementById('default-cache-home-ttl').value = data.data.default_cache_home_ttl || 30;
                    document.getElementById('default-cache-other-ttl').value = data.data.default_cache_other_ttl || 1440;
                    
                    // 错误页面设置
                    document.getElementById('site-not-found-html').value = data.data.site_not_found_html || '';
                    document.getElementById('default-404-html').value = data.data.default_404_html || '';
                    document.getElementById('enable-404-cache').checked = data.data.enable_404_cache !== false;
                    
                    // 性能配置设置
                    document.getElementById('db_max_open_conns').value = data.data.db_max_open_conns || '';
                    document.getElementById('db_max_idle_conns').value = data.data.db_max_idle_conns || '';
                    document.getElementById('db_conn_max_lifetime').value = data.data.db_conn_max_lifetime || '';
                    document.getElementById('db_slave_max_open_conns').value = data.data.db_slave_max_open_conns || '';
                    document.getElementById('http_max_idle_conns').value = data.data.http_max_idle_conns || '';
                    document.getElementById('http_max_idle_conns_per_host').value = data.data.http_max_idle_conns_per_host || '';
                    document.getElementById('http_max_conns_per_host').value = data.data.http_max_conns_per_host || '';
                    document.getElementById('http_idle_conn_timeout').value = data.data.http_idle_conn_timeout || '';
                    document.getElementById('scheduler_max_workers').value = data.data.scheduler_max_workers || '';
                    document.getElementById('scheduler_queue_size').value = data.data.scheduler_queue_size || '';
                    document.getElementById('crawler_rate_limit').value = data.data.crawler_rate_limit || '';
                    document.getElementById('cache_lock_timeout').value = data.data.cache_lock_timeout || '';
                    document.getElementById('cache_lock_retry_interval').value = data.data.cache_lock_retry_interval || '';
                    document.getElementById('cache-404-ttl').value = data.data.cache_404_ttl || 86400;
                    
                    // 缓存超时设置
                    if (document.getElementById('redis-connect-timeout')) {
                        // Redis超时配置
                        document.getElementById('redis-connect-timeout').value = data.data.redis_connect_timeout || 5000;
                        document.getElementById('redis-read-timeout').value = data.data.redis_read_timeout || 2000;
                        document.getElementById('redis-write-timeout').value = data.data.redis_write_timeout || 3000;
                        document.getElementById('redis-pool-timeout').value = data.data.redis_pool_timeout || 4000;
                        document.getElementById('redis-max-pool-size').value = data.data.redis_max_pool_size || 30;
                        
                        // 文件缓存超时配置
                        document.getElementById('file-read-timeout').value = data.data.file_read_timeout || 5000;
                        document.getElementById('file-write-timeout').value = data.data.file_write_timeout || 10000;
                        document.getElementById('async-queue-size').value = data.data.async_queue_size || 10000;
                        document.getElementById('async-worker-count').value = data.data.async_worker_count || 10;
                        
                    }
                    
                    // 加载资源限流配置
                    if (document.getElementById('max-database-conn')) {
                        document.getElementById('max-database-conn').value = data.data.max_database_conn || 80;
                        document.getElementById('current-database-conn').textContent = data.data.max_database_conn || 80;
                    }
                    if (document.getElementById('max-redis-conn')) {
                        document.getElementById('max-redis-conn').value = data.data.max_redis_conn || 160;
                        document.getElementById('current-redis-conn').textContent = data.data.max_redis_conn || 160;
                    }
                    if (document.getElementById('max-http-requests')) {
                        document.getElementById('max-http-requests').value = data.data.max_http_requests || 40;
                        document.getElementById('current-http-requests').textContent = data.data.max_http_requests || 40;
                    }
                    if (document.getElementById('max-file-ops')) {
                        document.getElementById('max-file-ops').value = data.data.max_file_ops || 24;
                        document.getElementById('current-file-ops').textContent = data.data.max_file_ops || 24;
                    }
                    if (document.getElementById('max-crawler-tasks')) {
                        document.getElementById('max-crawler-tasks').value = data.data.max_crawler_tasks || 16;
                        document.getElementById('current-crawler-tasks').textContent = data.data.max_crawler_tasks || 16;
                    }
                    
                    // 加载超时配置
                    if (document.getElementById('route-timeout')) {
                        document.getElementById('route-timeout').value = data.data.route_timeout || 1200;
                    }
                    if (document.getElementById('database-query-timeout')) {
                        document.getElementById('database-query-timeout').value = data.data.database_query_timeout || 800;
                    }
                    if (document.getElementById('redis-op-timeout')) {
                        document.getElementById('redis-op-timeout').value = data.data.redis_op_timeout || 200;
                    }
                    if (document.getElementById('http-request-timeout')) {
                        document.getElementById('http-request-timeout').value = data.data.http_request_timeout || 500;
                    }
                    if (document.getElementById('file-op-timeout')) {
                        document.getElementById('file-op-timeout').value = data.data.file_op_timeout || 1000;
                    }
                    if (document.getElementById('crawler-task-timeout')) {
                        document.getElementById('crawler-task-timeout').value = data.data.crawler_task_timeout || 10000;
                    }
                    
                    // 加载站点地图设置
                    if (document.getElementById('sitemap-auto-refresh')) {
                        document.getElementById('sitemap-auto-refresh').checked = data.data.enable_sitemap_auto_refresh || false;
                        document.getElementById('sitemap-refresh-interval').value = data.data.sitemap_refresh_interval || 720;
                        
                        // 显示上次刷新时间
                        if (data.data.last_sitemap_refresh_time) {
                            const lastRefreshTime = new Date(data.data.last_sitemap_refresh_time);
                            document.getElementById('last-sitemap-refresh-time').textContent = 
                                lastRefreshTime.toLocaleString('zh-CN');
                        } else {
                            document.getElementById('last-sitemap-refresh-time').textContent = '尚未刷新';
                        }
                    }
                }
            } catch (error) {
                console.error('加载系统设置失败:', error);
            }
        }
        
        // 加载统计设置
        async function loadAnalytics() {
            try {
                const res = await fetch('/api/v1/analytics');
                const data = await res.json();
                
                if (data.success && data.data) {
                    document.getElementById('analytics-enabled').checked = data.data.enabled;
                    document.getElementById('analytics-code').value = data.data.code || '';
                }
            } catch (error) {
                console.error('加载统计设置失败:', error);
            }
        }
        
        // 保存设置
        async function saveSettings() {
            if (currentTab === 'analytics') {
                await saveAnalytics();
            } else if (currentTab === 'weight-monitor') {
                await saveWeightMonitorConfig();
            } else if (currentTab === 'sitemap') {
                await saveSystemSettings(); // 站点地图设置也是系统设置的一部分
            } else if (currentTab === 'general' || currentTab === 'basic' || currentTab === 'ua-check' || currentTab === 'referer-check' || currentTab === 'cache' || currentTab === 'cache-timeout' || currentTab === 'crawler' || currentTab === 'error-pages' || currentTab === 'performance' || currentTab === 'resource') {
                await saveSystemSettings();
            } else {
                showToast('设置保存成功！', 'success');
            }
        }
        
        // 保存系统设置
        async function saveSystemSettings() {
            // 先获取当前的系统设置，以保留其他字段的值
            let currentSettings = {};
            try {
                const response = await fetch('/api/v1/system/settings');
                const result = await response.json();
                if (result.success) {
                    currentSettings = result.data || {};
                }
            } catch (error) {
                console.error('获取当前设置失败:', error);
            }
            
            const settingsData = { ...currentSettings };  // 保留所有现有设置
            
            if (currentTab === 'general' || currentTab === 'basic') {
                
                // 日志配置
                settingsData.log_enabled = document.getElementById('log_enabled').checked;
                settingsData.log_level = document.getElementById('log-level').value;
                settingsData.log_storage = document.getElementById('log_storage').value || 'file';
                
                // 验证码配置
                const enableCaptchaEl = document.getElementById('enable-captcha');
                const captchaLengthEl = document.getElementById('captcha-length');
                const captchaExpiryEl = document.getElementById('captcha-expiry');
                
                if (enableCaptchaEl) {
                    settingsData.enable_captcha = enableCaptchaEl.checked;
                }
                if (captchaLengthEl) {
                    settingsData.captcha_length = parseInt(captchaLengthEl.value) || 4;
                } else {
                    // 确保captcha_length总是整数
                    if (settingsData.captcha_length) {
                        settingsData.captcha_length = parseInt(settingsData.captcha_length) || 4;
                    }
                }
                if (captchaExpiryEl) {
                    settingsData.captcha_expiry = parseInt(captchaExpiryEl.value) || 300;
                } else {
                    // 确保captcha_expiry总是整数，即使元素不存在
                    if (settingsData.captcha_expiry) {
                        settingsData.captcha_expiry = parseInt(settingsData.captcha_expiry) || 300;
                    }
                }
                
                // CSRF配置
                const enableCSRFEl = document.getElementById('enable-csrf');
                if (enableCSRFEl) {
                    settingsData.enable_csrf = enableCSRFEl.checked;
                }
                
                settingsData.log_retention_days = parseInt(document.getElementById('log_retention_days').value) || 7;
                settingsData.log_max_size = parseInt(document.getElementById('log_max_size').value) || 100;
                settingsData.log_max_backups = parseInt(document.getElementById('log_max_backups').value) || 10;
                settingsData.log_access_enabled = document.getElementById('log_access_enabled').checked;
                settingsData.log_error_enabled = document.getElementById('log_error_enabled').checked;
            } else if (currentTab === 'crawler') {
                settingsData.crawler_concurrency = parseInt(document.getElementById('crawler-concurrency').value);
                settingsData.http_client_timeout = parseInt(document.getElementById('http-client-timeout-main').value) || 30000;
                settingsData.proxy_request_timeout = parseInt(document.getElementById('proxy-request-timeout-main').value) || 60000;
                settingsData.user_agent_list = document.getElementById('user-agent-list').value;
            } else if (currentTab === 'ua-check') {
                // 只更新UA判断相关字段，保留enable_global_spider_block的原值
                settingsData.enable_global_ua_check = document.getElementById('enable-global-ua-check').checked;
                settingsData.default_allowed_ua = document.getElementById('default-allowed-ua').value;
                settingsData.default_non_spider_html = document.getElementById('default-non-spider-html').value;
                settingsData.global_redirect_www = document.getElementById('global-redirect-www').checked;
                // 确保不覆盖enable_global_spider_block字段
            } else if (currentTab === 'referer-check') {
                // 更新来源判断相关字段
                settingsData.enable_global_referer_check = document.getElementById('enable-global-referer-check').checked;
                settingsData.global_allowed_referers = document.getElementById('global-allowed-referers').value;
                settingsData.global_referer_block_code = parseInt(document.getElementById('global-referer-block-code').value) || 403;
                settingsData.global_referer_block_html = document.getElementById('global-referer-block-html').value;
            } else if (currentTab === 'cache') {
                settingsData.default_cache_home_ttl = parseInt(document.getElementById('default-cache-home-ttl').value);
                settingsData.default_cache_other_ttl = parseInt(document.getElementById('default-cache-other-ttl').value);
            } else if (currentTab === 'cache-timeout') {
                // Redis超时配置
                settingsData.redis_connect_timeout = parseInt(document.getElementById('redis-connect-timeout').value) || 5000;
                settingsData.redis_read_timeout = parseInt(document.getElementById('redis-read-timeout').value) || 2000;
                settingsData.redis_write_timeout = parseInt(document.getElementById('redis-write-timeout').value) || 3000;
                settingsData.redis_pool_timeout = parseInt(document.getElementById('redis-pool-timeout').value) || 4000;
                settingsData.redis_max_pool_size = parseInt(document.getElementById('redis-max-pool-size').value) || 30;
                
                // 文件缓存超时配置
                settingsData.file_read_timeout = parseInt(document.getElementById('file-read-timeout').value) || 5000;
                settingsData.file_write_timeout = parseInt(document.getElementById('file-write-timeout').value) || 10000;
                settingsData.async_queue_size = parseInt(document.getElementById('async-queue-size').value) || 10000;
                settingsData.async_worker_count = parseInt(document.getElementById('async-worker-count').value) || 10;
                
            } else if (currentTab === 'error-pages') {
                settingsData.site_not_found_html = document.getElementById('site-not-found-html').value;
                settingsData.default_404_html = document.getElementById('default-404-html').value;
                settingsData.enable_404_cache = document.getElementById('enable-404-cache').checked;
                settingsData.cache_404_ttl = parseInt(document.getElementById('cache-404-ttl').value) || 86400;
            } else if (currentTab === 'performance') {
                // 并发控制配置 - HTTP请求并发数（从性能配置面板读取）
                const crawlerConcurrency = document.getElementById('crawler-concurrency-perf').value;
                if (crawlerConcurrency) {
                    settingsData.crawler_concurrency = parseInt(crawlerConcurrency);
                    // 同步更新请求设置面板的值
                    document.getElementById('crawler-concurrency').value = crawlerConcurrency;
                }
                
                // 数据库连接池配置
                const dbMaxOpenConns = document.getElementById('db_max_open_conns').value;
                const dbMaxIdleConns = document.getElementById('db_max_idle_conns').value;
                const dbConnMaxLifetime = document.getElementById('db_conn_max_lifetime').value;
                const dbSlaveMaxOpenConns = document.getElementById('db_slave_max_open_conns').value;
                
                if (dbMaxOpenConns) settingsData.db_max_open_conns = parseInt(dbMaxOpenConns);
                if (dbMaxIdleConns) settingsData.db_max_idle_conns = parseInt(dbMaxIdleConns);
                if (dbConnMaxLifetime) settingsData.db_conn_max_lifetime = parseInt(dbConnMaxLifetime);
                if (dbSlaveMaxOpenConns) settingsData.db_slave_max_open_conns = parseInt(dbSlaveMaxOpenConns);
                
                // HTTP客户端配置
                const httpMaxIdleConns = document.getElementById('http_max_idle_conns').value;
                const httpMaxIdleConnsPerHost = document.getElementById('http_max_idle_conns_per_host').value;
                const httpMaxConnsPerHost = document.getElementById('http_max_conns_per_host').value;
                const httpIdleConnTimeout = document.getElementById('http_idle_conn_timeout').value;
                
                if (httpMaxIdleConns) settingsData.http_max_idle_conns = parseInt(httpMaxIdleConns);
                if (httpMaxIdleConnsPerHost) settingsData.http_max_idle_conns_per_host = parseInt(httpMaxIdleConnsPerHost);
                if (httpMaxConnsPerHost) settingsData.http_max_conns_per_host = parseInt(httpMaxConnsPerHost);
                if (httpIdleConnTimeout) settingsData.http_idle_conn_timeout = parseInt(httpIdleConnTimeout);
                
                // 任务调度器配置
                const schedulerMaxWorkers = document.getElementById('scheduler_max_workers').value;
                const schedulerQueueSize = document.getElementById('scheduler_queue_size').value;
                const crawlerRateLimit = document.getElementById('crawler_rate_limit').value;
                
                if (schedulerMaxWorkers) settingsData.scheduler_max_workers = parseInt(schedulerMaxWorkers);
                if (schedulerQueueSize) settingsData.scheduler_queue_size = parseInt(schedulerQueueSize);
                if (crawlerRateLimit) settingsData.crawler_rate_limit = parseFloat(crawlerRateLimit);
                
                // 缓存锁配置
                const cacheLockTimeout = document.getElementById('cache_lock_timeout').value;
                const cacheLockRetryInterval = document.getElementById('cache_lock_retry_interval').value;
                
                if (cacheLockTimeout) settingsData.cache_lock_timeout = parseInt(cacheLockTimeout);
                if (cacheLockRetryInterval) settingsData.cache_lock_retry_interval = parseInt(cacheLockRetryInterval);
            } else if (currentTab === 'resource') {
                // 资源限流配置
                settingsData.max_database_conn = parseInt(document.getElementById('max-database-conn').value) || 80;
                settingsData.max_redis_conn = parseInt(document.getElementById('max-redis-conn').value) || 160;
                settingsData.max_http_requests = parseInt(document.getElementById('max-http-requests').value) || 40;
                settingsData.max_file_ops = parseInt(document.getElementById('max-file-ops').value) || 24;
                settingsData.max_crawler_tasks = parseInt(document.getElementById('max-crawler-tasks').value) || 16;
                
                // 超时配置
                settingsData.route_timeout = parseInt(document.getElementById('route-timeout').value) || 1200;
                settingsData.database_query_timeout = parseInt(document.getElementById('database-query-timeout').value) || 800;
                settingsData.redis_op_timeout = parseInt(document.getElementById('redis-op-timeout').value) || 200;
                settingsData.http_request_timeout = parseInt(document.getElementById('http-request-timeout').value) || 500;
                settingsData.file_op_timeout = parseInt(document.getElementById('file-op-timeout').value) || 1000;
                settingsData.crawler_task_timeout = parseInt(document.getElementById('crawler-task-timeout').value) || 10000;
            }
            
            // 获取站点地图设置
            if (activePanel === 'sitemap') {
                settingsData.enable_sitemap_auto_refresh = document.getElementById('sitemap-auto-refresh').checked;
                settingsData.sitemap_refresh_interval = parseInt(document.getElementById('sitemap-refresh-interval').value) || 720;
            }
            
            try {
                // 确保某些字段是正确的数据类型
                if (settingsData.captcha_expiry !== undefined) {
                    settingsData.captcha_expiry = parseInt(settingsData.captcha_expiry) || 300;
                }
                if (settingsData.captcha_length !== undefined) {
                    settingsData.captcha_length = parseInt(settingsData.captcha_length) || 4;
                }
                
                // 确保所有数字字段都是整数类型
                const intFields = [
                    'default_cache_home_ttl', 'default_cache_other_ttl',
                    'cache_404_ttl', 'redis_max_pool_size', 'file_read_timeout', 'file_write_timeout',
                    'async_queue_size', 'async_worker_count', 'proxy_request_timeout',
                    'db_max_open_conns', 'db_max_idle_conns', 'db_conn_max_lifetime',
                    'db_slave_max_open_conns', 'db_slave_max_idle_conns', 'http_max_idle_conns',
                    'http_max_idle_conns_per_host', 'http_max_conns_per_host', 'http_idle_conn_timeout',
                    'cache_lock_timeout', 'cache_lock_retry_interval', 'log_retention_days',
                    'log_max_size', 'log_max_backups', 'max_database_conn', 'max_redis_conn',
                    'max_http_requests', 'max_file_ops', 'max_crawler_tasks', 'route_timeout',
                    'database_query_timeout', 'redis_op_timeout', 'http_request_timeout',
                    'file_op_timeout', 'crawler_task_timeout'
                ];
                
                intFields.forEach(field => {
                    if (settingsData[field] !== undefined) {
                        settingsData[field] = parseInt(settingsData[field]) || 0;
                    }
                });
                
                // crawler_rate_limit 是浮点数
                if (settingsData.crawler_rate_limit !== undefined) {
                    settingsData.crawler_rate_limit = parseFloat(settingsData.crawler_rate_limit) || 2000;
                }
                
                // 调试：打印验证码相关字段的类型
                console.log('发送前的验证码设置:', {
                    enable_captcha: settingsData.enable_captcha,
                    captcha_length: settingsData.captcha_length,
                    captcha_length_type: typeof settingsData.captcha_length,
                    captcha_expiry: settingsData.captcha_expiry,
                    captcha_expiry_type: typeof settingsData.captcha_expiry
                });
                
                const res = await fetch('/api/v1/system/settings', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(settingsData)
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showToast('设置保存成功！', 'success');
                } else {
                    showToast(data.error || '保存失败', 'error');
                }
            } catch (error) {
                showToast('保存失败: ' + error.message, 'error');
            }
        }
        
        // 保存统计设置
        async function saveAnalytics() {
            const analyticsData = {
                code: document.getElementById('analytics-code').value,
                enabled: document.getElementById('analytics-enabled').checked
            };
            
            try {
                const res = await fetch('/api/v1/analytics', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(analyticsData)
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showToast('统计设置保存成功！', 'success');
                } else {
                    showToast(data.error || '保存失败', 'error');
                }
            } catch (error) {
                showToast('保存失败: ' + error.message, 'error');
            }
        }
        
        // 刷新所有站点的sitemap
        async function refreshAllSitemaps() {
            const refreshBtn = event.target.closest('button');
            const statusSpan = document.getElementById('sitemap-refresh-status');
            
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>正在刷新站点地图...';
            statusSpan.textContent = '正在刷新所有站点的sitemap文件...';
            
            try {
                const res = await fetch('/api/v1/sitemap/refresh-all', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showToast(data.message || '所有站点地图已刷新成功！', 'success');
                    statusSpan.textContent = `成功: ${data.data.success_count}个, 失败: ${data.data.failed_count}个`;
                    
                    // 如果有错误，显示错误详情
                    if (data.data.errors && data.data.errors.length > 0) {
                        console.error('Sitemap刷新错误:', data.data.errors);
                    }
                } else {
                    showToast(data.error || '刷新失败', 'error');
                    statusSpan.textContent = '刷新失败';
                }
            } catch (error) {
                showToast('刷新失败: ' + error.message, 'error');
                statusSpan.textContent = '刷新失败';
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="fas fa-sitemap mr-2"></i>刷新所有站点地图';
                
                // 5秒后清除状态信息
                setTimeout(() => {
                    statusSpan.textContent = '';
                }, 5000);
            }
        }
        
        // 清空站点地图缓存
        async function clearSitemapCache() {
            const clearBtn = event.target.closest('button');
            const statusSpan = document.getElementById('sitemap-refresh-status');
            
            if (!confirm('确定要清空所有站点地图缓存吗？')) {
                return;
            }
            
            clearBtn.disabled = true;
            clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>正在清空缓存...';
            
            try {
                const res = await fetch('/api/v1/sitemap/clear-cache', {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showToast('站点地图缓存已清空', 'success');
                    statusSpan.textContent = '缓存已清空';
                    loadSitemapStats(); // 重新加载统计
                } else {
                    showToast(data.error || '清空失败', 'error');
                    statusSpan.textContent = '清空失败';
                }
            } catch (error) {
                showToast('清空失败: ' + error.message, 'error');
                statusSpan.textContent = '清空失败';
            } finally {
                clearBtn.disabled = false;
                clearBtn.innerHTML = '<i class="fas fa-trash mr-2"></i>清空站点地图缓存';
                
                // 5秒后清除状态信息
                setTimeout(() => {
                    statusSpan.textContent = '';
                }, 5000);
            }
        }
        
        // 加载站点地图统计
        async function loadSitemapStats() {
            try {
                const res = await fetch('/api/v1/sitemap/stats', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                const data = await res.json();
                
                if (data.success && data.data) {
                    document.getElementById('sitemap-enabled-count').textContent = data.data.enabled_count || 0;
                    document.getElementById('sitemap-total-urls').textContent = data.data.total_urls || 0;
                    document.getElementById('sitemap-pending-count').textContent = data.data.pending_count || 0;
                }
            } catch (error) {
                console.error('加载站点地图统计失败:', error);
            }
        }
        
        async function refreshAnalyticsJS() {
            const refreshBtn = event.target;
            const statusSpan = document.getElementById('refresh-status');
            
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>刷新中...';
            statusSpan.textContent = '正在刷新统计文件...';
            
            try {
                const res = await fetch('/api/v1/analytics/refresh', {
                    method: 'POST'
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showToast('所有站点统计JS已刷新成功！', 'success');
                    statusSpan.textContent = `已刷新 ${data.data.updated_count} 个站点的统计文件`;
                } else {
                    showToast(data.error || '刷新失败', 'error');
                    statusSpan.textContent = '刷新失败';
                }
            } catch (error) {
                showToast('刷新失败: ' + error.message, 'error');
                statusSpan.textContent = '刷新失败';
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>刷新所有站点统计JS';
                
                // 5秒后清除状态信息
                setTimeout(() => {
                    statusSpan.textContent = '';
                }, 5000);
            }
        }
        
        // 页面加载完成后执行
        // 应用性能配置预设
        function applyPerformancePreset(preset) {
            if (preset === 'default') {
                // 恢复默认值
                document.getElementById('db_max_open_conns').value = 100;
                document.getElementById('db_max_idle_conns').value = 50;
                document.getElementById('db_conn_max_lifetime').value = 600;
                document.getElementById('db_slave_max_open_conns').value = 50;
                document.getElementById('http_max_idle_conns').value = 200;
                document.getElementById('http_max_idle_conns_per_host').value = 50;
                document.getElementById('http_max_conns_per_host').value = 100;
                document.getElementById('http_idle_conn_timeout').value = 90;
                // scheduler_max_workers 和 scheduler_queue_size 已废弃，由工作池和信号量机制替代
                document.getElementById('crawler_rate_limit').value = 2000;
                document.getElementById('cache_lock_timeout').value = 1000;
                document.getElementById('cache_lock_retry_interval').value = 100;
                showToast('已恢复默认配置值', 'info');
            } else if (preset === '200sites') {
                // 200站点优化配置（配合资源限流：数据库并发100，HTTP并发40）
                document.getElementById('db_max_open_conns').value = 200;  // 2倍并发限制
                document.getElementById('db_max_idle_conns').value = 100;
                document.getElementById('db_conn_max_lifetime').value = 600;
                document.getElementById('db_slave_max_open_conns').value = 100;
                document.getElementById('http_max_idle_conns').value = 300;
                document.getElementById('http_max_idle_conns_per_host').value = 50;
                document.getElementById('http_max_conns_per_host').value = 80;  // 2倍HTTP并发限制(40)
                document.getElementById('http_idle_conn_timeout').value = 90;
                // scheduler_max_workers 和 scheduler_queue_size 已废弃
                document.getElementById('crawler_rate_limit').value = 3000;
                document.getElementById('cache_lock_timeout').value = 2000;
                document.getElementById('cache_lock_retry_interval').value = 100;
                showToast('已应用200站点优化配置', 'success');
            } else if (preset === '500sites') {
                // 500站点优化配置（配合资源限流：数据库并发200，HTTP并发80）
                document.getElementById('db_max_open_conns').value = 400;  // 2倍并发限制
                document.getElementById('db_max_idle_conns').value = 200;
                document.getElementById('db_conn_max_lifetime').value = 600;
                document.getElementById('db_slave_max_open_conns').value = 200;
                document.getElementById('http_max_idle_conns').value = 500;
                document.getElementById('http_max_idle_conns_per_host').value = 100;
                document.getElementById('http_max_conns_per_host').value = 160;  // 2倍HTTP并发限制(80)
                document.getElementById('http_idle_conn_timeout').value = 90;
                // scheduler_max_workers 和 scheduler_queue_size 已废弃
                document.getElementById('crawler_rate_limit').value = 5000;
                document.getElementById('cache_lock_timeout').value = 3000;
                document.getElementById('cache_lock_retry_interval').value = 100;
                showToast('已应用500站点优化配置', 'success');
            }
        }
        
        // 权重监测相关函数
        async function loadWeightMonitorConfig() {
            console.log('开始加载权重监测配置...');
            try {
                const res = await fetch('/api/v1/weight/config');
                const data = await res.json();
                console.log('权重监测配置响应:', data);
                
                if (data.success && data.data) {
                    const config = data.data;
                    
                    // 检查元素是否存在
                    const elements = {
                        'weight-monitor-enabled': document.getElementById('weight-monitor-enabled'),
                        'weight-api-key': document.getElementById('weight-api-key'),
                        'weight-check-interval': document.getElementById('weight-check-interval'),
                        'weight-batch-size': document.getElementById('weight-batch-size'),
                        'weight-batch-delay': document.getElementById('weight-batch-delay'),
                        'weight-cycle-wait': document.getElementById('weight-cycle-wait')
                    };
                    
                    console.log('检查元素存在性:', elements);
                    
                    // 安全设置值
                    if (elements['weight-monitor-enabled']) {
                        elements['weight-monitor-enabled'].checked = config.enabled || false;
                    }
                    if (elements['weight-api-key']) {
                        elements['weight-api-key'].value = config.api_key || '';
                    }
                    if (elements['weight-check-interval']) {
                        elements['weight-check-interval'].value = config.check_interval || 60;
                    }
                    if (elements['weight-batch-size']) {
                        elements['weight-batch-size'].value = config.batch_size || 5;
                    }
                    if (elements['weight-batch-delay']) {
                        elements['weight-batch-delay'].value = config.batch_delay || 5;
                    }
                    if (elements['weight-cycle-wait']) {
                        elements['weight-cycle-wait'].value = config.cycle_wait || 24;
                    }
                    
                    // 更新状态信息
                    updateWeightMonitorStatus(config);
                }
            } catch (error) {
                console.error('加载权重监测配置失败:', error);
            }
        }
        
        async function saveWeightMonitorConfig() {
            try {
                const config = {
                    enabled: document.getElementById('weight-monitor-enabled').checked,
                    api_key: document.getElementById('weight-api-key').value,
                    check_interval: parseInt(document.getElementById('weight-check-interval').value),
                    batch_size: parseInt(document.getElementById('weight-batch-size').value),
                    batch_delay: parseInt(document.getElementById('weight-batch-delay').value),
                    cycle_wait: parseInt(document.getElementById('weight-cycle-wait').value)
                };
                
                const res = await fetch('/api/v1/weight/config', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                
                const data = await res.json();
                if (data.success) {
                    showToast('权重监测配置保存成功', 'success');
                    // 重新加载配置以更新状态
                    loadWeightMonitorConfig();
                } else {
                    showToast(data.error || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存权重监测配置失败:', error);
                showToast('保存失败: ' + error.message, 'error');
            }
        }
        
        async function testAPIKey() {
            const apiKey = document.getElementById('weight-api-key').value;
            if (!apiKey) {
                showToast('请先输入API密钥', 'warning');
                return;
            }
            
            try {
                const res = await fetch('/api/v1/weight/test-api', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ api_key: apiKey })
                });
                
                const data = await res.json();
                if (data.success) {
                    showToast('API密钥验证成功', 'success');
                } else {
                    showToast(data.error || 'API密钥无效', 'error');
                }
            } catch (error) {
                showToast('验证失败: ' + error.message, 'error');
            }
        }
        
        async function manualCheckWeights() {
            if (!confirm('确定要立即执行权重检查吗？')) return;
            
            try {
                const res = await fetch('/api/v1/weight/check', {
                    method: 'POST'
                });
                
                const data = await res.json();
                if (data.success) {
                    showToast('权重检查已触发，请稍后查看结果', 'success');
                } else {
                    showToast(data.error || '触发失败', 'error');
                }
            } catch (error) {
                showToast('触发失败: ' + error.message, 'error');
            }
        }
        
        function viewWeightHistory() {
            window.location.href = '/admin/weight-monitor';
        }
        
        function updateWeightMonitorStatus(config) {
            // 更新服务状态
            const statusEl = document.getElementById('weight-service-status');
            if (!statusEl) return;
            
            if (!config.api_key) {
                statusEl.textContent = '未配置';
                statusEl.className = 'text-sm font-medium text-gray-500';
            } else if (config.enabled) {
                statusEl.textContent = '运行中';
                statusEl.className = 'text-sm font-medium text-green-600';
            } else {
                statusEl.textContent = '已停止';
                statusEl.className = 'text-sm font-medium text-yellow-600';
            }
            
            // 更新最后检查时间
            const lastCheckEl = document.getElementById('weight-last-check');
            if (lastCheckEl) {
                if (config.last_check_time && config.last_check_time !== '0001-01-01T08:05:43+08:05') {
                    const lastCheck = new Date(config.last_check_time);
                    lastCheckEl.textContent = lastCheck.toLocaleString('zh-CN');
                } else {
                    lastCheckEl.textContent = '从未执行';
                }
            }
            
            // 更新下次检查时间
            const nextCheckEl = document.getElementById('weight-next-check');
            if (nextCheckEl) {
                if (config.next_check_time && config.next_check_time !== '0001-01-01T08:05:43+08:05') {
                    const nextCheck = new Date(config.next_check_time);
                    nextCheckEl.textContent = nextCheck.toLocaleString('zh-CN');
                } else if (config.enabled && config.check_interval && config.last_check_time && config.last_check_time !== '0001-01-01T08:05:43+08:05') {
                    // 如果没有next_check_time，基于last_check_time和interval计算
                    const lastCheck = new Date(config.last_check_time);
                    const nextCheck = new Date(lastCheck.getTime() + config.check_interval * 60000);
                    nextCheckEl.textContent = nextCheck.toLocaleString('zh-CN');
                } else {
                    nextCheckEl.textContent = '-';
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemInfo();
            loadSystemSettings();
            loadAnalytics();
            // 初始化显示第一个标签页（general）
            switchTab('general');
        });
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
