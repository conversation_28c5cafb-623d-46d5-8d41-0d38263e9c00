<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员管理 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
                        <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">管理员管理</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                        <button onclick="showAddAdminModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i>
                            添加管理员
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 管理员列表 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50 border-b">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户名</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">昵称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">邮箱</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="admins-tbody" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                                        <div class="flex justify-center">
                                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                                        </div>
                                        <p class="mt-4">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t flex items-center justify-between">
                        <div id="pagination" class="flex space-x-2">
                            <!-- 分页按钮将在这里渲染 -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <!-- 添加/编辑管理员模态框 -->
    <div id="admin-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold" id="modal-title">添加管理员</h3>
                    <button onclick="closeAdminModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="admin-form" class="p-6">
                    <input type="hidden" id="admin-id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">用户名 <span class="text-red-500">*</span></label>
                        <input type="text" id="username" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="用户名">
                    </div>
                    
                    <div class="mb-4" id="password-group">
                        <label class="block text-sm font-medium text-gray-700 mb-2">密码 <span class="text-red-500">*</span></label>
                        <input type="password" id="password" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="至少6位">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">昵称</label>
                        <input type="text" id="nickname"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="显示名称">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                        <input type="email" id="email"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="<EMAIL>">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                        <select id="status" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="active">活跃</option>
                            <option value="disabled">禁用</option>
                        </select>
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closeAdminModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="saveAdmin()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 修改密码模态框 -->
    <div id="password-modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">修改密码</h3>
                    <button onclick="closePasswordModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="password-form" class="p-6">
                    <input type="hidden" id="password-admin-id">
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">新密码 <span class="text-red-500">*</span></label>
                        <input type="password" id="new-password" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="至少6位">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">确认密码 <span class="text-red-500">*</span></label>
                        <input type="password" id="confirm-password" required
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="再次输入密码">
                    </div>
                </form>
                
                <div class="px-6 py-4 border-t flex justify-end space-x-3">
                    <button onclick="closePasswordModal()"
                            class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button onclick="changePassword()"
                            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        修改密码
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script>
        let currentPage = 1;
        const pageSize = 10;
        
        // 显示系统时间
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'info': 'bg-blue-500',
                'warning': 'bg-yellow-500'
            }[type] || 'bg-gray-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 10);
            
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
        
        // 兼容旧的通知函数
        function showSuccess(message) { showToast(message, 'success'); }
        function showError(message) { showToast(message, 'error'); }
        function showWarning(message) { showToast(message, 'warning'); }
        function showInfo(message) { showToast(message, 'info'); }

        // 退出登录
        async function logout() {
            try {
                const res = await fetch('/api/v1/auth/logout', { method: 'POST' });
                if (res.ok) {
                    window.location.href = '/login';
                }
            } catch (error) {
                showToast('退出登录失败', 'error');
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadAdmins();
        });
        
        // 加载管理员列表
        async function loadAdmins(page = 1) {
            currentPage = page;
            
            try {
                const res = await fetch(`/api/v1/admins?page=${page}&limit=${pageSize}`);
                const data = await res.json();
                
                if (data.success) {
                    renderAdmins(data.data.admins || []);
                    renderPagination(data.data.total || 0);
                } else {
                    showError(data.error || '加载失败');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        // 渲染管理员列表
        function renderAdmins(admins) {
            const tbody = document.getElementById('admins-tbody');
            
            if (!admins || admins.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="px-6 py-12 text-center text-gray-500">暂无管理员</td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = admins.map(admin => `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${admin.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${admin.username}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${admin.nickname || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${admin.email || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        ${admin.status === 'active' 
                            ? '<span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">活跃</span>' 
                            : '<span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">禁用</span>'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(admin.last_login)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatDate(admin.created_at)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                        <button class="text-blue-600 hover:text-blue-800 mr-2" onclick="editAdmin(${admin.id})">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="text-indigo-600 hover:text-indigo-800 mr-2" onclick="showPasswordModal(${admin.id})">
                            <i class="fas fa-key"></i> 修改密码
                        </button>
                        ${admin.username !== 'admin' ? 
                            `<button class="text-red-600 hover:text-red-800" onclick="deleteAdmin(${admin.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>` : 
                            '<span class="text-gray-400 text-xs">系统管理员</span>'
                        }
                    </td>
                </tr>
            `).join('');
        }
        
        // 渲染分页
        function renderPagination(total) {
            const totalPages = Math.ceil(total / pageSize);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }
            
            let html = '';
            
            if (currentPage > 1) {
                html += `<button onclick="loadAdmins(${currentPage - 1})" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">上一页</button>`;
            }
            
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    html += `<span class="px-3 py-1 text-sm bg-blue-500 text-white rounded">${i}</span>`;
                } else {
                    html += `<button onclick="loadAdmins(${i})" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">${i}</button>`;
                }
            }
            
            if (currentPage < totalPages) {
                html += `<button onclick="loadAdmins(${currentPage + 1})" class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">下一页</button>`;
            }
            
            pagination.innerHTML = html;
        }
        
        // 显示添加管理员模态框
        function showAddAdminModal() {
            document.getElementById('modal-title').textContent = '添加管理员';
            document.getElementById('admin-form').reset();
            document.getElementById('admin-id').value = '';
            document.getElementById('password-group').style.display = 'block';
            document.getElementById('password').setAttribute('required', 'required');
            document.getElementById('admin-modal').classList.remove('hidden');
        }
        
        // 关闭管理员模态框
        function closeAdminModal() {
            document.getElementById('admin-modal').classList.add('hidden');
        }
        
        // 保存管理员
        async function saveAdmin() {
            const id = document.getElementById('admin-id').value;
            const isEdit = !!id;
            
            const adminData = {
                username: document.getElementById('username').value,
                nickname: document.getElementById('nickname').value,
                email: document.getElementById('email').value,
                status: document.getElementById('status').value
            };
            
            if (!isEdit) {
                adminData.password = document.getElementById('password').value;
                if (adminData.password.length < 6) {
                    showError('密码至少需要6位');
                    return;
                }
            }
            
            try {
                const url = isEdit ? `/api/v1/admins/${id}` : '/api/v1/admins';
                const method = isEdit ? 'PUT' : 'POST';
                
                const res = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(adminData)
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess(isEdit ? '更新成功' : '添加成功');
                    closeAdminModal();
                    loadAdmins(currentPage);
                } else {
                    showError(data.error || '保存失败');
                }
            } catch (error) {
                showError('保存失败: ' + error.message);
            }
        }
        
        // 编辑管理员
        async function editAdmin(id) {
            try {
                const res = await fetch(`/api/v1/admins/${id}`);
                const data = await res.json();
                
                if (data.success) {
                    const admin = data.data;
                    document.getElementById('modal-title').textContent = '编辑管理员';
                    document.getElementById('admin-id').value = admin.id;
                    document.getElementById('username').value = admin.username;
                    document.getElementById('nickname').value = admin.nickname || '';
                    document.getElementById('email').value = admin.email || '';
                    document.getElementById('status').value = admin.status;
                    document.getElementById('password-group').style.display = 'none';
                    document.getElementById('password').removeAttribute('required');
                    document.getElementById('admin-modal').classList.remove('hidden');
                } else {
                    showError(data.error || '加载失败');
                }
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }
        
        // 删除管理员
        async function deleteAdmin(id) {
            if (!confirm('确定要删除这个管理员吗？')) return;
            
            try {
                const res = await fetch(`/api/v1/admins/${id}`, { method: 'DELETE' });
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('删除成功');
                    loadAdmins(currentPage);
                } else {
                    showError(data.error || '删除失败');
                }
            } catch (error) {
                showError('删除失败: ' + error.message);
            }
        }
        
        // 显示修改密码模态框
        function showPasswordModal(id) {
            document.getElementById('password-admin-id').value = id;
            document.getElementById('password-form').reset();
            document.getElementById('password-modal').classList.remove('hidden');
        }
        
        // 关闭修改密码模态框
        function closePasswordModal() {
            document.getElementById('password-modal').classList.add('hidden');
        }
        
        // 修改密码
        async function changePassword() {
            const id = document.getElementById('password-admin-id').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            if (newPassword.length < 6) {
                showError('密码至少需要6位');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                showError('两次输入的密码不一致');
                return;
            }
            
            try {
                const res = await fetch(`/api/v1/admins/${id}/password`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        password: newPassword
                    })
                });
                
                const data = await res.json();
                
                if (data.success) {
                    showSuccess('密码修改成功');
                    closePasswordModal();
                } else {
                    showError(data.error || '修改失败');
                }
            } catch (error) {
                showError('修改失败: ' + error.message);
            }
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.id === 'admin-modal' || event.target.id === 'password-modal') {
                event.target.classList.add('hidden');
            }
        }
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>
