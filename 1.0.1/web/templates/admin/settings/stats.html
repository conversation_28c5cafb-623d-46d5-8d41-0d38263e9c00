<!-- 统计设置部分 -->
<div class="space-y-6">
    <!-- 统计代码设置 -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="p-6">
            <div id="panel-analytics">
                <div class="space-y-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">统计代码配置</h3>
                        <p class="text-sm text-gray-600 mb-6">配置全站统计代码，支持Google Analytics、百度统计等主流统计工具</p>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="flex items-center cursor-pointer group">
                                <input type="checkbox" id="analytics-enabled" 
                                       class="mr-3 w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <div>
                                    <span class="text-sm font-medium text-gray-700 group-hover:text-gray-900">启用统计功能</span>
                                    <p class="text-xs text-gray-500 mt-0.5">启用后将在所有镜像页面自动插入统计代码</p>
                                </div>
                            </label>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                统计代码
                            </label>
                            <div class="relative">
                                <textarea id="analytics-code" rows="10" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm bg-gray-50"
                                          placeholder="<!-- 请输入您的统计代码 -->
<!-- Google Analytics 示例 -->
<script async src='https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID'></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>"></textarea>
                                <div class="absolute top-2 right-2">
                                    <button id="clear-analytics-btn" 
                                            class="text-gray-400 hover:text-gray-600 transition">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="mt-2 text-xs text-gray-500">
                                <i class="fas fa-info-circle mr-1"></i>
                                支持多种统计代码格式，系统会自动处理并优化加载性能
                            </p>
                        </div>
                        
                        <div class="border-t pt-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-6">
                                    <label class="flex items-center cursor-pointer">
                                        <input type="checkbox" id="auto-refresh" 
                                               class="mr-2 w-4 h-4 text-blue-600 border-gray-300 rounded">
                                        <span class="text-sm text-gray-700">自动刷新</span>
                                    </label>
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">刷新间隔：</label>
                                        <input type="number" id="refresh-interval" value="60" min="10" max="1440"
                                               class="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <span class="text-sm text-gray-500 ml-1">分钟</span>
                                    </div>
                                </div>
                                <div class="flex space-x-3">
                                    <button id="refresh-analytics-js-btn" 
                                            class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-lg text-sm font-medium transition">
                                        <i class="fas fa-sync-alt mr-2"></i>刷新统计JS
                                    </button>
                                    <button id="test-analytics-btn" 
                                            class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition">
                                        <i class="fas fa-vial mr-2"></i>测试代码
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 最后更新信息 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mt-0.5 mr-3"></i>
                                <div class="text-sm">
                                    <p class="font-medium text-blue-900 mb-1">统计代码使用说明</p>
                                    <ul class="text-blue-700 space-y-1 text-xs">
                                        <li>• 统计代码会自动插入到每个镜像页面的 &lt;/body&gt; 标签前</li>
                                        <li>• 支持异步加载，不会影响页面加载速度</li>
                                        <li>• 修改代码后需要刷新静态资源才能生效</li>
                                        <li>• 建议使用压缩后的统计代码以提升性能</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 静态资源刷新 -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="p-6">
            <div id="panel-refresh">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">站点静态资源刷新</h3>
                            <p class="text-sm text-gray-600 mt-1">批量更新站点缓存中的统计代码和静态资源</p>
                        </div>
                        <div class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                            <i class="fas fa-server mr-1"></i>
                            <span id="total-sites">0</span> 个站点
                        </div>
                    </div>
                    
                    <!-- 快速操作栏 -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4">
                        <div class="flex flex-wrap items-center justify-between gap-3">
                            <div class="flex flex-wrap gap-2">
                                <button id="refresh-all-btn" 
                                        class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center text-sm font-medium transition shadow-sm border border-gray-200">
                                    <i class="fas fa-sync-alt mr-2 text-green-500"></i>
                                    刷新所有
                                </button>
                                <button id="refresh-selected-btn" 
                                        class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center text-sm font-medium transition shadow-sm border border-gray-200">
                                    <i class="fas fa-check-square mr-2 text-blue-500"></i>
                                    刷新选中
                                </button>
                                <button id="reload-list-btn" 
                                        class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg flex items-center text-sm font-medium transition shadow-sm border border-gray-200">
                                    <i class="fas fa-redo mr-2 text-gray-500"></i>
                                    刷新列表
                                </button>
                            </div>
                            <div class="flex items-center space-x-3">
                                <label class="text-sm text-gray-600">
                                    <input type="checkbox" id="auto-select-active" class="mr-1">
                                    仅选择活跃站点
                                </label>
                                <button id="clear-selection-btn" 
                                        class="text-sm text-gray-500 hover:text-gray-700">
                                    <i class="fas fa-times-circle mr-1"></i>清除选择
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 站点列表表格 -->
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left">
                                            <input type="checkbox" id="select-all" 
                                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                        </th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">站点信息</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">刷新状态</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后刷新</th>
                                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="site-list-tbody" class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                                            <i class="fas fa-spinner fa-spin mr-2"></i>正在加载站点列表...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 分页控件 -->
                    <div id="pagination-controls" class="mt-4 flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            显示第 <span id="page-start">1</span> - <span id="page-end">10</span> 条，共 <span id="total-count">0</span> 条
                        </div>
                        <div class="flex items-center space-x-2">
                            <button id="prev-page" class="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-chevron-left"></i> 上一页
                            </button>
                            <div id="page-numbers" class="flex items-center space-x-1">
                                <!-- 页码按钮将动态生成 -->
                            </div>
                            <button id="next-page" class="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                下一页 <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 刷新进度 -->
                    <div id="refresh-progress" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-blue-900">刷新进度</span>
                            <span class="text-sm text-blue-700" id="progress-text">0/0 完成</span>
                        </div>
                        <div class="w-full bg-blue-200 rounded-full h-2">
                            <div id="progress-bar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-blue-600 mt-2" id="progress-message">正在处理...</p>
                    </div>
                    
                    <!-- 操作日志 -->
                    <div class="border border-gray-200 rounded-lg">
                        <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <h4 class="text-sm font-medium text-gray-700">
                                    <i class="fas fa-terminal mr-2"></i>操作日志
                                </h4>
                                <button id="clear-logs-btn" class="text-xs text-gray-500 hover:text-gray-700 transition">
                                    <i class="fas fa-trash mr-1"></i>清空
                                </button>
                            </div>
                        </div>
                        <div id="refresh-log" class="p-3 text-xs text-gray-600 max-h-40 overflow-y-auto font-mono space-y-1 bg-white">
                            <div class="text-gray-400">等待操作...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 确保在全局作用域定义所有函数
(function() {
    'use strict';
    
    // 全局变量
    let siteList = [];
    let refreshStatuses = {};
    let isRefreshing = false;
    let pollingTimer = null;
    let currentPage = 1;
    let pageSize = 10;
    let totalCount = 0;
    let totalPages = 0;
    
    // ==================== 工具函数 ====================
    
    function showToast(message, type = 'info') {
        if (typeof window.showToast === 'function') {
            window.showToast(message, type);
        } else {
            const colors = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            };
            
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-times-circle',
                'warning': 'fas fa-exclamation-circle',
                'info': 'fas fa-info-circle'
            };
            
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 ${colors[type] || colors['info']} text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-3 animate-slide-in`;
            toast.innerHTML = `
                <i class="${icons[type] || icons['info']}"></i>
                <span>${message}</span>
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('animate-slide-out');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
    }
    
    function addLog(message, type = 'info') {
        const logDiv = document.getElementById('refresh-log');
        if (!logDiv) {
            console.log(`[${type}] ${message}`);
            return;
        }
        
        const time = new Date().toLocaleTimeString('zh-CN');
        const icons = {
            'info': '<i class="fas fa-info-circle text-blue-500"></i>',
            'success': '<i class="fas fa-check-circle text-green-500"></i>',
            'error': '<i class="fas fa-times-circle text-red-500"></i>',
            'warning': '<i class="fas fa-exclamation-triangle text-yellow-500"></i>'
        };
        
        const colors = {
            'info': 'text-gray-600',
            'success': 'text-green-600',
            'error': 'text-red-600',
            'warning': 'text-yellow-600'
        };
        
        const logEntry = document.createElement('div');
        logEntry.className = `flex items-start space-x-2 ${colors[type] || colors['info']}`;
        logEntry.innerHTML = `
            <span class="flex-shrink-0">${icons[type] || icons['info']}</span>
            <span class="text-xs opacity-60">[${time}]</span>
            <span class="flex-1">${message}</span>
        `;
        
        const placeholder = logDiv.querySelector('.text-gray-400');
        if (placeholder) {
            placeholder.remove();
        }
        
        logDiv.insertBefore(logEntry, logDiv.firstChild);
        
        while (logDiv.children.length > 100) {
            logDiv.removeChild(logDiv.lastChild);
        }
        
        logDiv.scrollTop = 0;
    }
    
    function getSiteStatusBadge(status) {
        const badges = {
            'active': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><i class="fas fa-circle text-green-400 mr-1" style="font-size: 6px;"></i>活跃</span>',
            'inactive': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"><i class="fas fa-circle text-gray-400 mr-1" style="font-size: 6px;"></i>停用</span>',
            'pending': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"><i class="fas fa-circle text-yellow-400 mr-1" style="font-size: 6px;"></i>待定</span>'
        };
        return badges[status] || badges['pending'];
    }
    
    function getRefreshStatusBadge(status) {
        const badges = {
            'pending': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"><i class="fas fa-clock mr-1"></i>待刷新</span>',
            'refreshing': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700"><i class="fas fa-spinner fa-spin mr-1"></i>刷新中</span>',
            'completed': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-700"><i class="fas fa-check-circle mr-1"></i>已完成</span>',
            'failed': '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-700"><i class="fas fa-times-circle mr-1"></i>失败</span>'
        };
        return badges[status] || badges['pending'];
    }
    
    function formatTime(timeStr) {
        if (!timeStr) return '未知';
        const date = new Date(timeStr);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
        if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`;
        
        return date.toLocaleDateString('zh-CN');
    }
    
    function showProgress(current, total) {
        const progressDiv = document.getElementById('refresh-progress');
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        const progressMessage = document.getElementById('progress-message');
        
        progressDiv.classList.remove('hidden');
        const percentage = total > 0 ? (current / total * 100) : 0;
        progressBar.style.width = percentage + '%';
        progressText.textContent = `${current}/${total} 完成`;
        
        if (current === total) {
            progressMessage.textContent = '刷新完成！';
        } else {
            progressMessage.textContent = `正在处理第 ${current + 1} 个站点...`;
        }
    }
    
    function hideProgress() {
        setTimeout(() => {
            document.getElementById('refresh-progress').classList.add('hidden');
        }, 2000);
    }
    
    function updateSiteStatus(siteId, status) {
        if (!refreshStatuses[siteId]) {
            refreshStatuses[siteId] = {};
        }
        refreshStatuses[siteId].status = status;
        renderSiteList();
    }
    
    // ==================== 核心功能函数 ====================
    
    async function loadAnalyticsSettings() {
        try {
            const res = await fetch('/api/v1/analytics', {
                credentials: 'same-origin'
            });
            const data = await res.json();
            
            if (data.success && data.data) {
                document.getElementById('analytics-enabled').checked = data.data.enabled || false;
                document.getElementById('analytics-code').value = data.data.code || '';
                document.getElementById('auto-refresh').checked = data.data.auto_refresh || false;
                document.getElementById('refresh-interval').value = data.data.refresh_interval || 60;
            }
        } catch (error) {
            console.error('加载统计设置失败:', error);
        }
    }
    
    async function saveAnalytics() {
        const settings = {
            enabled: document.getElementById('analytics-enabled').checked,
            code: document.getElementById('analytics-code').value,
            auto_refresh: document.getElementById('auto-refresh').checked,
            refresh_interval: parseInt(document.getElementById('refresh-interval').value)
        };
        
        try {
            const res = await fetch('/api/v1/analytics', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin',
                body: JSON.stringify(settings)
            });
            
            const data = await res.json();
            
            if (data.success) {
                showToast('统计设置保存成功', 'success');
                
                if (settings.enabled && settings.code) {
                    setTimeout(() => {
                        if (confirm('统计代码已更新，是否立即刷新所有站点的静态资源？')) {
                            document.getElementById('panel-refresh').scrollIntoView({ behavior: 'smooth' });
                            setTimeout(() => refreshAllSites(), 500);
                        }
                    }, 1000);
                }
            } else {
                showToast(data.error || '保存失败', 'error');
            }
        } catch (error) {
            showToast('保存失败: ' + error.message, 'error');
        }
    }
    
    async function loadSiteList(page = 1) {
        console.log('开始加载站点列表，页码:', page);
        currentPage = page;
        
        try {
            addLog(`正在加载第 ${page} 页站点列表...`);
            
            const sitesRes = await fetch(`/api/v1/sites?page=${page}&limit=${pageSize}`, {
                credentials: 'same-origin'
            });
            const sitesData = await sitesRes.json();
            
            if (sitesData.success && sitesData.data) {
                if (sitesData.data.sites) {
                    siteList = sitesData.data.sites;
                    totalCount = sitesData.data.total || siteList.length;
                } else if (Array.isArray(sitesData.data)) {
                    siteList = sitesData.data;
                    totalCount = siteList.length;
                } else {
                    siteList = [];
                    totalCount = 0;
                }
                
                // 计算总页数
                totalPages = Math.ceil(totalCount / pageSize);
                
                // 更新分页信息
                updatePaginationInfo();
                
                const totalSitesEl = document.getElementById('total-sites');
                if (totalSitesEl) {
                    totalSitesEl.textContent = totalCount;
                }
                
                try {
                    const statusRes = await fetch('/api/v1/static-refresh/status', {
                        credentials: 'same-origin'
                    });
                    const statusData = await statusRes.json();
                    
                    if (statusData.success) {
                        refreshStatuses = statusData.data || {};
                    }
                } catch (error) {
                    console.error('获取刷新状态失败:', error);
                }
                
                renderSiteList();
                addLog(`成功加载第 ${currentPage} 页，共 ${totalCount} 个站点`, 'success');
            } else {
                addLog('站点列表为空', 'warning');
                totalCount = 0;
                totalPages = 0;
                updatePaginationInfo();
                renderEmptyList();
            }
        } catch (error) {
            console.error('加载站点列表失败:', error);
            addLog('加载站点列表失败: ' + error.message, 'error');
            totalCount = 0;
            totalPages = 0;
            updatePaginationInfo();
            renderEmptyList();
        }
    }
    
    function renderSiteList() {
        const tbody = document.getElementById('site-list-tbody');
        if (!tbody) return;
        
        tbody.innerHTML = '';
        
        if (!siteList || siteList.length === 0) {
            renderEmptyList();
            return;
        }
        
        siteList.forEach((site, index) => {
            const status = refreshStatuses[site.id] || {
                status: 'pending',
                last_refresh: null,
                files_count: 0
            };
            
            const row = document.createElement('tr');
            row.className = index % 2 === 0 ? 'bg-white hover:bg-gray-50' : 'bg-gray-50 hover:bg-gray-100';
            row.innerHTML = `
                <td class="px-4 py-3">
                    <input type="checkbox" class="site-checkbox w-4 h-4 text-blue-600 border-gray-300 rounded" 
                           value="${site.id}" data-domain="${site.domain}" data-status="${site.status}">
                </td>
                <td class="px-4 py-3">
                    <div>
                        <div class="flex items-center">
                            <span class="text-sm font-medium text-gray-900">#${site.id}</span>
                            <span class="mx-2 text-gray-300">|</span>
                            <span class="text-sm text-gray-700">${site.domain}</span>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-folder mr-1"></i>${status.files_count || 0} 个文件
                        </div>
                    </div>
                </td>
                <td class="px-4 py-3">
                    ${getSiteStatusBadge(site.status)}
                </td>
                <td class="px-4 py-3">
                    ${getRefreshStatusBadge(status.status)}
                </td>
                <td class="px-4 py-3">
                    <div class="text-sm text-gray-500">
                        ${status.last_refresh ? formatTime(status.last_refresh) : '<span class="text-gray-400">从未刷新</span>'}
                    </div>
                </td>
                <td class="px-4 py-3 text-center">
                    <button data-site-id="${site.id}" data-domain="${site.domain}" 
                            class="refresh-single-btn inline-flex items-center px-3 py-1 border border-blue-300 text-sm leading-4 font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition"
                            ${isRefreshing ? 'disabled' : ''}>
                        <i class="fas fa-sync-alt mr-1"></i>刷新
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
        
        // 重新绑定单个刷新按钮事件
        document.querySelectorAll('.refresh-single-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const siteId = parseInt(this.dataset.siteId);
                const domain = this.dataset.domain;
                refreshSingleSite(siteId, domain);
            });
        });
    }
    
    function renderEmptyList() {
        const tbody = document.getElementById('site-list-tbody');
        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="px-4 py-12 text-center">
                    <div class="flex flex-col items-center">
                        <i class="fas fa-inbox text-5xl text-gray-300 mb-3"></i>
                        <p class="text-gray-500 text-sm">暂无站点数据</p>
                        <button onclick="location.href='/admin/sites'" class="mt-3 text-blue-600 hover:text-blue-800 text-sm">
                            前往添加站点 →
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
    
    // 更新分页信息显示
    function updatePaginationInfo() {
        // 更新显示信息
        const pageStart = (currentPage - 1) * pageSize + 1;
        const pageEnd = Math.min(currentPage * pageSize, totalCount);
        
        document.getElementById('page-start').textContent = totalCount > 0 ? pageStart : 0;
        document.getElementById('page-end').textContent = pageEnd;
        document.getElementById('total-count').textContent = totalCount;
        
        // 更新分页按钮状态
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        
        prevBtn.disabled = currentPage <= 1;
        nextBtn.disabled = currentPage >= totalPages;
        
        // 生成页码按钮
        const pageNumbers = document.getElementById('page-numbers');
        pageNumbers.innerHTML = '';
        
        // 计算显示的页码范围
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);
        
        // 如果总页数较少，显示所有页码
        if (totalPages <= 5) {
            startPage = 1;
            endPage = totalPages;
        }
        
        // 添加首页
        if (startPage > 1) {
            pageNumbers.appendChild(createPageButton(1));
            if (startPage > 2) {
                const dots = document.createElement('span');
                dots.className = 'px-2 text-gray-500';
                dots.textContent = '...';
                pageNumbers.appendChild(dots);
            }
        }
        
        // 添加中间页码
        for (let i = startPage; i <= endPage; i++) {
            pageNumbers.appendChild(createPageButton(i));
        }
        
        // 添加末页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                const dots = document.createElement('span');
                dots.className = 'px-2 text-gray-500';
                dots.textContent = '...';
                pageNumbers.appendChild(dots);
            }
            pageNumbers.appendChild(createPageButton(totalPages));
        }
    }
    
    // 创建页码按钮
    function createPageButton(page) {
        const btn = document.createElement('button');
        btn.className = page === currentPage 
            ? 'px-3 py-1 text-sm bg-blue-500 text-white rounded-md'
            : 'px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50';
        btn.textContent = page;
        btn.onclick = () => {
            if (page !== currentPage) {
                loadSiteList(page);
            }
        };
        return btn;
    }
    
    async function refreshAllSites() {
        if (isRefreshing) {
            showToast('正在刷新中，请稍候...', 'warning');
            return;
        }
        
        if (!confirm('确定要刷新所有站点的静态资源吗？这可能需要一些时间。')) {
            return;
        }
        
        isRefreshing = true;
        addLog('开始刷新所有站点...');
        showProgress(0, siteList.length);
        
        try {
            const res = await fetch('/api/v1/static-refresh/all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin'
            });
            
            const data = await res.json();
            
            if (data.success) {
                showToast('已开始刷新所有站点', 'success');
                addLog('所有站点刷新任务已提交', 'success');
                startPollingStatus();
            } else {
                showToast(data.error || '刷新失败', 'error');
                addLog('刷新失败: ' + (data.error || '未知错误'), 'error');
                isRefreshing = false;
                hideProgress();
            }
        } catch (error) {
            showToast('刷新失败: ' + error.message, 'error');
            addLog('刷新失败: ' + error.message, 'error');
            isRefreshing = false;
            hideProgress();
        }
    }
    
    async function refreshSelectedSites() {
        if (isRefreshing) {
            showToast('正在刷新中，请稍候...', 'warning');
            return;
        }
        
        const selectedSites = [];
        document.querySelectorAll('.site-checkbox:checked').forEach(cb => {
            selectedSites.push(parseInt(cb.value));
        });
        
        if (selectedSites.length === 0) {
            showToast('请先选择要刷新的站点', 'warning');
            return;
        }
        
        if (!confirm(`确定要刷新选中的 ${selectedSites.length} 个站点吗？`)) {
            return;
        }
        
        isRefreshing = true;
        addLog(`开始刷新 ${selectedSites.length} 个选中的站点`);
        showProgress(0, selectedSites.length);
        
        try {
            const res = await fetch('/api/v1/static-refresh/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin',
                body: JSON.stringify({ site_ids: selectedSites })
            });
            
            const data = await res.json();
            
            if (data.success) {
                showToast(`已开始刷新 ${selectedSites.length} 个站点`, 'success');
                addLog(`${selectedSites.length} 个站点刷新任务已提交`, 'success');
                startPollingStatus();
            } else {
                showToast(data.error || '刷新失败', 'error');
                addLog('刷新失败: ' + (data.error || '未知错误'), 'error');
                isRefreshing = false;
                hideProgress();
            }
        } catch (error) {
            showToast('刷新失败: ' + error.message, 'error');
            addLog('刷新失败: ' + error.message, 'error');
            isRefreshing = false;
            hideProgress();
        }
    }
    
    async function refreshSingleSite(siteId, domain) {
        if (isRefreshing) {
            showToast('正在刷新中，请稍候...', 'warning');
            return;
        }
        
        if (!confirm(`确定要刷新站点 ${domain} 的静态资源吗？`)) {
            return;
        }
        
        isRefreshing = true;
        addLog(`开始刷新站点: ${domain}`);
        updateSiteStatus(siteId, 'refreshing');
        
        try {
            const res = await fetch(`/api/v1/static-refresh/site/${siteId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin'
            });
            
            const data = await res.json();
            
            if (data.success) {
                showToast(`站点 ${domain} 刷新成功`, 'success');
                addLog(`站点 ${domain} 刷新成功，共更新 ${data.data.files_count || 0} 个文件`, 'success');
                refreshStatuses[siteId] = data.data;
                renderSiteList();
            } else {
                showToast(`站点 ${domain} 刷新失败`, 'error');
                addLog(`站点 ${domain} 刷新失败: ${data.error || '未知错误'}`, 'error');
                updateSiteStatus(siteId, 'failed');
            }
        } catch (error) {
            showToast('刷新失败: ' + error.message, 'error');
            addLog(`站点 ${domain} 刷新失败: ${error.message}`, 'error');
            updateSiteStatus(siteId, 'failed');
        } finally {
            isRefreshing = false;
        }
    }
    
    function startPollingStatus() {
        if (pollingTimer) {
            clearInterval(pollingTimer);
        }
        
        let pollCount = 0;
        pollingTimer = setInterval(async () => {
            try {
                const res = await fetch('/api/v1/static-refresh/status', {
                    credentials: 'same-origin'
                });
                const data = await res.json();
                
                if (data.success) {
                    refreshStatuses = data.data || {};
                    renderSiteList();
                    
                    const statuses = Object.values(refreshStatuses);
                    const completed = statuses.filter(s => s.status === 'completed').length;
                    const failed = statuses.filter(s => s.status === 'failed').length;
                    const refreshing = statuses.filter(s => s.status === 'refreshing').length;
                    const total = statuses.length;
                    
                    showProgress(completed + failed, total);
                    
                    if (refreshing === 0 && pollCount > 2) {
                        clearInterval(pollingTimer);
                        pollingTimer = null;
                        isRefreshing = false;
                        addLog(`刷新完成: ${completed} 成功, ${failed} 失败`, completed > 0 ? 'success' : 'warning');
                        hideProgress();
                    }
                }
            } catch (error) {
                console.error('轮询状态失败:', error);
            }
            
            pollCount++;
            
            if (pollCount > 60) {
                clearInterval(pollingTimer);
                pollingTimer = null;
                isRefreshing = false;
                addLog('轮询超时，请手动刷新列表查看状态', 'warning');
                hideProgress();
            }
        }, 2000);
    }
    
    // ==================== 事件绑定 ====================
    
    function bindEvents() {
        // 清空统计代码
        document.getElementById('clear-analytics-btn').addEventListener('click', function() {
            if (confirm('确定要清空统计代码吗？')) {
                document.getElementById('analytics-code').value = '';
            }
        });
        
        // 测试统计代码
        document.getElementById('test-analytics-btn').addEventListener('click', function() {
            const code = document.getElementById('analytics-code').value;
            if (!code) {
                showToast('请先输入统计代码', 'warning');
                return;
            }
            
            if (code.includes('<script') || code.includes('gtag') || code.includes('analytics')) {
                showToast('统计代码格式正确', 'success');
            } else {
                showToast('请检查统计代码格式', 'warning');
            }
        });
        
        // 刷新统计JS
        document.getElementById('refresh-analytics-js-btn').addEventListener('click', async function() {
            try {
                const btn = this;
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>刷新中...';
                btn.disabled = true;
                
                const response = await fetch('/api/v1/analytics/refresh', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showToast('统计JS刷新成功！', 'success');
                } else {
                    showToast(data.error || '刷新失败', 'error');
                }
            } catch (error) {
                console.error('刷新统计JS失败:', error);
                showToast('刷新失败: ' + error.message, 'error');
            } finally {
                const btn = this;
                btn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>刷新统计JS';
                btn.disabled = false;
            }
        });
        
        // 刷新所有
        document.getElementById('refresh-all-btn').addEventListener('click', refreshAllSites);
        
        // 刷新选中
        document.getElementById('refresh-selected-btn').addEventListener('click', refreshSelectedSites);
        
        // 刷新列表
        document.getElementById('reload-list-btn').addEventListener('click', loadSiteList);
        
        // 清除选择
        document.getElementById('clear-selection-btn').addEventListener('click', function() {
            document.getElementById('select-all').checked = false;
            document.getElementById('auto-select-active').checked = false;
            document.querySelectorAll('.site-checkbox').forEach(cb => cb.checked = false);
        });
        
        // 全选
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.site-checkbox');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });
        
        // 自动选择活跃站点
        document.getElementById('auto-select-active').addEventListener('change', function() {
            const checked = this.checked;
            document.querySelectorAll('.site-checkbox').forEach((cb, index) => {
                if (siteList[index] && siteList[index].status === 'active') {
                    cb.checked = checked;
                } else if (checked) {
                    cb.checked = false;
                }
            });
        });
        
        // 清空日志
        document.getElementById('clear-logs-btn').addEventListener('click', function() {
            const logDiv = document.getElementById('refresh-log');
            logDiv.innerHTML = '<div class="text-gray-400">等待操作...</div>';
        });
        
        // 分页按钮事件
        document.getElementById('prev-page').addEventListener('click', function() {
            if (currentPage > 1) {
                loadSiteList(currentPage - 1);
            }
        });
        
        document.getElementById('next-page').addEventListener('click', function() {
            if (currentPage < totalPages) {
                loadSiteList(currentPage + 1);
            }
        });
    }
    
    // ==================== 初始化 ====================
    
    function initStatsPage() {
        console.log('初始化stats页面');
        loadAnalyticsSettings();
        loadSiteList();
        bindEvents();
    }
    
    // 暴露必要的函数到全局
    window.initStatsPage = initStatsPage;
    window.saveAnalytics = saveAnalytics;
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initStatsPage);
    } else {
        initStatsPage();
    }
})();
</script>

<style>
/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-out {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.animate-slide-in {
    animation: slide-in 0.3s ease-out;
}

.animate-slide-out {
    animation: slide-out 0.3s ease-out;
}

/* 滚动条样式 */
#refresh-log::-webkit-scrollbar {
    width: 6px;
}

#refresh-log::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
}

#refresh-log::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

#refresh-log::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}
</style>