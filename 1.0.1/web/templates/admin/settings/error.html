<div class="settings-panel p-6" id="error-pages-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">错误页面设置</h3>
                        
                        <!-- 说明信息 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
                            <p class="text-sm font-medium text-blue-800 mb-1.5">
                                <i class="fas fa-info-circle mr-1 text-xs"></i>错误页面说明
                            </p>
                            <ul class="space-y-0.5 text-xs text-blue-700">
                                <li class="flex items-start">
                                    <span class="mr-1 text-xs">•</span>
                                    <span>站点未配置页面：当访问未在系统中配置的域名时显示</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="mr-1 text-xs">•</span>
                                    <span>404页面：当目标站点返回404时显示，并会缓存该状态</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="mr-1 text-xs">•</span>
                                    <span>支持自定义HTML，可包含CSS和JavaScript</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="mr-1 text-xs">•</span>
                                    <span>留空则使用系统默认页面</span>
                                </li>
                            </ul>
                        </div>
                        
                        <!-- 左右布局容器 -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 左侧：404页面设置 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2 text-orange-500"></i>
                                    404页面设置
                                </h4>
                                
                                <div class="space-y-4">
                                    <!-- 启用404缓存 -->
                                    <div>
                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" id="enable-404-cache" class="mr-2">
                                            <span class="text-sm font-medium text-gray-700">启用404缓存</span>
                                        </label>
                                        <p class="text-xs text-gray-600 mt-1 ml-6">缓存404状态，避免重复请求</p>
                                    </div>
                                    
                                    <!-- 缓存时间 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">缓存时间（秒）</label>
                                        <input type="number" id="cache-404-ttl" value="86400" min="60" max="604800"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <p class="mt-1 text-xs text-gray-500">默认24小时（86400秒）</p>
                                    </div>
                                    
                                    <!-- 自定义404页面 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">自定义404页面</label>
                                        <textarea id="default-404-html" rows="12" 
                                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs"
                                                  placeholder="<!DOCTYPE html>
<html>
<head>
    <title>404 Not Found</title>
</head>
<body>
    <h1>页面未找到</h1>
</body>
</html>"></textarea>
                                        <p class="mt-1 text-xs text-gray-500">目标站点返回404时显示</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 右侧：站点未配置页面 -->
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="font-medium text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-globe mr-2 text-blue-500"></i>
                                    站点未配置页面
                                </h4>
                                
                                <div class="space-y-4">
                                    <!-- 自定义页面 -->
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">自定义页面</label>
                                        <textarea id="site-not-found-html" rows="18" 
                                                  class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs"
                                                  placeholder="<!DOCTYPE html>
<html>
<head>
    <title>站点未配置</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            text-align: center; 
            padding: 50px; 
        }
    </style>
</head>
<body>
    <h1>站点未配置</h1>
    <p>该域名尚未在系统中配置</p>
</body>
</html>"></textarea>
                                        <p class="mt-1 text-xs text-gray-500">未配置域名访问时显示</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="flex justify-center space-x-4 mt-6">
                            <button onclick="previewErrorPages()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center text-sm">
                                <i class="fas fa-eye mr-2"></i>
                                预览页面
                            </button>
                            <button onclick="resetErrorPages()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg flex items-center text-sm">
                                <i class="fas fa-undo mr-2"></i>
                                恢复默认
                            </button>
                        </div>
                    </div>

<script>
// 预览错误页面
function previewErrorPages() {
    // 创建预览菜单
    const choice = confirm('点击"确定"预览404页面，点击"取消"预览站点未配置页面');
    
    if (choice) {
        // 预览404页面
        const html = document.getElementById('default-404-html').value || getDefault404HTML();
        const previewWindow = window.open('', '_blank');
        previewWindow.document.write(html);
        previewWindow.document.close();
    } else {
        // 预览站点未配置页面
        const html = document.getElementById('site-not-found-html').value || getDefaultSiteNotFoundHTML();
        const previewWindow = window.open('', '_blank');
        previewWindow.document.write(html);
        previewWindow.document.close();
    }
}

// 恢复默认设置
function resetErrorPages() {
    if (confirm('确定要恢复所有错误页面的默认设置吗？')) {
        document.getElementById('site-not-found-html').value = '';
        document.getElementById('default-404-html').value = '';
        document.getElementById('enable-404-cache').checked = true;
        document.getElementById('cache-404-ttl').value = '86400';
        if (window.showNotification) {
            window.showNotification('已恢复默认设置', 'success');
        }
    }
}


// 获取默认的404页面 HTML
function getDefault404HTML() {
    return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>404 - 页面未找到</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            text-align: center;
            color: white;
            padding: 40px;
        }
        h1 { 
            font-size: 120px;
            margin: 0;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        h2 {
            font-size: 24px;
            margin: 20px 0;
            font-weight: normal;
        }
        p { 
            font-size: 16px;
            opacity: 0.9;
        }
        a {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 30px;
            background: white;
            color: #667eea;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 500;
            transition: transform 0.2s;
        }
        a:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>404</h1>
        <h2>页面未找到</h2>
        <p>抱歉，您访问的页面不存在或已被移除。</p>
        <a href="/">返回首页</a>
    </div>
</body>
</html>`;
}

// 获取默认的站点未配置 HTML
function getDefaultSiteNotFoundHTML() {
    return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>站点未配置</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
            height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            background: white;
            padding: 60px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            text-align: center;
            max-width: 500px;
        }
        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }
        h1 { 
            color: #333;
            margin: 0 0 20px;
            font-size: 28px;
        }
        p { 
            color: #666;
            line-height: 1.6;
            margin: 10px 0;
        }
        .admin-link {
            display: inline-block;
            margin-top: 30px;
            padding: 12px 30px;
            background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 500;
            transition: transform 0.2s;
        }
        .admin-link:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🌐</div>
        <h1>站点未配置</h1>
        <p>该域名尚未在系统中配置。</p>
        <p>如果您是网站管理员，请登录后台添加此站点。</p>
        <a href="/admin" class="admin-link">进入管理后台</a>
    </div>
</body>
</html>`;
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载已保存的设置（如果有）
    if (typeof loadErrorSettings === 'function') {
        loadErrorSettings();
    }
});
</script>