<div class="settings-panel p-6" id="basic-panel">
    <h3 class="text-lg font-semibold text-gray-800 mb-6">基础设置</h3>
    
    <!-- 日志设置区域 -->
    <div class="mb-8 bg-white rounded-lg border border-gray-200 p-6">
        <h4 class="text-md font-semibold text-gray-700 mb-4 flex items-center">
            <i class="fas fa-file-alt mr-2 text-green-500"></i>
            日志设置
        </h4>
        <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">日志级别</label>
                    <select id="log_level" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="debug">Debug - 详细调试信息</option>
                        <option value="info" selected>Info - 常规信息</option>
                        <option value="warn">Warn - 警告信息</option>
                        <option value="error">Error - 仅错误信息</option>
                    </select>
                    <p class="mt-1 text-sm text-gray-600">控制日志输出的详细程度</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">日志存储方式</label>
                    <select id="log_storage" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="file" selected>文件</option>
                        <option value="database">数据库</option>
                        <option value="both">文件和数据库</option>
                    </select>
                    <p class="mt-1 text-sm text-gray-600">选择日志的存储位置</p>
                </div>
            </div>
            
            <div class="grid grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">日志保留天数</label>
                    <input type="number" id="log_retention_days" value="7" min="1" max="365"
                           class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="mt-1 text-xs text-gray-600">自动清理超期日志</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">单文件大小(MB)</label>
                    <input type="number" id="log_max_size" value="100" min="10" max="1000"
                           class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="mt-1 text-xs text-gray-600">超过大小自动轮转</p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">最大备份文件数</label>
                    <input type="number" id="log_max_backups" value="10" min="1" max="100"
                           class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="mt-1 text-xs text-gray-600">保留的备份数量</p>
                </div>
            </div>
            
            <div class="grid grid-cols-3 gap-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">启用日志记录</label>
                        <p class="text-xs text-gray-600 mt-1">关闭后不记录日志</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="log_enabled" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">记录访问日志</label>
                        <p class="text-xs text-gray-600 mt-1">记录HTTP请求</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="log_access_enabled" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">记录错误日志</label>
                        <p class="text-xs text-gray-600 mt-1">记录系统异常</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="log_error_enabled" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 验证码设置区域 -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <h4 class="text-md font-semibold text-gray-700 mb-4 flex items-center">
            <i class="fas fa-shield-alt mr-2 text-purple-500"></i>
            验证码设置
        </h4>
        <div class="space-y-4">
            <!-- 验证码开关 -->
            <div class="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
                <div>
                    <label class="block text-sm font-medium text-gray-700">启用登录验证码</label>
                    <p class="text-xs text-gray-600 mt-1">开启后登录时需要输入图形验证码，可有效防止暴力破解</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="enable-captcha" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
            </div>
            
            <!-- 验证码配置 -->
            <div id="captcha-config" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">验证码长度</label>
                        <input type="number" id="captcha-length" value="4" min="3" max="8"
                               class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="mt-1 text-sm text-gray-600">验证码字符数量，建议4-6位</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">验证码有效期</label>
                        <select id="captcha-expiry" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="60">1分钟</option>
                            <option value="180">3分钟</option>
                            <option value="300" selected>5分钟</option>
                            <option value="600">10分钟</option>
                            <option value="900">15分钟</option>
                            <option value="1800">30分钟</option>
                        </select>
                        <p class="mt-1 text-sm text-gray-600">验证码过期后需要重新获取</p>
                    </div>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-info-circle mr-2"></i>
                        验证码配置修改后立即生效，无需重启服务。建议在发现异常登录尝试时开启验证码功能。
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- CSRF设置区域 -->
    <div class="mt-8 bg-white rounded-lg border border-gray-200 p-6">
        <h4 class="text-md font-semibold text-gray-700 mb-4 flex items-center">
            <i class="fas fa-lock mr-2 text-indigo-500"></i>
            CSRF防护设置
        </h4>
        <div class="space-y-4">
            <!-- CSRF开关 -->
            <div class="flex items-center justify-between p-4 bg-indigo-50 rounded-lg">
                <div>
                    <label class="block text-sm font-medium text-gray-700">启用CSRF防护</label>
                    <p class="text-xs text-gray-600 mt-1">开启后所有POST请求需要携带CSRF令牌，可防止跨站请求伪造攻击</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="enable-csrf" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
            </div>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p class="text-sm text-yellow-800">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>注意：</strong>修改CSRF设置需要重启服务才能生效。开启CSRF后，请确保所有表单和AJAX请求都包含CSRF令牌。
                </p>
            </div>
        </div>
    </div>
</div>

<script>
// 切换验证码配置显示
function toggleCaptchaConfig() {
    const isEnabled = document.getElementById('enable-captcha').checked;
    const configDiv = document.getElementById('captcha-config');
    if (configDiv) {
        if (isEnabled) {
            configDiv.classList.remove('opacity-50', 'pointer-events-none');
        } else {
            configDiv.classList.add('opacity-50', 'pointer-events-none');
        }
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    const captchaToggle = document.getElementById('enable-captcha');
    if (captchaToggle) {
        captchaToggle.addEventListener('change', toggleCaptchaConfig);
        // 初始化验证码配置显示状态
        toggleCaptchaConfig();
    }
});
</script>