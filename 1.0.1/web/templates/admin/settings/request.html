<div class="settings-panel p-6" id="request-panel">
    <h3 class="text-lg font-semibold text-gray-800 mb-6">请求设置</h3>
    <div class="space-y-6">
        <!-- 注意提示 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p class="text-sm text-blue-800">
                <i class="fas fa-info-circle mr-1"></i>
                <strong>说明：</strong>HTTP并发数和超时设置已移至「资源限流」页面统一管理
            </p>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">代理服务器地址</label>
            <input type="text" id="proxy-server-url" 
                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                   placeholder="http://proxy.example.com:8080">
            <p class="mt-1 text-sm text-gray-600">留空表示不使用代理</p>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">请求重试次数</label>
            <input type="number" id="request-retry-count" value="3" min="0" max="10"
                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <p class="mt-1 text-sm text-gray-600">请求失败时的自动重试次数</p>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">User-Agent列表（随机使用）</label>
            <textarea id="user-agent-list" rows="8"
                      class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                      placeholder="每行一个User-Agent，访问时随机选择使用">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0
Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15
Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36</textarea>
            <p class="mt-1 text-sm text-gray-600">每行输入一个User-Agent，系统会在请求时随机选择使用</p>
        </div>
    </div>
</div>