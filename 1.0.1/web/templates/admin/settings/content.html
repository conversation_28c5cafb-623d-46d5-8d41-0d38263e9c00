<!-- 拼音标注特殊符管理 -->
<div class="space-y-6">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">
        <i class="fas fa-asterisk mr-2"></i>拼音标注特殊符管理
    </h2>
    
    <!-- 全局特殊字符配置 -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-800 mb-4">
            全局配置
        </h3>
        
        <div class="space-y-4">
            <!-- 全局拼音转换开关 -->
            <div>
                <label class="flex items-center space-x-3">
                    <input type="checkbox" 
                           id="enable_global_pinyin" 
                           name="enable_global_pinyin"
                           class="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                    <span class="text-sm font-medium text-gray-700">启用全局拼音转换</span>
                </label>
                <p class="text-xs text-gray-500 mt-2 ml-8">开启后，选择"使用全局设置"的站点将自动应用拼音标注功能</p>
            </div>
            <!-- 特殊字符列表 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">特殊字符列表（用|分隔）</label>
                <textarea id="pinyin_special_chars" name="pinyin_special_chars" 
                        rows="3" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="示例：…|⁖|⸪|⸬|⸫">…|⁖|⸪|⸬|⸫</textarea>
                <p class="text-xs text-gray-500 mt-2">这些特殊字符将在拼音标注后随机插入，用于增强内容的独特性</p>
            </div>
            
            <!-- 全局插入比例 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">默认插入比例</label>
                <div class="flex items-center space-x-4">
                    <input type="range" 
                           id="pinyin_special_chars_ratio" 
                           name="pinyin_special_chars_ratio"
                           min="0" 
                           max="100" 
                           value="30"
                           class="flex-1"
                           oninput="document.getElementById('ratio_display').textContent = this.value">
                    <span class="text-sm font-medium text-gray-700 w-16">
                        <span id="ratio_display">30</span>%
                    </span>
                </div>
                <p class="text-xs text-gray-500 mt-2">设置特殊字符的默认插入概率（站点可以独立调整）</p>
            </div>
            
            <!-- 说明信息 -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                <div class="flex">
                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                    <div class="text-sm text-gray-700">
                        <p class="font-medium mb-2">使用说明：</p>
                        <ul class="list-disc list-inside space-y-1 text-gray-600">
                            <li>这里配置的特殊字符列表将被所有站点共享</li>
                            <li>默认插入比例为新建站点提供初始值</li>
                            <li>站点可以在各自的设置中选择是否启用拼音标注</li>
                            <li>站点可以选择是否插入特殊字符</li>
                            <li>站点可以独立调整插入比例（覆盖全局默认值）</li>
                            <li>特殊字符会随机插入到拼音标注的 &lt;/rt&gt; 标签后</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 示例展示 -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <p class="text-sm font-medium text-gray-700 mb-2">效果示例：</p>
                <div class="text-sm text-gray-600 space-y-1">
                    <p><strong>原文：</strong>春天来了</p>
                    <p><strong>拼音标注：</strong><ruby>春<rt>chūn</rt></ruby><ruby>天<rt>tiān</rt></ruby><ruby>来<rt>lái</rt></ruby><ruby>了<rt>le</rt></ruby></p>
                    <p><strong>插入特殊字符后：</strong><ruby>春<rt>chūn</rt></ruby>⁖<ruby>天<rt>tiān</rt></ruby><ruby>来<rt>lái</rt></ruby>⸪<ruby>了<rt>le</rt></ruby></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 加载拼音特殊字符设置
function loadContentSettings() {
    if (typeof settingsData !== 'undefined' && settingsData) {
        // 加载全局拼音开关
        if (settingsData.enable_global_pinyin !== undefined) {
            document.getElementById('enable_global_pinyin').checked = settingsData.enable_global_pinyin;
        }
        if (settingsData.pinyin_special_chars) {
            document.getElementById('pinyin_special_chars').value = settingsData.pinyin_special_chars;
        }
        if (settingsData.pinyin_special_chars_ratio !== undefined) {
            const ratio = Math.round(settingsData.pinyin_special_chars_ratio * 100);
            document.getElementById('pinyin_special_chars_ratio').value = ratio;
            document.getElementById('ratio_display').textContent = ratio;
        }
    }
}

// 收集拼音特殊字符设置数据（供settings.html主页面的统一保存按钮调用）
function collectContentSettings() {
    return {
        enable_global_pinyin: document.getElementById('enable_global_pinyin').checked,
        pinyin_special_chars: document.getElementById('pinyin_special_chars').value.trim() || '…|⁖|⸪|⸬|⸫',
        pinyin_special_chars_ratio: parseFloat(document.getElementById('pinyin_special_chars_ratio').value) / 100
    };
}

// 页面加载后调用
setTimeout(loadContentSettings, 100);
</script>