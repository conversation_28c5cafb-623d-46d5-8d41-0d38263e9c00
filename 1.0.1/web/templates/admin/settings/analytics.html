<!-- 统计代码设置页面 -->
<div id="analytics-settings" class="bg-white rounded-lg shadow-md p-6">
    <h2 class="text-xl font-semibold mb-4">统计代码设置</h2>
    
    <div class="space-y-4">
        <!-- 启用统计 -->
        <div class="flex items-center space-x-3">
            <input type="checkbox" id="analytics_enabled" class="rounded border-gray-300">
            <label for="analytics_enabled" class="text-sm font-medium text-gray-700">
                启用网站统计功能
            </label>
        </div>

        <!-- 统计代码输入 -->
        <div>
            <label for="analytics_script" class="block text-sm font-medium text-gray-700 mb-2">
                统计代码 (HTML/JavaScript)
            </label>
            <textarea id="analytics_script" rows="10" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm font-mono"
                placeholder="<!-- 在此粘贴您的统计代码，例如 Google Analytics、百度统计等 -->"></textarea>
            <p class="mt-1 text-sm text-gray-500">
                支持 Google Analytics、百度统计、CNZZ 等各种统计代码。代码将被插入到所有镜像页面的 &lt;/body&gt; 标签前。
            </p>
        </div>

        <!-- 生成静态文件说明 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <svg class="h-5 w-5 text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">关于静态文件生成</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>保存后系统会自动生成静态JS文件</li>
                            <li>文件名格式：<code class="bg-blue-100 px-1 rounded">/[hash].js?t=[timestamp]</code></li>
                            <li>如需刷新已缓存页面中的统计代码，请使用<a href="/admin/settings/refresh" class="text-blue-600 hover:text-blue-800 underline">静态资源刷新</a>功能</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 加载统计设置
async function loadAnalyticsSettings() {
    try {
        const response = await fetch('/api/v1/system/settings', {
            credentials: 'same-origin'
        });
        
        if (!response.ok) {
            throw new Error('获取设置失败');
        }
        
        const result = await response.json();
        if (result.success && result.data) {
            document.getElementById('analytics_enabled').checked = result.data.analytics_enabled || false;
            document.getElementById('analytics_script').value = result.data.analytics_script || '';
        }
    } catch (error) {
        console.error('加载统计设置失败:', error);
        showNotification('加载统计设置失败: ' + error.message, 'error');
    }
}

// 保存统计设置 - 暴露给父页面调用
window.saveAnalytics = async function() {
    const enabled = document.getElementById('analytics_enabled').checked;
    const script = document.getElementById('analytics_script').value;
    
    try {
        const response = await fetch('/api/v1/system/settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin',
            body: JSON.stringify({
                analytics_enabled: enabled,
                analytics_script: script
            })
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '保存失败');
        }
        
        const result = await response.json();
        if (result.success) {
            showNotification('统计代码设置已保存', 'success');
            return true;
        } else {
            throw new Error(result.error || '保存失败');
        }
    } catch (error) {
        console.error('保存统计设置失败:', error);
        showNotification('保存失败: ' + error.message, 'error');
        return false;
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadAnalyticsSettings();
});
</script>