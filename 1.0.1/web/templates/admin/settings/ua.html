<div class="settings-panel p-6" id="ua-check-panel">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">全局UA判断默认设置</h3>
                        
                        <!-- 紧凑的提示信息 -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2.5 mb-4 flex items-start">
                            <i class="fas fa-info-circle text-yellow-600 mt-0.5 mr-2"></i>
                            <p class="text-xs text-yellow-800">
                                这里是全局默认设置，新建站点时会使用这些默认值。每个站点可以在"站点管理"中单独配置UA判断规则。
                            </p>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- 开关设置 - 使用更紧凑的布局 -->
                            <div class="grid grid-cols-2 gap-4">
                                <!-- 全局UA判断开关 -->
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-800">启用全局UA判断</h4>
                                            <p class="text-xs text-gray-600 mt-0.5">所有站点将默认使用UA判断规则（站点可单独设置）</p>
                                        </div>
                                        <label class="switch switch-sm">
                                            <input type="checkbox" id="enable_global_ua_check">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- 域名跳转设置 -->
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-800">全局 @ 跳转到 www</h4>
                                            <p class="text-xs text-gray-600 mt-0.5">所有根域名访问将自动跳转到 www 子域名</p>
                                        </div>
                                        <label class="switch switch-sm">
                                            <input type="checkbox" id="global_redirect_www">
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- UA判断规则说明 - 更紧凑的版本 -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                                    <div class="flex-1">
                                        <p class="text-xs font-medium text-blue-800 mb-1">UA判断规则说明：</p>
                                        <ul class="text-xs text-blue-700 space-y-0.5">
                                            <li>• 全局开关关闭时，不进行UA判断</li>
                                            <li>• 全局开关开启且设置了关键词后，只有匹配的UA才能访问正常内容</li>
                                            <li>• 未匹配的访问将显示自定义页面</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">允许的UA关键词</label>
                                <textarea id="default_allowed_ua" rows="4" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="输入允许访问的UA关键词，用 | 分隔，例如：
googlebot|baiduspider|bingbot|yandexbot
留空则不进行UA判断（允许所有访问）"></textarea>
                                <p class="mt-1 text-sm text-gray-600">设置允许访问的User-Agent关键词，多个关键词用 | 分隔，不区分大小写。<strong>留空表示不进行UA判断</strong></p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">非爬虫访问页面</label>
                                
                                <!-- 标签说明 -->
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                                    <h5 class="font-semibold text-blue-900 mb-2">
                                        <i class="fas fa-tags mr-1"></i>支持的自定义标签
                                    </h5>
                                    <div class="grid grid-cols-2 gap-2 text-xs">
                                        <div class="space-y-1">
                                            <div><code class="bg-white px-1 py-0.5 rounded">{title}</code> - 站点首页标题</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{description}</code> - 站点首页描述</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{keywords}</code> - 站点首页关键词</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{analytics}</code> - 统计JS引用</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{company}</code> - 企业名称</div>
                                        </div>
                                        <div class="space-y-1">
                                            <div><code class="bg-white px-1 py-0.5 rounded">{domain}</code> - 当前域名</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{year}</code> - 当前年份</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{date}</code> - 当前日期</div>
                                            <div><code class="bg-white px-1 py-0.5 rounded">{time}</code> - 当前时间</div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-xs text-blue-700">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        提示：{analytics}会自动生成统计JS文件引用，如：&lt;script src="/xxxxx.js"&gt;&lt;/script&gt;
                                    </div>
                                </div>
                                
                                <textarea id="default_non_spider_html" rows="15" 
                                          class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                                          placeholder="在此输入自定义HTML模板..."></textarea>
                                <p class="mt-1 text-sm text-gray-600">当UA不匹配时显示的HTML页面（仅在设置了UA关键词时生效）</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 缓存设置面板 -->