<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        /* 开关按钮样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
        }
        
        input:checked + .slider {
            background-color: #3B82F6;
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        .slider.round {
            border-radius: 24px;
        }
        
        .slider.round:before {
            border-radius: 50%;
        }
        
        /* 加载动画 */
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-radius: 50%;
            border-top: 3px solid #3B82F6;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 确保保存按钮容器始终可见 */
        .settings-panel-footer {
            background-color: white;
            border-top: 1px solid #e5e7eb;
            box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
        }
        
        /* 面板容器样式 */
        .settings-panel {
            min-height: 100px;
        }
        
        /* 确保主容器使用flex布局 */
        .main-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
            <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">系统设置</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-600" id="system-time"></span>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8" style="height: calc(100vh - 64px); overflow-y: auto;">
                <!-- 设置面板容器 -->
                <div class="bg-white rounded-lg shadow">
                    <!-- 动态加载内容区域 -->
                    <div id="settings-content" class="p-6">
                        <!-- 加载中提示 -->
                        <div class="flex items-center justify-center h-64">
                            <div class="text-center">
                                <div class="loading-spinner mx-auto mb-4"></div>
                                <p class="text-gray-600">加载中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 保存按钮区域 -->
                    <div class="border-t p-4 bg-gray-50">
                        <div class="flex justify-end space-x-4">
                            <button onclick="resetSettings()" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                                <i class="fas fa-undo mr-2"></i>重置
                            </button>
                            <button onclick="saveSettings()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-save mr-2"></i>保存设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // 当前激活的Tab
        let currentTab = 'basic';
        let settingsData = {};
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL路径或参数获取Tab
            let tab = 'basic';
            const path = window.location.pathname;
            
            if (path.startsWith('/admin/settings/')) {
                // 从路径获取 /admin/settings/basic
                tab = path.split('/').pop() || 'basic';
            } else {
                // 从URL参数获取 ?tab=basic
                const urlParams = new URLSearchParams(window.location.search);
                tab = urlParams.get('tab') || 'basic';
            }
            
            // 加载设置数据
            loadSettings();
            
            // 切换到指定Tab
            switchTab(tab);
            
            // 更新系统时间
            updateSystemTime();
            setInterval(updateSystemTime, 1000);
        });
        
        // 切换Tab（通过左侧菜单）
        async function switchTab(tab) {
            currentTab = tab;
            
            // sitemap使用独立的API，不需要加载系统设置
            if (tab !== 'sitemap') {
                // 每次切换tab时重新加载设置数据
                await loadSettings();
            }
            
            // 加载Tab内容
            loadTabContent(tab);
        }
        
        // 加载Tab内容
        async function loadTabContent(tab) {
            const contentEl = document.getElementById('settings-content');
            
            // stats 页面不需要从partial加载，已经是完整页面
            if (tab === 'stats' || tab === 'analytics') {
                tab = 'stats'; // 统一为stats
            }
            
            // 显示加载中
            contentEl.innerHTML = `
                <div class="flex items-center justify-center h-64">
                    <div class="text-center">
                        <div class="loading-spinner mx-auto mb-4"></div>
                        <p class="text-gray-600">加载中...</p>
                    </div>
                </div>
            `;
            
            try {
                // 加载对应的HTML片段
                const response = await fetch(`/admin/settings/partial/${tab}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const html = await response.text();
                contentEl.innerHTML = html;
                
                // 执行HTML中的script标签
                const scripts = contentEl.querySelectorAll('script');
                scripts.forEach(script => {
                    const newScript = document.createElement('script');
                    if (script.src) {
                        newScript.src = script.src;
                    } else {
                        newScript.textContent = script.textContent;
                    }
                    document.body.appendChild(newScript);
                    // 执行后立即移除，避免重复
                    if (!script.src) {
                        document.body.removeChild(newScript);
                    }
                });
                
                // 应用设置数据到表单
                applySettingsToForm();
                
                // 如果加载的是 stats 页面，初始化其功能
                if (tab === 'stats') {
                    // 等待页面加载完成后初始化
                    setTimeout(() => {
                        // 优先使用initStatsPage函数
                        if (window.initStatsPage) {
                            console.log('调用initStatsPage初始化stats页面');
                            window.initStatsPage();
                        } else {
                            // 兼容旧版本
                            if (window.loadAnalyticsSettings) {
                                window.loadAnalyticsSettings();
                            }
                            if (window.loadSiteList) {
                                window.loadSiteList();
                            }
                        }
                    }, 100);
                }
                
            } catch (error) {
                console.error('加载Tab内容失败:', error);
                contentEl.innerHTML = `
                    <div class="text-center py-12">
                        <i class="fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"></i>
                        <p class="text-gray-600">加载失败，请刷新页面重试</p>
                    </div>
                `;
            }
        }
        
        // 加载设置数据
        async function loadSettings() {
            try {
                const response = await fetch('/api/v1/system/settings', {
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        // 存储整个响应，包括 data 字段
                        settingsData = result.data || result || {};
                    }
                }
            } catch (error) {
                console.error('加载设置失败:', error);
            }
        }
        
        // 应用设置到表单
        async function applySettingsToForm() {
            console.log('应用设置到表单, 当前Tab:', currentTab);
            
            // 统计设置使用单独的API
            if (currentTab === 'stats') {
                await loadAnalytics();
                return;
            }
            
            // 权重监测使用单独的API
            if (currentTab === 'weight') {
                await loadWeightMonitorConfig();
                return;
            }
            
            // 系统信息页面需要加载系统信息
            if (currentTab === 'system') {
                // 延迟执行，确保页面已加载
                setTimeout(() => {
                    if (typeof window.loadSystemInfo === 'function') {
                        window.loadSystemInfo();
                    }
                }, 100);
                return;
            }
            
            console.log('设置数据:', settingsData);
            
            // 获取实际的数据对象
            const data = settingsData.data || settingsData;
            
            // 遍历所有表单元素，自动匹配字段名
            const inputs = document.querySelectorAll('#settings-content input, #settings-content select, #settings-content textarea');
            
            // 先处理radio按钮
            document.querySelectorAll('#settings-content input[type="radio"]').forEach(radio => {
                const name = radio.name;
                if (!name) return;
                
                // 将name转换为下划线格式来匹配API字段名
                const fieldName = name.replace(/-/g, '_');
                
                // 如果数据中存在该字段，且值匹配，则选中
                if (data.hasOwnProperty(fieldName) && data[fieldName] === radio.value) {
                    radio.checked = true;
                    // 触发change事件以更新UI
                    if (fieldName === 'worker_pool_mode') {
                        updateWorkerPoolMode(radio.value);
                    }
                }
            });
            
            inputs.forEach(input => {
                // 跳过radio按钮（已处理）
                if (input.type === 'radio') {
                    return;
                }
                
                const id = input.id;
                if (!id) return;
                
                // 将ID中的横线转换为下划线来匹配API字段名
                const fieldName = id.replace(/-/g, '_');
                
                // 如果数据中存在该字段，则设置值
                if (data.hasOwnProperty(fieldName)) {
                    if (input.type === 'checkbox') {
                        input.checked = data[fieldName] || false;
                    } else if (input.type === 'number') {
                        input.value = data[fieldName] || 0;
                    } else {
                        input.value = data[fieldName] || '';
                    }
                }
            });
        }
        
        // 加载统计设置
        async function loadAnalytics() {
            try {
                const response = await fetch('/api/v1/analytics', {
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                });
                const result = await response.json();
                
                if (result.success && result.data) {
                    const enabledCheckbox = document.getElementById('analytics-enabled');
                    if (enabledCheckbox) enabledCheckbox.checked = result.data.enabled || false;
                    
                    const codeTextarea = document.getElementById('analytics-code');
                    if (codeTextarea) codeTextarea.value = result.data.code || '';
                    
                    const refreshIntervalInput = document.getElementById('analytics-refresh-interval');
                    if (refreshIntervalInput) refreshIntervalInput.value = result.data.refresh_interval || 60;
                }
            } catch (error) {
                console.error('加载统计设置失败:', error);
            }
        }
        
        // 保存设置
        async function saveSettings() {
            // 统计设置使用单独的API
            if (currentTab === 'stats') {
                await saveAnalytics();
                return;
            }
            
            // 权重监测设置使用单独的API
            if (currentTab === 'weight') {
                await saveWeightMonitorConfig();
                return;
            }
            
            // sitemap设置使用单独的API
            if (currentTab === 'sitemap') {
                if (typeof window.saveSitemapSettings === 'function') {
                    await window.saveSitemapSettings();
                    return;
                }
            }
            
            // 其他设置使用系统设置API
            const formData = collectCurrentFormData();
            
            console.log('保存的设置数据:', formData);
            
            try {
                const response = await fetch('/api/v1/system/settings', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('设置保存成功', 'success');
                    // 更新本地缓存的设置数据
                    settingsData = { ...settingsData, ...formData };
                } else {
                    showNotification(result.message || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存设置失败:', error);
                showNotification('保存失败，请重试', 'error');
            }
        }
        
        // 保存统计设置
        async function saveAnalytics() {
            // 如果stats.html中定义了saveAnalytics函数，使用它
            if (window.saveAnalytics && window.saveAnalytics !== saveAnalytics) {
                return await window.saveAnalytics();
            }
            
            // 否则使用默认实现
            const analyticsData = {
                code: document.getElementById('analytics-code')?.value || '',
                enabled: document.getElementById('analytics-enabled')?.checked || false,
                refresh_interval: parseInt(document.getElementById('refresh-interval')?.value) || 60,
                auto_refresh: document.getElementById('auto-refresh')?.checked || false
            };
            
            console.log('保存统计设置:', analyticsData);
            
            try {
                const response = await fetch('/api/v1/analytics', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    },
                    body: JSON.stringify(analyticsData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('统计设置保存成功', 'success');
                } else {
                    showNotification(result.message || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存统计设置失败:', error);
                showNotification('保存失败，请重试', 'error');
            }
        }
        
        // 加载权重监测配置
        async function loadWeightMonitorConfig() {
            try {
                const response = await fetch('/api/v1/weight/config', {
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                });
                
                const result = await response.json();
                
                if (result.success && result.data) {
                    const config = result.data;
                    
                    // 设置表单值
                    const enabledInput = document.getElementById('weight_monitor_enabled');
                    if (enabledInput) enabledInput.checked = config.enabled || false;
                    
                    const apiKeyInput = document.getElementById('weight_api_key');
                    if (apiKeyInput) apiKeyInput.value = config.api_key || '';
                    
                    const checkIntervalInput = document.getElementById('weight_check_interval');
                    if (checkIntervalInput) checkIntervalInput.value = config.check_interval || 60;
                    
                    const batchSizeInput = document.getElementById('weight_batch_size');
                    if (batchSizeInput) batchSizeInput.value = config.batch_size || 5;
                    
                    const batchDelayInput = document.getElementById('weight_batch_delay');
                    if (batchDelayInput) batchDelayInput.value = config.batch_delay || 5;
                    
                    const cycleWaitInput = document.getElementById('weight_cycle_wait');
                    if (cycleWaitInput) cycleWaitInput.value = config.cycle_wait || 24;
                    
                    // 调用weight.html中的loadWeightMonitorStatus函数来更新状态显示
                    if (typeof window.loadWeightMonitorStatus === 'function') {
                        window.loadWeightMonitorStatus();
                    }
                }
            } catch (error) {
                console.error('加载权重监测配置失败:', error);
            }
        }
        
        // 保存权重监测配置
        async function saveWeightMonitorConfig() {
            const config = {
                enabled: document.getElementById('weight_monitor_enabled')?.checked || false,
                api_key: document.getElementById('weight_api_key')?.value || '',
                check_interval: parseInt(document.getElementById('weight_check_interval')?.value) || 60,
                batch_size: parseInt(document.getElementById('weight_batch_size')?.value) || 5,
                batch_delay: parseInt(document.getElementById('weight_batch_delay')?.value) || 5,
                cycle_wait: parseInt(document.getElementById('weight_cycle_wait')?.value) || 24
            };
            
            console.log('保存权重监测配置:', config);
            
            try {
                const response = await fetch('/api/v1/weight/config', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('权重监测配置保存成功', 'success');
                    // 重新加载配置以更新状态
                    loadWeightMonitorConfig();
                } else {
                    showNotification(result.error || '保存失败', 'error');
                }
            } catch (error) {
                console.error('保存权重监测配置失败:', error);
                showNotification('保存失败，请重试', 'error');
            }
        }
        
        // 收集当前页面的实际表单数据
        function collectCurrentFormData() {
            // 保留所有现有设置
            const baseData = settingsData.data || settingsData;
            const data = { ...baseData };
            
            // 遍历当前页面所有的input、select、textarea元素
            const inputs = document.querySelectorAll('#settings-content input, #settings-content select, #settings-content textarea');
            
            // 需要转换为整数的字段列表
            const integerFields = [
                'captcha_expiry', 'captcha_length', 'web_port', 'admin_port',
                'crawler_concurrency', 'crawler_timeout', 'default_cache_home_ttl', 
                'default_cache_other_ttl', 'cache_404_ttl', 'redis_max_pool_size',
                'file_read_timeout', 'file_write_timeout', 'async_queue_size',
                'async_worker_count', 'proxy_request_timeout', 'db_max_open_conns',
                'db_max_idle_conns', 'db_conn_max_lifetime', 'db_slave_max_open_conns',
                'db_slave_max_idle_conns', 'http_max_idle_conns', 'http_max_idle_conns_per_host',
                'http_max_conns_per_host', 'http_idle_conn_timeout', 'crawler_rate_limit',
                'cache_lock_timeout', 'cache_lock_retry_interval', 'log_retention_days',
                'log_max_size', 'log_max_backups', 'max_database_conn', 'max_redis_conn',
                'max_http_requests', 'max_file_ops', 'max_crawler_tasks', 'route_timeout',
                'database_query_timeout', 'redis_op_timeout', 'http_request_timeout',
                'file_op_timeout', 'crawler_task_timeout', 'worker_pool_size', 
                'worker_pool_min_size', 'worker_pool_max_size'
            ];
            
            // 需要转换为浮点数的字段列表
            const floatFields = [
                'pinyin_special_chars_ratio', 'keyword_density', 'worker_pool_scale_ratio'
            ];
            
            // 先处理radio按钮（需要特殊处理）
            const radioGroups = {};
            document.querySelectorAll('#settings-content input[type="radio"]').forEach(radio => {
                const name = radio.name;
                if (name && radio.checked) {
                    // 将name转换为下划线格式作为字段名
                    const fieldName = name.replace(/-/g, '_');
                    data[fieldName] = radio.value;
                    radioGroups[name] = true;
                }
            });
            
            inputs.forEach(input => {
                // 跳过已处理的radio按钮
                if (input.type === 'radio') {
                    return;
                }
                
                const id = input.id;
                if (!id) return;
                
                // 直接将ID中的横线转换为下划线作为字段名
                const fieldName = id.replace(/-/g, '_');
                
                if (input.type === 'checkbox') {
                    data[fieldName] = input.checked;
                } else if (input.type === 'number' || integerFields.includes(fieldName)) {
                    // 对于number类型的input或者需要整数的字段，都转换为整数
                    data[fieldName] = parseInt(input.value) || 0;
                } else if (floatFields.includes(fieldName)) {
                    // 对于浮点数字段，转换为浮点数
                    // 如果是pinyin_special_chars_ratio，需要除以100（因为UI上是百分比）
                    if (fieldName === 'pinyin_special_chars_ratio') {
                        data[fieldName] = parseFloat(input.value) / 100 || 0;
                    } else {
                        data[fieldName] = parseFloat(input.value) || 0;
                    }
                } else {
                    data[fieldName] = input.value;
                }
            });
            
            // 保持ID不变
            if (baseData.id) {
                data.id = baseData.id;
            }
            
            return data;
        }
        
        // 收集表单数据（旧的备用方法）
        function collectFormData() {
            // 根据当前Tab收集对应的表单数据
            const data = { ...settingsData };
            
            // 根据每个Tab收集对应的字段
            if (currentTab === 'basic') {
                const webPortInput = document.getElementById('web-port');
                if (webPortInput) data.web_port = parseInt(webPortInput.value);
                
                const adminPortInput = document.getElementById('admin-port');
                if (adminPortInput) data.admin_port = parseInt(adminPortInput.value);
                
                const logLevelSelect = document.getElementById('log-level');
                if (logLevelSelect) data.log_level = logLevelSelect.value;
            } 
            else if (currentTab === 'request') {
                // 请求设置
                const crawlerConcurrency = document.getElementById('crawler-concurrency');
                if (crawlerConcurrency) data.crawler_concurrency = parseInt(crawlerConcurrency.value);
                
                const httpTimeout = document.getElementById('http-client-timeout-main');
                if (httpTimeout) data.http_client_timeout = parseInt(httpTimeout.value);
                
                const proxyTimeout = document.getElementById('proxy-request-timeout-main');
                if (proxyTimeout) data.proxy_request_timeout = parseInt(proxyTimeout.value);
                
                const userAgentList = document.getElementById('user-agent-list');
                if (userAgentList) data.user_agent_list = userAgentList.value;
            }
            else if (currentTab === 'ua') {
                // UA判断设置
                const globalUACheck = document.getElementById('global-ua-check');
                if (globalUACheck) data.global_ua_check = globalUACheck.checked;
                
                const globalAllowedUA = document.getElementById('global-allowed-ua');
                if (globalAllowedUA) data.global_allowed_ua = globalAllowedUA.value;
                
                const globalNonSpiderHTML = document.getElementById('global-non-spider-html');
                if (globalNonSpiderHTML) data.global_non_spider_html = globalNonSpiderHTML.value;
            }
            else if (currentTab === 'cache') {
                // 缓存设置
                const cacheExpiration = document.getElementById('cache-expiration');
                if (cacheExpiration) data.cache_expiration = parseInt(cacheExpiration.value);
                
                const cacheMode = document.getElementById('cache-mode');
                if (cacheMode) data.cache_mode = cacheMode.value;
                
                const storageDir = document.getElementById('storage-dir');
                if (storageDir) data.storage_dir = storageDir.value;
                
                const maxCacheSize = document.getElementById('max-cache-size');
                if (maxCacheSize) data.max_cache_size = parseInt(maxCacheSize.value);
                
                const cacheCompression = document.getElementById('cache-compression');
                if (cacheCompression) data.cache_compression = cacheCompression.checked;
            }
            else if (currentTab === 'performance') {
                // 性能配置
                const maxWorkers = document.getElementById('scheduler_max_workers');
                if (maxWorkers) data.scheduler_max_workers = parseInt(maxWorkers.value);
                
                const queueSize = document.getElementById('scheduler_queue_size');
                if (queueSize) data.scheduler_queue_size = parseInt(queueSize.value);
                
                const rateLimitPerMinute = document.getElementById('crawler_rate_limit');
                if (rateLimitPerMinute) data.crawler_rate_limit = parseInt(rateLimitPerMinute.value);
                
                const dbMaxIdleConns = document.getElementById('db_max_idle_conns');
                if (dbMaxIdleConns) data.db_max_idle_conns = parseInt(dbMaxIdleConns.value);
                
                const dbMaxOpenConns = document.getElementById('db_max_open_conns');
                if (dbMaxOpenConns) data.db_max_open_conns = parseInt(dbMaxOpenConns.value);
                
                const dbConnMaxLifetime = document.getElementById('db_conn_max_lifetime');
                if (dbConnMaxLifetime) data.db_conn_max_lifetime = parseInt(dbConnMaxLifetime.value);
                
                const httpMaxIdleConns = document.getElementById('http_max_idle_conns');
                if (httpMaxIdleConns) data.http_max_idle_conns = parseInt(httpMaxIdleConns.value);
                
                const httpMaxIdleConnsPerHost = document.getElementById('http_max_idle_conns_per_host');
                if (httpMaxIdleConnsPerHost) data.http_max_idle_conns_per_host = parseInt(httpMaxIdleConnsPerHost.value);
                
                const httpMaxConnsPerHost = document.getElementById('http_max_conns_per_host');
                if (httpMaxConnsPerHost) data.http_max_conns_per_host = parseInt(httpMaxConnsPerHost.value);
                
                const httpIdleConnTimeout = document.getElementById('http_idle_conn_timeout');
                if (httpIdleConnTimeout) data.http_idle_conn_timeout = parseInt(httpIdleConnTimeout.value);
                
                const cacheLockTimeout = document.getElementById('cache_lock_timeout');
                if (cacheLockTimeout) data.cache_lock_timeout = parseInt(cacheLockTimeout.value);
                
                const cacheLockRetryInterval = document.getElementById('cache_lock_retry_interval');
                if (cacheLockRetryInterval) data.cache_lock_retry_interval = parseInt(cacheLockRetryInterval.value);
            }
            else if (currentTab === 'error') {
                // 错误页面设置
                const siteNotFoundHTML = document.getElementById('site-not-found-html');
                if (siteNotFoundHTML) data.site_not_found_html = siteNotFoundHTML.value;
            }
            // sitemap设置现在独立处理，不再在这里收集
            
            return data;
        }
        
        // 重置设置
        function resetSettings() {
            if (confirm('确定要重置当前设置吗？')) {
                // 重新加载设置并刷新当前页面
                loadSettings().then(() => {
                    loadTabContent(currentTab);
                });
            }
        }
        
        // 显示通知
        function showNotification(message, type) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // 3秒后自动消失
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        // 更新系统时间
        function updateSystemTime() {
            const now = new Date();
            const timeStr = now.toLocaleDateString('zh-CN') + ' ' + now.toLocaleTimeString('zh-CN');
            document.getElementById('system-time').textContent = timeStr;
        }
        
        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('/api/v1/auth/logout', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                }).then(() => {
                    window.location.href = '/login';
                });
            }
        }
        
        // 获取CSRF Token
        function getCSRFToken() {
            return document.querySelector('meta[name="csrf-token"]')?.content || 
                   localStorage.getItem('csrf_token') || '';
        }
        
        // ===== 日志管理相关函数 =====
        
        // 查看日志
        window.viewLogs = function() {
            // 打开新窗口查看日志
            window.open('/admin/logs', '_blank');
        }
        
        // 下载日志
        window.downloadLogs = async function() {
            try {
                const response = await fetch('/api/v1/system/logs/download', {
                    method: 'GET',
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `logs_${new Date().toISOString().split('T')[0]}.zip`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    showNotification('日志下载成功', 'success');
                } else {
                    showNotification('下载失败', 'error');
                }
            } catch (error) {
                console.error('下载日志失败:', error);
                showNotification('下载失败: ' + error.message, 'error');
            }
        }
        
        // 清理旧日志
        window.clearLogs = async function() {
            const days = prompt('请输入要保留的天数（清理此天数之前的日志）:', '7');
            if (!days || isNaN(days)) {
                return;
            }
            
            if (!confirm(`确定要清理 ${days} 天前的日志吗？`)) {
                return;
            }
            
            try {
                const response = await fetch('/api/v1/system/logs/clean', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    },
                    body: JSON.stringify({ days: parseInt(days) })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`成功清理 ${result.deleted || 0} 个日志文件`, 'success');
                } else {
                    showNotification(result.message || '清理失败', 'error');
                }
            } catch (error) {
                console.error('清理日志失败:', error);
                showNotification('清理失败: ' + error.message, 'error');
            }
        }
        
        // ===== 权重监测相关函数（全局） =====
        
        // 测试API密钥
        window.testAPIKey = async function() {
            const apiKey = document.getElementById('weight_api_key')?.value;
            if (!apiKey) {
                showNotification('请先输入API密钥', 'warning');
                return;
            }
            
            showNotification('正在验证API密钥...', 'info');
            
            try {
                const res = await fetch('/api/v1/weight/test-api', {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': getCSRFToken()
                    },
                    body: JSON.stringify({ api_key: apiKey })
                });
                
                const data = await res.json();
                if (data.success) {
                    showNotification('API密钥验证成功', 'success');
                } else {
                    showNotification(data.error || 'API密钥无效', 'error');
                }
            } catch (error) {
                console.error('验证API密钥失败:', error);
                showNotification('验证失败: ' + error.message, 'error');
            }
        }
        
        // 手动执行权重检查
        window.manualCheckWeights = async function() {
            if (!confirm('确定要立即执行权重检查吗？这可能需要一些时间。')) {
                return;
            }
            
            showNotification('正在执行权重检查...', 'info');
            
            try {
                const res = await fetch('/api/v1/weight/check-now', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                });
                
                const data = await res.json();
                if (data.success) {
                    showNotification('权重检查已启动', 'success');
                } else {
                    showNotification(data.error || '执行失败', 'error');
                }
            } catch (error) {
                console.error('执行权重检查失败:', error);
                showNotification('执行失败: ' + error.message, 'error');
            }
        }
        
        // 查看权重历史
        window.viewWeightHistory = function() {
            window.location.href = '/admin/weight-monitor';
        }
        
        // ===== 系统信息相关函数（全局） =====
        
        // 加载系统信息
        window.loadSystemInfo = async function() {
            try {
                const response = await fetch('/api/v1/system/info', {
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success && result.data) {
                        updateSystemDisplay(result.data);
                    }
                }
            } catch (error) {
                console.error('加载系统信息失败:', error);
                showNotification('加载系统信息失败', 'error');
            }
        }
        
        // 更新系统信息显示
        window.updateSystemDisplay = function(data) {
            // 更新系统版本信息
            const versionEl = document.getElementById('system-version');
            if (versionEl && data.app_version) versionEl.textContent = data.app_version;
            
            const goVersionEl = document.getElementById('go-version');
            if (goVersionEl && data.go_version) goVersionEl.textContent = data.go_version;
            
            // 更新数据库状态
            if (data.database) {
                const dbStatusEl = document.getElementById('db-status');
                if (dbStatusEl) {
                    dbStatusEl.innerHTML = data.database.connected ? 
                        '<span class="text-green-600">已连接</span>' : 
                        '<span class="text-red-600">未连接</span>';
                }
                
                updateElementText('db-size', data.database.size || '-');
                updateElementText('db-tables', data.database.table_count || 0);
                updateElementText('db-connections', data.database.stats?.open_connections || '-');
                updateElementText('db-active', data.database.stats?.in_use || 0);
                updateElementText('db-idle', data.database.stats?.idle || 0);
                updateElementText('db-queries', data.database.active_queries || 0);
            }
            
            // 更新Redis状态
            if (data.redis) {
                const redisStatusEl = document.getElementById('redis-status');
                if (redisStatusEl) {
                    redisStatusEl.innerHTML = data.redis.connected ? 
                        '<span class="text-green-600">已连接</span>' : 
                        '<span class="text-red-600">未连接</span>';
                }
                
                updateElementText('redis-version', data.redis.stats?.version || '-');
                updateElementText('redis-memory', data.redis.stats?.used_memory_human || '-');
                updateElementText('redis-keys', data.redis.db_size || 0);
                updateElementText('redis-hit-rate', data.redis.stats?.hit_rate || '-');
                updateElementText('redis-clients', data.redis.stats?.connected_clients || 0);
                updateElementText('redis-ops', data.redis.stats?.instantaneous_ops_per_sec || 0);
                
                // 转换uptime为秒数
                const uptimeStr = data.redis.stats?.uptime_in_seconds;
                if (uptimeStr) {
                    updateElementText('redis-uptime', formatUptime(parseInt(uptimeStr) || 0));
                } else {
                    updateElementText('redis-uptime', '-');
                }
            }
        }
        
        // 辅助函数
        function updateElementText(id, text) {
            const el = document.getElementById(id);
            if (el) el.textContent = text;
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatUptime(seconds) {
            if (seconds < 60) return seconds + '秒';
            if (seconds < 3600) return Math.floor(seconds / 60) + '分钟';
            if (seconds < 86400) return Math.floor(seconds / 3600) + '小时';
            return Math.floor(seconds / 86400) + '天';
        }
        
        // ===== 统计页面相关函数（全局） =====
        
        // 刷新所有站点统计JS
        window.refreshAnalyticsJS = async function() {
            const statusEl = document.getElementById('refresh-status');
            if (statusEl) {
                statusEl.textContent = '正在刷新统计JS...';
                statusEl.className = 'text-sm text-blue-600';
            }
            
            try {
                const response = await fetch('/api/v1/analytics/refresh', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    if (statusEl) {
                        statusEl.textContent = '统计JS刷新成功！';
                        statusEl.className = 'text-sm text-green-600';
                    }
                    showNotification('统计JS刷新成功！', 'success');
                } else {
                    if (statusEl) {
                        statusEl.textContent = '刷新失败：' + (result.message || '未知错误');
                        statusEl.className = 'text-sm text-red-600';
                    }
                    showNotification('刷新失败：' + (result.message || '未知错误'), 'error');
                }
            } catch (error) {
                if (statusEl) {
                    statusEl.textContent = '刷新失败：' + error.message;
                    statusEl.className = 'text-sm text-red-600';
                }
                showNotification('刷新失败：' + error.message, 'error');
            }
            
            // 3秒后清除状态
            if (statusEl) {
                setTimeout(() => {
                    statusEl.textContent = '';
                }, 3000);
            }
        }
        
        // 刷新所有站点地图
        window.refreshAllSitemaps = async function() {
            const statusEl = document.getElementById('refresh-status');
            if (statusEl) {
                statusEl.textContent = '正在刷新站点地图...';
                statusEl.className = 'text-sm text-blue-600';
            }
            
            try {
                const response = await fetch('/api/v1/sitemap/refresh-all', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-Token': getCSRFToken()
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const message = `成功刷新 ${result.data?.success_count || 0} 个站点地图！`;
                    if (statusEl) {
                        statusEl.textContent = message;
                        statusEl.className = 'text-sm text-green-600';
                    }
                    showNotification(message, 'success');
                } else {
                    if (statusEl) {
                        statusEl.textContent = '刷新失败：' + (result.message || '未知错误');
                        statusEl.className = 'text-sm text-red-600';
                    }
                    showNotification('刷新失败：' + (result.message || '未知错误'), 'error');
                }
            } catch (error) {
                if (statusEl) {
                    statusEl.textContent = '刷新失败：' + error.message;
                    statusEl.className = 'text-sm text-red-600';
                }
                showNotification('刷新失败：' + error.message, 'error');
            }
            
            // 3秒后清除状态
            if (statusEl) {
                setTimeout(() => {
                    statusEl.textContent = '';
                }, 3000);
            }
        }
    </script>
    
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>