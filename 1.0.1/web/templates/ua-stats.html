<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UA统计分析 - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/common.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="w-64 bg-gray-900 text-white overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
            <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
            
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 overflow-y-auto">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between px-8 py-4">
                    <h1 class="text-2xl font-semibold text-gray-800">UA统计分析</h1>
                    <div class="flex items-center space-x-3">
                        <span class="text-gray-600" id="system-time"></span>
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" id="uaStatsSwitch" 
                                   class="sr-only peer" onchange="toggleUAStats()">
                            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            <span class="ml-3 text-sm font-medium text-gray-900">启用UA统计</span>
                        </label>
                        <button onclick="flushUACache()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-sync-alt mr-2"></i>
                            刷新缓存
                        </button>
                        <button onclick="clearUAStats()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center">
                            <i class="fas fa-trash mr-2"></i>
                            清空统计
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 内容区 -->
            <div class="p-8">
                <!-- 统计概览卡片 -->
                <div class="bg-white rounded-lg shadow mb-6 p-6">
                    <h3 class="text-lg font-semibold mb-4">统计概览</h3>
                    <div class="grid grid-cols-4 gap-4">
                        <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-3xl font-bold text-blue-600" id="ua-total">0</div>
                                    <div class="text-sm text-gray-600 mt-1">总UA数</div>
                                </div>
                                <div class="text-blue-500">
                                    <i class="fas fa-fingerprint text-3xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-3xl font-bold text-green-600" id="ua-hits">0</div>
                                    <div class="text-sm text-gray-600 mt-1">总访问次数</div>
                                </div>
                                <div class="text-green-500">
                                    <i class="fas fa-chart-line text-3xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-3xl font-bold text-purple-600" id="ua-spiders">0</div>
                                    <div class="text-sm text-gray-600 mt-1">爬虫UA数</div>
                                </div>
                                <div class="text-purple-500">
                                    <i class="fas fa-spider text-3xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-3xl font-bold text-orange-600" id="ua-normal">0</div>
                                    <div class="text-sm text-gray-600 mt-1">普通UA数</div>
                                </div>
                                <div class="text-orange-500">
                                    <i class="fas fa-users text-3xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 图表分析 -->
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <!-- 浏览器分布 -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">浏览器分布</h3>
                        <div id="browserChart" style="height: 300px;"></div>
                    </div>
                    
                    <!-- 操作系统分布 -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">操作系统分布</h3>
                        <div id="osChart" style="height: 300px;"></div>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <!-- 设备类型分布 -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">设备类型分布</h3>
                        <div id="deviceChart" style="height: 300px;"></div>
                    </div>
                    
                    <!-- 爬虫分布 -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold mb-4">爬虫分布TOP10</h3>
                        <div id="spiderChart" style="height: 300px;"></div>
                    </div>
                </div>
                
                <!-- UA详细列表 -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold">UA访问详情</h3>
                            <div class="flex items-center space-x-3">
                                <select id="ua-filter-type" onchange="resetPageAndLoad()" 
                                        class="border border-gray-300 rounded px-3 py-1 text-sm">
                                    <option value="">全部类型</option>
                                    <option value="real_user">真实用户</option>
                                    <option value="crawler">爬虫</option>
                                    <option value="bot">机器人</option>
                                    <option value="unknown">未知</option>
                                </select>
                                <select id="ua-device-type" onchange="resetPageAndLoad()" 
                                        class="border border-gray-300 rounded px-3 py-1 text-sm">
                                    <option value="">全部设备</option>
                                    <option value="PC">PC</option>
                                    <option value="Mobile">Mobile</option>
                                    <option value="Tablet">Tablet</option>
                                    <option value="Bot">Bot</option>
                                    <option value="Unknown">Unknown</option>
                                </select>
                                <div class="relative">
                                    <input type="text" id="ua-search" placeholder="搜索UA、浏览器、系统..." 
                                           class="pl-8 pr-4 py-1 border border-gray-300 rounded text-sm w-80"
                                           onkeyup="handleSearch(event)">
                                    <i class="fas fa-search absolute left-2.5 top-2 text-gray-400 text-sm"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead class="bg-gray-50 border-b">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 30%;">User-Agent</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">浏览器</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">系统</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">访问者</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="changeSortBy('session_count')">
                                        会话/页面 <i class="fas fa-sort text-gray-400"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="changeSortBy('hit_count')">
                                        访问次数 <i class="fas fa-sort text-gray-400"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100" onclick="changeSortBy('last_seen')">
                                        最后访问 <i class="fas fa-sort text-gray-400"></i>
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody id="ua-list" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="10" class="px-6 py-12 text-center text-gray-500">
                                        <div class="flex justify-center">
                                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
                                        </div>
                                        <p class="mt-4">加载中...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="text-sm text-gray-700">
                                共 <span id="total-uas">0</span> 条记录
                            </div>
                            <div class="flex items-center space-x-2">
                                <label class="text-sm text-gray-700">每页显示</label>
                                <select id="page-size-select" onchange="changePageSize()" class="px-2 py-1 border rounded text-sm">
                                    <option value="10">10条</option>
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="previousPage()" id="prev-btn" class="px-3 py-1 bg-gray-200 text-gray-600 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                <i class="fas fa-chevron-left"></i> 上一页
                            </button>
                            <span class="text-sm text-gray-600">
                                第 <span id="current-page">1</span> / <span id="total-pages">1</span> 页
                            </span>
                            <button onclick="nextPage()" id="next-btn" class="px-3 py-1 bg-gray-200 text-gray-600 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                下一页 <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Toast 通知 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>
    
    <!-- UA详情模态框 -->
    <div id="uaDetailModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold">UA详细信息</h3>
                    <button onclick="closeUADetailModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">完整UA字符串</label>
                            <div class="p-3 bg-gray-50 rounded text-sm text-gray-800 break-all" id="detail-ua"></div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">浏览器</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-browser">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">操作系统</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-os">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">设备类型</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-device">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">爬虫名称</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-spider">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">访问次数</label>
                                <div class="p-2 bg-gray-50 rounded text-sm font-semibold" id="detail-hits">0</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">会话数</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-sessions">0</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">页面浏览量</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-page-views">0</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">资源请求数</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-resource-hits">0</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">访问者类型</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-visitor-type">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">置信度</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-confidence">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">平均资源数</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-avg-resources">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">最后IP</label>
                                <div class="p-2 bg-gray-50 rounded text-sm font-mono" id="detail-last-ip">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">UA哈希</label>
                                <div class="p-2 bg-gray-50 rounded text-sm font-mono" id="detail-hash">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">首次访问</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-first">-</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">最后访问</label>
                                <div class="p-2 bg-gray-50 rounded text-sm" id="detail-last">-</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 border-t bg-gray-50 flex justify-end">
                    <button onclick="closeUADetailModal()" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let allUAData = [];
        let browserChart = null;
        let osChart = null;
        let deviceChart = null;
        let spiderChart = null;
        let currentPage = 1;
        let pageSize = 10; // 默认改为10条
        let totalPages = 1;
        let totalRecords = 0;
        let currentSortBy = 'hit_count';
        let searchQuery = '';
        let filterType = '';
        let globalSpiderBlockEnabled = false; // 全局蜘蛛屏蔽状态
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            
            // 检查必要的DOM元素
            const requiredElements = [
                'system-time', 'ua-total', 'ua-hits', 'ua-spiders', 'ua-normal',
                'browserChart', 'osChart', 'deviceChart', 'spiderChart',
                'ua-list', 'uaStatsSwitch'
            ];
            
            const missingElements = requiredElements.filter(id => !document.getElementById(id));
            if (missingElements.length > 0) {
                console.error('缺少必要的DOM元素:', missingElements);
                showError('页面结构不完整，缺少元素: ' + missingElements.join(', '));
                return;
            }
            
            updateSystemTime();
            setInterval(updateSystemTime, 1000);
            loadSystemSettings();
            
            // 设置分页大小选择器的默认值
            const pageSizeSelect = document.getElementById('page-size-select');
            if (pageSizeSelect) {
                pageSizeSelect.value = pageSize.toString();
            }
            
            // 检查ECharts是否加载
            if (typeof echarts === 'undefined') {
                console.error('ECharts库未加载');
                showError('图表库加载失败，请刷新页面重试');
                return;
            }
            
            initCharts();
            loadUAStats();
            
            console.log('UA统计页面初始化完成');
        });
        
        // 更新系统时间
        function updateSystemTime() {
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('system-time').textContent = timeStr;
        }
        
        // 加载系统设置
        async function loadSystemSettings() {
            try {
                const response = await get('/api/v1/system/settings');
                if (response.success && response.data) {
                    document.getElementById('uaStatsSwitch').checked = response.data.enable_ua_stats || false;
                }
            } catch (error) {
                console.error('加载系统设置失败:', error);
            }
        }
        
        // 切换UA统计开关
        async function toggleUAStats() {
            const enabled = document.getElementById('uaStatsSwitch').checked;
            try {
                const response = await put('/api/v1/system/settings', {
                    enable_ua_stats: enabled
                });
                if (response.success) {
                    showSuccess(enabled ? 'UA统计已启用' : 'UA统计已关闭');
                } else {
                    showError(response.error || '设置失败');
                    document.getElementById('uaStatsSwitch').checked = !enabled;
                }
            } catch (error) {
                showError('设置失败: ' + error.message);
                document.getElementById('uaStatsSwitch').checked = !enabled;
            }
        }
        
        // 初始化图表
        function initCharts() {
            try {
                const browserEl = document.getElementById('browserChart');
                const osEl = document.getElementById('osChart');
                const deviceEl = document.getElementById('deviceChart');
                const spiderEl = document.getElementById('spiderChart');
                
                if (browserEl) browserChart = echarts.init(browserEl);
                if (osEl) osChart = echarts.init(osEl);
                if (deviceEl) deviceChart = echarts.init(deviceEl);
                if (spiderEl) spiderChart = echarts.init(spiderEl);
                
                // 响应窗口大小变化
                window.addEventListener('resize', () => {
                    if (browserChart) browserChart.resize();
                    if (osChart) osChart.resize();
                    if (deviceChart) deviceChart.resize();
                    if (spiderChart) spiderChart.resize();
                });
            } catch (error) {
                console.error('初始化图表失败:', error);
                showError('图表初始化失败');
            }
        }
        
        // 加载UA统计数据
        async function loadUAStats() {
            try {
                filterType = document.getElementById('ua-filter-type')?.value || '';
                const deviceType = document.getElementById('ua-device-type')?.value || '';
                searchQuery = document.getElementById('ua-search')?.value || '';
                
                let url = `/api/v1/spider-block/ua-stats?sort_by=${currentSortBy}&page=${currentPage}&page_size=${pageSize}`;
                if (filterType) {
                    url += `&filter_type=${filterType}`;
                }
                if (deviceType) {
                    url += `&device_type=${deviceType}`;
                }
                if (searchQuery) {
                    url += `&search=${encodeURIComponent(searchQuery)}`;
                }
                
                const response = await get(url);
                
                if (response.success) {
                    const { list, total, page, page_size, total_stats } = response.data || {};
                    allUAData = list || [];
                    totalRecords = total || 0;
                    currentPage = page || 1;
                    pageSize = page_size || 10;
                    totalPages = Math.ceil(totalRecords / pageSize) || 1;
                    
                    // 更新统计卡片
                    if (total_stats) {
                        document.getElementById('ua-total').textContent = total_stats.total_ua || 0;
                        document.getElementById('ua-hits').textContent = (total_stats.total_hits || 0).toLocaleString();
                        document.getElementById('ua-spiders').textContent = total_stats.spider_ua || 0;
                        document.getElementById('ua-normal').textContent = total_stats.normal_ua || 0;
                    } else {
                        // 初始化统计卡片为0
                        document.getElementById('ua-total').textContent = 0;
                        document.getElementById('ua-hits').textContent = 0;
                        document.getElementById('ua-spiders').textContent = 0;
                        document.getElementById('ua-normal').textContent = 0;
                    }
                    
                    // 更新分页信息
                    updatePagination();
                    
                    // 更新UA列表
                    renderUAList();
                    
                    // 加载图表数据
                    loadUACharts();
                } else {
                    console.error('UA统计API返回错误:', response.error);
                    showError('加载UA统计失败: ' + (response.error || '未知错误'));
                    
                    // 显示错误状态
                    const tbody = document.getElementById('ua-list');
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="10" class="px-6 py-12 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                                <p>加载UA统计失败: ${response.error || '未知错误'}</p>
                                <button onclick="loadUAStats()" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                                    重新加载
                                </button>
                            </td>
                        </tr>
                    `;
                }
            } catch (error) {
                console.error('加载UA统计失败:', error);
                showError('加载UA统计失败: ' + error.message);
                
                // 显示错误状态
                const tbody = document.getElementById('ua-list');
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="px-6 py-12 text-center text-red-500">
                            <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                            <p>加载失败: ${error.message}</p>
                            <button onclick="loadUAStats()" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                                重新加载
                            </button>
                        </td>
                    </tr>
                `;
            }
        }
        
        // 更新分页信息
        function updatePagination() {
            document.getElementById('total-uas').textContent = totalRecords;
            document.getElementById('current-page').textContent = currentPage;
            document.getElementById('total-pages').textContent = totalPages;
            
            // 更新分页大小选择器
            const pageSizeSelect = document.getElementById('page-size-select');
            if (pageSizeSelect) {
                pageSizeSelect.value = pageSize.toString();
            }
            
            // 更新按钮状态
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            
            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }
        
        // 上一页
        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                loadUAStats();
            }
        }
        
        // 下一页
        function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                loadUAStats();
            }
        }
        
        // 改变分页大小
        function changePageSize() {
            const select = document.getElementById('page-size-select');
            pageSize = parseInt(select.value);
            currentPage = 1; // 重置到第一页
            loadUAStats();
        }
        
        // 改变排序
        function changeSortBy(sortBy) {
            currentSortBy = sortBy;
            currentPage = 1;
            loadUAStats();
        }
        
        // 重置页码并加载
        function resetPageAndLoad() {
            currentPage = 1;
            loadUAStats();
        }
        
        // 处理搜索
        function handleSearch(event) {
            if (event.key === 'Enter' || event.type === 'click') {
                currentPage = 1;
                loadUAStats();
            }
        }
        
        // 渲染UA列表
        function renderUAList() {
            const tbody = document.getElementById('ua-list');
            
            if (allUAData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="px-6 py-12 text-center text-gray-500">
                            <p>暂无数据</p>
                        </td>
                    </tr>
                `;
                return;
            }
            
            tbody.innerHTML = allUAData.map(ua => {
                // 获取访问者类型显示
                let visitorTypeDisplay = '';
                let visitorTypeClass = '';
                switch(ua.visitor_type) {
                    case 'real_user':
                        visitorTypeDisplay = '真实用户';
                        visitorTypeClass = 'bg-green-100 text-green-800';
                        break;
                    case 'crawler':
                        visitorTypeDisplay = '爬虫';
                        visitorTypeClass = 'bg-red-100 text-red-800';
                        break;
                    case 'bot':
                        visitorTypeDisplay = '机器人';
                        visitorTypeClass = 'bg-yellow-100 text-yellow-800';
                        break;
                    default:
                        visitorTypeDisplay = ua.is_spider ? (ua.spider_name || '爬虫') : '未知';
                        visitorTypeClass = ua.is_spider ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800';
                }
                
                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div class="max-w-xs truncate" title="${escapeHtml(ua.user_agent)}">
                                ${escapeHtml(ua.user_agent.substring(0, 50))}${ua.user_agent.length > 50 ? '...' : ''}
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-600">
                            ${ua.browser || '-'} ${ua.browser_version || ''}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-600">
                            ${ua.os || '-'} ${ua.os_version || ''}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-600">
                            <span class="px-2 py-1 text-xs rounded-full ${getDeviceClass(ua.device)}">
                                ${ua.device || '-'}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm">
                            <div class="flex flex-col items-start space-y-1">
                                <span class="px-2 py-1 text-xs rounded-full ${visitorTypeClass}">
                                    ${visitorTypeDisplay}
                                </span>
                                ${ua.confidence > 0 ? `
                                    <span class="px-2 py-0.5 text-xs rounded-full ${
                                        ua.confidence >= 0.9 ? 'bg-green-100 text-green-700' :
                                        ua.confidence >= 0.7 ? 'bg-blue-100 text-blue-700' :
                                        ua.confidence >= 0.5 ? 'bg-yellow-100 text-yellow-700' :
                                        'bg-gray-100 text-gray-600'
                                    }">
                                        ${(ua.confidence * 100).toFixed(0)}%
                                    </span>
                                ` : ''}
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-600">
                            <div>
                                <span title="会话数">${ua.session_count || 0}</span> /
                                <span title="页面浏览量">${ua.page_views || 0}</span> /
                                <span title="资源请求数">${ua.resource_hits || 0}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 font-semibold">
                            ${ua.hit_count.toLocaleString()}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-600">
                            ${formatDate(ua.last_seen_at)}
                        </td>
                        <td class="px-6 py-4 text-sm">
                            <div class="flex items-center space-x-2">
                                <button onclick="showUADetail('${ua.ua_hash}')" 
                                        class="text-blue-600 hover:text-blue-800" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${ua.is_blocked ? 
                                    `<div class="flex items-center space-x-2">
                                        <span class="px-2 py-1 text-xs rounded bg-red-100 text-red-700">已拦截</span>
                                        <button onclick="unblockUA('${ua.spider_key || getSpiderKeyword(ua.user_agent)}', ${ua.block_rule_id || 0})" 
                                                class="text-orange-600 hover:text-orange-800" 
                                                title="取消拦截 (规则ID: ${ua.block_rule_id || '无'})">
                                            <i class="fas fa-times-circle"></i>
                                        </button>
                                    </div>` :
                                    `<button onclick="blockUA('${ua.spider_key || getSpiderKeyword(ua.user_agent)}', '${ua.spider_name || ua.visitor_type || 'unknown'}')" 
                                            class="text-red-600 hover:text-red-800" title="加入拦截">
                                        <i class="fas fa-ban"></i>
                                    </button>`
                                }
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }
        
        // 获取设备类型样式
        function getDeviceClass(device) {
            switch(device) {
                case 'Desktop': return 'bg-blue-100 text-blue-800';
                case 'Mobile': return 'bg-purple-100 text-purple-800';
                case 'Tablet': return 'bg-indigo-100 text-indigo-800';
                case 'Bot': return 'bg-gray-100 text-gray-800';
                default: return 'bg-gray-100 text-gray-600';
            }
        }
        
        // 加载UA图表
        async function loadUACharts() {
            try {
                // 加载浏览器分布
                const browserResponse = await get('/api/v1/spider-block/ua-stats?stat_type=browser');
                if (browserResponse.success && browserResponse.data) {
                    renderPieChart(browserChart, browserResponse.data, '浏览器分布');
                } else {
                    renderPieChart(browserChart, {}, '浏览器分布');
                }
                
                // 加载操作系统分布
                const osResponse = await get('/api/v1/spider-block/ua-stats?stat_type=os');
                if (osResponse.success && osResponse.data) {
                    renderPieChart(osChart, osResponse.data, '操作系统分布');
                } else {
                    renderPieChart(osChart, {}, '操作系统分布');
                }
                
                // 加载设备类型分布
                const deviceResponse = await get('/api/v1/spider-block/ua-stats?stat_type=device');
                if (deviceResponse.success && deviceResponse.data) {
                    renderPieChart(deviceChart, deviceResponse.data, '设备类型分布');
                } else {
                    renderPieChart(deviceChart, {}, '设备类型分布');
                }
                
                // 加载爬虫分布
                const spiderResponse = await get('/api/v1/spider-block/ua-stats?stat_type=spider');
                if (spiderResponse.success && spiderResponse.data) {
                    renderBarChart(spiderChart, spiderResponse.data, '爬虫分布');
                } else {
                    renderBarChart(spiderChart, {}, '爬虫分布');
                }
            } catch (error) {
                console.error('加载UA图表失败:', error);
                // 即使图表加载失败，也要渲染空图表，避免页面布局错乱
                renderPieChart(browserChart, {}, '浏览器分布');
                renderPieChart(osChart, {}, '操作系统分布');
                renderPieChart(deviceChart, {}, '设备类型分布');
                renderBarChart(spiderChart, {}, '爬虫分布');
            }
        }
        
        // 渲染饼图
        function renderPieChart(chart, data, title) {
            if (!data || typeof data !== 'object') {
                data = {};
            }
            
            const entries = Object.entries(data);
            const chartData = entries.length > 0 
                ? entries.map(([name, value]) => ({ name, value }))
                    .sort((a, b) => b.value - a.value)
                    .slice(0, 10)
                : [{ name: '暂无数据', value: 1 }];
            
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    textStyle: {
                        fontSize: 11
                    }
                },
                series: [
                    {
                        name: title,
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '14',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: chartData
                    }
                ]
            };
            
            chart.setOption(option);
        }
        
        // 渲染柱状图
        function renderBarChart(chart, data, title) {
            if (!data || typeof data !== 'object') {
                data = {};
            }
            
            const entries = Object.entries(data);
            const chartData = entries.length > 0 
                ? entries.map(([name, value]) => ({ name, value }))
                    .sort((a, b) => b.value - a.value)
                    .slice(0, 10)
                : [{ name: '暂无数据', value: 0 }];
            
            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value'
                },
                yAxis: {
                    type: 'category',
                    data: chartData.map(item => item.name).reverse(),
                    axisLabel: {
                        fontSize: 11
                    }
                },
                series: [
                    {
                        name: '访问次数',
                        type: 'bar',
                        data: chartData.map(item => item.value).reverse(),
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#667eea' },
                                { offset: 1, color: '#764ba2' }
                            ]),
                            borderRadius: [0, 5, 5, 0]
                        }
                    }
                ]
            };
            
            chart.setOption(option);
        }
        
        // 显示UA详情
        async function showUADetail(hash) {
            try {
                console.log('获取UA详情, hash:', hash);
                const response = await get(`/api/v1/spider-block/ua-stats/${hash}`);
                console.log('UA详情响应:', response);
                
                if (response.success && response.data) {
                    const ua = response.data;
                    document.getElementById('detail-ua').textContent = ua.user_agent;
                    document.getElementById('detail-browser').textContent = 
                        ua.browser ? `${ua.browser} ${ua.browser_version || ''}` : '-';
                    document.getElementById('detail-os').textContent = 
                        ua.os ? `${ua.os} ${ua.os_version || ''}` : '-';
                    document.getElementById('detail-device').textContent = 
                        ua.device ? `${ua.device} ${ua.device_name || ''}` : '-';
                    document.getElementById('detail-spider').textContent = 
                        ua.is_spider ? (ua.spider_name || '未知爬虫') : '非爬虫';
                    document.getElementById('detail-hits').textContent = ua.hit_count.toLocaleString();
                    document.getElementById('detail-sessions').textContent = ua.session_count || 0;
                    document.getElementById('detail-page-views').textContent = ua.page_views || 0;
                    document.getElementById('detail-resource-hits').textContent = ua.resource_hits || 0;
                    
                    // 访问者类型
                    let visitorType = '未知';
                    switch(ua.visitor_type) {
                        case 'real_user': visitorType = '真实用户'; break;
                        case 'crawler': visitorType = '爬虫'; break;
                        case 'bot': visitorType = '机器人'; break;
                    }
                    document.getElementById('detail-visitor-type').textContent = visitorType;
                    document.getElementById('detail-confidence').textContent = 
                        ua.confidence > 0 ? `${(ua.confidence * 100).toFixed(0)}%` : '-';
                    document.getElementById('detail-avg-resources').textContent = 
                        ua.avg_resources > 0 ? ua.avg_resources.toFixed(2) : '-';
                    document.getElementById('detail-last-ip').textContent = ua.last_ip || '-';
                    
                    document.getElementById('detail-hash').textContent = ua.ua_hash;
                    document.getElementById('detail-first').textContent = formatDate(ua.first_seen_at);
                    document.getElementById('detail-last').textContent = formatDate(ua.last_seen_at);
                    
                    document.getElementById('uaDetailModal').classList.remove('hidden');
                } else {
                    console.error('响应失败或无数据:', response);
                    showError('获取UA详情失败: ' + (response.error || '无数据'));
                }
            } catch (error) {
                console.error('获取UA详情失败:', error);
                showError('获取UA详情失败: ' + error.message);
            }
        }
        
        // 关闭UA详情模态框
        function closeUADetailModal() {
            document.getElementById('uaDetailModal').classList.add('hidden');
        }
        
        // 过滤UA列表
        function filterUAList() {
            renderUAList();
        }
        
        // 刷新UA缓存到数据库
        async function flushUACache() {
            try {
                const response = await post('/api/v1/spider-block/ua-stats/flush', {});
                if (response.success) {
                    showSuccess('缓存已刷新到数据库');
                    // 等待一秒后重新加载数据
                    setTimeout(() => {
                        loadUAStats();
                    }, 1000);
                } else {
                    showError(response.error || '刷新失败');
                }
            } catch (error) {
                showError('刷新缓存失败: ' + error.message);
            }
        }
        
        // 清空UA统计
        async function clearUAStats() {
            if (!confirm('确定要清空所有UA统计数据吗？此操作不可恢复！')) {
                return;
            }
            
            try {
                const response = await del('/api/v1/spider-block/ua-stats');
                if (response.success) {
                    showSuccess('UA统计数据已清空');
                    loadUAStats();
                } else {
                    showError(response.error || '清空失败');
                }
            } catch (error) {
                showError('清空UA统计失败: ' + error.message);
            }
        }
        
        // 从UA字符串中提取爬虫关键词（小写标识）
        function getSpiderKeyword(userAgent) {
            const spiderPatterns = [
                { pattern: /googlebot/i, key: 'googlebot' },
                { pattern: /baiduspider/i, key: 'baiduspider' },
                { pattern: /bingbot/i, key: 'bingbot' },
                { pattern: /yandexbot/i, key: 'yandexbot' },
                { pattern: /facebookexternalhit/i, key: 'facebookexternalhit' },
                { pattern: /twitterbot/i, key: 'twitterbot' },
                { pattern: /linkedinbot/i, key: 'linkedinbot' },
                { pattern: /whatsapp/i, key: 'whatsapp' },
                { pattern: /slackbot/i, key: 'slackbot' },
                { pattern: /discord/i, key: 'discord' },
                { pattern: /telegrambot/i, key: 'telegrambot' },
                { pattern: /semrushbot/i, key: 'semrushbot' },
                { pattern: /ahrefs/i, key: 'ahrefs' },
                { pattern: /mj12bot/i, key: 'mj12bot' },
                { pattern: /dotbot/i, key: 'dotbot' },
                { pattern: /petalbot/i, key: 'petalbot' },
                { pattern: /yisou/i, key: 'yisou' },
                { pattern: /360spider/i, key: '360spider' },
                { pattern: /sogou/i, key: 'sogou' },
                { pattern: /bytespider/i, key: 'bytespider' }
            ];
            
            const lowerUA = userAgent.toLowerCase();
            for (const spider of spiderPatterns) {
                if (spider.pattern.test(lowerUA)) {
                    return spider.key;
                }
            }
            
            // 如果没有匹配到已知爬虫，返回简化的UA
            if (userAgent.length > 50) {
                return userAgent.substring(0, 50);
            }
            return userAgent;
        }
        
        // 拦截UA
        async function blockUA(spiderName, description) {
            if (!confirm(`确定要拦截 ${spiderName} 吗？`)) {
                return;
            }
            
            try {
                // 使用统一的请求方式
                const requestData = {
                    user_agent: spiderName,
                    description: description || spiderName + '爬虫',
                    return_code: 403,
                    enabled: true
                };
                
                console.log('正在创建拦截规则:', requestData);
                
                const response = await post('/api/v1/spider-block', requestData);
                
                if (response.success) {
                    // 记录新创建的规则ID
                    const newRuleId = response.data && response.data.id;
                    console.log('拦截规则创建成功，ID:', newRuleId);
                    showSuccess(`UA已加入拦截列表 (ID: ${newRuleId || '未知'})`);
                    // 重新加载数据
                    setTimeout(() => {
                        loadUAStats();
                    }, 500);
                } else {
                    showError('拦截失败: ' + (response.error || '未知错误'));
                }
            } catch (error) {
                console.error('拦截UA失败:', error);
                showError('拦截失败: ' + error.message);
            }
        }
        
        // 取消拦截UA
        async function unblockUA(spiderName, ruleId) {
            // 必须有规则ID才能删除
            if (!ruleId || ruleId <= 0) {
                showError('无法删除：缺少拦截规则ID。这个UA可能没有被真正拦截，或者需要刷新页面。');
                // 刷新页面以更新状态
                setTimeout(() => loadUAStats(), 1000);
                return;
            }
            
            if (!confirm(`确定要取消拦截 ${spiderName} 吗？`)) {
                return;
            }
            
            try {
                // 使用规则ID删除
                const response = await del(`/api/v1/spider-block/${ruleId}`);
                
                if (response.success) {
                    showSuccess('已取消拦截');
                    // 重新加载数据
                    setTimeout(() => {
                        loadUAStats();
                    }, 500);
                } else {
                    showError('取消拦截失败: ' + (response.error || '未知错误'));
                }
            } catch (error) {
                console.error('取消拦截失败:', error);
                showError('取消拦截失败: ' + error.message);
            }
        }
        
        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        // HTML转义
        function escapeHtml(str) {
            const div = document.createElement('div');
            div.textContent = str;
            return div.innerHTML;
        }
        
        // Toast通知和API请求函数已在common.js中定义
    </script>
    <!-- 引入动态菜单分组组件 -->
    <script src="/static/js/menu-groups.js?v=1756618802"></script>
</body>
</html>