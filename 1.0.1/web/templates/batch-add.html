<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - 站群管理系统</title>
    <script src="/static/js/csrf.js"></script>
    <script src="/static/js/session-timeout.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/admin.css?v=1.0.0">
    <link rel="stylesheet" href="/static/css/custom.css?v=1.0.0">
    <style>
        /* 修复侧边栏高度问题 */
        #sidebar {
            height: 100vh !important;
            position: sticky;
            top: 0;
        }
        /* 防止页面闪烁 */
        body {
            visibility: visible !important;
        }
        /* 防止内容加载时的闪烁 */
        [id$="-libraries"], 
        [id$="-settings"],
        #batch-status-content {
            min-height: 20px;
            will-change: contents;
        }
        /* 禁用不必要的动画以减少闪烁 */
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
        /* 保留必要的过渡效果 */
        .hover\:bg-green-600,
        .hover\:bg-blue-600,
        .hover\:text-gray-800 {
            transition-duration: 200ms !important;
        }
        /* 优化系统时间显示区域 */
        #system-time {
            display: inline-block;
            min-width: 150px;
            text-align: right;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside id="sidebar" class="bg-gray-900 text-white w-64 h-full overflow-y-auto">
            <div class="p-4">
                <h2 class="text-2xl font-bold text-center">站群管理系统</h2>
            </div>
            
            <nav class="mt-8">
                <!-- 动态菜单由 menu-groups.js 生成 -->
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm">
                <div class="flex items-center justify-between p-4">
                    <div class="flex items-center">
                        <button onclick="toggleSidebar()" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="ml-4 text-xl font-semibold text-gray-800">批量添加站点</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                       
                        <span class="text-sm text-gray-600">系统时间：<span id="system-time"></span></span>
                    </div>
                </div>
            </header>

            <!-- 主要内容 -->
            <main class="flex-1 overflow-y-auto p-6 bg-gray-50">
                <form id="batch-site-form">
                    <!-- 单个大卡片布局 -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6">
                            <!-- 标题 -->
                            <h3 class="text-lg font-semibold text-gray-800 mb-6">
                                <i class="fas fa-list-alt text-blue-500 mr-2"></i>站点信息
                            </h3>
                            
                            <!-- 站点输入区 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    站点信息 * <span class="text-xs text-gray-500">（可添加一个或多个）</span>
                                </label>
                                <textarea id="batch-sites" name="batch_sites" required
                                          class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-y"
                                          style="min-height: 180px;"
                                          placeholder="格式1（基础格式）：&#10;example.com|||https://www.example.com|||示例网站|||关键词1,关键词2|||网站描述&#10;&#10;格式2（带子域名）：&#10;wap,m,@,www,example.com|||https://www.example.com|||示例网站|||关键词1,关键词2|||网站描述&#10;&#10;说明：子域名前缀用逗号分隔，@表示裸域名，最后一个是主域名&#10;使用 ||| 作为字段分隔符，避免与URL中的 | 冲突"></textarea>
                                <div class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div class="text-xs text-yellow-800">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        <strong>重要提示：</strong>现在使用 <code class="px-1 py-0.5 bg-yellow-100 text-yellow-900 rounded">|||</code> 作为字段分隔符（三个竖线），不再使用单个 <code class="px-1 py-0.5 bg-yellow-100 text-yellow-900 rounded">|</code>
                                    </div>
                                    <div class="mt-1 text-xs text-gray-600">
                                        这样可以避免与URL中可能包含的单个竖线字符冲突
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 站点分类和批量添加按钮在同一行 -->
                            <div class="flex items-end gap-4 mb-4">
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">站点分类</label>
                                    <select id="batch-category-id" name="category_id"
                                            class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">选择分类</option>
                                    </select>
                                </div>
                                
                                <!-- 批次大小设置 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">批次大小</label>
                                    <div class="flex items-center gap-2">
                                        <input type="number" id="batch-size" min="1" max="50" value="10" 
                                               class="w-20 border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <span class="text-sm text-gray-500">条/批</span>
                                    </div>
                                </div>
                                
                                <!-- 批量添加按钮 -->
                                <button type="submit" class="px-8 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm font-medium whitespace-nowrap">
                                    <i class="fas fa-plus-circle mr-2"></i>批量添加
                                </button>
                            </div>
                            
                            <!-- 待添加数量提示 -->
                            <div class="text-sm text-gray-600">
                                <i class="fas fa-info-circle text-blue-500 mr-1"></i>
                                <span id="batch-count">0</span> 个站点待添加
                            </div>
                        </div>
                    </div>
                    
                    <!-- 通用配置部分 - 简化版 -->
                    <div class="bg-white rounded-lg shadow mt-6">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">
                                <i class="fas fa-cog text-gray-500 mr-2"></i>通用配置（应用于所有站点）
                            </h3>
                            
                            <!-- 标签页导航 -->
                            <div class="border-b">
                                <nav class="flex space-x-4">
                                    <button type="button" onclick="switchBatchTab('batch-basic')" id="batch-tab-basic" 
                                            class="px-3 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600">
                                        基础配置
                                    </button>
                                    <button type="button" onclick="switchBatchTab('batch-seo')" id="batch-tab-seo" 
                                            class="px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-800">
                                        SEO优化
                                    </button>
                                    <button type="button" onclick="switchBatchTab('batch-injection')" id="batch-tab-injection"
                                            class="px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-800">
                                        内容注入
                                    </button>
                                    <button type="button" onclick="switchBatchTab('batch-security')" id="batch-tab-security"
                                            class="px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-800">
                                        安全设置
                                    </button>
                                </nav>
                            </div>
                            
                            <!-- 标签页内容 -->
                            <div class="pt-4">
                                <!-- 基础配置标签页 -->
                                <div id="batch-tab-content-basic" class="space-y-4">
                                    <!-- 基础设置 -->
                                    <div class="space-y-3">
                                        <h5 class="text-sm font-medium text-gray-700 border-b pb-1">基础设置</h5>
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">站点状态</label>
                                                <select id="batch-status" name="status"
                                                        class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                    <option value="active">活跃</option>
                                                    <option value="inactive">未激活</option>
                                                </select>
                                            </div>
                                            
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">缓存深度</label>
                                                <input type="number" id="batch-crawl-depth" name="crawl_depth" min="1" max="10" value="2"
                                                       class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 功能开关 -->
                                    <div class="space-y-3">
                                        <h5 class="text-sm font-medium text-gray-700 border-b pb-1">功能开关</h5>
                                        <div class="grid grid-cols-2 gap-x-6 gap-y-3">
                                            <label class="flex items-center">
                                                <input type="checkbox" id="batch-enable-preload" name="enable_preload" class="mr-2" checked>
                                                <span class="text-sm">预加载下一层链接</span>
                                            </label>
                                            
                                            <label class="flex items-center">
                                                <input type="checkbox" id="batch-download-external" name="download_external_resources" class="mr-2" checked>
                                                <span class="text-sm">下载外部资源</span>
                                            </label>
                                            
                                            <label class="flex items-center">
                                                <input type="checkbox" id="batch-enable-https-check" name="enable_https_check" class="mr-2">
                                                <span class="text-sm">HTTPS证书检查</span>
                                            </label>
                                            
                                            <label class="flex items-center">
                                                <input type="checkbox" id="batch-traditional-convert" name="enable_traditional_convert" class="mr-2" checked>
                                                <span class="text-sm">简繁转换</span>
                                            </label>
                                            
                                            <label class="flex items-center">
                                                <input type="checkbox" id="batch-enable-analytics" name="enable_analytics" class="mr-2" checked>
                                                <span class="text-sm">启用统计代码</span>
                                            </label>
                                            
                                            <label class="flex items-center">
                                                <input type="checkbox" id="batch-filter-external" name="filter_external_links" class="mr-2" checked>
                                                <span class="text-sm">过滤外部链接</span>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <!-- 拼音标注设置 -->
                                    <div class="space-y-3">
                                        <h5 class="text-sm font-medium text-gray-700 border-b pb-1">拼音标注设置</h5>
                                        <div class="mb-3">
                                            <select id="batch-pinyin-mode" name="pinyin_mode" class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="toggleBatchPinyinMode()">
                                                <option value="">请选择拼音设置</option>
                                                <option value="global" selected>使用全局设置</option>
                                                <option value="enable">独立开启</option>
                                                <option value="disable">独立关闭</option>
                                            </select>
                                        </div>
                                        
                                        <!-- 批量站点独立的拼音设置 -->
                                        <div id="batch-pinyin-custom-settings" style="display:none;" class="space-y-2 p-2 bg-gray-50 rounded text-sm">
                                            <!-- 拼音特殊字符设置 -->
                                            <div id="batch-pinyin-special-chars-settings" class="ml-4 space-y-2" style="display:none;">
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="batch-enable-pinyin-special-chars" name="enable_pinyin_special_chars" class="mr-2">
                                                    <span class="text-xs">插入特殊字符</span>
                                                </label>
                                                
                                                <div class="ml-4 space-y-1">
                                                    <div class="flex items-center space-x-2">
                                                        <label class="text-xs">插入比例：</label>
                                                        <input type="number" id="batch-pinyin-special-chars-ratio" name="pinyin_special_chars_ratio" 
                                                               min="0" max="100" value="30" class="w-16 px-1 py-0.5 border rounded text-xs">
                                                        <span class="text-xs">%</span>
                                                    </div>
                                                    
                                                    <div class="text-xs text-gray-500 mt-1">
                                                        <i class="fas fa-info-circle mr-1"></i>
                                                        特殊字符从<a href="/admin/settings/content" class="text-blue-500 underline">全局设置</a>中继承
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 批量全局设置提示 -->
                                        <div id="batch-pinyin-global-hint" class="p-2 bg-blue-50 rounded text-xs text-blue-600" style="display:none;">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            使用全局拼音设置，请在 <a href="/admin/settings/content" class="underline">系统设置-内容注入</a> 中配置
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- SEO优化标签页 -->
                                <div id="batch-tab-content-seo" class="hidden space-y-4">
                                    <p class="text-sm text-gray-500 mb-3">批量添加时，SEO信息可在上方站点信息中逐个指定，或使用内容注入功能自动生成</p>
                                    
                                    <!-- 批量企业名称设置 -->
                                    <div class="border rounded p-3 bg-gray-50">
                                        <label class="flex items-center mb-2">
                                            <input type="checkbox" id="batch-enable-company-name" name="enable_company_name" class="mr-2" onchange="toggleBatchCompanyNameSettings()">
                                            <span class="text-sm font-medium">启用企业名称注入</span>
                                        </label>
                                        <div id="batch-company-name-settings" class="ml-6 space-y-3 hidden">
                                            <div>
                                                <label class="text-xs text-gray-600 block mb-1">选择企业库:</label>
                                                <!-- 复选框列表 -->
                                                <div id="batch-company-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                    <div class="text-gray-500 text-xs">加载企业库中...</div>
                                                </div>
                                            </div>
                                            <div>
                                                <label class="text-xs text-gray-600">或直接输入企业名称:</label>
                                                <input type="text" id="batch-company-name" placeholder="例如：北京科技有限公司"
                                                       class="w-full text-sm border rounded px-2 py-1">
                                                <div class="text-xs text-gray-500 mt-1">注：选择企业库后将自动随机分配企业名称，每个站点将获得不同的企业名称</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 内容注入标签页 -->
                                <div id="batch-tab-content-injection" class="hidden space-y-4">
                                    <div class="grid grid-cols-2 gap-6">
                                        <div class="space-y-3">
                                            <h5 class="text-sm font-medium text-gray-700 border-b pb-1">基础注入</h5>
                                            <!-- 批量关键词注入设置 -->
                                            <div class="border rounded p-2 bg-gray-50">
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" id="batch-enable-keyword" name="enable_keyword" onchange="toggleBatchKeywordsGroup()" class="mr-2">
                                                    <span class="text-sm font-medium">启用关键词注入</span>
                                                </label>
                                                <div id="batch-keywords-group" class="ml-6 space-y-3 hidden">
                                                    <!-- SEO关键词注入设置 -->
                                                    <div class="space-y-3 mt-3 p-3 bg-blue-50 rounded">
                                                        <div class="text-sm font-medium text-blue-700 mb-2">SEO关键词注入设置</div>
                                                        
                                                        <!-- 标题设置 -->
                                                        <div class="p-3 bg-white rounded border border-blue-200">
                                                            <div class="flex items-center justify-between mb-2">
                                                                <label class="flex items-center text-sm font-medium text-gray-700">
                                                                    <input type="checkbox" id="batch-keyword-inject-title" name="keyword_inject_title" checked class="mr-2">
                                                                    <span>注入到标题</span>
                                                                </label>
                                                            </div>
                                                            <div class="ml-6 space-y-2">
                                                                <div>
                                                                    <label class="text-xs text-gray-600">选择标题关键词库:</label>
                                                                    <div id="batch-title-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-gray-50 rounded border max-h-20 overflow-y-auto">
                                                                        <div class="text-gray-500 text-xs">加载中...</div>
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label class="text-xs text-gray-600">标题模板:</label>
                                                                    <input type="text" id="batch-keyword-title-template" name="keyword_title_template" placeholder="{keyword1}-{keyword2}-{original}" 
                                                                           value="{keyword1}-{original}" 
                                                                           class="w-full text-xs border border-gray-300 rounded px-2 py-1 mt-1">
                                                                    <span class="text-xs text-gray-400">可用变量: {keyword1-5}, {original}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- Meta关键词设置 -->
                                                        <div class="p-3 bg-white rounded border border-blue-200">
                                                            <div class="flex items-center justify-between mb-2">
                                                                <label class="flex items-center text-sm font-medium text-gray-700">
                                                                    <input type="checkbox" id="batch-keyword-inject-meta" name="keyword_inject_meta" checked class="mr-2">
                                                                    <span>注入到Meta关键词</span>
                                                                </label>
                                                            </div>
                                                            <div class="ml-6 space-y-2">
                                                                <div>
                                                                    <label class="text-xs text-gray-600">选择Meta关键词库:</label>
                                                                    <div id="batch-meta-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-gray-50 rounded border max-h-20 overflow-y-auto">
                                                                        <div class="text-gray-500 text-xs">加载中...</div>
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label class="text-xs text-gray-600">Meta关键词模板:</label>
                                                                    <input type="text" id="batch-keyword-meta-template" name="keyword_meta_template" placeholder="{keyword1},{keyword2},{keyword3}" 
                                                                           value="{keyword1},{keyword2},{keyword3}" 
                                                                           class="w-full text-xs border border-gray-300 rounded px-2 py-1 mt-1">
                                                                    <span class="text-xs text-gray-400">可用变量: {keyword1-10}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- 描述设置 -->
                                                        <div class="p-3 bg-white rounded border border-blue-200">
                                                            <div class="flex items-center justify-between mb-2">
                                                                <label class="flex items-center text-sm font-medium text-gray-700">
                                                                    <input type="checkbox" id="batch-keyword-inject-desc" name="keyword_inject_desc" checked class="mr-2">
                                                                    <span>注入到描述</span>
                                                                </label>
                                                            </div>
                                                            <div class="ml-6 space-y-2">
                                                                <div>
                                                                    <label class="text-xs text-gray-600">选择描述关键词库:</label>
                                                                    <div id="batch-desc-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-gray-50 rounded border max-h-20 overflow-y-auto">
                                                                        <div class="text-gray-500 text-xs">加载中...</div>
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label class="text-xs text-gray-600">描述模板:</label>
                                                                    <input type="text" id="batch-keyword-desc-template" name="keyword_desc_template" placeholder="{keyword1}是专业的{keyword2}服务商" 
                                                                           value="{keyword1}是专业的{keyword2}服务商" 
                                                                           class="w-full text-xs border border-gray-300 rounded px-2 py-1 mt-1">
                                                                    <span class="text-xs text-gray-400">可用变量: {keyword1-10}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- 正文注入设置 -->
                                                    <div class="mt-3">
                                                        <!-- 正文关键词库选择 -->
                                                        <div class="mb-3">
                                                            <label class="text-xs text-gray-600">选择关键词库（正文注入）:</label>
                                                            <div id="batch-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                                <div class="text-gray-500 text-xs">加载关键词库中...</div>
                                                            </div>
                                                            <textarea id="batch-keywords" name="keywords" class="hidden"></textarea>
                                                        </div>
                                                        
                                                        <label class="text-xs text-gray-600 mb-1 block">正文注入位置:</label>
                                                        <div class="grid grid-cols-2 gap-1 text-xs">
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-keyword-inject-body" name="keyword_inject_body" checked class="mr-1">
                                                                <span>正文段落</span>
                                                            </label>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-keyword-inject-h1" name="keyword_inject_h1" class="mr-1">
                                                                <span>H1标题</span>
                                                            </label>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-keyword-inject-h2" name="keyword_inject_h2" class="mr-1">
                                                                <span>H2标题</span>
                                                            </label>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-keyword-inject-alt" name="keyword_inject_alt" class="mr-1">
                                                                <span>图片Alt</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- 注入参数 -->
                                                    <div>
                                                        <label class="text-xs text-gray-600 mb-1 block">控制参数:</label>
                                                        <div class="grid grid-cols-2 gap-2">
                                                            <div>
                                                                <label class="text-xs text-gray-500">每页数量</label>
                                                                <input type="number" id="batch-keyword-max-per-page" value="10" min="1" max="50"
                                                                       class="w-full text-xs border rounded px-1 py-0.5">
                                                            </div>
                                                            <div>
                                                                <label class="text-xs text-gray-500">注入概率(%)</label>
                                                                <input type="number" id="batch-keyword-inject-ratio" name="keyword_inject_ratio" value="30" min="1" max="100"
                                                                       class="w-full text-xs border rounded px-1 py-0.5">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- 高级选项折叠 -->
                                                    <details class="text-xs">
                                                        <summary class="cursor-pointer text-gray-600 hover:text-gray-800">更多选项...</summary>
                                                        <div class="mt-2 space-y-2">
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-keyword-inject-hidden" name="keyword_inject_hidden" class="mr-1">
                                                                <span>隐藏元素注入</span>
                                                            </label>
                                                            <div class="grid grid-cols-2 gap-2">
                                                                <div>
                                                                    <label class="text-xs text-gray-500">最小字数</label>
                                                                    <input type="number" id="batch-keyword-min-word-count" name="keyword_min_word_count" value="20" min="10" max="200"
                                                                           class="w-full text-xs border rounded px-1 py-0.5">
                                                                </div>
                                                                <div>
                                                                    <label class="text-xs text-gray-500">密度(%)</label>
                                                                    <input type="number" id="batch-keyword-density" name="keyword_density" value="2" min="0" max="10" step="0.5"
                                                                           class="w-full text-xs border rounded px-1 py-0.5">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </details>
                                                </div>
                                            </div>
                                            
                                            <!-- 批量结构注入设置 -->
                                            <div class="border rounded p-2 bg-gray-50">
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" id="batch-enable-structure" name="enable_structure" onchange="toggleBatchStructureSettings()" class="mr-2">
                                                    <span class="text-sm font-medium">启用结构注入</span>
                                                </label>
                                                <div id="batch-structure-settings" class="ml-6 space-y-2 hidden">
                                                    <!-- 结构注入关键词库选择 -->
                                                    <div>
                                                        <label class="text-xs text-gray-600 block mb-1">选择关键词库:</label>
                                                        <!-- 复选框列表 -->
                                                        <div id="batch-structure-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                            <div class="text-gray-500 text-xs">加载关键词库中...</div>
                                                        </div>
                                                    </div>
                                                    <!-- 注入数量范围 -->
                                                    <div class="grid grid-cols-2 gap-2">
                                                        <div>
                                                            <label for="batch-structure-min-per-page" class="text-xs text-gray-600">最少注入数量:</label>
                                                            <input type="number" id="batch-structure-min-per-page" name="structure_min_per_page" value="10" min="1" max="100" class="w-full text-xs border rounded px-1 py-0.5">
                                                        </div>
                                                        <div>
                                                            <label for="batch-structure-max-per-page" class="text-xs text-gray-600">最多注入数量:</label>
                                                            <input type="number" id="batch-structure-max-per-page" name="structure_max_per_page" value="20" min="1" max="100" class="w-full text-xs border rounded px-1 py-0.5">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 批量伪原创设置 -->
                                            <div class="border rounded p-2 bg-gray-50">
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" id="batch-enable-pseudo" name="enable_pseudo" class="mr-2" onchange="toggleBatchPseudoSettings()">
                                                    <span class="text-sm font-medium">启用伪原创（对body内容生效）</span>
                                                </label>
                                                <div id="batch-pseudo-settings" class="ml-6 space-y-2 hidden">
                                                    <label class="text-xs text-gray-600">选择伪原创词库:</label>
                                                    <div id="batch-pseudo-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                        <div class="text-gray-500 text-xs">加载词库中...</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="space-y-3">
                                            <h5 class="text-sm font-medium text-gray-700 border-b pb-1">高级注入</h5>
                                            <div class="space-y-2">
                                                <!-- Unicode转码设置 -->
                                                <div class="border rounded p-2 bg-gray-50">
                                                    <label class="flex items-center mb-2">
                                                        <input type="checkbox" id="batch-enable-unicode" name="enable_unicode" class="mr-2" onchange="toggleBatchUnicodeOptions()">
                                                        <span class="text-sm font-medium">启用Unicode转码</span>
                                                    </label>
                                                    <div id="batch-unicode-options" class="ml-6 space-y-2 hidden">
                                                        <div class="space-y-1">
                                                            <label class="text-xs text-gray-600">应用范围:</label>
                                                            <select id="batch-unicode-scope" name="unicode_scope" class="w-full text-sm border rounded px-2 py-1">
                                                                <option value="homepage">仅首页</option>
                                                                <option value="all">全站</option>
                                                            </select>
                                                        </div>
                                                        <div class="space-y-1">
                                                            <label class="text-xs text-gray-600">转码选项:</label>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-enable-unicode-title" name="enable_unicode_title" class="mr-1">
                                                                <span class="text-xs">标题</span>
                                                            </label>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-enable-unicode-keywords" name="enable_unicode_keywords" class="mr-1">
                                                                <span class="text-xs">关键词</span>
                                                            </label>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-enable-unicode-desc" name="enable_unicode_desc" class="mr-1">
                                                                <span class="text-xs">描述</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 批量随机字符串注入设置 -->
                                            <div class="border rounded p-2 bg-gray-50">
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" id="batch-enable-random-string" name="enable_random_string" class="mr-2" onchange="toggleBatchRandomStringSettings()">
                                                    <span class="text-sm font-medium">启用随机字符串注入</span>
                                                </label>
                                                <div id="batch-random-string-settings" class="ml-6 space-y-2 hidden">
                                                    <label class="text-xs text-gray-600">随机字符串长度:</label>
                                                    <input type="number" id="batch-random-string-length" name="random_string_length" min="3" max="10" value="4"
                                                           class="w-full text-xs border rounded px-1 py-0.5"
                                                           placeholder="建议4-6位">
                                                </div>
                                            </div>
                                            
                                            <!-- 批量H1标签注入设置（仅首页） -->
                                            <div class="border rounded p-2 bg-gray-50">
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" id="batch-enable-h1-tag" name="enable_h1_tag" class="mr-2" onchange="toggleBatchH1TagSettings()">
                                                    <span class="text-sm font-medium">仅首页插入H1标签</span>
                                                </label>
                                                <div id="batch-h1-tag-settings" class="ml-6 space-y-2 hidden">
                                                    <div class="text-xs text-gray-500 mb-2">
                                                        将使用"首页SEO设置"中的标题作为H1标签内容
                                                    </div>
                                                    <div>
                                                        <label class="text-xs text-gray-600">注入位置:</label>
                                                        <select id="batch-h1-tag-position" name="h1_tag_position"
                                                                class="w-full text-xs border rounded px-1 py-0.5">
                                                            <option value="both">顶部和底部</option>
                                                            <option value="top">仅顶部</option>
                                                            <option value="bottom">仅底部</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 批量隐藏HTML注入设置 -->
                                            <div class="border rounded p-2 bg-gray-50">
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" id="batch-enable-hidden-html" name="enable_hidden_html" class="mr-2" onchange="toggleBatchHiddenHTMLSettings()">
                                                    <span class="text-sm font-medium">启用隐藏HTML注入</span>
                                                </label>
                                                <div id="batch-hidden-html-settings" class="ml-6 space-y-2 hidden">
                                                    <div class="grid grid-cols-2 gap-2">
                                                        <div>
                                                            <label class="text-xs text-gray-600">元素数量:</label>
                                                            <input type="number" id="batch-hidden-html-length" name="hidden_html_length" min="10" max="200" value="50"
                                                                   class="w-full text-xs border rounded px-1 py-0.5"
                                                                   placeholder="10-200个">
                                                        </div>
                                                        <div>
                                                            <label class="text-xs text-gray-600">注入位置:</label>
                                                            <select id="batch-hidden-html-position" name="hidden_html_position"
                                                                    class="w-full text-xs border rounded px-1 py-0.5">
                                                                <option value="both">顶部和底部</option>
                                                                <option value="top">仅顶部</option>
                                                                <option value="bottom">仅底部</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <label class="flex items-center">
                                                        <input type="checkbox" id="batch-hidden-html-random-id" name="hidden_html_random_id" checked class="mr-1">
                                                        <span class="text-xs">使用随机ID</span>
                                                    </label>
                                                </div>
                                            </div>
                                            
                                            <!-- 批量首页关键词注入设置 -->
                                            <div class="border rounded p-2 bg-gray-50">
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" id="batch-enable-home-keyword-inject" name="enable_home_keyword_inject" onchange="toggleBatchHomeKeywordSettings()" class="mr-2">
                                                    <span class="text-sm font-medium">启用首页关键词注入</span>
                                                </label>
                                                <div id="batch-home-keyword-settings" class="ml-6 space-y-2 hidden">
                                                    <div class="grid grid-cols-2 gap-2">
                                                        <div>
                                                            <label class="text-xs text-gray-600">注入数量:</label>
                                                            <input type="number" id="batch-home-keyword-inject-count" name="home_keyword_inject_count" min="1" max="100" value="10"
                                                                   class="w-full text-xs border rounded px-1 py-0.5"
                                                                   placeholder="1-100个">
                                                        </div>
                                                        <div>
                                                            <label class="flex items-center">
                                                                <input type="checkbox" id="batch-enable-home-keyword-unicode" name="enable_home_keyword_unicode" class="mr-1">
                                                                <span class="text-xs">Unicode转码</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="mt-2">
                                                        <label class="text-xs text-gray-600 block mb-1">选择关键词库:</label>
                                                        <!-- 复选框列表 -->
                                                        <div id="batch-home-keyword-libraries" class="mt-1 flex flex-wrap gap-x-4 gap-y-2 p-2 bg-white rounded border max-h-32 overflow-y-auto">
                                                            <div class="text-gray-500 text-xs">加载关键词库中...</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 安全设置标签页 -->
                                <div id="batch-tab-content-security" class="hidden space-y-4">
                                    <div class="grid grid-cols-2 gap-6">
                                        <!-- 左侧列 -->
                                        <div class="space-y-4">
                                            <!-- 站点UA判断 -->
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                                    <i class="fas fa-user-shield text-blue-500 mr-2"></i>站点UA判断
                                                </h5>
                                                <p class="text-xs text-gray-500 mb-3">选择此站点的UA判断策略</p>
                                                <select id="batch-ua_check_mode" name="ua_check_mode" class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="toggleBatchUASettingsMode()">
                                                    <option value="global">使用全局设置</option>
                                                    <option value="enable">独立开启UA判断</option>
                                                    <option value="disable">独立关闭UA判断</option>
                                                </select>
                                                <div id="batch-ua-settings-mode" class="hidden mt-3 space-y-2">
                                                    <input type="text" id="batch-allowed-ua-mode" name="allowed_ua" 
                                                           class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                           placeholder="允许的UA关键词（如：google|baidu）">
                                                    <!-- 标签说明 -->
                                                    <div class="bg-blue-50 border border-blue-200 rounded p-2">
                                                        <p class="text-xs font-semibold text-blue-900 mb-1">支持的标签：</p>
                                                        <div class="grid grid-cols-2 gap-1 text-xs text-blue-700">
                                                            <div><code class="bg-white px-1 rounded">{title}</code> - 首页标题</div>
                                                            <div><code class="bg-white px-1 rounded">{description}</code> - 首页描述</div>
                                                            <div><code class="bg-white px-1 rounded">{keywords}</code> - 首页关键词</div>
                                                            <div><code class="bg-white px-1 rounded">{analytics}</code> - 统计JS</div>
                                                            <div><code class="bg-white px-1 rounded">{company}</code> - 企业名称</div>
                                                            <div><code class="bg-white px-1 rounded">{domain}</code> - 当前域名</div>
                                                            <div><code class="bg-white px-1 rounded">{year}</code> - 当前年份</div>
                                                            <div><code class="bg-white px-1 rounded">{date}</code> - 当前日期</div>
                                                        </div>
                                                    </div>
                                                    <textarea id="batch-non-spider-html-mode" name="non_spider_html" rows="4"
                                                              class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                              placeholder="非爬虫访问时显示的HTML（支持上述标签）"></textarea>
                                                </div>
                                            </div>
                                            
                                            <!-- 来源判断设置 -->
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                                    <i class="fas fa-link text-purple-500 mr-2"></i>来源判断
                                                </h5>
                                                <div class="space-y-2">
                                                    <select id="batch-referer-check-mode" name="referer_check_mode" class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="toggleBatchRefererSettings()">
                                                        <option value="global">使用全局设置</option>
                                                        <option value="enable">独立设置</option>
                                                        <option value="disable">关闭</option>
                                                    </select>
                                                    <div id="batch-referer-settings" class="hidden space-y-2 mt-3">
                                                        <input type="text" id="batch-allowed-referers" name="allowed_referers" 
                                                               class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                               placeholder="允许的来源域名（如：*.google.com|www.baidu.com）">
                                                        <div class="grid grid-cols-2 gap-2">
                                                            <input type="number" id="batch-referer-block-code" name="referer_block_code" 
                                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                                   placeholder="状态码（默认403）" min="100" max="599" value="403">
                                                            <span class="text-xs text-gray-500 self-center">拒绝状态码</span>
                                                        </div>
                                                        <textarea id="batch-referer-block-html" name="referer_block_html" rows="3"
                                                                  class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                                  placeholder="拒绝时显示的HTML（支持 {domain}, {referer}, {ip} 标签）"></textarea>
                                                    </div>
                                                    <p class="text-xs text-gray-500">控制允许哪些来源域名访问此站点</p>
                                                </div>
                                            </div>
                                            
                                            <!-- 域名跳转设置 -->
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                                    <i class="fas fa-directions text-green-500 mr-2"></i>域名跳转
                                                </h5>
                                                <div class="space-y-2">
                                                    <label class="block text-sm text-gray-700">@ 跳转到 www</label>
                                                    <select id="batch-redirect-www" name="redirect_www" class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                        <option value="">使用全局设置</option>
                                                        <option value="true">启用</option>
                                                        <option value="false">禁用</option>
                                                    </select>
                                                    <p class="text-xs text-gray-500">当用户访问 example.com 时自动跳转到 www.example.com</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 右侧列 -->
                                        <div class="space-y-4">
                                            <!-- 爬虫屏蔽设置 -->
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                                    <i class="fas fa-shield-alt text-red-500 mr-2"></i>爬虫屏蔽设置
                                                </h5>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="batch-enable-spider-block" name="enable_spider_block" class="mr-2" onchange="toggleBatchSpiderBlockSettings()">
                                                    <span class="text-sm">启用爬虫屏蔽</span>
                                                </label>
                                                <div id="batch-spider-block-settings" class="hidden mt-3 space-y-2">
                                                    <label class="flex items-center mb-2">
                                                        <input type="checkbox" id="batch-use-global-spider-ua" name="use_global_spider_ua" checked class="mr-2" onchange="toggleBatchSpiderUAInput()">
                                                        <span class="text-sm text-gray-600">使用全局爬虫UA规则</span>
                                                    </label>
                                                    <input type="text" id="batch-spider-ua" name="custom_spider_ua" disabled
                                                           class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm disabled:bg-gray-100 disabled:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                           placeholder="自定义爬虫UA（如：google|baidu|bing）">
                                                    <textarea id="batch-spider-block-403-template" name="spider_block_403_template" rows="3"
                                                              class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                              placeholder="被屏蔽时显示的内容（可选）"></textarea>
                                                </div>
                                            </div>
                                            
                                            <!-- Sitemap设置 -->
                                            <div class="bg-gray-50 rounded-lg p-4">
                                                <h5 class="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2 mb-3">
                                                    <i class="fas fa-sitemap text-orange-500 mr-2"></i>Sitemap设置
                                                </h5>
                                                <label class="flex items-center">
                                                    <input type="checkbox" id="batch-enable-sitemap" name="enable_sitemap" class="mr-2" checked onchange="toggleBatchSitemapSettings()">
                                                    <span class="text-sm">启用Sitemap</span>
                                                </label>
                                                <div id="batch-sitemap-settings" class="hidden mt-3 space-y-3">
                                                    <div class="grid grid-cols-2 gap-3">
                                                        <div>
                                                            <label class="block text-xs text-gray-600 mb-1">更新间隔（分钟）</label>
                                                            <input type="number" id="batch-sitemap-update-interval" name="sitemap_update_interval" value="60"
                                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                                   placeholder="60">
                                                        </div>
                                                        <div>
                                                            <label class="block text-xs text-gray-600 mb-1">优先级</label>
                                                            <input type="number" id="batch-sitemap-priority" name="sitemap_priority" value="0.8" min="0" max="1" step="0.1"
                                                                   class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                                   placeholder="0.5">
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <label class="block text-xs text-gray-600 mb-1">更新频率</label>
                                                        <select id="batch-sitemap-changefreq" name="sitemap_changefreq"
                                                                class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                            <option value="always">always</option>
                                                            <option value="hourly">hourly</option>
                                                            <option value="daily" selected>daily</option>
                                                            <option value="weekly">weekly</option>
                                                            <option value="monthly">monthly</option>
                                                            <option value="yearly">yearly</option>
                                                            <option value="never">never</option>
                                                        </select>
                                                    </div>
                                                    <div>
                                                        <label class="block text-xs text-gray-600 mb-1">最大URL数量</label>
                                                        <input type="number" id="batch-sitemap-max-urls" name="sitemap_max_urls" value="50000"
                                                               class="w-full border border-gray-300 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                               placeholder="50000" min="100" max="50000">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- 添加状态列表 -->
                <div id="batch-status-list" class="bg-white rounded-lg shadow" style="display:none;">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">
                            <i class="fas fa-tasks text-green-500 mr-2"></i>添加状态
                        </h3>
                        
                        <!-- 进度条 -->
                        <div id="batch-progress-container" class="mb-4">
                            <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                                <span>进度: <span id="batch-progress-current">0</span> / <span id="batch-progress-total">0</span></span>
                                <span>成功: <span id="batch-success-count" class="text-green-600 font-semibold">0</span> | 失败: <span id="batch-error-count" class="text-red-600 font-semibold">0</span></span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div id="batch-progress-bar" class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div id="batch-status-content" class="space-y-2 max-h-96 overflow-y-auto">
                            <!-- 状态列表将在这里动态显示 -->
                        </div>
                    </div>
                </div>

            </main>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div id="toast-container" class="fixed top-4 right-4 z-50"></div>

    <!-- 加载 JavaScript -->
    <script src="/static/js/common.js?v=2.0.0"></script>
    <script src="/static/js/menu-groups.js?v=1756618802.0.0"></script>
    <script src="/static/js/batch-add.js?v=1.3.2"></script>
</body>
</html>