/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    color: #333;
    background-color: #f5f5f5;
    line-height: 1.6;
}

/* 工具类 */
.loading {
    text-align: center;
    padding: 40px;
    color: #999;
}

.empty {
    text-align: center;
    padding: 40px;
    color: #999;
}

.required {
    color: #e74c3c;
}

.help-text {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn:hover {
    opacity: 0.8;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    color: #fff;
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.btn-secondary {
    color: #333;
    background-color: #f8f9fa;
    border-color: #ddd;
}

.btn-danger {
    color: #fff;
    background-color: #f44336;
    border-color: #f44336;
}

.btn-warning {
    color: #333;
    background-color: #ff9800;
    border-color: #ff9800;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-icon {
    padding: 4px 8px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    transition: color 0.3s;
}

.btn-icon:hover {
    color: #333;
}

.btn-icon.danger {
    color: #f44336;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="url"],
.form-group input[type="number"],
.form-group input[type="password"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
}

fieldset {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

legend {
    padding: 0 10px;
    font-weight: 600;
    color: #333;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
}

table th,
table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
}

table tr:hover {
    background-color: #f8f9fa;
}

table .actions {
    text-align: center;
    white-space: nowrap;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-top: 20px;
}

.pagination button,
.pagination span {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background-color: #fff;
    color: #333;
    cursor: pointer;
    border-radius: 4px;
}

.pagination button:hover {
    background-color: #f8f9fa;
}

.pagination .current {
    background-color: #4CAF50;
    color: #fff;
    border-color: #4CAF50;
}

/* 状态徽章 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-badge.processing {
    background-color: #cce5ff;
    color: #004085;
}

.status-badge.completed {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.cancelled {
    background-color: #e2e3e5;
    color: #383d41;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #888;
    border-radius: 8px;
    width: 600px;
    max-width: 90%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-content.large {
    width: 900px;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.modal-footer button {
    margin-left: 10px;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: #000;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background-color: #4CAF50;
    transition: width 0.3s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 500;
}

/* 图标 */
[class^="icon-"], [class*=" icon-"] {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 5px;
}

/* Toast 提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 24px;
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 2000;
    animation: slideIn 0.3s ease;
}

.toast.success {
    background-color: #4CAF50;
}

.toast.error {
    background-color: #f44336;
}

.toast.warning {
    background-color: #ff9800;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}