// 会话超时管理 - 简化版
(function() {
    
    // 配置（可以根据需要修改）
    const CONFIG = {
        TIMEOUT_SECONDS: 1800,           // 超时时间（秒） - 2分钟
        WARNING_BEFORE_SECONDS: 300,     // 提前警告时间（秒） - 1分钟
    };
    
    // 如果需要使用分钟配置，可以这样设置：
    // CONFIG.TIMEOUT_SECONDS = 30 * 60;  // 30分钟
    // CONFIG.WARNING_BEFORE_SECONDS = 5 * 60;  // 5分钟

    let lastActivityTime = Date.now();
    let warningShown = false;
    let timeoutTimer = null;
    let warningTimer = null;

    // 创建并显示警告
    function showWarning() {
        if (warningShown) {
            return;
        }
        
        // 先检查是否已存在
        if (document.getElementById('my-session-warning')) {
            return;
        }
        
        warningShown = true;
        
        // 创建样式
        if (!document.getElementById('session-warning-styles')) {
            const style = document.createElement('style');
            style.id = 'session-warning-styles';
            style.textContent = `
                #my-session-warning-overlay {
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    bottom: 0 !important;
                    background: rgba(0, 0, 0, 0.6) !important;
                    z-index: 999999 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }
                #my-session-warning {
                    background: #FEF3C7 !important;
                    border: 3px solid #F59E0B !important;
                    border-radius: 12px !important;
                    padding: 24px !important;
                    box-shadow: 0 20px 50px rgba(0,0,0,0.5) !important;
                    max-width: 450px !important;
                    min-width: 400px !important;
                }
            `;
            document.head.appendChild(style);
        }
        
        // 创建HTML结构
        const overlay = document.createElement('div');
        overlay.id = 'my-session-warning-overlay';
        
        const warningBox = document.createElement('div');
        warningBox.id = 'my-session-warning';
        
        // 计算剩余秒数
        const remainingSeconds = CONFIG.WARNING_BEFORE_SECONDS;
        
        warningBox.innerHTML = `
            <div style="display: flex; align-items: flex-start;">
                <div style="margin-right: 16px;">
                    <svg width="32" height="32" style="color: #F59E0B;" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div style="flex: 1;">
                    <h2 style="margin: 0 0 12px 0; font-size: 20px; font-weight: bold; color: #92400E;">
                        会话即将超时
                    </h2>
                    <p style="margin: 0 0 8px 0; color: #78350F; font-size: 15px;">
                        由于长时间未操作，您的会话将在 
                        <strong id="warning-countdown" style="color: #DC2626; font-size: 18px;">
                            ${Math.floor(remainingSeconds/60)}:${(remainingSeconds%60).toString().padStart(2,'0')}
                        </strong> 
                        后自动退出。
                    </p>
                    <p style="margin: 0 0 16px 0; color: #78350F; font-size: 14px;">
                        请点击下方按钮延长会话时间。
                    </p>
                    <div style="display: flex; gap: 12px;">
                        <button onclick="window.extendSession()" style="
                            padding: 10px 24px;
                            background: #10B981;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            font-size: 15px;
                            font-weight: bold;
                            cursor: pointer;
                            transition: background 0.3s;
                        " onmouseover="this.style.background='#059669'" onmouseout="this.style.background='#10B981'">
                            延长会话
                        </button>
                        <button onclick="window.logoutNow()" style="
                            padding: 10px 24px;
                            background: #EF4444;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            font-size: 15px;
                            font-weight: bold;
                            cursor: pointer;
                            transition: background 0.3s;
                        " onmouseover="this.style.background='#DC2626'" onmouseout="this.style.background='#EF4444'">
                            立即退出
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        overlay.appendChild(warningBox);
        
        // 添加到页面
        document.body.appendChild(overlay);
        
        // 启动倒计时
        startCountdown(remainingSeconds);
    }

    // 倒计时
    function startCountdown(seconds) {
        let remaining = seconds;
        const timer = setInterval(() => {
            remaining--;
            if (remaining <= 0) {
                clearInterval(timer);
                return;
            }
            
            const el = document.getElementById('warning-countdown');
            if (el) {
                const mins = Math.floor(remaining / 60);
                const secs = remaining % 60;
                el.textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
                
                if (remaining <= 30) {
                    el.style.fontSize = '20px';
                    el.style.color = '#DC2626';
                }
            } else {
                clearInterval(timer);
            }
        }, 1000);
    }

    // 处理超时
    async function handleTimeout() {
        // 显示超时提示
        showTimeoutMessage();
        
        // 等待2秒让用户看到提示
        setTimeout(async () => {
            try {
                // 调用退出API
                await fetch('/api/v1/auth/logout', {
                    method: 'POST',
                    credentials: 'same-origin'
                });
            } catch (error) {
                // 忽略错误
            }
            
            // 跳转到登录页
            window.location.href = '/login';
        }, 2000);
    }
    
    // 显示超时消息
    function showTimeoutMessage() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 999999;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        overlay.innerHTML = `
            <div style="
                background: white;
                border-radius: 12px;
                padding: 32px;
                text-align: center;
                max-width: 400px;
            ">
                <div style="color: #EF4444; font-size: 48px; margin-bottom: 16px;">
                    <i class="fas fa-clock"></i>
                </div>
                <h2 style="font-size: 24px; font-weight: bold; color: #1F2937; margin: 0 0 12px 0;">
                    会话已超时
                </h2>
                <p style="color: #6B7280; font-size: 16px; margin: 0 0 8px 0;">
                    由于长时间未操作，您的会话已过期
                </p>
                <p style="color: #9CA3AF; font-size: 14px;">
                    正在跳转到登录页面...
                </p>
                <div style="margin-top: 20px;">
                    <div style="
                        display: inline-block;
                        width: 30px;
                        height: 30px;
                        border: 3px solid #E5E7EB;
                        border-top-color: #3B82F6;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    "></div>
                </div>
            </div>
            <style>
                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
            </style>
        `;
        
        document.body.appendChild(overlay);
    }

    // 重置计时器
    function resetTimers() {
        if (timeoutTimer) clearTimeout(timeoutTimer);
        if (warningTimer) clearTimeout(warningTimer);
        
        const timeoutMs = CONFIG.TIMEOUT_SECONDS * 1000;
        const warningMs = (CONFIG.TIMEOUT_SECONDS - CONFIG.WARNING_BEFORE_SECONDS) * 1000;
        
        // 设置警告计时器
        warningTimer = setTimeout(() => {
            showWarning();
        }, warningMs);
        
        // 设置超时计时器
        timeoutTimer = setTimeout(() => {
            handleTimeout();
        }, timeoutMs);
    }

    // 用户活动
    function updateActivity(force = false) {
        if (warningShown && !force) {
            return;
        }
        
        const now = Date.now();
        if (now - lastActivityTime < 5000 && !force) {
            return;
        }
        
        lastActivityTime = now;
        
        if (force) {
            // 移除警告
            const overlay = document.getElementById('my-session-warning-overlay');
            if (overlay) {
                overlay.remove();
            }
            warningShown = false;
        }
        
        resetTimers();
    }

    // 全局函数
    window.extendSession = function() {
        updateActivity(true);
    };
    
    window.logoutNow = async function() {
        try {
            // 先调用退出API清除会话
            await fetch('/api/v1/auth/logout', {
                method: 'POST',
                credentials: 'same-origin'
            });
        } catch (error) {
            // 忽略错误
        }
        
        // 无论成功与否都跳转到登录页
        window.location.href = '/login';
    };
    
    window.closeWarning = function() {
        const overlay = document.getElementById('my-session-warning-overlay');
        if (overlay) {
            overlay.remove();
        }
    };

    // 初始化
    function init() {
        // 检查路径
        if (window.location.pathname === '/login' || 
            window.location.pathname === '/' ||
            !window.location.pathname.includes('/admin')) {
            return;
        }
        
        // 绑定事件
        ['click', 'keypress', 'mousedown'].forEach(event => {
            document.addEventListener(event, () => {
                if (!warningShown) {
                    updateActivity();
                }
            });
        });
        
        // 启动计时器
        resetTimers();
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();