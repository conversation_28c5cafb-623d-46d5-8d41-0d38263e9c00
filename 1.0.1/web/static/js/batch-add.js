// 批量添加站点页面的JavaScript
// 版本：1.3.2
// 更新：2025-09-05 - 设置部分选项默认选中

// 全局变量
let keywordLibraries = [];
let pseudoLibraries = [];
let companyLibraries = [];
let siteCategories = [];
let selectedBatchKeywordLibs = [];
let selectedBatchPseudoLibs = [];
let selectedBatchStructureLibs = [];
let selectedBatchTitleLibs = [];
let selectedBatchMetaLibs = [];
let selectedBatchDescLibs = [];
let selectedBatchHomeKeywordLibs = [];

// 页面初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 使用 Promise.all 并行加载所有数据，减少页面重绘次数
    await Promise.all([
        loadKeywordLibraries(),
        loadPseudoLibraries(),
        loadCompanyLibraries(),
        loadSiteCategories()
    ]);
    
    // 初始化表单
    initBatchAddForm();
    
    // 初始化拼音设置显示
    toggleBatchPinyinMode();
    
    // 初始化Sitemap设置显示（因为默认选中）
    toggleBatchSitemapSettings();
    
    // 更新站点数量
    updateBatchCount();
    
    // 更新系统时间（改为10秒更新一次，进一步减少闪烁）
    updateSystemTime();
    setInterval(updateSystemTime, 10000);
    
    // 生成侧边栏菜单
    if (typeof generateMenuHTML === 'function') {
        const nav = document.querySelector('nav');
        if (nav) {
            nav.innerHTML = generateMenuHTML();
            // 设置当前页面激活状态
            const currentMenuItem = document.querySelector('[data-page="batch-add"]');
            if (currentMenuItem) {
                currentMenuItem.classList.add('bg-gray-700');
            }
        }
    }
});

// 更新系统时间（优化：只在时间变化时更新DOM）
function updateSystemTime() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN');
    const timeElement = document.getElementById('system-time');
    if (timeElement && timeElement.textContent !== timeStr) {
        // 使用 requestAnimationFrame 确保在浏览器重绘前更新
        requestAnimationFrame(() => {
            timeElement.textContent = timeStr;
        });
    }
}

// 切换侧边栏
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
        sidebar.classList.toggle('hidden');
    }
}

// 切换用户菜单
function toggleUserMenu() {
    const menu = document.getElementById('user-menu');
    if (menu) {
        menu.classList.toggle('hidden');
    }
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        fetch('/api/v1/auth/logout', {
            method: 'POST'
        }).then(() => {
            window.location.href = '/admin/login';
        });
    }
}

// 加载关键词库
async function loadKeywordLibraries() {
    try {
        const response = await fetch('/api/v1/keywords/libraries');
        const data = await response.json();
        if (data.success && data.data) {
            keywordLibraries = data.data;
            updateBatchKeywordLibraryCheckboxes();
        }
    } catch (error) {
        console.error('加载关键词库失败:', error);
    }
}

// 加载伪原创词库
async function loadPseudoLibraries() {
    try {
        const response = await fetch('/api/v1/pseudo/libraries');
        const data = await response.json();
        if (data.success && data.data) {
            pseudoLibraries = data.data;
            updateBatchPseudoLibraryCheckboxes();
        }
    } catch (error) {
        console.error('加载伪原创词库失败:', error);
    }
}

// 加载企业名称库
async function loadCompanyLibraries() {
    try {
        const response = await fetch('/api/v1/company/libraries');
        const data = await response.json();
        if (data.success && data.data) {
            companyLibraries = data.data;
            updateBatchCompanyLibrarySelect();
        }
    } catch (error) {
        console.error('加载企业名称库失败:', error);
    }
}

// 加载站点分类
async function loadSiteCategories() {
    try {
        const response = await fetch('/api/v1/site-categories');
        const data = await response.json();
        if (data.success && data.data) {
            siteCategories = data.data;
            updateBatchCategorySelect();
        }
    } catch (error) {
        console.error('加载站点分类失败:', error);
    }
}

// 更新批量添加的关键词库复选框
function updateBatchKeywordLibraryCheckboxes() {
    // 使用 DocumentFragment 减少重绘
    const fragment = document.createDocumentFragment();
    
    // 正文关键词库
    const bodyContainer = document.getElementById('batch-keyword-libraries');
    if (bodyContainer) {
        // 先创建HTML字符串
        const html = keywordLibraries.map(lib => `
            <label class="flex items-center">
                <input type="checkbox" value="${lib.id}" class="batch-keyword-lib mr-1" 
                       onchange="onBatchKeywordLibraryChange()">
                <span class="text-xs">${lib.name}</span>
            </label>
        `).join('') || '<div class="text-gray-500 text-xs">暂无关键词库</div>';
        
        // 使用 requestAnimationFrame 确保在下一帧渲染
        requestAnimationFrame(() => {
            bodyContainer.innerHTML = html;
        });
    }
    
    // 标题关键词库
    const titleContainer = document.getElementById('batch-title-keyword-libraries');
    if (titleContainer) {
        const html = keywordLibraries.map(lib => `
            <label class="flex items-center">
                <input type="checkbox" value="${lib.id}" class="batch-title-keyword-lib mr-1" 
                       onchange="onBatchTitleKeywordLibraryChange()">
                <span class="text-xs">${lib.name}</span>
            </label>
        `).join('') || '<div class="text-gray-500 text-xs">暂无关键词库</div>';
        
        requestAnimationFrame(() => {
            titleContainer.innerHTML = html;
        });
    }
    
    // Meta关键词库
    const metaContainer = document.getElementById('batch-meta-keyword-libraries');
    if (metaContainer) {
        const html = keywordLibraries.map(lib => `
            <label class="flex items-center">
                <input type="checkbox" value="${lib.id}" class="batch-meta-keyword-lib mr-1" 
                       onchange="onBatchMetaKeywordLibraryChange()">
                <span class="text-xs">${lib.name}</span>
            </label>
        `).join('') || '<div class="text-gray-500 text-xs">暂无关键词库</div>';
        
        requestAnimationFrame(() => {
            metaContainer.innerHTML = html;
        });
    }
    
    // 描述关键词库
    const descContainer = document.getElementById('batch-desc-keyword-libraries');
    if (descContainer) {
        const html = keywordLibraries.map(lib => `
            <label class="flex items-center">
                <input type="checkbox" value="${lib.id}" class="batch-desc-keyword-lib mr-1" 
                       onchange="onBatchDescKeywordLibraryChange()">
                <span class="text-xs">${lib.name}</span>
            </label>
        `).join('') || '<div class="text-gray-500 text-xs">暂无关键词库</div>';
        
        requestAnimationFrame(() => {
            descContainer.innerHTML = html;
        });
    }
    
    // 结构注入关键词库复选框
    const structureContainer = document.getElementById('batch-structure-libraries');
    if (structureContainer) {
        structureContainer.innerHTML = keywordLibraries.map(lib => `
            <label class="flex items-center">
                <input type="checkbox" value="${lib.id}" class="batch-structure-keyword-lib mr-1" 
                       onchange="onBatchStructureLibraryChange()">
                <span class="text-xs">${lib.name}</span>
            </label>
        `).join('') || '<div class="text-gray-500 text-xs">暂无关键词库</div>';
    }
    
    // 首页关键词库复选框
    const homeContainer = document.getElementById('batch-home-keyword-libraries');
    if (homeContainer) {
        homeContainer.innerHTML = keywordLibraries.map(lib => `
            <label class="flex items-center">
                <input type="checkbox" value="${lib.id}" class="batch-home-keyword-lib mr-1" 
                       onchange="onBatchHomeKeywordLibraryChange()">
                <span class="text-xs">${lib.name}</span>
            </label>
        `).join('') || '<div class="text-gray-500 text-xs">暂无关键词库</div>';
    }
}

// 更新批量添加的伪原创词库复选框
function updateBatchPseudoLibraryCheckboxes() {
    const container = document.getElementById('batch-pseudo-libraries');
    if (container) {
        container.innerHTML = pseudoLibraries.map(lib => `
            <label class="flex items-center">
                <input type="checkbox" value="${lib.id}" class="batch-pseudo-lib mr-1" 
                       onchange="onBatchPseudoLibraryChange()">
                <span class="text-xs">${lib.name}</span>
            </label>
        `).join('') || '<div class="text-gray-500 text-xs">暂无伪原创词库</div>';
    }
}

// 更新批量添加的企业库复选框
function updateBatchCompanyLibrarySelect() {
    const container = document.getElementById('batch-company-libraries');
    if (container) {
        container.innerHTML = companyLibraries.map(lib => `
            <label class="flex items-center">
                <input type="checkbox" value="${lib.id}" class="batch-company-lib mr-1" 
                       onchange="onBatchCompanyLibraryChange()">
                <span class="text-xs">${lib.name}</span>
            </label>
        `).join('') || '<div class="text-gray-500 text-xs">暂无企业库</div>';
    }
}

// 更新批量添加的分类下拉
function updateBatchCategorySelect() {
    const select = document.getElementById('batch-category-id');
    if (select) {
        select.innerHTML = '<option value="">选择分类</option>' +
            siteCategories.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('');
    }
}

// 批量关键词库选择变化
function onBatchKeywordLibraryChange() {
    selectedBatchKeywordLibs = Array.from(document.querySelectorAll('.batch-keyword-lib:checked'))
        .map(cb => parseInt(cb.value));
}

function onBatchTitleKeywordLibraryChange() {
    selectedBatchTitleLibs = Array.from(document.querySelectorAll('.batch-title-keyword-lib:checked'))
        .map(cb => parseInt(cb.value));
}

function onBatchMetaKeywordLibraryChange() {
    selectedBatchMetaLibs = Array.from(document.querySelectorAll('.batch-meta-keyword-lib:checked'))
        .map(cb => parseInt(cb.value));
}

function onBatchDescKeywordLibraryChange() {
    selectedBatchDescLibs = Array.from(document.querySelectorAll('.batch-desc-keyword-lib:checked'))
        .map(cb => parseInt(cb.value));
}

// 结构注入关键词库选择变化
function onBatchStructureLibraryChange() {
    selectedBatchStructureLibs = Array.from(document.querySelectorAll('.batch-structure-keyword-lib:checked'))
        .map(cb => parseInt(cb.value));
}

// 首页关键词库选择变化
function onBatchHomeKeywordLibraryChange() {
    selectedBatchHomeKeywordLibs = Array.from(document.querySelectorAll('.batch-home-keyword-lib:checked'))
        .map(cb => parseInt(cb.value));
}

// 批量伪原创库选择变化
function onBatchPseudoLibraryChange() {
    selectedBatchPseudoLibs = Array.from(document.querySelectorAll('.batch-pseudo-lib:checked'))
        .map(cb => parseInt(cb.value));
}

// 批量企业库选择变化
function onBatchCompanyLibraryChange() {
    const checkedLibs = document.querySelectorAll('.batch-company-lib:checked');
    const nameInput = document.getElementById('batch-company-name');
    if (nameInput) {
        if (checkedLibs.length > 0) {
            nameInput.disabled = true;
            nameInput.placeholder = '将自动从企业库随机分配';
        } else {
            nameInput.disabled = false;
            nameInput.placeholder = '例如：北京科技有限公司';
        }
    }
}

// 切换批量添加标签页
function switchBatchTab(tabName) {
    const tabs = ['batch-seo', 'batch-basic', 'batch-injection', 'batch-security'];
    tabs.forEach(tab => {
        const tabBtn = document.getElementById(`batch-tab-${tab.replace('batch-', '')}`);
        const tabContent = document.getElementById(`batch-tab-content-${tab.replace('batch-', '')}`);
        
        if (tab === tabName) {
            if (tabBtn) {
                tabBtn.classList.add('text-blue-600', 'border-b-2', 'border-blue-600');
                tabBtn.classList.remove('text-gray-600');
            }
            if (tabContent) {
                tabContent.classList.remove('hidden');
            }
        } else {
            if (tabBtn) {
                tabBtn.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600');
                tabBtn.classList.add('text-gray-600');
            }
            if (tabContent) {
                tabContent.classList.add('hidden');
            }
        }
    });
}

// 切换批量关键词设置
function toggleBatchKeywordsGroup() {
    const checkbox = document.getElementById('batch-enable-keyword');
    const group = document.getElementById('batch-keywords-group');
    if (checkbox && group) {
        group.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量伪原创设置
function toggleBatchPseudoSettings() {
    const checkbox = document.getElementById('batch-enable-pseudo');
    const settings = document.getElementById('batch-pseudo-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量结构注入设置
function toggleBatchStructureSettings() {
    const checkbox = document.getElementById('batch-enable-structure');
    const settings = document.getElementById('batch-structure-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 注意：结构注入库现在使用复选框，以下函数已废弃
// function addBatchStructureLibrary(select) { ... }
// function removeBatchStructureLibrary(libId, btn) { ... }

// 切换批量Unicode选项
function toggleBatchUnicodeOptions() {
    const checkbox = document.getElementById('batch-enable-unicode');
    const options = document.getElementById('batch-unicode-options');
    if (checkbox && options) {
        options.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量随机字符串设置
function toggleBatchRandomStringSettings() {
    const checkbox = document.getElementById('batch-enable-random-string');
    const settings = document.getElementById('batch-random-string-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量H1标签设置
function toggleBatchH1TagSettings() {
    const checkbox = document.getElementById('batch-enable-h1-tag');
    const settings = document.getElementById('batch-h1-tag-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量隐藏HTML设置
function toggleBatchHiddenHTMLSettings() {
    const checkbox = document.getElementById('batch-enable-hidden-html');
    const settings = document.getElementById('batch-hidden-html-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量首页关键词设置
function toggleBatchHomeKeywordSettings() {
    const checkbox = document.getElementById('batch-enable-home-keyword-inject');
    const settings = document.getElementById('batch-home-keyword-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 注意：首页关键词库现在使用复选框，以下函数已废弃
// function addBatchHomeKeywordLibrary() { ... }
// function removeBatchHomeKeywordLibrary(libId, btn) { ... }

// 切换批量企业名称设置
function toggleBatchCompanyNameSettings() {
    const checkbox = document.getElementById('batch-enable-company-name');
    const settings = document.getElementById('batch-company-name-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量UA设置模式
function toggleBatchUASettingsMode() {
    const select = document.getElementById('batch-ua_check_mode');
    const settings = document.getElementById('batch-ua-settings-mode');
    if (select && settings) {
        settings.classList.toggle('hidden', select.value !== 'enable');
    }
}

// 切换批量来源判断设置
function toggleBatchRefererSettings() {
    const select = document.getElementById('batch-referer-check-mode');
    const settings = document.getElementById('batch-referer-settings');
    if (select && settings) {
        settings.classList.toggle('hidden', select.value !== 'enable');
    }
}

// 切换批量爬虫屏蔽设置
function toggleBatchSpiderBlockSettings() {
    const checkbox = document.getElementById('batch-enable-spider-block');
    const settings = document.getElementById('batch-spider-block-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量爬虫UA输入
function toggleBatchSpiderUAInput() {
    const checkbox = document.getElementById('batch-use-global-spider-ua');
    const input = document.getElementById('batch-spider-ua');
    if (checkbox && input) {
        input.disabled = checkbox.checked;
    }
}

// 切换批量Sitemap设置
function toggleBatchSitemapSettings() {
    const checkbox = document.getElementById('batch-enable-sitemap');
    const settings = document.getElementById('batch-sitemap-settings');
    if (checkbox && settings) {
        settings.classList.toggle('hidden', !checkbox.checked);
    }
}

// 切换批量拼音模式
function toggleBatchPinyinMode() {
    const select = document.getElementById('batch-pinyin-mode');
    const customSettings = document.getElementById('batch-pinyin-custom-settings');
    const specialChars = document.getElementById('batch-pinyin-special-chars-settings');
    const globalHint = document.getElementById('batch-pinyin-global-hint');
    
    if (select && customSettings && specialChars && globalHint) {
        const value = select.value;
        
        if (value === 'enable') {
            customSettings.style.display = 'block';
            specialChars.style.display = 'block';
            globalHint.style.display = 'none';
        } else if (value === 'disable') {
            customSettings.style.display = 'none';
            specialChars.style.display = 'none';
            globalHint.style.display = 'none';
        } else {
            customSettings.style.display = 'none';
            specialChars.style.display = 'none';
            globalHint.style.display = value === 'global' ? 'block' : 'none';
        }
    }
}

// 更新批量添加站点数量
function updateBatchCount() {
    const textarea = document.getElementById('batch-sites');
    const countSpan = document.getElementById('batch-count');
    
    if (textarea && countSpan) {
        // 移除旧的事件监听器（如果存在）
        if (textarea._updateCountHandler) {
            textarea.removeEventListener('input', textarea._updateCountHandler);
        }
        
        // 创建新的事件处理函数
        textarea._updateCountHandler = function() {
            const lines = this.value.split('\n').filter(line => {
                const trimmed = line.trim();
                if (!trimmed) return false;
                const parts = trimmed.split('|||');
                return parts.length >= 2 && parts[0].trim() && parts[1].trim();
            });
            countSpan.textContent = lines.length;
        };
        
        // 添加事件监听器
        textarea.addEventListener('input', textarea._updateCountHandler);
        
        // 初始化计数
        textarea._updateCountHandler.call(textarea);
    }
}

// 初始化批量添加表单
function initBatchAddForm() {
    const form = document.getElementById('batch-site-form');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            await submitBatchSites();
        });
    }
}

// 提交批量站点 - 批次提交模式
async function submitBatchSites() {
    const sitesText = document.getElementById('batch-sites').value.trim();
    if (!sitesText) {
        showToast('请输入站点信息', 'error');
        return;
    }
    
    // 获取原始行数据
    const lines = sitesText.split('\n').map(line => line.trim()).filter(line => line);
    if (lines.length === 0) {
        showToast('没有有效的站点信息', 'error');
        return;
    }
    
    // 获取批次大小
    const batchSizeInput = document.getElementById('batch-size');
    const batchSize = batchSizeInput ? parseInt(batchSizeInput.value) || 10 : 10;
    
    // 收集配置
    const config = collectBatchConfig();
    
    // 显示状态列表和进度条
    const statusList = document.getElementById('batch-status-list');
    const statusContent = document.getElementById('batch-status-content');
    const progressBar = document.getElementById('batch-progress-bar');
    const progressCurrent = document.getElementById('batch-progress-current');
    const progressTotal = document.getElementById('batch-progress-total');
    const successCountEl = document.getElementById('batch-success-count');
    const errorCountEl = document.getElementById('batch-error-count');
    
    if (statusList) {
        statusList.style.display = 'block';
        statusContent.innerHTML = '';
    }
    
    // 初始化进度信息
    let successCount = 0;
    let errorCount = 0;
    let currentIndex = 0;
    const totalCount = lines.length;
    
    // 设置进度条初始值
    if (progressTotal) progressTotal.textContent = totalCount;
    if (progressCurrent) progressCurrent.textContent = '0';
    if (successCountEl) successCountEl.textContent = '0';
    if (errorCountEl) errorCountEl.textContent = '0';
    if (progressBar) progressBar.style.width = '0%';
    
    // 禁用提交按钮，避免重复提交
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>正在添加...';
    }
    
    // 将行数据分批
    const batches = [];
    for (let i = 0; i < lines.length; i += batchSize) {
        batches.push(lines.slice(i, i + batchSize));
    }
    
    // 按批次提交站点
    for (const batch of batches) {
        // 为批次中的每个站点创建状态项
        const statusItems = [];
        for (const line of batch) {
            const statusItem = createStatusItem({ domain: line.split('|||')[0] });
            statusContent.insertBefore(statusItem, statusContent.firstChild);
            statusItems.push({ item: statusItem, line: line });
        }
        
        // 准备批次数据
        const batchData = {
            lines: batch,  // 批次数据
            ...config,
            inject_config: config.inject_config
        };
        
        try {
            // 提交批次站点
            const response = await fetch('/api/v1/sites/batch-with-aliases', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(batchData)
            });
            
            const result = await response.json();
            
            // 处理批次响应
            if (result.success && result.data) {
                // 处理成功的站点
                const successedCount = result.data.success_count || 0;
                const errors = result.data.errors || [];
                const details = result.data.details || [];
                
                // 更新每个站点的状态
                for (let i = 0; i < statusItems.length; i++) {
                    const { item, line } = statusItems[i];
                    const domain = line.split('|||')[0];
                    
                    // 查找该站点的处理结果
                    let hasError = false;
                    let errorMsg = '';
                    
                    // 检查是否在错误列表中
                    for (const error of errors) {
                        if (error.includes(domain)) {
                            hasError = true;
                            errorMsg = error;
                            break;
                        }
                    }
                    
                    // 检查详细信息中的错误
                    if (!hasError && details && details[i]) {
                        const detail = details[i];
                        if (detail.error) {
                            hasError = true;
                            errorMsg = detail.error;
                        }
                    }
                    
                    if (hasError) {
                        errorCount++;
                        updateStatusItem(item, 'error', errorMsg || '添加失败');
                        if (errorCountEl) errorCountEl.textContent = errorCount;
                    } else {
                        successCount++;
                        updateStatusItem(item, 'success', '添加成功');
                        if (successCountEl) successCountEl.textContent = successCount;
                    }
                    
                    currentIndex++;
                    if (progressCurrent) progressCurrent.textContent = currentIndex;
                    const progressPercent = (currentIndex / totalCount) * 100;
                    if (progressBar) progressBar.style.width = `${progressPercent}%`;
                }
            } else if (!result.success) {
                // 整个批次失败
                const errorMsg = result.error || result.message || '批次添加失败';
                for (const { item, line } of statusItems) {
                    errorCount++;
                    updateStatusItem(item, 'error', errorMsg);
                    if (errorCountEl) errorCountEl.textContent = errorCount;
                    
                    currentIndex++;
                    if (progressCurrent) progressCurrent.textContent = currentIndex;
                    const progressPercent = (currentIndex / totalCount) * 100;
                    if (progressBar) progressBar.style.width = `${progressPercent}%`;
                }
            }
        } catch (error) {
            // 网络错误，整个批次失败
            for (const { item, line } of statusItems) {
                errorCount++;
                updateStatusItem(item, 'error', `网络错误: ${error.message}`);
                if (errorCountEl) errorCountEl.textContent = errorCount;
                
                currentIndex++;
                if (progressCurrent) progressCurrent.textContent = currentIndex;
                const progressPercent = (currentIndex / totalCount) * 100;
                if (progressBar) progressBar.style.width = `${progressPercent}%`;
            }
        }
        
        // 添加小延迟，避免请求过快（批次间延迟可以更短）
        await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    // 恢复提交按钮
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-plus-circle mr-2"></i>批量添加';
    }
    
    // 显示汇总结果（在最上面）
    const summaryDiv = document.createElement('div');
    summaryDiv.className = errorCount === 0 ? 
        'mb-4 p-4 bg-green-100 border border-green-300 rounded text-green-700' :
        'mb-4 p-4 bg-yellow-100 border border-yellow-300 rounded text-yellow-700';
    summaryDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${errorCount === 0 ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
            <span>${errorCount === 0 ? 
                `全部成功！共添加 ${successCount} 个站点` : 
                `添加完成：成功 ${successCount} 个，失败 ${errorCount} 个`}</span>
        </div>
    `;
    statusContent.insertBefore(summaryDiv, statusContent.firstChild);
    
    // 如果有失败的，收集并显示失败列表
    if (errorCount > 0) {
        const failedItems = statusContent.querySelectorAll('.text-red-600');
        const failedDomains = [];
        const failedDomainsOnly = [];  // 仅域名，用于复制
        
        failedItems.forEach(item => {
            const domain = item.closest('.flex').querySelector('.text-gray-800').textContent;
            const error = item.textContent;
            failedDomains.push(`${domain}: ${error}`);
            
            // 提取原始行数据（域名部分）
            const originalLine = lines.find(line => line.includes(domain.split(',')[0]));
            if (originalLine) {
                failedDomainsOnly.push(originalLine);
            }
        });
        
        const failedDiv = document.createElement('div');
        failedDiv.className = 'mt-2 p-3 bg-red-50 border border-red-200 rounded text-sm text-red-600';
        failedDiv.innerHTML = `
            <div class="flex items-center justify-between mb-2">
                <span class="font-semibold">失败详情：</span>
                <button onclick="copyFailedDomains()" class="px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600">
                    <i class="fas fa-copy mr-1"></i>复制失败域名
                </button>
            </div>
            <div class="space-y-1">
                ${failedDomains.map(d => `<div>• ${d}</div>`).join('')}
            </div>
            <textarea id="failed-domains-data" class="hidden">${failedDomainsOnly.join('\n')}</textarea>
        `;
        statusContent.appendChild(failedDiv);
    }
}

// 复制失败的域名
function copyFailedDomains() {
    const textarea = document.getElementById('failed-domains-data');
    if (textarea && textarea.value) {
        // 创建临时textarea用于复制
        const tempTextarea = document.createElement('textarea');
        tempTextarea.value = textarea.value;
        tempTextarea.style.position = 'fixed';
        tempTextarea.style.opacity = '0';
        document.body.appendChild(tempTextarea);
        tempTextarea.select();
        document.execCommand('copy');
        document.body.removeChild(tempTextarea);
        
        // 显示成功提示
        showToast('失败域名已复制到剪贴板', 'success');
    }
}

// 注意：以下函数已废弃，现在使用逐条提交模式
// function handleBatchResults() { ... }
// function submitBatchSitesOneByOne() { ... }

// 解析批量站点信息
function parseBatchSites(text) {
    const sites = [];
    const lines = text.split('\n');
    
    for (const line of lines) {
        const trimmed = line.trim();
        if (!trimmed) continue;
        
        const parts = trimmed.split('|||').map(p => p.trim());
        if (parts.length < 2) continue;
        
        // 检查是否有子域名格式
        const domainParts = parts[0].split(',').map(p => p.trim()).filter(p => p);
        let siteData;
        
        if (domainParts.length > 1) {
            // 格式2：带子域名
            const mainDomain = domainParts[domainParts.length - 1];
            const subdomains = domainParts.slice(0, -1);
            const aliases = [];
            
            subdomains.forEach(sub => {
                if (sub === '@') {
                    aliases.push(mainDomain);
                } else {
                    aliases.push(`${sub}.${mainDomain}`);
                }
            });
            
            // 主域名使用www前缀（如果没有的话）
            const primaryDomain = subdomains.includes('www') ? `www.${mainDomain}` : mainDomain;
            
            siteData = {
                domain: primaryDomain,
                target_url: parts[1],
                aliases: aliases.filter(a => a !== primaryDomain)
            };
        } else {
            // 格式1：基础格式
            siteData = {
                domain: parts[0],
                target_url: parts[1]
            };
            
            // 可能有别名字段
            if (parts.length > 4) {
                // 有别名
                const aliasesStr = parts[4];
                if (aliasesStr) {
                    siteData.aliases = aliasesStr.split(',').map(a => a.trim()).filter(a => a);
                }
            }
        }
        
        // SEO信息（如果提供）
        if (parts[2]) siteData.home_title = parts[2];
        if (parts[3]) siteData.home_keywords = parts[3];
        if (parts[4] && !siteData.aliases) siteData.home_description = parts[4];
        
        sites.push(siteData);
    }
    
    return sites;
}

// 收集批量配置
function collectBatchConfig() {
    const config = {
        // 基础配置
        status: document.getElementById('batch-status')?.value || 'active',
        crawl_depth: parseInt(document.getElementById('batch-crawl-depth')?.value) || 2,
        enable_preload: document.getElementById('batch-enable-preload')?.checked || false,
        download_external_resources: document.getElementById('batch-download-external')?.checked || false,
        enable_https_check: document.getElementById('batch-enable-https-check')?.checked || false,
        enable_traditional_convert: document.getElementById('batch-traditional-convert')?.checked || false,
        
        // 缓存配置 - 添加默认值
        enable_cache: true,  // 默认启用缓存
        cache_ttl: 7200,     // 默认缓存时间（秒）
        max_cache_size: 500, // 默认最大缓存大小（MB）
        cache_home_ttl: 30,  // 首页缓存时间（分钟）
        cache_other_ttl: 1440, // 其他页面缓存时间（分钟）
        enable_redis_cache: false, // 默认不启用Redis缓存
        use_global_cache: null, // 使用全局缓存设置
        
        // 统计配置
        enable_analytics: document.getElementById('batch-enable-analytics')?.checked || false,
        use_global_analytics: null, // 使用全局统计设置
        analytics_code: '', // 统计代码
        
        // 注入配置对象
        inject_config: {}
    };
    
    // 分类
    const categoryId = document.getElementById('batch-category-id')?.value;
    if (categoryId) {
        config.category_id = parseInt(categoryId);
    } else {
        config.category_id = null;
    }
    
    // 企业名称
    config.enable_company_name = document.getElementById('batch-enable-company-name')?.checked || false;
    if (config.enable_company_name) {
        // 获取选中的企业库
        const checkedLibs = Array.from(document.querySelectorAll('.batch-company-lib:checked'))
            .map(cb => parseInt(cb.value));
        const companyName = document.getElementById('batch-company-name')?.value;
        
        if (checkedLibs.length > 0) {
            // 如果有选中的企业库，使用第一个（后端会随机选择）
            config.company_library_id = checkedLibs[0];
        } else {
            config.company_library_id = null;
        }
        
        if (companyName) {
            config.company_name = companyName;
        }
    } else {
        config.company_library_id = null;
    }
    
    // 拼音设置
    const pinyinMode = document.getElementById('batch-pinyin-mode')?.value;
    if (pinyinMode === 'enable') {
        config.inject_config.use_global_pinyin = false;
        config.inject_config.enable_pinyin = true;
        
        if (document.getElementById('batch-enable-pinyin-special-chars')?.checked) {
            config.inject_config.enable_pinyin_special_chars = true;
            config.inject_config.pinyin_special_chars_ratio = 
                parseFloat(document.getElementById('batch-pinyin-special-chars-ratio')?.value) / 100 || 0.3;
            config.inject_config.pinyin_special_chars = ''; // 从全局设置继承
        } else {
            config.inject_config.enable_pinyin_special_chars = false;
        }
    } else if (pinyinMode === 'disable') {
        config.inject_config.use_global_pinyin = false;
        config.inject_config.enable_pinyin = false;
    } else if (pinyinMode === 'global') {
        config.inject_config.use_global_pinyin = null; // null表示使用全局设置
    } else {
        config.inject_config.use_global_pinyin = null; // 默认使用全局设置
    }
    
    // 内容注入配置
    config.inject_config.filter_external_links = document.getElementById('batch-filter-external')?.checked || false;
    
    // 关键词注入
    if (document.getElementById('batch-enable-keyword')?.checked) {
        config.inject_config.enable_keyword = true;
        config.inject_config.keyword_library_ids = selectedBatchKeywordLibs;
        config.inject_config.title_keyword_library_ids = selectedBatchTitleLibs;
        config.inject_config.meta_keyword_library_ids = selectedBatchMetaLibs;
        config.inject_config.desc_keyword_library_ids = selectedBatchDescLibs;
        
        config.inject_config.keyword_inject_title = document.getElementById('batch-keyword-inject-title')?.checked || false;
        config.inject_config.keyword_inject_body = document.getElementById('batch-keyword-inject-body')?.checked || false;
        config.inject_config.keyword_inject_meta = document.getElementById('batch-keyword-inject-meta')?.checked || false;
        config.inject_config.keyword_inject_desc = document.getElementById('batch-keyword-inject-desc')?.checked || false;
        config.inject_config.keyword_inject_h1 = document.getElementById('batch-keyword-inject-h1')?.checked || false;
        config.inject_config.keyword_inject_h2 = document.getElementById('batch-keyword-inject-h2')?.checked || false;
        config.inject_config.keyword_inject_alt = document.getElementById('batch-keyword-inject-alt')?.checked || false;
        config.inject_config.keyword_inject_hidden = document.getElementById('batch-keyword-inject-hidden')?.checked || false;
        
        config.inject_config.keyword_title_template = document.getElementById('batch-keyword-title-template')?.value || '{keyword1}-{original}';
        config.inject_config.keyword_meta_template = document.getElementById('batch-keyword-meta-template')?.value || '{keyword1},{keyword2},{keyword3}';
        config.inject_config.keyword_desc_template = document.getElementById('batch-keyword-desc-template')?.value || '{keyword1}是专业的{keyword2}服务商';
        
        config.inject_config.keyword_max_per_page = parseInt(document.getElementById('batch-keyword-max-per-page')?.value) || 10;
        config.inject_config.keyword_inject_ratio = parseInt(document.getElementById('batch-keyword-inject-ratio')?.value) || 30;
        config.inject_config.keyword_min_word_count = parseInt(document.getElementById('batch-keyword-min-word-count')?.value) || 20;
        config.inject_config.keyword_density = parseFloat(document.getElementById('batch-keyword-density')?.value) || 2;
    }
    
    // 结构注入
    if (document.getElementById('batch-enable-structure')?.checked) {
        config.inject_config.enable_structure = true;
        config.inject_config.structure_library_ids = selectedBatchStructureLibs;
        config.inject_config.structure_min_per_page = parseInt(document.getElementById('batch-structure-min-per-page')?.value) || 10;
        config.inject_config.structure_max_per_page = parseInt(document.getElementById('batch-structure-max-per-page')?.value) || 20;
    }
    
    // 伪原创
    if (document.getElementById('batch-enable-pseudo')?.checked) {
        config.inject_config.enable_pseudo = true;
        config.inject_config.pseudo_library_ids = selectedBatchPseudoLibs;
    }
    
    // Unicode转码
    if (document.getElementById('batch-enable-unicode')?.checked) {
        config.inject_config.enable_unicode = true;
        config.inject_config.unicode_scope = document.getElementById('batch-unicode-scope')?.value || 'homepage';
        config.inject_config.enable_unicode_title = document.getElementById('batch-enable-unicode-title')?.checked || false;
        config.inject_config.enable_unicode_keywords = document.getElementById('batch-enable-unicode-keywords')?.checked || false;
        config.inject_config.enable_unicode_desc = document.getElementById('batch-enable-unicode-desc')?.checked || false;
    }
    
    // 随机字符串
    if (document.getElementById('batch-enable-random-string')?.checked) {
        config.inject_config.enable_random_string = true;
        config.inject_config.random_string_length = parseInt(document.getElementById('batch-random-string-length')?.value) || 4;
    }
    
    // H1标签
    if (document.getElementById('batch-enable-h1-tag')?.checked) {
        config.inject_config.enable_h1_tag = true;
        config.inject_config.h1_tag_position = document.getElementById('batch-h1-tag-position')?.value || 'both';
    }
    
    // 隐藏HTML
    if (document.getElementById('batch-enable-hidden-html')?.checked) {
        config.inject_config.enable_hidden_html = true;
        config.inject_config.hidden_html_length = parseInt(document.getElementById('batch-hidden-html-length')?.value) || 50;
        config.inject_config.hidden_html_position = document.getElementById('batch-hidden-html-position')?.value || 'both';
        config.inject_config.hidden_html_random_id = document.getElementById('batch-hidden-html-random-id')?.checked || false;
    }
    
    // 首页关键词注入
    if (document.getElementById('batch-enable-home-keyword-inject')?.checked) {
        config.inject_config.enable_home_keyword_inject = true;
        config.inject_config.home_keyword_library_ids = selectedBatchHomeKeywordLibs;
        config.inject_config.home_keyword_inject_count = parseInt(document.getElementById('batch-home-keyword-inject-count')?.value) || 10;
        config.inject_config.enable_home_keyword_unicode = document.getElementById('batch-enable-home-keyword-unicode')?.checked || false;
    }
    
    // 首页SEO设置（需要添加默认值）
    config.inject_config.home_title = '';
    config.inject_config.home_description = '';
    config.inject_config.home_keywords = '';
    
    // UA判断配置
    const uaCheckMode = document.getElementById('batch-ua_check_mode')?.value;
    if (uaCheckMode === 'enable') {
        config.use_global_ua_check = false;
        config.enable_ua_check = true;
        config.allowed_ua = document.getElementById('batch-allowed-ua-mode')?.value || '';
        config.non_spider_html = document.getElementById('batch-non-spider-html-mode')?.value || '';
    } else if (uaCheckMode === 'disable') {
        config.use_global_ua_check = false;
        config.enable_ua_check = false;
        config.allowed_ua = '';
        config.non_spider_html = '';
    } else {
        config.use_global_ua_check = true; // 使用全局设置
        config.enable_ua_check = false;
        config.allowed_ua = '';
        config.non_spider_html = '';
    }
    
    // 来源判断配置
    const refererCheckMode = document.getElementById('batch-referer-check-mode')?.value;
    if (refererCheckMode === 'enable') {
        config.use_global_referer_check = false;
        config.enable_referer_check = true;
        config.allowed_referers = document.getElementById('batch-allowed-referers')?.value || '';
        config.referer_block_code = parseInt(document.getElementById('batch-referer-block-code')?.value) || 403;
        config.referer_block_html = document.getElementById('batch-referer-block-html')?.value || '';
        config.non_referer_html = ''; // 添加这个字段
    } else if (refererCheckMode === 'disable') {
        config.use_global_referer_check = false;
        config.enable_referer_check = false;
        config.allowed_referers = '';
        config.referer_block_code = 403;
        config.referer_block_html = '';
        config.non_referer_html = '';
    } else {
        config.use_global_referer_check = true; // 使用全局设置
        config.enable_referer_check = false;
        config.allowed_referers = '';
        config.referer_block_code = 403;
        config.referer_block_html = '';
        config.non_referer_html = '';
    }
    
    // 域名跳转
    const redirectWww = document.getElementById('batch-redirect-www')?.value;
    if (redirectWww) {
        config.redirect_www = redirectWww === 'true';
    }
    
    // 爬虫屏蔽配置
    config.enable_spider_block = document.getElementById('batch-enable-spider-block')?.checked || false;
    if (config.enable_spider_block) {
        config.use_global_spider_ua = document.getElementById('batch-use-global-spider-ua')?.checked !== false; // 默认true
        if (!config.use_global_spider_ua) {
            config.custom_spider_ua = document.getElementById('batch-spider-ua')?.value || '';
        } else {
            config.custom_spider_ua = '';
        }
        config.spider_block_403_template = document.getElementById('batch-spider-block-403-template')?.value || '';
    } else {
        config.use_global_spider_ua = true;
        config.custom_spider_ua = '';
        config.spider_block_403_template = '';
    }
    
    // Sitemap配置
    config.enable_sitemap = document.getElementById('batch-enable-sitemap')?.checked || false;
    if (config.enable_sitemap) {
        config.sitemap_update_interval = parseInt(document.getElementById('batch-sitemap-update-interval')?.value) || 60;
        config.sitemap_priority = parseFloat(document.getElementById('batch-sitemap-priority')?.value) || 0.8;
        config.sitemap_changefreq = document.getElementById('batch-sitemap-changefreq')?.value || 'daily';
        config.sitemap_max_urls = parseInt(document.getElementById('batch-sitemap-max-urls')?.value) || 50000;
    } else {
        config.sitemap_update_interval = 60;
        config.sitemap_priority = 0.8;
        config.sitemap_changefreq = 'daily';
        config.sitemap_max_urls = 50000;
    }
    
    return config;
}

// 创建状态项
function createStatusItem(site) {
    const div = document.createElement('div');
    div.className = 'flex items-center justify-between p-2 border rounded';
    
    const info = document.createElement('div');
    info.className = 'flex items-center space-x-2';
    
    const icon = document.createElement('span');
    icon.className = 'status-icon';
    icon.innerHTML = '<i class="fas fa-spinner fa-spin text-blue-500"></i>';
    
    const text = document.createElement('span');
    text.className = 'text-sm';
    text.textContent = site.domain;
    if (site.aliases && site.aliases.length > 0) {
        text.textContent += ` (+${site.aliases.length}个别名)`;
    }
    
    const status = document.createElement('span');
    status.className = 'status-text text-sm text-gray-500';
    status.textContent = '正在添加...';
    
    info.appendChild(icon);
    info.appendChild(text);
    div.appendChild(info);
    div.appendChild(status);
    
    return div;
}

// 更新状态项
function updateStatusItem(item, status, message) {
    const icon = item.querySelector('.status-icon');
    const text = item.querySelector('.status-text');
    
    if (status === 'success') {
        item.classList.add('bg-green-50', 'border-green-200');
        icon.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
        text.className = 'status-text text-sm text-green-600';
    } else {
        item.classList.add('bg-red-50', 'border-red-200');
        icon.innerHTML = '<i class="fas fa-times-circle text-red-500"></i>';
        text.className = 'status-text text-sm text-red-600';
    }
    
    text.textContent = message;
}

// 显示Toast提示
function showToast(message, type = 'info') {
    const container = document.getElementById('toast-container');
    if (!container) return;
    
    const toast = document.createElement('div');
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'info': 'bg-blue-500',
        'warning': 'bg-yellow-500'
    }[type] || 'bg-gray-500';
    
    toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-4 transform transition-all duration-300 translate-x-full`;
    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    container.appendChild(toast);
    
    // 触发动画
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}