#!/bin/bash

echo "=== 开始备份PostgreSQL数据库 ==="

# 检查Docker容器是否运行
if ! docker ps | grep -q postgres; then
    echo "PostgreSQL容器未运行，尝试启动..."
    cd /Users/<USER>/Desktop/站群/deploy
    docker compose up -d postgres
    sleep 10
fi

# 备份数据库
echo "正在导出完整数据库..."
docker exec postgres pg_dump -U sitecluster -d sitecluster --no-owner --no-privileges --clean --if-exists > /Users/<USER>/Desktop/站群/deploy/db_backup.sql

if [ $? -eq 0 ]; then
    echo "✅ 数据库备份成功"
    echo "备份文件: deploy/db_backup.sql"
    ls -lah /Users/<USER>/Desktop/站群/deploy/db_backup.sql
else
    echo "❌ 数据库备份失败"
    exit 1
fi

echo "=== 数据库备份完成 ==="