#!/bin/bash

echo "=== 清理Docker环境并创建新的干净数据库 ==="
echo

# 1. 停止现有的PostgreSQL容器
echo "步骤1: 停止现有的PostgreSQL容器..."
docker stop site-cluster-postgres 2>/dev/null
docker stop site-cluster-redis 2>/dev/null
echo "✅ 容器已停止"

# 2. 删除容器
echo -e "\n步骤2: 删除旧容器..."
docker rm site-cluster-postgres 2>/dev/null
docker rm site-cluster-redis 2>/dev/null
echo "✅ 容器已删除"

# 3. 删除旧的数据卷（包含旧数据）
echo -e "\n步骤3: 删除旧的数据卷..."
docker volume rm site-cluster_postgres_data 2>/dev/null
docker volume rm site-cluster_redis_data 2>/dev/null
echo "✅ 旧数据卷已删除"

# 4. 创建新的PostgreSQL容器（使用新的卷）
echo -e "\n步骤4: 创建新的PostgreSQL容器..."
docker run -d \
  --name site-cluster-postgres \
  --health-cmd="pg_isready -U sitecluster" \
  --health-interval=10s \
  --health-timeout=5s \
  --health-retries=5 \
  -e POSTGRES_USER=sitecluster \
  -e POSTGRES_PASSWORD=sitecluster123 \
  -e POSTGRES_DB=sitecluster \
  -p 5432:5432 \
  -v site-cluster_postgres_data_new:/var/lib/postgresql/data \
  postgres:15-alpine

# 5. 创建新的Redis容器
echo -e "\n步骤5: 创建新的Redis容器..."
docker run -d \
  --name site-cluster-redis \
  -p 6379:6379 \
  -v site-cluster_redis_data_new:/data \
  redis:7-alpine

# 6. 等待PostgreSQL启动
echo -e "\n步骤6: 等待PostgreSQL启动..."
sleep 5

# 检查PostgreSQL是否健康
for i in {1..10}; do
  if docker exec site-cluster-postgres pg_isready -U sitecluster > /dev/null 2>&1; then
    echo "✅ PostgreSQL已就绪"
    break
  fi
  echo "等待PostgreSQL启动... ($i/10)"
  sleep 2
done

# 7. 显示容器状态
echo -e "\n步骤7: 检查容器状态..."
docker ps | grep -E "site-cluster|CONTAINER"

echo -e "\n=== 清理完成 ==="
echo "现在你有一个全新的、干净的数据库环境！"
echo "数据卷名称："
echo "  - PostgreSQL: site-cluster_postgres_data_new"
echo "  - Redis: site-cluster_redis_data_new"
echo
echo "下一步："
echo "1. 运行 ./start.sh 启动应用"
echo "2. 应用会自动创建表结构"
echo "3. 不会有任何旧的spider_blocks规则了！"